<script setup lang="ts" name="slxxcx-view">
import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Tabs } from 'ant-design-vue';

import { apiHjxxZpytbView, apiZjtRxxxbView } from '#/api';
import XtfzxxDetailTab from '#/components/ksxtYwjbxxb/xtfzxxDetailTab.vue';
import CldyDetailTab from '#/components/zjtSlxxb/cldyDetailTab.vue';
import MaterialTab from '#/components/zjtSlxxb/materialTab.vue';
import Photo from '#/components/zjtSlxxb/photo.vue';
import RzxxDetailTab from '#/components/zjtSlxxb/rzxxDetailTab.vue';
import SfqdDetailTab from '#/components/zjtSlxxb/sfqdDetailTab.vue';
import SlxxDetailTab from '#/components/zjtSlxxb/slxxDetailTab.vue';
import SqxxDetailTab from '#/components/zjtSlxxb/sqxxDetailTab.vue';
import ZwxxDetailTab from '#/components/zjtSlxxb/zwxxDetailTab.vue';

const zpyp = ref<any>();
const lzrzp = ref<any>();
const data = ref();
const activeKey = ref('slxx');
const [Drawer, drawerApi] = useVbenDrawer({
  zIndex: 999,
  title: '受理信息详情',
  showCancelButton: false,
  confirmText: '关闭',
  class: 'w-full',
  appendToMain: true,
  onConfirm: () => {
    drawerApi.close();
  },
  onOpenChange: async (isOpen: boolean) => {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
      if (data.value.zpytbid) {
        zpyp.value = await apiHjxxZpytbView({ id: data.value.zpytbid });
      }
      if (data.value.lqrzpid) {
        lzrzp.value = await apiZjtRxxxbView({ id: data.value.lqrzpid });
      }
    }
  },
});

const tabs = ref([
  {
    key: 'slxx',
    tab: '详细信息',
    component: shallowRef(SlxxDetailTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'sqxx',
    tab: '申请信息',
    component: shallowRef(SqxxDetailTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'blrz',
    tab: '办理日志',
    component: shallowRef(RzxxDetailTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'blcl',
    tab: '办理材料',
    component: shallowRef(MaterialTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'zwxx',
    tab: '指纹信息',
    component: shallowRef(ZwxxDetailTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'sfqd',
    tab: '收费清单',
    component: shallowRef(SfqdDetailTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'lzrzp',
    tab: '领证人照片',
    component: shallowRef(Photo),
    photo: lzrzp,
  },
  {
    key: 'cldyxx',
    tab: '材料打印信息',
    component: shallowRef(CldyDetailTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'zpyt',
    tab: '照片原图',
    component: shallowRef(Photo),
    photo: zpyp,
  },
  {
    key: 'ksxtfzxx',
    tab: '跨省协同辅助信息',
    component: shallowRef(XtfzxxDetailTab),
    props: (data: any) => ({ data }),
  },
]);
</script>

<template>
  <Drawer>
    <Tabs v-model:active-key="activeKey">
      <Tabs.TabPane v-for="tab in tabs" :key="tab.key" :tab="tab.tab">
        <component
          :is="tab.component"
          v-bind="tab.props ? tab.props(data) : {}"
          v-if="tab.key !== 'lzrzp' && tab.key !== 'zpyt'"
        />
        <component v-else :is="tab.component" :photo-data="tab.photo" />
      </Tabs.TabPane>
    </Tabs>
  </Drawer>
</template>
