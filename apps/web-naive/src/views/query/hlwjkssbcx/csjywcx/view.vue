<script setup lang="ts" name="csjywcx-view">
import { ref, computed, shallowRef } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Tabs } from 'ant-design-vue';

import JbxxDetailTab from '#/components/ksywYwjbxxb/jbxxDetailTab.vue';
import RzxxDetailTab from '#/components/ksywYwjbxxb/rzxxDetailTab.vue';
import RkxxDetailTab from '#/components/ksywYwjbxxb/rkxxDetailTab.vue';
import QyxxDetailTab from '#/components/ksywYwjbxxb/qyxxDetailTab.vue'
import ZqyxxDetailTab from '#/components/ksywYwjbxxb/zqyxxDetailTab.vue';

const data = ref();
const activeKey = ref('jbxx');
const [Drawer, drawerApi] = useVbenDrawer({
  zIndex: 999,
  title: '受理信息详情',
  showCancelButton: false,
  confirmText: '关闭',
  class: 'w-full',
  appendToMain: true,
  onConfirm: () => {
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
      drawerApi.setState({ title: data.value.ywlxLabel });
    }
  },
});

let tabs = ref([
  {
    key: 'jbxx',
    tab: '基本信息',
    component: shallowRef(JbxxDetailTab),
    props: (data: any) => ({ data }),
  },

  {
    key: 'rzxx',
    tab: '日志信息',
    component: shallowRef(RzxxDetailTab),
    props: (data: any) => ({ data }),
  },
]);
const tabsItem = ref([
  {
    key: 'rkxx',
    tab: '人口信息',
    component: shallowRef(RkxxDetailTab),
    code: '010104',
    props: (data: any) => ({ data }),
  },
  {
    key: 'qyxx',
    tab: '迁移信息',
    component: shallowRef(QyxxDetailTab),
    code: '010124',
    props: (data: any) => ({ data }),
  },
  {
    key: 'zqyxx',
    tab: '准迁信息',
    code: '010122',
    component: shallowRef(ZqyxxDetailTab),
    props: (data: any) => ({ data }),
  },
]);
let tabList = computed(() => {
  let baseTabs: any = tabs.value.slice();
  if (data.value && data.value.ywlx) {
    const extraTabs = tabsItem.value.find((e) => e.code === data.value.ywlx);
    if (extraTabs) {
      baseTabs.splice(1, 0, extraTabs); // 这样 baseTabs 被插入了
    }
    return baseTabs; // 返回插入后的新数组
  }
  return baseTabs;
});
</script>

<template>
  <Drawer>
    <Tabs v-model:active-key="activeKey">
      <Tabs.TabPane v-for="tab in tabList" :key="tab.key" :tab="tab.tab">
        <component
          :is="tab.component"
          v-bind="tab.props ? tab.props(data) : {}"
        />
      </Tabs.TabPane>
    </Tabs>
  </Drawer>
</template>
