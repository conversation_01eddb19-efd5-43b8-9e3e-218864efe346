import type { BaseFormComponentType, VbenFormSchema } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

// 搜索表单配置
export const QueryFormSchema: VbenFormSchema<BaseFormComponentType>[] = [
  {
    component: 'Input',
    componentProps: {},
    fieldName: 'sqrxm',
    label: '申请人姓名',
  },
  {
    component: 'Input',
    componentProps: {},
    fieldName: 'sqrgmsfhm',
    label: '申请人公民身份证号码',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      options: [],
      params: {
        type: '5028',
      },
    },
    fieldName: 'ywlx',
    label: '业务类型',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      options: [],
      params: {
        type: '5029',
      },
    },
    fieldName: 'blzt',
    label: '办理状态',
  },
  {
    component: 'RangePicker',
    fieldName: 'slsj',
    componentProps: {
      placeholder: ['请选择', '请选择'],
    },
    label: '受理时间范围',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      options: [],
      params: {
        type: 'ywbm',
      },
    },
    fieldName: 'spywlx',
    label: '审批业务类型',
  },
  {
    component: 'ApiCascader',
    componentProps: {
      params: {
        minLevel: 9,
        dataType: 'ga',
        glmType: 'cxglm',
        ssxq: '',
      },
    },
    fieldName: 'hjdsjgsdwdm',
    label: '户籍地数据归属单位',
  },
  {
    component: 'ApiCascader',
    componentProps: {
      params: {
        minLevel: 9,
        dataType: 'ga',
        glmType: 'cxglm',
        ssxq: '',
      },
    },
    fieldName: 'sldsjgsdwdm',
    label: '受理地数据归属单位代码',
  },
];

// 表格列数据
export function useColumns<T = any>(
  onActionClick: T,
): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { title: '业务类型', field: 'ywlxLabel' },
    { title: '治安管理类别', field: 'zaglywlbdmLabel' },
    { title: '办理状态', field: 'blztLabel' },
    { title: '受理时间', field: 'slsj' },
    { title: '审批业务类型', field: 'spywlxLabel' },
    { title: '户籍地数据归属单位名称', field: 'hjdsjgsdwmc' },
    { title: '受理地归属单位名称', field: 'sldsjgsdwmc' },
    { title: '申请人姓名', field: 'sqrxm' },
    { title: '申请人公民身份号码', field: 'sqrgmsfhm', width: 150 },
    { title: '申请人联系电话', field: 'sqrlxdh', width: 150 },
    { title: '户主公民身份号码', field: 'hzgmsfhm' },
    { title: '户主姓名', field: 'hzxm' },
    { title: '迁移范围代码', field: 'qyfwdm' },
    { title: '户籍地归属单位代码', field: 'hjdsjgsdwdm', width: 150 },
    { title: '受理地联系电话', field: 'sldlxdh' },
    { title: '受理人姓名', field: 'slrxm' },
    { title: '治安管理业务协同编号', field: 'zaglywxtbh' },
    { title: '入库时间', field: 'rksj', width: 150 },
    { title: '业务办理时间', field: 'ywblsj', width: 150 },
    { title: '业务办理人姓名', field: 'ywblrxm' },
    { title: '审核结果', field: 'shjgLabel' },
    { title: '审核人姓名', field: 'shrxm' },
    { title: '是否需要快递', field: 'sfxykdbzLabel' },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: ['view'],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 160,
    },
  ];
}
