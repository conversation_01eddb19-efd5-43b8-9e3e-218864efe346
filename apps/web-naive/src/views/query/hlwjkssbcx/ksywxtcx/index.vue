<script lang="ts" setup name="ksywxtcx-index">
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { KsywYwjbxxbPageResp } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { defaultFormConfig } from '#/adapter/form';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import {  apiKsxtYwjbxxbPage } from '#/api';

import { QueryFormSchema, useColumns } from './data';
import _View from './view.vue';

const searchCount = ref(true);

const onActionClick = ({
  code,
  row,
}: OnActionClickParams<KsywYwjbxxbPageResp>) => {
  switch (code) {
    case 'view': {
      handleView(row);
      break;
    }
    default: {
      break;
    }
  }
};

const [View, viewApi] = useVbenDrawer({
  connectedComponent: _View,
  destroyOnClose: true,
});

const handleView = async (row: KsywYwjbxxbPageResp) => {
  viewApi.setData({ ...row}).open();
};
const [Grid] = useVbenVxeGrid({
  formOptions: {
    schema: QueryFormSchema,
    ...defaultFormConfig,
    fieldMappingTime: [['slsj', ['slsjStart', 'slsjEnd'], 'YYYY-MM-DD']],
  },
  gridOptions: {
    columns: useFormatterColumns(useColumns(onActionClick)),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage, pageSize } = page;
          formValues.slfsx = '52';
          const submitData = formatCascaderValue(formValues, [
            'hjdsjgsdwdm',
            'sldsjgsdwdm',
          ]);
          if (submitData.hjdsjgsdwdm) {
            submitData.hjdsjgsdwdm = `${submitData.hjdsjgsdwdm}000`;
          }
          if (submitData.sldsjgsdwdm) {
            submitData.sldsjgsdwdm = `${submitData.sldsjgsdwdm}000`;
          }

          const result = await apiKsxtYwjbxxbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: searchCount.value,
            ...submitData,
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'ksywid',
    },
  } as VxeTableGridOptions,
});
</script>
<template>
  <Page class="flex h-full flex-col">
    <Grid class="flex-1">
      <template #submit-before>
        <a-switch v-model:checked="searchCount" />
      </template>
    </Grid>
    <View />
  </Page>
</template>
<style lang="scss" scoped></style>
