<script setup lang="ts" name="ksywxtcx-view">
import { ref, computed, shallowRef } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Tabs } from 'ant-design-vue';

import JbxxDetailTab from '#/components/ksxtYwjbxxb/jbxxDetailTab.vue';
import RzxxDetailTab from '#/components/ksxtYwjbxxb/rzxxDetailTab.vue';
import MaterialTab from '#/components/zjtSlxxb/materialTab.vue';
import XtfkxxDetailTab from '#/components/ksxtYwjbxxb/xtfkxxDetailTab.vue';
import XtfzxxDetailTab from '#/components/ksxtYwjbxxb/xtfzxxDetailTab.vue';
import HjlzmsqTab from '#/components/ksxtYwjbxxb/hjlzmsqTab.vue';
import CsxxTab from '#/components/ksxtYwjbxxb/csxxTab.vue';
import QcsqTab from '#/components/ksxtYwjbxxb/qcsqTab.vue';

const data = ref();
const activeKey = ref('jbxx');
const [Drawer, drawerApi] = useVbenDrawer({
  zIndex: 999,
  title: '受理信息详情',
  showCancelButton: false,
  confirmText: '关闭',
  class: 'w-full',
  appendToMain: true,
  onConfirm: () => {
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
      let code = data.value.ywlx.slice(0, 6);
      data.value.tabType =
        code !== '010111' && code !== '010145' ? '010143' : code;
      data.value.ywslh = data.value.ksxtid;

      drawerApi.setState({
        title:
          data.value.tabType === '010111'
            ? '跨省迁出申请'
            : data.value.tabType === '010145'
              ? '跨省出生信息'
              : '跨省户籍类证明申请',
      });
    }
  },
});

let tabs = ref([
  {
    key: 'jbxx',
    tab: '基本信息',
    component: shallowRef(JbxxDetailTab),
    props: (data: any) => ({ data }),
  },

  {
    key: 'rzxx',
    tab: '日志信息',
    component: shallowRef(RzxxDetailTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'dzqz',
    tab: '电子签章（签名）的文件',
    component: shallowRef(MaterialTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'xtfkxx',
    tab: '跨省协同迁出反馈信息',
    component: shallowRef(XtfkxxDetailTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'xtfzxx',
    tab: '跨省协同辅助信息',
    component: shallowRef(XtfzxxDetailTab),
    props: (data: any) => ({ data }),
  },
]);
const tabsItem = ref([
  {
    key: 'hjlzmsq',
    tab: '采集人员信息',
    code: '010143',
    component: shallowRef(HjlzmsqTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'csxx',
    tab: '采集人员信息',
    code: '010145',
    component: shallowRef(CsxxTab),
    props: (data: any) => ({ data }),
  },
  {
    key: 'qcsq',
    tab: '采集人员信息',
    code: '010111',
    component: shallowRef(QcsqTab),
    props: (data: any) => ({ data }),
  },
]);
let tabList = computed(() => {
  let baseTabs: any = tabs.value.slice();
  if (data.value && data.value.tabType) {
    const extraTabs = tabsItem.value.find((e) => e.code === data.value.tabType);
    if (extraTabs) {
      baseTabs.splice(1, 0, extraTabs); // 这样 baseTabs 被插入了
    }
    return baseTabs; // 返回插入后的新数组
  }
  return baseTabs;
});
</script>

<template>
  <Drawer>
    <Tabs v-model:active-key="activeKey">
      <Tabs.TabPane v-for="tab in tabList" :key="tab.key" :tab="tab.tab">
        <component
          :is="tab.component"
          v-bind="tab.props ? tab.props(data) : {}"
        />
      </Tabs.TabPane>
    </Tabs>
  </Drawer>
</template>
