<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { apiZjtSlxxbApplyLtz } from '#/api';
import { fileToBase64 } from '#/utils/helper';

const data = ref();

// 存储待上传文件的引用
const pendingFiles = ref<any>([]);

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  class: 'min-h-[365px]',
  onConfirm: async () => {
    const { gridApi, queryLtzfjsyl: parentQueryLtzfjsyl } = data.value;
    const values: any = await formApi.validateAndSubmitForm();
    if (!values) return;

    modalApi.lock();
    modalApi.setState({ confirmLoading: true });

    // 将文件转换为base64
    const sqclList = await Promise.all(
      pendingFiles.value.map(async (file: any) => {
        const base64Data = await fileToBase64(file, false);
        return {
          base64Data,
          clmc: file.name,
          wjlx: file.type,
        };
      }),
    );
    const { xm: _xm, files: _files, ...submitValues } = values;
    await apiZjtSlxxbApplyLtz({
      nbslid: data.value.nbslid,
      ...submitValues,
      sqclList,
    })
      .then(() => {
        message.success('申请成功');
        modalApi.close();
        gridApi?.query();
        // 调用父组件的 queryLtzfjsyl 方法
        if (parentQueryLtzfjsyl) {
          parentQueryLtzfjsyl();
        }
      })
      .finally(() => {
        modalApi.unlock();
        modalApi.setState({ confirmLoading: false });
      });
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      formApi.setValues({ xm: data.value.xm });
      // 重置待上传文件列表
      pendingFiles.value = [];
    }
  },
});

const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 120,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      fieldName: 'xm',
      label: '绿通证申请人',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        options: [],
        params: {
          type: 5082,
        },
      },
      fieldName: 'ltzsqsy',
      label: '绿通证申请事由',
      rules: 'required',
    },
    {
      component: 'Upload',
      componentProps: {
        accept: '.jpg,.jpeg',
        // 自动携带认证信息
        disabled: false,
        maxCount: 10,
        multiple: true,
        showUploadList: true,
        // 上传列表的内建样式，支持四种基本样式 text, picture, picture-card 和 picture-circle
        listType: 'picture-card',
        // 阻止默认上传行为，保存文件引用
        beforeUpload: (file: File) => {
          const isJpg =
            file.type === 'image/jpeg' ||
            file.name.toLowerCase().endsWith('.jpg') ||
            file.name.toLowerCase().endsWith('.jpeg');
          if (!isJpg) {
            message.error('只能上传jpg/jpeg格式的文件!');
            return false;
          }
          pendingFiles.value.push(file);
          return false;
        },
        // 文件移除时同步更新pendingFiles
        onRemove: (file: any) => {
          const index = pendingFiles.value.findIndex(
            (f: any) => f.uid === file.uid,
          );
          if (index !== -1) {
            pendingFiles.value.splice(index, 1);
          }
        },
      },
      fieldName: 'files',
      label: '申请材料',
      renderComponentContent: () => {
        return {
          default: () => ['点击上传文件'],
        };
      },
      rules: 'required',
    },
    {
      fieldName: 'ltzsqyy',
      component: 'Textarea',
      componentProps: {
        maxLength: 60,
        rows: 4,
        showCount: true,
      },
      label: '绿通证申请原因',
      formItemClass: 'items-start',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});
</script>
<template>
  <Modal title="绿通证申请">
    <Form />
  </Modal>
</template>
