<script lang="ts" setup name="jlxxxb-index">
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { ZjtSlxxbPageResp } from '#/api';

import { onMounted } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

import { defaultFormConfig } from '#/adapter/form';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import { apiQueryLtzfjsyl, apiZjtSlxxbPage } from '#/api';

import ApplyFormModal from './applyFormModal.vue';
import { QueryFormSchema, useColumns } from './data';

const searchCount = ref(true);
const syl = ref<number>(0);
const onActionClick = ({
  code,
  row,
}: OnActionClickParams<ZjtSlxxbPageResp>) => {
  switch (code) {
    case 'apply': {
      handlerApply(row);
      break;
    }
    default: {
      break;
    }
  }
};
const handlerApply = async (row: ZjtSlxxbPageResp) => {
  if (syl.value <= 0) {
    message.warning('当前无剩余申请次数');
    return;
  }

  modalApi.setData({ ...row, gridApi, queryLtzfjsyl }).open();
};

const queryLtzfjsyl = async () => {
  const res = await apiQueryLtzfjsyl();
  syl.value = res.syl;
  message.success('查询剩余量成功');
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: QueryFormSchema,
    ...defaultFormConfig,
  },
  gridOptions: {
    columns: useFormatterColumns(useColumns(onActionClick)),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage, pageSize } = page;
          formValues.slztList = [
            '06',
            '11',
            '13',
            '16',
            '07',
            '08',
            '12',
            '17',
            '30',
            '24',
            '31',
          ].join(',');
          formValues.zzlxList = ['1', '6'].join(',');
          formValues.sldsjfwcx = true;
          const result = await apiZjtSlxxbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: searchCount.value,
            ...formatCascaderValue(formValues, ['pcs']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'nbslid',
    },
  } as VxeTableGridOptions,
});

const [Modal, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: ApplyFormModal,
});

onMounted(async () => {
  queryLtzfjsyl();
});
</script>
<template>
  <Page>
    <Grid>
      <template #toolbar-actions>
        <a-button type="primary" size="small" @click="queryLtzfjsyl()">
          查询剩余量
        </a-button>
        <span class="ml-5">剩余量：{{ syl }}</span>
        <a-tooltip class="ml-5">
          <template #title>
            查询可申请绿通证的受理信息<br />
            包括的受理状态：<br />
            允许申请：06,11,13,16<br />
            需要重办：07,08,12,17,30<br />
            已在中心无需申请：24,31
          </template>
          <InfoCircleOutlined />
          <span class="ml-1">申请说明</span>
        </a-tooltip>
      </template>
      <template #submit-before>
        <a-switch v-model:checked="searchCount" />
      </template>
    </Grid>
    <Modal />
  </Page>
</template>
