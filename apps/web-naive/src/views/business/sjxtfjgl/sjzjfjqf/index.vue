<script lang="ts" setup name="jlxxxb-index">
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { ZjtSlxxbPageResp } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { message } from 'ant-design-vue';

import { defaultFormConfig } from '#/adapter/form';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  apiIssueQx,
  apiZjtRxxxbView,
  apiZjtSlxxbCountWshLtz,
  apiZjtSlxxbPage,
  apiZjtSlxxbView,
} from '#/api';

import { QueryFormSchema, useColumns } from './data';
import _View from './view.vue';

const searchCount = ref(true);
const wshltz = ref<number>(0);

const countWshLtz = async () => {
  const res = await apiZjtSlxxbCountWshLtz({ zzlxList: '1,6', slzt: '06' });
  wshltz.value = res;
};
const onActionClick = ({
  code,
  row,
}: OnActionClickParams<ZjtSlxxbPageResp>) => {
  switch (code) {
    case 'audit': {
      handlerAudit(row);
      break;
    }
    case 'issue': {
      handlerIssue(row);
      break;
    }
    default: {
      break;
    }
  }
};

const [View, viewApi] = useVbenDrawer({
  connectedComponent: _View,
  destroyOnClose: true,
});

const handlerIssue = async (row: ZjtSlxxbPageResp) => {
  await apiIssueQx({ nbslid: row.nbslid });
  message.success('签发通过');
  gridApi?.query();
};

const handlerAudit = async (row: ZjtSlxxbPageResp) => {
  const res = await apiZjtSlxxbView({ id: row.nbslid });
  const zpRes = await apiZjtRxxxbView({ id: res.zpid });
  viewApi.setData({ ...res, base64zp: zpRes.base64zp, gridApi }).open();
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: QueryFormSchema,
    ...defaultFormConfig,
  },
  gridOptions: {
    columns: useFormatterColumns(useColumns(onActionClick)),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage, pageSize } = page;
          // formValues.slztList = ['11', '62'].join(',');
          formValues.slztDqf = true;
          formValues.hjdsjfwcx = true;
          const result = await apiZjtSlxxbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: searchCount.value,
            ...formatCascaderValue(formValues, ['pcs']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'nbslid',
      isHover: true,
    },
  } as VxeTableGridOptions,
});

onMounted(async () => {
  countWshLtz();
});
</script>
<template>
  <Page>
    <Grid>
      <template #toolbar-actions>
        <!-- <a-button type="primary" size="small" @click="batchIssue()">
          批量签发
        </a-button> -->
        <span class="ml-5" :class="{ 'text-red-600': wshltz > 0 }">
          未审核绿通证: {{ wshltz }}
        </span>
      </template>
      <template #submit-before>
        <a-switch v-model:checked="searchCount" />
      </template>
      <template #czsj="{ row }">
        <span class="text-red-600">{{ row.czsj }}</span>
      </template>
      <template #sldsjgsdwmc="{ row }">
        <span class="text-red-600">{{ row.sldsjgsdwmc }}</span>
      </template>
    </Grid>
    <View />
    <Form />
  </Page>
</template>
<style lang="scss" scoped></style>
