<script lang="ts" setup name="jlxxxb-index">
import { computed, defineAsyncComponent, provide, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { fixedQueryParams } from './data';

const detailData = ref<any>(null);
const updateDetailData = (data: any) => {
  detailData.value = data;
};

provide('fixedQueryParams', fixedQueryParams);
provide('detailData', detailData);
provide('updateDetailData', updateDetailData);

const tabs = [
  { label: '详细信息', key: 'detail' },
  // { label: '采集人员信息', key: 'collect' },
  { label: '日志信息', key: 'log' },
  { label: '材料信息', key: 'material' },
  { label: '户成员信息', key: 'member' },
  { label: '省迁移迁出户成员信息', key: 'moveout' },
  { label: '材料打印信息', key: 'print' },
];

const value = ref(tabs[0].key);

const tabComponents = {
  detail: defineAsyncComponent(() => import('#/components/spls/detailTab.vue')),
  collect: defineAsyncComponent(
    () => import('#/components/spls/collectTab.vue'),
  ),
  log: defineAsyncComponent(() => import('#/components/spls/logTab.vue')),
  material: defineAsyncComponent(
    () => import('#/components/spls/materialTab.vue'),
  ),
  member: defineAsyncComponent(() => import('#/components/spls/memberTab.vue')),
  moveout: defineAsyncComponent(
    () => import('#/components/spls/moveoutTab.vue'),
  ),
  print: defineAsyncComponent(() => import('#/components/spls/printTab.vue')),
};

const CurrentTab = computed(() => tabComponents[value.value]);
</script>
<template>
  <Page>
    <div class="tab-header">
      <a-segmented
        v-model:value="value"
        block
        :options="tabs.map((tab) => ({ label: tab.label, value: tab.key }))"
        class="tab-segmented"
      />
    </div>
    <div class="tab-content">
      <component :is="CurrentTab" />
    </div>
  </Page>
</template>
<style lang="scss" scoped>
.tab-header {
  padding: 24px 0 12px;
  background: #fff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
}

.tab-segmented {
  font-size: 16px;
  font-weight: 500;
}

.tab-content {
  min-height: 400px;
  padding: 0 32px;
  background: #fafbfc;
  border-radius: 0 0 8px 8px;
}
</style>
