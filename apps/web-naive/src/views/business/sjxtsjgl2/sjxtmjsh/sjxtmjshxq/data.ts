import type { BaseFormComponentType, VbenFormSchema } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import pinyin from 'js-pinyin';

pinyin.setOptions({ charCase: 2, checkPolyphone: false });

// 固定查询参数
export const fixedQueryParams = {
  pageSize: 10,
  detail: {},
  collect: {},
  log: {
    useLogColumns: () => useLogColumns(),
  },
  material: {
    params: {
      ywbz: '1',
    },
    imgParams: {
      ywbz: '1',
      bhclnr: '1',
      all: '1',
      istoslt: '1',
    },
    useMaterialLeftColumns: () => useMaterialLeftColumns(),
    useMaterialRightColumns: () => useMaterialRightColumns(),
  },
  member: {
    params: {
      ryzt: '0',
      jlbz: '1',
      cxbz: '0',
    },
    useMemberColumns: () => useMemberColumns(),
  },
  moveout: {
    params: {
      ryzt: '0',
      jlbz: '1',
      cxbz: '0',
    },
    useMemberColumns: () => useMemberColumns(),
  },
  print: {
    usePrintTabColumns: () => usePrintTabColumns(),
  },
  yjtqhlwcl: {
    useYjtqhlwclLeftColumns: () => useYjtqhlwclLeftColumns(),
    useYjtqhlwclRightColumns: () => useYjtqhlwclRightColumns(),
  },
};

// 搜索表单配置
export const QueryFormSchema: VbenFormSchema<BaseFormComponentType>[] = [
  {
    component: 'Input',
    componentProps: {},
    fieldName: 'slh',
    label: '受理号',
  },
  {
    component: 'Input',
    componentProps: {},
    fieldName: 'gmsfhm',
    label: '公民身份号码',
  },
  {
    component: 'Input',
    componentProps: {},
    fieldName: 'xm',
    label: '姓名',
  },
  {
    component: 'ApiCascader',
    componentProps: {
      params: {
        minLevel: 6,
        dataType: 'zf',
        glmType: 'cxglm',
        ssxq: '',
      },
    },
    fieldName: 'ssxq',
    label: '所属区县',
  },
];

// 表格列数据
export function useColumns<T = any>(
  onActionClick: T,
): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    // { title: '业务受理号', field: 'ywslh' },
    // { title: '流程定义ID，格式为 流程KEY：版本号：部署号', field: 'lcdyid' },
    { title: '业务类型', field: 'lcywlxLabel' },
    { title: '办理状态', field: 'blztLabel' },
    { title: '审批流程', field: 'lcmc' },
    { title: '姓名', field: 'xm' },
    { title: '公民身份号码', field: 'gmsfhm' },
    { title: '申请人姓名', field: 'sqrxm' },
    { title: '申请人公民身份号码', field: 'sqrgmsfhm' },
    { title: '申请日期', field: 'sqrq' },
    { title: '申请人联系电话', field: 'sqrlxdh' },
    { title: '受理数量', field: 'slrs' },
    { title: '办理模式', field: 'spxxlylbLabel' },
    { title: '**打印出件类型', field: 'dycjlx' },
    // { title: '受理单位_公安机关机构代码', field: 'sldwgajgjgdm' },
    { title: '受理单位名称', field: 'sldwgajgmc' },
    // { title: '数据归属单位代码', field: 'sjgsdwdm' },
    { title: '数据归属单位', field: 'sjgsdwmc' },
    { title: '受理时间', field: 'slsj' },
    { title: '审批是否完成', field: 'lcztLabel' },
    { title: '采集方式', field: 'spxxcjfsLabel' },
    { title: '**互联网申请业务', field: 'hlwsqlx' },
    { title: '**政务大厅对应的常口受理点', field: 'zwwdtmc' },
    { title: '**政务大厅用户名称', field: 'hallusername' },
    { title: '**互联网申报来源', field: 'applyfrom' },
    { title: '**户籍事项查询类别', field: 'hjsxcxlb' },
    { title: '**是否跨省协同迁入', field: 'sfksxtqr' },
    { title: '是否需要邮寄证件', field: 'sfxyyjzjLabel' },
    { title: '收件人联系电话', field: 'sjrlxdh' },
    { title: '审批单位', field: 'spdwgajgmc' },
    { title: '审批时间', field: 'spsj' },
    { title: '受理人姓名', field: 'slrxm' },
    { title: '户主姓名', field: 'hzxm' },
    { title: '户主公民身份号码', field: 'hzgmsfhm' },
    { title: '评价结果', field: 'pjjgLabel' },
    { title: '跑了几次评价', field: 'pjpljcLabel' },

    // { title: '审批意见', field: 'spyj' },
    // { title: '关于**等几人落户的申请报告', field: 'lcywbt' },
    // { title: '申请人与变动人关系', field: 'sqrybdrgxLabel' },
    // { title: '申请人住址省市县（区）', field: 'sqrzzssxq' },
    // { title: '申请人住址详址', field: 'sqrzzxz' },
    // { title: '申请人户口登记机关', field: 'sqrhkdjjg' },
    // { title: '收件人姓名', field: 'sjrxm' },
    // { title: '申请人通讯地址', field: 'sqrtxdz' },
    // { title: '是否生成电子签章户口页', field: 'sfscdzqzhkyLabel' },
    // { title: '受理人联系电话', field: 'slrlxdh' },
    // { title: '审批人联系电话', field: 'sprlxdh' },
    // { title: '是否已经邮寄证件', field: 'sfyjyjzjLabel' },
    // { title: '审批单位公安机关机构代码', field: 'spdwgajgjgdm' },
    // { title: '审批人姓名', field: 'sprxm' },
    // { title: '居（村）委会', field: 'jcwh' },
    // { title: '受理人ID', field: 'slrid' },
    // { title: '是否需要上报0否1是', field: 'sbsfxyLabel' },
    // { title: '上报状态10受理中30办结可以上报80上报完成', field: 'sbztLabel' },
    // { title: '上报业务类型代码', field: 'sbywlxLabel' },
    // { title: '档案备案标志', field: 'dababzLabel' },
    // { title: '入立户标志', field: 'rlhbzLabel' },
    {
      align: 'center',
      cellRender: {
        attrs: { nameField: 'name', onClick: onActionClick },
        name: 'CellOperation',
        options: ['view'],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 160,
    },
  ];
}

// 材料左侧表格列
export function useMaterialLeftColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '材料名称', field: 'cllxmc' },
    { title: '材料数量', field: 'clsl' },
  ];
}

// 材料右侧表格列
export function useMaterialRightColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '材料名称', field: 'clmc' },
    { title: '序号', field: 'xh' },
    { title: '材料类型代码', field: 'cllxdm' },
    { title: '材料类型名称', field: 'cllxmc' },
    { title: '是否已签章', field: 'sfyqzLabel' },
    { title: '材料采集时间', field: 'clcjsj' },
    { title: '材料采集人', field: 'clcjr' },
    { title: '文件大小', field: 'wjdx' },
    { title: '文件类型', field: 'wjlx' },
    { title: '材料来源类型', field: 'cllylxLabel' },
    { title: '数据归属单位代码', field: 'sjgsdwdm' },
    // { title: '材料创建(删除)人', field: 'clcjr' },
    // { title: '材料创建(删除)时间', field: 'clcjsj' },
    { title: '打印次数', field: 'dycs' },
    { title: '是否保存签名证据', field: 'sfbcqmzjLabel' },
    { title: '公民身份号码', field: 'gmsfhm' },

    // { title: '受理材料编号', field: 'slclbh' },
    // { title: '受理材料清单编号', field: 'slclqdbh' },
    // { title: '业务受理号', field: 'ywslh' },
    // {
    //   title: '金铖流程业务类型，扩展自D_ZAGLYWFL治安管理业务分类与代码',
    //   field: 'lcywlx',
    // },
    // { title: '材料类型大类', field: 'cllxdl' },
    // { title: '材料类型中类', field: 'cllxzl' },
    // { title: '材料类型细类', field: 'cllxxl' },
    // { title: '用来记录具体文件数据存储的位置', field: 'wjsjdz' },
    // { title: '文件的哈希校验值', field: 'hash' },
    // { title: '经度', field: 'lng' },
    // { title: '维度', field: 'lat' },
    // { title: '海拔', field: 'alt' },
    // { title: '文件其他属性，以json格式保存', field: 'qtsx' },
    // { title: '居（村）委会', field: 'jcwh' },
    // { title: '引用受理材料编号', field: 'yyslclbh' },
    // { title: '材料被复用次数', field: 'fycs' },
    // { title: '材料采集人ID', field: 'clcjrid' },
    // { title: '材料采集人IP', field: 'clcjrip' },
    // { title: '材料修改人', field: 'clxgr' },
    // { title: '材料修改人ID', field: 'clxgrid' },
    // { title: '材料修改人IP', field: 'clxgrip' },
    // { title: '请求日志编号', field: 'gaggsjLogWjbh' },
    // { title: '公安公共数据日志数据索引', field: 'gaggsjIndex' },
    // { title: '公安公共数据证件照原始', field: 'gaggsjOrgiUrl' },
    // { title: '公安公共数据证件照文件编号', field: 'gaggsjOrgiWjbh' },
    // { title: '指纹确认图像id', field: 'zwZwqrtxid' },
  ];
}

// 打印表格列数据
export function usePrintTabColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    // { title: '指纹图像ID', field: 'dyid' },
    // { title: '人员ID', field: 'ryid' },
    { title: '姓名', field: 'xm' },
    { title: '公民身份号码', field: 'gmsfhm' },
    { title: '打印类别', field: 'dylbLabel' },
    { title: '证件编号', field: 'zjbh' },
    { title: '印制年份', field: 'yznf' },
    { title: '材料名称', field: 'clmc' },
    { title: '业务类型', field: 'lcywlxLabel' },
    { title: '打印时间', field: 'czsj' },
    // { title: '操作人ID', field: 'czrid' },
    { title: '操作员姓名', field: 'czyxm' },
    // { title: '操作员单位代码', field: 'czydwdm' },
    { title: '打印单位', field: 'czydwmc' },
    { title: '户籍地单位名称', field: 'hjdsjgsdwmc' },
    { title: '操作ip', field: 'czip' },
    // { title: '数据归属单位代码', field: 'hjdsjgsdwdm' },
    // { title: '数据归属单位代码', field: 'sldsjgsdwdm' },
    // { title: '数据归属单位名称', field: 'sldsjgsdwmc' },
    // { title: '业务受理号', field: 'ywslh' },
    // { title: '受理材料编号', field: 'slclbh' },
    // {
    //   align: 'center',
    //   cellRender: {
    //     attrs: { nameField: 'name', onClick: onActionClick },
    //     name: 'CellOperation',
    //     options: ['view', 'edit', 'delete'],
    //   },
    //   field: 'operation',
    //   fixed: 'right',
    //   title: '操作',
    //   width: 160,
    // },
  ];
}

// 日志列
export function useLogColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '办理状态', field: 'blztLabel' },
    { title: '操作时间', field: 'czsj' },
    { title: '操作员姓名', field: 'czyxm' },
    { title: '操作员联系电话', field: 'czylxdh' },
    { title: '操作员单位', field: 'czydwmc' },
    { title: '不合格原因', field: 'spjgdm' },
    { title: '审批意见', field: 'bz' },
    { title: '操作IP', field: 'czip' },
  ];
}

// 户成员&迁移户成员列
export function useMemberColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { title: '姓名', field: 'xm' },
    { title: '性别', field: 'xbLabel' },
    { title: '出生日期', field: 'csrq' },
    { title: '公民身份号码', field: 'gmsfhm' },
    { title: '民族', field: 'mzLabel' },
    { title: '与户主关系', field: 'yhzgxLabel' },
    { title: '城乡属性', field: 'cxsxLabel' },
    { title: '人员状态', field: 'ryztLabel' },
    { title: '户号', field: 'hh' },
    { title: '户类型', field: 'hlxLabel' },
    { title: '派出所', field: 'pcsLabel' },
    { title: '居（村）委会', field: 'jcwhLabel' },
    { title: '户籍地详细地址', field: 'hjdxxdz' },
    { title: '何时迁来', field: 'hsql' },
    { title: '何因迁来', field: 'hyqlLabel' },
    { title: '电话号码', field: 'dhhm' },
    { title: '人员类别', field: 'rylbLabel' },
    { title: '人员锁定状态', field: 'rysdztLabel' },
    { title: '冻结状态', field: 'djztLabel' },
    { title: '何时来本址', field: 'hslbz' },
    { title: '何因来本址', field: 'hylbzLabel' },
    { title: '现住地采集状态', field: 'xzdcjztLabel' },
  ];
}

// 互联网材料提取左侧表格列
export function useYjtqhlwclLeftColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '唯一标识', field: 'unid' },
    { title: '材料名称', field: 'attrname' },
  ];
}

// 互联网材料提取左右侧表格列
export function useYjtqhlwclRightColumns(): VxeTableGridOptions['columns'] {
  return [
    { type: 'checkbox', width: 30 },
    { title: '文件名称', field: 'filename' },
    { title: '文件类型', field: 'wjlx' },
    { title: '文件大小', field: 'wjdx' },
    { title: '完整地址', field: 'fileurl' },
  ];
}
