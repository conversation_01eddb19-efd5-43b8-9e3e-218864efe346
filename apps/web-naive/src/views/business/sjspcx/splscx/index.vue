<script lang="ts" setup name="jlxxxb-index">
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { RktYwslJbPageResp } from '#/api';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { alert, Page, useVbenModal } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { Modal, Result } from 'ant-design-vue';
import dayjs from 'dayjs';

import { defaultFormConfig } from '#/adapter/form';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import { apiRktYwslJbDaglywba, apiRktYwslJbPage } from '#/api';

import { QueryFormSchema, useColumns } from './data';

const router = useRouter();
const searchCount = ref(true);
const percent = ref(0);
const recordResult = ref<any[]>([]);

const openTabWithParams = (id: number) => {
  // 这里就是路由跳转，也可以用path
  router.push({ name: 'sjxtmjshxq', params: { id } });
};

const onRecordFiling = async (id: string) => {
  const data = await apiRktYwslJbDaglywba({ ywslh: id });
  alert({
    buttonAlign: 'center',
    content: h(Result, {
      status: data.fhjg === '00' ? 'success' : 'error',
      subTitle: data.fhjg === '00' ? `备案成功` : `失败：${data.fhms}`,
      title: data.fhjg === '00' ? '操作成功' : '操作失败',
    }),
  });
  gridApi.query();
};

const onActionClick = ({
  code,
  row,
}: OnActionClickParams<RktYwslJbPageResp>) => {
  switch (code) {
    case 'recordFiling': {
      onRecordFiling(row.id);
      break;
    }
    case 'reRecordFiling': {
      onRecordFiling(row.id);
      break;
    }
    case 'view': {
      openTabWithParams(row.id);
      break;
    }
    default: {
      break;
    }
  }
};

const batchRecordFiling = () => {
  const selectRecords = gridApi.grid.getCheckboxRecords();
  const ids = selectRecords.map((item) => item.ywslh);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认备案选中的${ids.length}条记录吗？`,
    onOk: async () => {
      // await batchRecordFiling(ids);
      // await gridApi.query();
      modalApi.open();
    },
  });
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: QueryFormSchema,
    ...defaultFormConfig,
  },
  gridOptions: {
    columns: useFormatterColumns(useColumns(onActionClick)),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage, pageSize } = page;
          let slsjStart: string | undefined;
          let slsjEnd: string | undefined;

          if (formValues.slsj && formValues.slsj.length === 2) {
            slsjStart = dayjs(formValues.slsj[0]).format('YYYYMMDD');
            slsjEnd = dayjs(formValues.slsj[1]).format('YYYYMMDD');
          }

          delete formValues.slsj;

          const result = await apiRktYwslJbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            slsjStart,
            slsjEnd,
            searchCount: searchCount.value,
            ...formatCascaderValue(formValues, ['ssxq']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'dm',
    },
  } as VxeTableGridOptions,
});

const [RecordModal, modalApi] = useVbenModal({
  onOpened: () => {
    const selectRecords = gridApi.grid.getCheckboxRecords();
    selectRecords.forEach(async (record) => {
      const result = await apiRktYwslJbDaglywba({ ywslh: record.ywslh });
      // messageList.value.push(`记录 ${record.ywslh} 备案结果: ${result.fhjg === '00' ? '成功' : '失败'} - ${result.fhms}`);
      recordResult.value.push(result);
      percent.value += 100 / selectRecords.length;
    });
  },
  onOpenChange: () => {
    percent.value = 0; // Reset percent when modal opens
    recordResult.value = []; // Reset message list
  },
});
</script>
<template>
  <Page>
    <Grid>
      <template #toolbar-actions>
        <a-button type="primary" size="small" @click="batchRecordFiling()">
          批量备案
        </a-button>
      </template>
      <template #submit-before>
        <a-switch v-model:checked="searchCount" />
      </template>
    </Grid>
    <Form />
    <RecordModal class="w-[600px]" title="档案备案">
      <div class="flex flex-col items-center">
        <a-card class="w-full" size="small">
          <a-progress
            :percent="percent"
            status="active"
            :size="[300, 20]"
            :format="(percent) => `${Math.round(percent)}%`"
          />
        </a-card>
        <div class="p-2"></div>
        <a-list
          size="large"
          bordered
          :data-source="recordResult"
          class="w-full"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-alert
                class="w-full"
                :message="item.fhms"
                :type="item.fhjg === '00' ? 'success' : 'error'"
                show-icon
              />
            </a-list-item>
          </template>
          <template #header>
            <div>操作结果</div>
          </template>
        </a-list>
      </div>
    </RecordModal>
  </Page>
</template>
<style lang="scss" scoped></style>
