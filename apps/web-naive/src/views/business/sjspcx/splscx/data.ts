import type { BaseFormComponentType, VbenFormSchema } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import dayjs from 'dayjs';
import pinyin from 'js-pinyin';

pinyin.setOptions({ charCase: 2, checkPolyphone: false });

// 搜索表单配置
export const QueryFormSchema: VbenFormSchema<BaseFormComponentType>[] = [
  {
    component: 'Input',
    componentProps: {},
    fieldName: 'gmsfhm',
    label: '公民身份号码',
  },
  {
    component: 'Input',
    componentProps: {},
    fieldName: 'sqrgmsfhm',
    label: '申请人身份号码',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      options: [],
      params: {
        type: 'lcblzt',
      },
    },
    fieldName: 'blzt',
    label: '办理状态',
  },
  {
    component: 'RangePicker',
    componentProps: {
      presets: [
        {
          label: '今日',
          value: [dayjs().startOf('day'), dayjs().endOf('day')],
        },
        {
          label: '本月',
          value: [dayjs().startOf('month'), dayjs().endOf('day')],
        },
        {
          label: '本年',
          value: [dayjs().startOf('year'), dayjs().endOf('day')],
        },
      ],
    },
    fieldName: 'slsj',
    label: '受理时间',
  },
  {
    component: 'ApiYwbmCascader',
    componentProps: {},
    fieldName: 'lcywlx2',
    label: '业务类型',
  },
];

// 表格列数据
export function useColumns<T = any>(
  onActionClick: T,
): VxeTableGridOptions['columns'] {
  return [
    { type: 'checkbox', width: 30 },
    { title: '序号', type: 'seq', width: 50 },
    // { title: '业务受理号', field: 'ywslh' },
    // { title: '流程定义ID，格式为 流程KEY：版本号：部署号', field: 'lcdyid' },
    { title: '业务类型', field: 'lcywlxLabel' },
    { title: '办理状态', field: 'blztLabel' },
    { title: '审批流程', field: 'lcmc' },
    { title: '姓名', field: 'xm' },
    { title: '公民身份号码', field: 'gmsfhm' },
    { title: '申请人姓名', field: 'sqrxm' },
    { title: '申请人公民身份号码', field: 'sqrgmsfhm' },
    { title: '申请日期', field: 'sqrq' },
    { title: '申请人联系电话', field: 'sqrlxdh' },
    { title: '受理数量', field: 'slrs' },
    { title: '办理模式', field: 'spxxlylbLabel' },
    { title: '打印出件类型', field: 'dycjlx' },
    // { title: '受理单位_公安机关机构代码', field: 'sldwgajgjgdm' },
    { title: '受理单位名称', field: 'sldwgajgmc' },
    // { title: '数据归属单位代码', field: 'sjgsdwdm' },
    { title: '数据归属单位', field: 'sjgsdwmc' },
    { title: '受理时间', field: 'slsj' },
    { title: '审批是否完成', field: 'lcztLabel' },
    { title: '采集方式', field: 'spxxcjfsLabel' },
    { title: '互联网申请业务', field: 'hlwsqlx' },
    { title: '政务大厅对应的常口受理点', field: 'zwwdtmc' },
    { title: '政务大厅用户名称', field: 'hallusername' },
    { title: '互联网申报来源', field: 'applyfrom' },
    { title: '户籍事项查询类别', field: 'hjsxcxlb' },
    { title: '是否跨省协同迁入', field: 'sfksxtqr' },
    { title: '是否需要邮寄证件', field: 'sfxyyjzjLabel' },
    { title: '收件人联系电话', field: 'sjrlxdh' },
    { title: '审批单位', field: 'spdwgajgmc' },
    { title: '审批时间', field: 'spsj' },
    { title: '受理人姓名', field: 'slrxm' },
    { title: '户主姓名', field: 'hzxm' },
    { title: '户主公民身份号码', field: 'hzgmsfhm' },
    { title: '评价结果', field: 'pjjgLabel' },
    { title: '跑了几次评价', field: 'pjpljcLabel' },

    // { title: '审批意见', field: 'spyj' },
    // { title: '关于**等几人落户的申请报告', field: 'lcywbt' },
    // { title: '申请人与变动人关系', field: 'sqrybdrgxLabel' },
    // { title: '申请人住址省市县（区）', field: 'sqrzzssxq' },
    // { title: '申请人住址详址', field: 'sqrzzxz' },
    // { title: '申请人户口登记机关', field: 'sqrhkdjjg' },
    // { title: '收件人姓名', field: 'sjrxm' },
    // { title: '申请人通讯地址', field: 'sqrtxdz' },
    // { title: '是否生成电子签章户口页', field: 'sfscdzqzhkyLabel' },
    // { title: '受理人联系电话', field: 'slrlxdh' },
    // { title: '审批人联系电话', field: 'sprlxdh' },
    // { title: '是否已经邮寄证件', field: 'sfyjyjzjLabel' },
    // { title: '审批单位公安机关机构代码', field: 'spdwgajgjgdm' },
    // { title: '审批人姓名', field: 'sprxm' },
    // { title: '居（村）委会', field: 'jcwh' },
    // { title: '受理人ID', field: 'slrid' },
    // { title: '是否需要上报0否1是', field: 'sbsfxyLabel' },
    // { title: '上报状态10受理中30办结可以上报80上报完成', field: 'sbztLabel' },
    // { title: '上报业务类型代码', field: 'sbywlxLabel' },
    // { title: '档案备案标志', field: 'dababzLabel' },
    // { title: '入立户标志', field: 'rlhbzLabel' },
    {
      align: 'center',
      cellRender: {
        attrs: { nameField: 'name', onClick: onActionClick },
        name: 'CellOperation',
        options: [
          'view',
          {
            code: 'recordFiling',
            text: '档案备案',
            confirm: true,
            disabled: (row: any) => row.blzt <= 94,
            show: (row: any) => row.dababz !== '1',
          },
          {
            code: 'reRecordFiling',
            text: '再次备案',
            disabled: (row: any) => row.blzt <= 94,
            show: (row: any) => row.dababz === '1',
            confirm: true,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 160,
    },
  ];
}
