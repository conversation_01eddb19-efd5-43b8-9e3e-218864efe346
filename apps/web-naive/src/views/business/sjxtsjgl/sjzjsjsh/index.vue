<script lang="ts" setup name="jlxxxb-index">
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { ZjtSlxxbPageResp } from '#/api';

import { Page, useVbenForm, useVbenModal } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { message } from 'ant-design-vue';

import { defaultFormConfig } from '#/adapter/form';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  apiApproveDs,
  apiRejectDs,
  apiZjtSlxxbCountWshLtz,
  apiZjtSlxxbPage,
} from '#/api';

import { QueryFormSchema, useColumns } from './data';

const searchCount = ref(true);
const modalData = ref();
const wshltz = ref<number>(0);

const countWshLtz = async () => {
  const res = await apiZjtSlxxbCountWshLtz({ ltzshjg: '11' });
  wshltz.value = res;
};
const onActionClick = ({
  code,
  row,
}: OnActionClickParams<ZjtSlxxbPageResp>) => {
  switch (code) {
    case 'approve': {
      handlerApprove(row);
      break;
    }
    case 'reject': {
      handlerReject(row);
      break;
    }
    default: {
      break;
    }
  }
};

const handlerApprove = async (row: ZjtSlxxbPageResp) => {
  await apiApproveDs({ nbslid: row.nbslid });
  message.success('审批通过');
  gridApi?.query();
};

const handlerReject = async (row: ZjtSlxxbPageResp) => {
  modalData.value = row;
  modalApi.open();
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: QueryFormSchema,
    ...defaultFormConfig,
  },
  gridOptions: {
    columns: useFormatterColumns(useColumns(onActionClick)),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage, pageSize } = page;
          formValues.slzt = '13';
          const result = await apiZjtSlxxbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: searchCount.value,
            ...formatCascaderValue(formValues, ['pcs']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'nbslid',
    },
  } as VxeTableGridOptions,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  class: 'min-h-[365px]',
  onConfirm: async () => {
    const values: any = await formApi.validateAndSubmitForm();
    if (!values) return;

    modalApi.lock();
    modalApi.setState({ confirmLoading: true });
    await apiRejectDs({ nbslid: modalData.value.nbslid, ...values })
      .then(() => {
        message.success('审批拒绝');
        modalApi.close();
        gridApi?.query();
      })
      .finally(() => {
        modalApi.unlock();
        modalApi.setState({ confirmLoading: false });
      });
  },
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 100,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        options: [],
        params: {
          type: 5017,
        },
      },
      fieldName: 'zzxxcwlb',
      label: '错误类型',
      rules: 'required',
    },
    {
      fieldName: 'cwms',
      component: 'Textarea',
      label: '详细描述',
      formItemClass: 'items-start',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

onMounted(async () => {
  countWshLtz();
});
</script>
<template>
  <Page>
    <Grid>
      <template #toolbar-actions>
        <!-- <a-button type="primary" size="small" @click="batchAudit()">
          批量签发
        </a-button> -->
        <span class="ml-5" :class="{ 'text-red-600': wshltz > 0 }">
          未审核绿通证: {{ wshltz }}
        </span>
      </template>
      <template #submit-before>
        <a-switch v-model:checked="searchCount" />
      </template>
      <template #czsj="{ row }">
        <span class="text-red-600">{{ row.czsj }}</span>
      </template>
      <template #sldsjgsdwmc="{ row }">
        <span class="text-red-600">{{ row.sldsjgsdwmc }}</span>
      </template>
    </Grid>
    <View />
    <Form />
    <Modal class="w-[600px]" title="填写审核不通过原因">
      <BasicForm />
    </Modal>
  </Page>
</template>
<style lang="scss" scoped></style>
