<script setup lang="ts" name="xxxx-tab">
import { apiZjtLssfzSlxxbView } from '#/api';
import { onMounted, ref } from 'vue';
const lzxx = ref<any>({});
const activeKey = ref(['1', '2', '3', '4', '5', '6', '7']);
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
onMounted(async () => {
  lzxx.value = await apiZjtLssfzSlxxbView({ id: props.data.lsslid });
});
</script>
<template>
  <a-collapse v-model:active-key="activeKey">
    <a-collapse-panel key="1" header="二代身份证信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="姓名">
          {{ lzxx.xm }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ lzxx.gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="起始日期">
          {{ lzxx.yxqxqsrq }}
        </a-descriptions-item>
        <a-descriptions-item label="有效期限截止日期">
          {{ lzxx.yxqxjzrq }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ lzxx.yxqxjzrqLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="民族">
          {{ lzxx.mzLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ lzxx.csrq }}
        </a-descriptions-item>
        <a-descriptions-item label="身份证地址" :span="2">
          {{ lzxx.qfrq }}
        </a-descriptions-item>
        <a-descriptions-item label="受理时间">
          {{ lzxx.czsj }}
        </a-descriptions-item>
        <a-descriptions-item label="制证类型">
          {{ lzxx.zzlxLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="操作员姓名">
          {{ lzxx.shrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="临时居民身份证卡号">
          {{ lzxx.lsjmsfzkh }}
        </a-descriptions-item>
        <a-descriptions-item label="收费">
          {{ lzxx.fxjsfztLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="非现金收费时间">
          {{ lzxx.fxjsfsj }}
        </a-descriptions-item>
        <a-descriptions-item label="收费类型">
          {{ lzxx.sflxLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="收费金额" :span="2">
          {{ lzxx.sfje }}
        </a-descriptions-item>
        <a-descriptions-item label="居民身份证受理号">
          {{ lzxx.jmsfzslh }}
        </a-descriptions-item>
        <a-descriptions-item label="公安部的受理地数据归属单位代码">
          {{ lzxx.sldsjgsdwdmgab }}
        </a-descriptions-item>
        <a-descriptions-item label="采集方式">
          {{ lzxx.cjfsLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="联系电话">
          {{ lzxx.lxdh }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="2" header="审核及打印信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="打印标志">
          {{ lzxx.dybzLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="审核结果">
          {{ lzxx.shjgLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="打印时间">
          {{ lzxx.dysj }}
        </a-descriptions-item>
        <a-descriptions-item label="打印人姓名">
          {{ lzxx.dyrxm }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="3" header="互联网申报信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="服务对象">
          {{ lzxx.fwdx }}
        </a-descriptions-item>
        <a-descriptions-item label="互联网申请id">
          {{ lzxx.hlwsqid }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="4" header="地址信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="出生地省市区县">
          {{ lzxx.csdssxqLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地省市区县">
          {{ lzxx.ssxqLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地街路巷">
          {{ lzxx.jlx }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地门楼牌号">
          {{ lzxx.mlph }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地门楼详址" :span="2">
          {{ lzxx.mlxz }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地乡镇街道">
          {{ lzxx.xzjd }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地居委会">
          {{ lzxx.jcwh }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地单位">
          {{ lzxx.hjdsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地单位名称">
          {{ lzxx.hjdsjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="受理地单位">
          {{ lzxx.sldsjgsdwmcLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="派出所">
          {{ lzxx.pcs }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="5" header="签发审核信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="区县审核人姓名">
          {{ lzxx.shrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="签发机关">
          {{ lzxx.qfjg }}
        </a-descriptions-item>
        <a-descriptions-item label="第三方用户">
          {{ lzxx.username }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地分局审核耗时(分)">
          {{ lzxx.fjshhs }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="6" header="投递信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="领取方式">
          {{ lzxx.lqfs }}
        </a-descriptions-item>
        <a-descriptions-item label="收件人姓名">
          {{ lzxx.sjrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="收件人联系电话">
          {{ lzxx.sjrlxdh }}
        </a-descriptions-item>
        <a-descriptions-item label="收件人邮编">
          {{ lzxx.sjryb }}
        </a-descriptions-item>
        <a-descriptions-item label="收件人省市县区">
          {{ lzxx.sjrssxq }}
        </a-descriptions-item>
        <a-descriptions-item label="收件人详址">
          {{ lzxx.sjrxz }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地居委会" :span="3">
          {{ lzxx.jcwh }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="3">
          {{ lzxx.bz }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="7" header="满意度评价信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="评价结果">
          {{ lzxx.pjjg }}
        </a-descriptions-item>
        <a-descriptions-item label="跑了几次评价">
          {{ lzxx.pjpljc }}
        </a-descriptions-item>
        <a-descriptions-item label="评价时间">
          {{ lzxx.pjsj }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
  </a-collapse>
</template>
