<script setup lang="ts" name="lzcx-view">
import { apiZjtSlxxbView } from '#/api';

import { ref, shallowRef } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Tabs } from 'ant-design-vue';
import XxxxDetailTab from './components/xxxxDetailTab.vue';
import MaterialTab from '#/components/zjtSlxxb/materialTab.vue';
const data = ref();
const info = ref({});
const activeKey = ref('xxxx');
const [Drawer, drawerApi] = useVbenDrawer({
  zIndex: 999,
  title: '临证信息详情',
  showCancelButton: false,
  confirmText: '关闭',
  class: 'w-full',
  appendToMain: true,
  onConfirm: () => {
    drawerApi.close();
  },
  onOpenChange: async (isOpen: boolean) => {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
      //获取内部ywlsh
      apiZjtSlxxbView({ id: data.value.nbslid }).then((res) => {
        info.value = res;
      });
    }
  },
});

let tabs = ref([
  {
    key: 'xxxx',
    tab: '详情信息',
    component: shallowRef(XxxxDetailTab),
    props: { data },
  },

  {
    key: 'dzqz',
    tab: '电子签章（签名）的文件',
    component: shallowRef(MaterialTab),
    props: { data: info },
  },
]);
</script>

<template>
  <Drawer>
    <Tabs v-model:active-key="activeKey">
      <Tabs.TabPane v-for="tab in tabs" :key="tab.key" :tab="tab.tab">
        <component :is="tab.component" v-bind="tab.props || {}" />
      </Tabs.TabPane>
    </Tabs>
  </Drawer>
</template>
