<script setup lang="ts" name="csxx-tab">
import { ref, onMounted, computed } from 'vue';

import { apiKsxtCssbxxbPage, apiKsxtCssbxxbView } from '#/api';

import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons-vue';

const activeKey = ref<any[]>(['1', '2', '3', '4']);
const personList = ref<any>([]);
const detailInfo = ref<any>({});
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
//获取
onMounted(async () => {
  const { records } = await apiKsxtCssbxxbPage({ ksxtid: props.data.ksxtid });
  personList.value = records;
});
const personChunks = computed(() => {
  const chunkSize = 3;
  const arr = [];
  for (let i = 0; i < personList.value.length; i += chunkSize) {
    arr.push(personList.value.slice(i, i + chunkSize));
  }
  return arr;
});

// 根据身份证号码推算年龄
function getAgeFromIdCard(idCard: string): number | '' {
  if (!idCard || idCard.length < 14) return '';
  let year = 0,
    month = 1,
    day = 1;
  if (idCard.length === 18) {
    year = parseInt(idCard.substr(6, 4));
    month = parseInt(idCard.substr(10, 2));
    day = parseInt(idCard.substr(12, 2));
  } else if (idCard.length === 15) {
    year = 1900 + parseInt(idCard.substr(6, 2));
    month = parseInt(idCard.substr(8, 2));
    day = parseInt(idCard.substr(10, 2));
  } else {
    return '';
  }
  const now = new Date();
  let age = now.getFullYear() - year;
  if (
    now.getMonth() + 1 < month ||
    (now.getMonth() + 1 === month && now.getDate() < day)
  ) {
    age--;
  }
  return age;
}

//获取反馈详情
async function getInfo(id: string) {
  detailInfo.value = await apiKsxtCssbxxbView({ id });
}
</script>

<template>
  <!-- 人员展示 -->
  <a-carousel arrows>
    <template #prevArrow>
      <div class="custom-slick-arrow" style="left: 10px; z-index: 1">
        <left-circle-outlined />
      </div>
    </template>
    <template #nextArrow>
      <div class="custom-slick-arrow" style="right: 10px">
        <right-circle-outlined />
      </div>
    </template>

    <div v-for="(chunk, idx) in personChunks" :key="idx">
      <div class="flex h-[110px] items-center gap-10">
        <div
          class="card relative h-full w-1/3 text-left"
          v-for="(item, i) in chunk"
          :key="i"
          @click="getInfo(item.ksxtcsid)"
        >
          <div class="title">{{ item.xm }}</div>
          <div class="content flex items-center justify-between">
            <div>
              <div>证件号码</div>
              <div>{{ item.gmsfhm }}</div>
            </div>
            <div class="xx"></div>
            <div>
              <div>性别</div>
              <div>{{ item.xbdmLabel }}</div>
            </div>
            <div class="xx"></div>
            <div>
              <div>年龄</div>
              <div>{{ getAgeFromIdCard(item.gmsfhm) }}</div>
            </div>
          </div>
          <div class="absolute right-0 top-0">
            <a-tag color="#108ee9" class="ml-1 mr-0">标签</a-tag>
          </div>
        </div>
      </div>
    </div>
  </a-carousel>
  <!-- 详情 -->
  <a-collapse v-model:active-key="activeKey" v-if="detailInfo.ksxtcsid">
    <a-collapse-panel key="1" header="业务信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="姓名">
          {{ detailInfo.xm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓">
          {{ detailInfo.x }}
        </a-descriptions-item>
        <a-descriptions-item label="名">
          {{ detailInfo.m }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ detailInfo.xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="民族">
          {{ detailInfo.mzdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ detailInfo.csrq }}
        </a-descriptions-item>
        <a-descriptions-item label="出生时间">
          {{ detailInfo.cssj }}
        </a-descriptions-item>
        <a-descriptions-item label="与户主关系">
          {{ detailInfo.yhzgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生证明编号">
          {{ detailInfo.cszmbh }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地省县">
          {{ detailInfo.csdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地详址" :span="2">
          {{ detailInfo.csdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯省县">
          {{ detailInfo.jgssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯详址" :span="2">
          {{ detailInfo.jgqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="现住址省市县区">
          {{ detailInfo.xzzssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="现住址区划内详址" :span="2">
          {{ detailInfo.xzzqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="血型">
          {{ detailInfo.xxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="申报人与出生人关系">
          {{ detailInfo.sbrycsrgxjtgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="母亲姓名">
          {{ detailInfo.mqxm }}
        </a-descriptions-item>
        <a-descriptions-item label="母亲身份号码">
          {{ detailInfo.mqgmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="父亲姓名">
          {{ detailInfo.fqxm }}
        </a-descriptions-item>
        <a-descriptions-item label="父亲身份号码">
          {{ detailInfo.fqgmsfhm }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="2" header="监护人信息(16周岁以下必须填写)">
      <a-descriptions label-placement="left" :column="4" size="small" bordered>
        <a-descriptions-item label="监护人一姓名">
          {{ detailInfo.jhryxm }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人一身份号码">
          {{ detailInfo.jhrygmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="与监护人一的监护关系">
          {{ detailInfo.jhryjhgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="与监护人一联系电话">
          {{ detailInfo.jhrylxdh }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二姓名">
          {{ detailInfo.jhrexm }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二身份号码">
          {{ detailInfo.jhregmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="与监护人二的监护关系">
          {{ detailInfo.jhrejhgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="与监护人二联系电话">
          {{ detailInfo.jhrelxdh }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="3" header="亲属其他信息">
      <a-descriptions label-placement="left" :column="4" size="small" bordered>
        <a-descriptions-item label="监护人一证件种类">
          {{ detailInfo.jhrycyzjdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人一证件号码">
          {{ detailInfo.jhryzjhm }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人一外文姓">
          {{ detailInfo.jhrywwx }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人一外文名">
          {{ detailInfo.jhrywwm }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二证件种类">
          {{ detailInfo.jhrecyzjdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二证件号码">
          {{ detailInfo.jhrezjhm }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二外文姓">
          {{ detailInfo.jhrewwx }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二外文名">
          {{ detailInfo.jhrewwm }}
        </a-descriptions-item>
        <a-descriptions-item label="父亲证件种类" >
          {{ detailInfo.fqcyzjdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="父亲证件号码" :span="3">
          {{ detailInfo.fqzjhm }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="父亲外文姓" >
          {{ detailInfo.fqcyzjdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="父亲外文名" >
          {{ detailInfo.fqzjhm }}
        </a-descriptions-item> -->
        <a-descriptions-item label="母亲证件种类" >
          {{ detailInfo.mqcyzjdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="母亲证件号码" :span="3">
          {{ detailInfo.mqzjhm }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="母亲外文姓" >
          {{ detailInfo.mqcyzjdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="母亲外文名" >
          {{ detailInfo.mqzjhm }}
        </a-descriptions-item> -->
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="4" header="公安部出生证信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="是否落户判断标志">
          {{ detailInfo.pdbzsflhLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生证明黑名单判断标志">
          {{ detailInfo.pdbzcszmhmdLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生证明比对结果">
          {{ detailInfo.bdjgLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="比对描述">
          {{ detailInfo.bdms }}
        </a-descriptions-item>
        <a-descriptions-item label="比对时间">
          {{ detailInfo.bdsj }}
        </a-descriptions-item>
        <a-descriptions-item label="比对人ID">
          {{ detailInfo.bdrid }}
        </a-descriptions-item>
        <a-descriptions-item label="比对人姓名">
          {{ detailInfo.bdrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="比对人IP">
          {{ detailInfo.bdrip }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
  </a-collapse>
</template>
<style scoped lang="scss">
/* For demo */
:deep(.slick-slide) {
  text-align: center;
  overflow: hidden;
  padding: 10px 20px 20px;
}

:deep(.slick-arrow.custom-slick-arrow) {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #000;
  transition: ease all 0.3s;
  opacity: 0.5;
  z-index: 1;
}
:deep(.slick-arrow.custom-slick-arrow:before) {
  display: none;
}
:deep(.slick-arrow.custom-slick-arrow:hover) {
  color: #000;
  opacity: 0.5;
}

:deep(.slick-slide h3) {
  color: #000;
}
:deep(.slick-dots li button) {
  background: #999;
}
:deep(.slick-dots li.slick-active button) {
  background: hsl(var(--primary));
}
.card {
  background: #ffffff;
  box-shadow:
    0px 0px 1px 0px rgba(0, 0, 0, 0.08),
    0px 1px 2px 0px rgba(25, 15, 15, 0.07),
    0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  padding: 10px 17px;
  cursor: pointer;
  .title {
    font-family: AlibabaPuHuiTiM;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
  .content {
    margin-top: 10px;
    > div {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      > div:first-child {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        line-height: 20px;
      }
    }
    .xx {
      height: 33px;
      border-right: 1px dashed rgba(0, 0, 0, 0.45);
    }
  }
}
</style>
