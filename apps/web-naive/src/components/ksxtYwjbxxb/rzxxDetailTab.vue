<template>
  <Page class="flex h-full flex-col">
    <Grid class="flex-1"> </Grid>
  </Page>
</template>
<script lang="ts" setup name="rzxx-tab">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import { Page } from '@vben/common-ui';
import { apiKsxtRzbPage } from '#/api';
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
// 搜索表单配置
function useColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { title: '办理状态', field: 'blztLabel' },
    { title: '操作时间', field: 'czsj' },
    { title: '操作员姓名', field: 'czyxm', width: 150 },
    { title: '操作ip', field: 'czip' },
    { title: '操作员单位', field: 'czydwmc' },
    { title: '审批意见', field: 'bz	' },

  ];
}
const [Grid] = useVbenVxeGrid({
  showSearchForm: false,
  gridOptions: {
    columns: useFormatterColumns(useColumns()),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          
          const { currentPage, pageSize } = page;
          const result = await apiKsxtRzbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: true,
            ksxtid:props.data.ksxtid
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      search: false,
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
    },
  } as VxeTableGridOptions,
});
</script>
<style lang="scss" scoped></style>
