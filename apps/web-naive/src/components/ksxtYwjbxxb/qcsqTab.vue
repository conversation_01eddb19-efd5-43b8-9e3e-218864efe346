<script setup lang="ts" name="qcsq-tab">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import { apiKsxtQcsqxxbPage, apiKsxtQcsqxxbView } from '#/api';
const activeKey = ref(['1', '2', '3', '4']);
const detailInfo = ref<any>({});

// 表格双击事件处理
const handleRightGridRowDblclick = async ({ row }: { row: any }) => {
  detailInfo.value = await apiKsxtQcsqxxbView({ id: row.ksxtqcsqid });
};

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
// 搜索表单配置
function useColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { title: '申请人公民身份号码', field: 'sqrgmsfhm' },
    { title: '申请人姓名', field: 'sqrxm' },
    { title: '申请人联系电话', field: 'sqrlxdh', width: 150 },
    { title: '申请人户口登记机关公安机关机构代码', field: 'sqrhkdjjggajgjgdm' },
    { title: '申请人户口登记机关公安机关名称', field: 'sqrhkdjjggajgmc' },
    { title: '迁出地户口登记机关公安机关机构代码', field: 'qcdhkdjjggajgjgdm' },
    { title: '迁出地户口登记机关公安机关名称', field: 'qcdhkdjjggajgmc' },
    { title: '迁出地数据归属单位代码', field: 'qcdsjgsdwdm' },
    { title: '迁出地数据归属单位名称', field: 'qcdsjgsdwmc' },
    {
      title: '迁入地户口登记机关公安机关机构代码  ',
      field: 'qrdhkdjjggajgjgdm',
    },
    { title: '迁入地户口登记机关公安机关名称', field: 'qrdhkdjjggajgmc' },
    { title: '迁移（流动）原因', field: 'qyldyydm' },
    { title: '与申请人关系_家庭关系', field: 'ysqrgxjtgxdm' },
    { title: '公民身份号码', field: 'gmsfhm' },
    { title: '姓名', field: 'xm' },
    { title: '性别', field: 'xbdmLabel' },
    { title: '出生日期', field: 'csrq' },
    { title: '准迁证编号', field: 'zqzbh' },
    { title: '准迁证_签发机关公安机关机构代码', field: 'zqzqfjggajgjgdm' },
    { title: '准迁证_签发机关公安机关名称', field: 'zqzqfjggajgmc' },
    { title: '准迁证_证签发日期', field: 'zqzqfrq' },
    { title: '准迁证_有效期截止日期', field: 'zqzyxqjzrq' },
    { title: '电子准迁_证照标识', field: 'dzzqdzzzbzLabel' },
    { title: '电子准迁治_安管理电子证照编号', field: 'dzzqzagldzzzbh' },
    { title: '备注', field: 'bz' },
    { title: '受理地公安机关机构代码', field: 'sldgajgjgdm' },
    { title: '受理地公安机关名称', field: 'sldgajgmc' },
    { title: '受理地数据归属单位代码', field: 'sldsjgsdwdm' },
    { title: '受理地数据归属单位名称', field: 'sldsjgsdwmc' },
    { title: '受理地联系电话', field: 'sldlxdh' },
    { title: '受理人姓名', field: 'slrxm' },
    { title: '归档配置版本号', field: 'gdpzbbh' },
    { title: '区域范围代码', field: 'qyfwdm' },
    { title: '发送单位数据归属单位代码', field: 'fsdwsjgsdwdm' },
    { title: '发送单位数据归属单位名称', field: 'fsdwsjgsdwmc' },
    { title: '接收单位数据归属单位代码', field: 'jsdwsjgsdwdm' },
    { title: '接收单位数据归属单位名称', field: 'jsdwsjgsdwmc' },
    { title: '户籍迁出标志', field: 'hjqcbzLabel' },
    { title: '审批业务受理号', field: 'spywslh' },
    { title: '审批业务受理时间', field: 'spywslsj' },
  ];
}
const [Grid] = useVbenVxeGrid({
  showSearchForm: false,
  gridOptions: {
    columns: useFormatterColumns(useColumns()),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage, pageSize } = page;
          const result = await apiKsxtQcsqxxbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: true,
            ksxtid: props.data.ksxtid,
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'ksxtfzxxid',
    },
    toolbarConfig: {
      search: false,
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
    },
  } as VxeTableGridOptions,
  gridEvents: {
    cellDblclick: handleRightGridRowDblclick, // 添加双击事件
  },
});
</script>

<template>
  <!-- 列表 -->
  <Grid class="h-auto flex-1"> </Grid>

  <!-- 详情 -->
  <a-collapse v-model:active-key="activeKey" v-if="detailInfo.ksxtqcsqid">
    <a-collapse-panel key="1" header="申请人信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="申请人公民身份号码">
          {{ detailInfo.sqrgmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人姓名">
          {{ detailInfo.sqrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人联系电话">
          {{ detailInfo.sqrlxdh }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人住址省市（区）">
          {{ detailInfo.sqrzzssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人住址区划内详细地址" :span="2">
          {{ detailInfo.sqrzzqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人户口登记机关公安机关机构代码">
          {{ detailInfo.sqrhkdjjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人户口登记机关公安机关名称">
          {{ detailInfo.sqrhkdjjggajgmc }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="2" header="迁移信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="迁出地省市县（区）">
          {{ detailInfo.qcdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="迁出地区划内详细地址" :span="2">
          {{ detailInfo.qcdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="迁出地户口登记机关公安机关机构代码">
          {{ detailInfo.qcdhkdjjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="迁出地户口登记机关公安机关名称">
          {{ detailInfo.qcdhkdjjggajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="迁出地数据归属单位代码">
          {{ detailInfo.qcdsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="数据归属单位名称">
          {{ detailInfo.qcdsjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="迁入地省市县（区）" :span="2">
          {{ detailInfo.qrdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="迁入地区划内详细地址" :span="2">
          {{ detailInfo.qrdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="迁入地户口登记机关公安机关机构代码">
          {{ detailInfo.qrdhkdjjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="迁入地户口登记机关公安机关名称">
          {{ detailInfo.qrdhkdjjggajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="迁移（流动）原因">
          {{ detailInfo.qyldyydm }}
        </a-descriptions-item>
        <a-descriptions-item label="与申请人关系_家庭关系">
          {{ detailInfo.ysqrgxjtgxdm }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ detailInfo.gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ detailInfo.xm }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ detailInfo.xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ detailInfo.csrq }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="3" header="准迁证信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="准迁证编号">
          {{ detailInfo.zqzbh }}
        </a-descriptions-item>
        <a-descriptions-item label="准迁证_签发机关公安机关机构代码">
          {{ detailInfo.zqzqfjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="准迁证_签发机关公安机关名称">
          {{ detailInfo.zqzqfjggajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="准迁证_签发日期">
          {{ detailInfo.zqzqfrq }}
        </a-descriptions-item>
        <a-descriptions-item label="准迁证_有效期截止日期">
          {{ detailInfo.zqzyxqjzrq }}
        </a-descriptions-item>
        <a-descriptions-item label="电子准迁_证照标识">
          {{ detailInfo.dzzqdzzzbzLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="电子准迁_治安管理电子证照编号">
          {{ detailInfo.dzzqzagldzzzbh }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="3">
          {{ detailInfo.bz }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="4" header="业务信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="受理地公安机关机构代码">
          {{ detailInfo.sldgajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="受理地公安机关名称">
          {{ detailInfo.sldgajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="受理地数据归属单位代码">
          {{ detailInfo.sldsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="受理地数据归属单位名称">
          {{ detailInfo.sldsjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="受理地联系电话">
          {{ detailInfo.sldlxdh }}
        </a-descriptions-item>
        <a-descriptions-item label="受理人姓名">
          {{ detailInfo.slrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="受理时间">
          {{ detailInfo.slsj }}
        </a-descriptions-item>
        <a-descriptions-item label="归档配置版本号">
          {{ detailInfo.gdpzbbh }}
        </a-descriptions-item>
        <a-descriptions-item label="区域范围代码">
          {{ detailInfo.qyfwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="发送单位数据归属单位代码">
          {{ detailInfo.fsdwsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="发送单位数据归属单位名称">
          {{ detailInfo.fsdwsjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="接收单位数据归属单位代码">
          {{ detailInfo.jsdwsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="接收单位数据归属单位名称">
          {{ detailInfo.jsdwsjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="入库时间">
          {{ detailInfo.rksj }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍迁出标志">
          {{ detailInfo.hjqcbzLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="审批业务受理号">
          {{ detailInfo.spywslh }}
        </a-descriptions-item>
        <a-descriptions-item label="审批业务受理时间">
          {{ detailInfo.spywslsj }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
  </a-collapse>
</template>
