<script setup lang="ts" name="xtfkxx-tab">
import { ref, onMounted, computed } from 'vue';

import { apiKsxtQcfkxxbPage, apiKsxtQcfkxxbView } from '#/api';

import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons-vue';

const personList = ref<any>([]);
const personInfo = ref<any>({});
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
//获取
onMounted(async () => {
  const { records } = await apiKsxtQcfkxxbPage({ ksxtid: props.data.ksxtid });
  personList.value = records;
});
const personChunks = computed(() => {
  const chunkSize = 3;
  const arr = [];
  for (let i = 0; i < personList.value.length; i += chunkSize) {
    arr.push(personList.value.slice(i, i + chunkSize));
  }
  return arr;
});

// 根据身份证号码推算年龄
function getAgeFromIdCard(idCard: string): number | '' {
  if (!idCard || idCard.length < 14) return '';
  let year = 0,
    month = 1,
    day = 1;
  if (idCard.length === 18) {
    year = parseInt(idCard.substr(6, 4));
    month = parseInt(idCard.substr(10, 2));
    day = parseInt(idCard.substr(12, 2));
  } else if (idCard.length === 15) {
    year = 1900 + parseInt(idCard.substr(6, 2));
    month = parseInt(idCard.substr(8, 2));
    day = parseInt(idCard.substr(10, 2));
  } else {
    return '';
  }
  const now = new Date();
  let age = now.getFullYear() - year;
  if (
    now.getMonth() + 1 < month ||
    (now.getMonth() + 1 === month && now.getDate() < day)
  ) {
    age--;
  }
  return age;
}

//获取反馈详情
async function getInfo(id: string) {
  personInfo.value = await apiKsxtQcfkxxbView({ id });
}
</script>

<template>
  <!-- 人员展示 -->
  <a-carousel arrows>
    <template #prevArrow>
      <div class="custom-slick-arrow" style="left: 10px; z-index: 1">
        <left-circle-outlined />
      </div>
    </template>
    <template #nextArrow>
      <div class="custom-slick-arrow" style="right: 10px">
        <right-circle-outlined />
      </div>
    </template>

    <div v-for="(chunk, idx) in personChunks" :key="idx">
      <div class="flex h-[110px] items-center gap-10">
        <div
          class="card relative h-full w-1/3 text-left"
          v-for="(item, i) in chunk"
          :key="i"
          @click="getInfo(item.ksxtqcfkid)"
        >
          <div class="title">{{ item.xm }}</div>
          <div class="content flex items-center justify-between">
            <div>
              <div>证件号码</div>
              <div>{{ item.gmsfhm }}</div>
            </div>
            <div class="xx"></div>
            <div>
              <div>性别</div>
              <div>{{ item.xbdmLabel }}</div>
            </div>
            <div class="xx"></div>
            <div>
              <div>年龄</div>
              <div>{{ getAgeFromIdCard(item.gmsfhm) }}</div>
            </div>
          </div>
          <div class="absolute right-0 top-0">
            <a-tag color="#108ee9" class="ml-1 mr-0">标签</a-tag>
          </div>
        </div>
      </div>
    </div>
  </a-carousel>
  <!-- 详情 -->
  <a-collapse active-key="1" v-if="personInfo.ksxtqcfkid">
    <a-collapse-panel key="1" header="业务信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="持证人公民身份号码">
          {{ personInfo.czrgmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="持证人姓名">
          {{ personInfo.czrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="原地址住址省市（区）">
          {{ personInfo.yzzssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="原住址住址区划内详细地址" :span="2">
          {{ personInfo.yzzqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="原住址城乡分类代码">
          {{ personInfo.yzzcxfldm }}
        </a-descriptions-item>
        <a-descriptions-item label="去往地_省市县（区）">
          {{ personInfo.qwdssxqdm }}
        </a-descriptions-item>
        <a-descriptions-item label="去往地_区划内详细地址" :span="2">
          {{ personInfo.qwdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="去往地_户口登记机关公安机关机构代码">
          {{ personInfo.qwdhkdjjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="去往地_户口登记机关公安机关名称">
          {{ personInfo.qwdhkdjjggajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="迁移证编号">
          {{ personInfo.qyzbh }}
        </a-descriptions-item>
        <a-descriptions-item label="迁移证_签发机关_公安机关机构代码">
          {{ personInfo.qyzqfjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="迁移证_签发机关_公安机关名称">
          {{ personInfo.qyzqfjggajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="迁移证_签发日期">
          {{ personInfo.qyzqfrq }}
        </a-descriptions-item>
        <a-descriptions-item label="迁移证_有效期截止日期">
          {{ personInfo.qyzyxqjzrq }}
        </a-descriptions-item>
        <a-descriptions-item label="电子迁移_证照标识">
          {{ personInfo.dzqydzzzbz }}
        </a-descriptions-item>
        <a-descriptions-item label="电子迁移_治安管理电子证照编号">
          {{ personInfo.dzqyzagldzzzbh }}
        </a-descriptions-item>
        <a-descriptions-item label="准迁证编号">
          {{ personInfo.zqzbh }}
        </a-descriptions-item>
        <a-descriptions-item label="电子准迁_证照标识">
          {{ personInfo.dzzqdzzzbz }}
        </a-descriptions-item>
        <a-descriptions-item label="电子准迁_治安管理电子证照编号">
          {{ personInfo.dzzqzagldzzzbh }}
        </a-descriptions-item>
         <a-descriptions-item label="备注" :span="2">
          {{ personInfo.bz }}
        </a-descriptions-item>
        <a-descriptions-item label="与持证人关系家庭关系">
          {{ personInfo.yczrgxjtgxdm }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ personInfo.gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ personInfo.xm }}
        </a-descriptions-item>
        <a-descriptions-item label="曾用名">
          {{ personInfo.cym }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ personInfo.xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="民族">
          {{ personInfo.mzdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ personInfo.csrq }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地国家地区">
          {{ personInfo.csdgjhdqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地省县" :span="2">
          {{ personInfo.csdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地详址" :span="2">
          {{ personInfo.csdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯国家地区">
          {{ personInfo.jggjhdqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯省县">
          {{ personInfo.jgssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯详址" :span="2">
          {{ personInfo.jgqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="文化程度">
          {{ personInfo.xldmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="婚姻状况">
          {{ personInfo.hyzkdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="职业">
          {{ personInfo.zy }}
        </a-descriptions-item>
        <a-descriptions-item label="迁移（流动）原因">
          {{ personInfo.qyldyydmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地_公安机关机构代码">
          {{ personInfo.hjdgajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地_公安机关名称">
          {{ personInfo.hjdgajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地_联系电话">
          {{ personInfo.hjdlxdh }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地数据归属单位代码">
          {{ personInfo.hjdsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地数据归属单位名称">
          {{ personInfo.hjdsjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="办理人姓名">
          {{ personInfo.blrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="办理时间">
          {{ personInfo.blsj }}
        </a-descriptions-item>
        <a-descriptions-item label="区域范围代码">
          {{ personInfo.qyfwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="发送单位数据归属单位代码">
          {{ personInfo.fsdwsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="发送单位数据归属单位名称">
          {{ personInfo.fsdwsjgsdwmc }}
        </a-descriptions-item>
         <a-descriptions-item label="接收单位数据归属单位代码">
          {{ personInfo.jsdwsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="接收单位数据归属单位名称">
          {{ personInfo.jsdwsjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="入库时间">
          {{ personInfo.rksj }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
  </a-collapse>
</template>
<style scoped lang="scss">
/* For demo */
:deep(.slick-slide) {
  text-align: center;
  overflow: hidden;
  padding: 10px 20px 20px;
}

:deep(.slick-arrow.custom-slick-arrow) {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #000;
  transition: ease all 0.3s;
  opacity: 0.5;
  z-index: 1;
}
:deep(.slick-arrow.custom-slick-arrow:before) {
  display: none;
}
:deep(.slick-arrow.custom-slick-arrow:hover) {
  color: #000;
  opacity: 0.5;
}

:deep(.slick-slide h3) {
  color: #000;
}
:deep(.slick-dots li button) {
  background: #999;
}
:deep(.slick-dots li.slick-active button) {
  background: hsl(var(--primary));
}
.card {
  background: #ffffff;
  box-shadow:
    0px 0px 1px 0px rgba(0, 0, 0, 0.08),
    0px 1px 2px 0px rgba(25, 15, 15, 0.07),
    0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  padding: 10px 17px;
  cursor: pointer;
  .title {
    font-family: AlibabaPuHuiTiM;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
  .content {
    margin-top: 10px;
    > div {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      > div:first-child {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        line-height: 20px;
      }
    }
    .xx {
      height: 33px;
      border-right: 1px dashed rgba(0, 0, 0, 0.45);
    }
  }
}
</style>
