<script setup lang="ts" name="xtfzxx-tab">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import { apiKsxtFzxxbPage, apiKsxtFzxxbView } from '#/api';

const detailInfo = ref<any>({});

// 表格双击事件处理
const handleRightGridRowDblclick = async ({ row }: { row: any }) => {
  detailInfo.value = await apiKsxtFzxxbView({ id: row.ksxtfzxxid });
};

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
// 搜索表单配置
function useColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { title: '治安管理业务协同编号', field: 'zaglywxtbh' },
    { title: '协同环节代码', field: 'kstbxthjdm' },
    { title: '跨省通办消息类型代码', field: 'kstbxxlxdm', width: 150 },
    { title: '处理标志', field: 'clbzLabel' },
    { title: '接收单位_数据归属单位名称', field: 'jsdwsjgsdwmc' },
    { title: '入库时间', field: 'rksj' },
    { title: '简要情况', field: 'jyqk' },
    { title: '操作员姓名', field: 'czyxm' },
    { title: '操作员ip', field: 'czyip' },
    { title: '区域范围代码', field: 'qyfwdm' },
    { title: '阅读人姓名', field: 'ydrxm' },
    { title: '阅读时间', field: 'ydsj' },
  ];
}
const [Grid] = useVbenVxeGrid({
  showSearchForm: false,
  gridOptions: {
    columns: useFormatterColumns(useColumns()),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage, pageSize } = page;
          const result = await apiKsxtFzxxbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: true,
            ksxtid: props.data.ksxtid,
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'ksxtfzxxid',
    },
    toolbarConfig: {
      search: false,
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
    },
  } as VxeTableGridOptions,
  gridEvents: {
    cellDblclick: handleRightGridRowDblclick, // 添加双击事件
  },
});
</script>

<template>
  <!-- 列表 -->
  <Grid class="flex-1 h-auto"> </Grid>

  <!-- 详情 -->
  <a-collapse active-key="1" v-if="detailInfo.ksxtfzxxid">
    <a-collapse-panel key="1" header="详情">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="治安管理业务协同编号">
          {{ detailInfo.zaglywxtbh }}
        </a-descriptions-item>
        <a-descriptions-item label="治安管理业务类别代码">
          {{ detailInfo.zaglywlbdm }}
        </a-descriptions-item>
        <a-descriptions-item label="协同环节代码">
          {{ detailInfo.kstbxthjdm }}
        </a-descriptions-item>
        <a-descriptions-item label="标题" :span="3">
          {{ detailInfo.bt }}
        </a-descriptions-item>
        <a-descriptions-item label="跨省通办消息类型代码">
          {{ detailInfo.kstbxxlxdm }}
        </a-descriptions-item>
        <a-descriptions-item label="处理标志">
          {{ detailInfo.clbzLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="发送单位_数据归属单位代码" >
          {{ detailInfo.fsdwsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="发送单位_数据归属单位名称">
          {{ detailInfo.fsdwsjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="接收单位_数据归属单位代码">
          {{ detailInfo.jsdwsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="接收单位_数据归属单位名称">
          {{ detailInfo.jsdwsjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="入库时间">
          {{ detailInfo.rksj }}
        </a-descriptions-item>
        <a-descriptions-item label="简要情况" :span="2">
          {{ detailInfo.jyqk }}
        </a-descriptions-item>
        <a-descriptions-item label="操作员姓名">
          {{ detailInfo.czyxm }}
        </a-descriptions-item>
        <a-descriptions-item label="操作员ip">
          {{ detailInfo.czyip }}
        </a-descriptions-item>
        <a-descriptions-item label="区域范围代码">
          {{ detailInfo.qyfwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="阅读人姓名">
          {{ detailInfo.ydrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="阅读时间">
          {{ detailInfo.ydsj }}
        </a-descriptions-item>
        <a-descriptions-item label="业务表名">
          {{ detailInfo.ywbm }}
        </a-descriptions-item>
        <a-descriptions-item label="跨省协同id">
          {{ detailInfo.ksxtid }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
  </a-collapse>
</template>
