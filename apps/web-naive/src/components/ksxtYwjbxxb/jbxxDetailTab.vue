<template>
  <div>
    <a-collapse v-model:active-key="activeKey">
      <a-collapse-panel key="1" header="业务信息">
        <a-descriptions
          label-placement="left"
          :column="3"
          size="small"
          bordered
        >
          <a-descriptions-item label="业务类型">
            {{ jbxx.ywlxLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="治安管理类别">
            {{ jbxx.zaglywlbdmLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="办理状态">
            {{ jbxx.blztLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="受理时间">
            {{ jbxx.slsj }}
          </a-descriptions-item>
          <a-descriptions-item label="审批业务类型">
            {{ jbxx.spywlxLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="户籍地数据归属单位名称">
            {{ jbxx.hjdsjgsdwmc }}
          </a-descriptions-item>
          <a-descriptions-item label="受理地数据归属单位名称">
            {{ jbxx.sldsjgsdwmc }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人公民身份号码">
            {{ jbxx.sqrgmsfhm }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人姓名">
            {{ jbxx.sqrxm }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人联系电话">
            {{ jbxx.sqrlxdh }}
          </a-descriptions-item>
          <a-descriptions-item label="户主公民身份号码">
            {{ jbxx.hzgmsfhm }}
          </a-descriptions-item>
          <a-descriptions-item label="户主姓名">
            {{ jbxx.hzxm }}
          </a-descriptions-item>
          <a-descriptions-item label="迁移范围代码">
            {{ jbxx.qyfwdm }}
          </a-descriptions-item>
          <a-descriptions-item label="户籍地数据归属单位代码">
            {{ jbxx.hjdsjgsdwdm }}
          </a-descriptions-item>
             <a-descriptions-item label="户籍地联系电话">
            {{ jbxx.hjdlxdh }}
          </a-descriptions-item>
             <a-descriptions-item label="受理地数据归属单位代码">
            {{ jbxx.sldsjgsdwdm }}
          </a-descriptions-item>
             <a-descriptions-item label="受理地联系电话">
            {{ jbxx.sldlxdh }}
          </a-descriptions-item>
             <a-descriptions-item label="受理人姓名">
            {{ jbxx.slrxm }}
          </a-descriptions-item>
             <a-descriptions-item label="治安管理业务协同编号">
            {{ jbxx.zaglywxtbh }}
          </a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
      <a-collapse-panel key="2" header="办理审核信息">
        <a-descriptions
          label-placement="left"
          :column="3"
          size="small"
          bordered
        >
          <a-descriptions-item label="入库时间">
            {{ jbxx.rksj }}
          </a-descriptions-item>
          <a-descriptions-item label="审批业务受理号">
            {{ jbxx.spywslh }}
          </a-descriptions-item>
          <a-descriptions-item label="业务办理时间">
            {{ jbxx.ywblsj }}
          </a-descriptions-item>
          <a-descriptions-item label="业务办理人姓名">
            {{ jbxx.ywblrxm }}
          </a-descriptions-item>
          <a-descriptions-item label="审核结果代码">
            {{ jbxx.shjg }}
          </a-descriptions-item>
          <a-descriptions-item label="审核简要情况">
            {{ jbxx.shms }}
          </a-descriptions-item>
          <a-descriptions-item label="审核人姓名">
            {{ jbxx.shrxm }}
          </a-descriptions-item>
          <a-descriptions-item label="审核时间">
            {{ jbxx.shsj }}
          </a-descriptions-item>
          <a-descriptions-item label="办结时间">
            {{ jbxx.bjsj }}
          </a-descriptions-item>
         
        </a-descriptions>
      </a-collapse-panel>
         <a-collapse-panel key="3" header="快递信息">
        <a-descriptions
          label-placement="left"
          :column="3"
          size="small"
          bordered
        >
          <a-descriptions-item label="是否需要快递">
            {{ jbxx.sfxykdbzLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="收件人_姓名">
            {{ jbxx.sjrxm }}
          </a-descriptions-item>
          <a-descriptions-item label="收件人_公民身份号码">
            {{ jbxx.sjrgmsfhm }}
          </a-descriptions-item>
          <a-descriptions-item label="收件人_联系电话">
            {{ jbxx.sjrlxdh }}
          </a-descriptions-item>
          <a-descriptions-item label="收件地址_省市县区">
            {{ jbxx.sjdzssxqdmLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="收件地址_区划内详址">
            {{ jbxx.sjdzqhnxxdz }}
          </a-descriptions-item>
    
         
        </a-descriptions>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<script setup lang="ts" name="jbxx-tab">
import { onMounted ,ref} from 'vue';

import { apiKsxtYwjbxxbView } from '#/api';
let activeKey = ref(['1', '2', '3', '4','5']);

// 定义接收的 props
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const jbxx = ref<any>({});
onMounted(async () => {
  jbxx.value = await apiKsxtYwjbxxbView({ id: props.data.ksxtid });
});
</script>
<style lang="less" scoped>
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3a4b;
  letter-spacing: 2px;
  padding-left: 2px;
  border-left: 4px solid #1890ff;
  background: linear-gradient(90deg, #e3f0ff 0%, #fff 100%);
  padding: 4px 0 4px 12px;
  border-radius: 4px 0 0 4px;
}
.section-title.green {
  border-left-color: #52c41a;
  background: linear-gradient(90deg, #eaffea 0%, #fff 100%);
}
.photo-placeholder {
  color: #aaa;
  font-size: 14px;
  padding: 32px 0;
  width: 100px;
  text-align: center;
}
</style>
