<script setup lang="ts" name="hjzmsq-tab">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import { apiKsxtHjlzmsqxxbPage, apiKsxtHjlzmsqxxbView } from '#/api';
const activeKey = ref(['1', '2', '3', '4']);
const detailInfo = ref<any>({});

// 表格双击事件处理
const handleRightGridRowDblclick = async ({ row }: { row: any }) => {
  detailInfo.value = await apiKsxtHjlzmsqxxbView({ id: row.ksxthjlzmid });
};

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
// 搜索表单配置
function useColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { title: '公民身份号码', field: 'gmsfhm' },
    { title: '姓名', field: 'xm' },
    { title: '户籍证明类型及事项代码', field: 'hjzmlxjsxdm', width: 150 },
    { title: '户籍协查结果类型', field: 'hjxcjglxdmLabel' },
    { title: '申请日期', field: 'sqrq' },
    { title: '迁移范围代码', field: 'qyfwdm' },
    { title: '户籍地数据归属单位代码', field: 'hjdsjgsdwdm' },
    { title: '户籍地数据归属单位名称', field: 'hjdsjgsdwmc' },
    { title: '受理地数据归属单位代码', field: 'sldsjgsdwdm' },
    { title: '受理地数据归属单位名称', field: 'sldsjgsdwmc' },
    { title: '入库时间', field: 'rksj' },
  ];
}
const [Grid] = useVbenVxeGrid({
  showSearchForm: false,
  gridOptions: {
    columns: useFormatterColumns(useColumns()),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage, pageSize } = page;
          const result = await apiKsxtHjlzmsqxxbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: true,
            ksxtid: props.data.ksxtid,
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'ksxtfzxxid',
    },
    toolbarConfig: {
      search: false,
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
    },
  } as VxeTableGridOptions,
  gridEvents: {
    cellDblclick: handleRightGridRowDblclick, // 添加双击事件
  },
});
</script>

<template>
  <!-- 列表 -->
  <Grid class="h-auto flex-1"> </Grid>

  <!-- 详情 -->
  <a-collapse v-model:active-key="activeKey" v-if="detailInfo.ksxthjlzmid">
    <a-collapse-panel key="1" header="查询人员信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="公民身份号码">
          {{ detailInfo.gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ detailInfo.xm }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地址_省市县区">
          {{ detailInfo.hjdzssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地址_区划内详址" :span="2">
          {{ detailInfo.hjdzqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="联系电话">
          {{ detailInfo.lxdh }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="2" header="查询内容">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="户籍证明类型及事项代码">
          {{ detailInfo.hjzmlxjsxdm }}
        </a-descriptions-item>
        <a-descriptions-item label="证明事项简要情况">
          {{ detailInfo.xyzmsxnrjyqk }}
        </a-descriptions-item>
        <a-descriptions-item label="证明要求备注">
          {{ detailInfo.bz }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="3" header="查询结果">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="户籍协查结果类型">
          {{ detailInfo.hjxcjglxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="查证结果描述">
          {{ detailInfo.czjgmsjyqk }}
        </a-descriptions-item>
        <a-descriptions-item label="查证结果备注">
          {{ detailInfo.bz1 }}
        </a-descriptions-item>
        <a-descriptions-item label="反馈日期">
          {{ detailInfo.fkrq }}
        </a-descriptions-item>
        <a-descriptions-item label="协查地_公安机关机构代码">
          {{ detailInfo.xcdgajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="协查地_公安机关名称">
          {{ detailInfo.xcdgajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="协查地_联系电话">
          {{ detailInfo.xcdlxdh }}
        </a-descriptions-item>
        <a-descriptions-item label="协查人_姓名">
          {{ detailInfo.xcrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="协查_日期时间">
          {{ detailInfo.xcrqsj }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="4" header="申请信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="申请日期">
          {{ detailInfo.sqrq }}
        </a-descriptions-item>
        <a-descriptions-item label="申请地_公安机关机构代码">
          {{ detailInfo.sqdgajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="申请地_公安机关名称">
          {{ detailInfo.sqdgajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="申请地_联系电话">
          {{ detailInfo.sqdlxdh }}
        </a-descriptions-item>
        <a-descriptions-item label="迁移范围代码">
          {{ detailInfo.qyfwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地数据归属单位代码">
          {{ detailInfo.hjdsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="户籍地数据归属单位名称">
          {{ detailInfo.hjdsjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="受理地数据归属单位代码">
          {{ detailInfo.sldsjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="受理地数据归属单位名称">
          {{ detailInfo.sldsjgsdwmc }}
        </a-descriptions-item>
         <a-descriptions-item label="入库时间">
          {{ detailInfo.rksj }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
  </a-collapse>
</template>
