<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { apiRktYwslClqdPage, apiRktYwslClysjPage } from '#/api';
import FilePreviewModal from '#/components/zjtSlxxb/filePreviewModal.vue';

// 定义接收的 props
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const type = ref('tab');
const list = ref<any>([]);
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      { field: 'cllxmc', title: '材料类型名称', minWidth: 100 },
      { field: 'clsl', title: '数量', minWidth: 50 },
    ],
    layouts: ['Table'],
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async () => {
          const result = await apiRktYwslClqdPage({
            pageNumber: 1,
            pageSize: 100,
            ywslh: props.data.ywslh,
          });
          return result.records;
        },
      },
    },
    toolbarConfig: {
      enabled: false,
    },
  } as VxeTableGridOptions,
});

// 右侧表格双击事件处理
const handleRightGridRowDblclick = ({ row }: { row: any }) => {
  if (row.base64Data) {
    modalApi.setData({ base64Data: row.base64Data, type: row.wjlx }).open();
  } else {
    message.warning('文件数据为空, 无法预览');
  }
};

const [RightGrid, rightGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      { title: '材料名称', field: 'clmc', minWidth: 150 },
      { title: '序号', field: 'xh', minWidth: 50 },
      { title: '材料类型代码', field: 'cllxdm', minWidth: 80 },
      { title: '材料类型名称', field: 'cllxmc', minWidth: 150 },
      { title: '是否已签章', field: 'sfyqzLabel', minWidth: 70 },
      { title: '材料采集时间', field: 'clcjsj', minWidth: 140 },
      { title: '材料采集人', field: 'clcjr', minWidth: 80 },
      { title: '文件大小', field: 'wjdx', minWidth: 60 },
      { title: '文件类型', field: 'wjlx', minWidth: 80 },
      { title: '材料来源类型', field: 'cllylxLabel', minWidth: 110 },
      { title: '数据归属单位代码', field: 'sjgsdwdm', minWidth: 120 },
      { title: '打印次数', field: 'dycs', minWidth: 80 },
      { title: '是否保存签名证据', field: 'sfbcqmzjLabel', minWidth: 110 },
      { title: '公民身份号码', field: 'gmsfhm', minWidth: 150 },
    ],
    layouts: ['Table'],
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async () => {
          const result = await apiRktYwslClysjPage({
            pageNumber: 1,
            pageSize: 100,
            bhclnr: '1',
            ywslh: props.data.ywslh,
          });
          return result.records;
        },
      },
    },
    toolbarConfig: {
      enabled: false,
    },
  } as VxeTableGridOptions,
  gridEvents: {
    cellDblclick: handleRightGridRowDblclick, // 添加双击事件
  },
});

const [Modal, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: FilePreviewModal,
});

// 组件挂载后自动查询
onMounted(async () => {
  if (props.data.ywslh) {
    gridApi?.reload();
    rightGridApi?.reload();
    const { records } = await apiRktYwslClysjPage({
      pageNumber: 1,
      pageSize: 100,
      bhclnr: '1',
      ywslh: props.data.ywslh,
    });
    list.value = records;
  } else {
    return message.error('数据异常');
  }
});
// 切换展示模式
const onChangeType = () => {
  type.value = type.value === 'tab' ? 'img' : 'tab';
};
</script>

<template>
  <div>
    <Page class="flex-1">
      <a-button type="link" @click="onChangeType">
        {{ type === 'tab' ? '切换图模式' : ' 切换表模式' }}
      </a-button>
      <div class="vp-raw flex h-full w-full">
        <!-- 左侧表格，占1/3宽度 -->
        <div class="mr-2 w-1/6">
          <Grid />
        </div>
        <!-- 右侧表格，占2/3宽度 -->
        <div class="ml-2 w-5/6" v-show="type === 'tab'">
          <RightGrid />
        </div>
        <!-- 图模式 -->
        <div class="ml-2 w-5/6" v-show="type === 'img'">
          <a-row :gutter="[16, 16]">
            <a-col v-for="item in list" :key="item.clxgrid" :span="6">
              <div
                @dblclick="handleRightGridRowDblclick({ row: item })"
                class="flex flex-col items-center justify-center"
              >
                <a-image
                  :width="150"
                  :height="150"
                  :src="`data:image/jpeg;base64,${item.base64Data}`"
                  fallback="data:image/png;base64,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"
                />
                <a-tooltip :title="item.clmc">
                  <div class="mt-2 w-1/2 truncate text-center">
                    {{ item.clmc }}
                  </div>
                </a-tooltip>
                <a-tooltip :title="item.cllylxLabel">
                  <div class="mt-2 w-1/2 truncate text-center">
                    {{ item.cllylxLabel }}
                  </div>
                </a-tooltip>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
      <Modal />
    </Page>
  </div>
</template>

<style lang="scss" scoped></style>
