<template>
  <div>
    <a-collapse active-key="1">
      <a-collapse-panel key="1" header="证件申请信息">
        <a-descriptions
          label-placement="left"
          :column="3"
          size="small"
          bordered
        >
          <a-descriptions-item label="业务类型">
            {{ jbxx.ywlxLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人姓名">
            {{ jbxx.sqrxm }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人公民身份号码">
            {{ jbxx.sqrgmsfhm }}
          </a-descriptions-item>
          <a-descriptions-item label="采集时间">
            {{ jbxx.rksj }}
          </a-descriptions-item>
          <a-descriptions-item label="受理单位">
            {{ jbxx.sldsjgsdwmc }}
          </a-descriptions-item>
          <a-descriptions-item label="采集省份">
            {{ jbxx.ywlysfLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="办件号">
            {{ jbxx.ywslh }}
          </a-descriptions-item>
          <a-descriptions-item label="办理状态">
            {{ jbxx.blztLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="准迁证编号">
            {{ jbxx.zqzbh }}
          </a-descriptions-item>
          <a-descriptions-item label="迁移证编号">
            {{ jbxx.qyzbh }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人联系电话">
            {{ jbxx.sqrlxdh }}
          </a-descriptions-item>
          <a-descriptions-item label="迁出地省市县区">
            {{ jbxx.qcdssxqdmLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="迁入地省市县区">
            {{ jbxx.qrdssxqdmLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="信息描述">
            {{ jbxx.msg }}
          </a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<script setup lang="ts" name="jbxx-tab">
import { onMounted } from 'vue';

import { apiKsywYwjbxxbView } from '#/api';

// 定义接收的 props
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const jbxx = ref<any>({});
onMounted(async () => {
  jbxx.value = await apiKsywYwjbxxbView({ id: props.data.ksywid });
});
</script>
<style lang="less" scoped>
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3a4b;
  letter-spacing: 2px;
  padding-left: 2px;
  border-left: 4px solid #1890ff;
  background: linear-gradient(90deg, #e3f0ff 0%, #fff 100%);
  padding: 4px 0 4px 12px;
  border-radius: 4px 0 0 4px;
}
.section-title.green {
  border-left-color: #52c41a;
  background: linear-gradient(90deg, #eaffea 0%, #fff 100%);
}
.photo-placeholder {
  color: #aaa;
  font-size: 14px;
  padding: 32px 0;
  width: 100px;
  text-align: center;
}
</style>
