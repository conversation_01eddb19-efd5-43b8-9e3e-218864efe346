<template>
  <a-collapse v-model:active-key="activeKey">
    <a-collapse-panel key="1" header="出生信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="出生证明编号">
          {{ cssbxx.cszmbh }}
        </a-descriptions-item>
        <a-descriptions-item label="户口登记姓名">
          {{ cssbxx.xm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓">
          {{ cssbxx.x }}
        </a-descriptions-item>
        <a-descriptions-item label="名">
          {{ cssbxx.m }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ cssbxx.xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ cssbxx.csrq }}
        </a-descriptions-item>
        <a-descriptions-item label="出生时间">
          {{ cssbxx.cssj }}
        </a-descriptions-item>
        <a-descriptions-item label="与户主关系">
          {{ cssbxx.yhzgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="民族">
          {{ cssbxx.mzdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ cssbxx.gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人与出生人员关系">
          {{ cssbxx.yhzgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="血型">
          {{ cssbxx.xxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地省县">
          {{ cssbxx.csdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地详址" :span="2">
          {{ cssbxx.csdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯省县">
          {{ cssbxx.jgssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯详址" :span="2">
          {{ cssbxx.jgqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="现居住地省县">
          {{ cssbxx.xzzssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="现居住地详址" :span="2">
          {{ cssbxx.xzzqhnxxdz }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="2" header="父母亲信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="母亲姓名">
          {{ cssbxx.mqxm }}
        </a-descriptions-item>
        <a-descriptions-item label="母亲身份号码">
          {{ cssbxx.mqgmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="父亲姓名">
          {{ cssbxx.fqxm }}
        </a-descriptions-item>
        <a-descriptions-item label="父亲身份号码">
          {{ cssbxx.fqgmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="户主姓名">
          {{ cssbxx.hzxm }}
        </a-descriptions-item>
        <a-descriptions-item label="户主身份号码">
          {{ cssbxx.hzgmsfhm }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="3" header="监护人信息(16周岁以下必须填写)">
      <a-descriptions label-placement="left" :column="4" size="small" bordered>
        <a-descriptions-item label="监护人一姓名">
          {{ cssbxx.jhryxm }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人一身份号码">
          {{ cssbxx.jhrygmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="与监护人一的监护关系">
          {{ cssbxx.jhryjhgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="与监护人一联系电话">
          {{ cssbxx.jhrylxdh }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二姓名">
          {{ cssbxx.jhrexm }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二身份号码">
          {{ cssbxx.jhregmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="与监护人二的监护关系">
          {{ cssbxx.jhrejhgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="与监护人二联系电话">
          {{ cssbxx.jhrelxdh }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="4" header="亲属其他信息">
      <a-descriptions label-placement="left" :column="4" size="small" bordered>
        <a-descriptions-item label="监护人一证件种类">
          {{ cssbxx.jhrycyzjdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人一证件号码">
          {{ cssbxx.jhryzjhm }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人一外文姓">
          {{ cssbxx.jhrywwx }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人一外文名">
          {{ cssbxx.jhrywwm }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二证件种类">
          {{ cssbxx.jhrecyzjdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二证件号码">
          {{ cssbxx.jhrezjhm }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二外文姓">
          {{ cssbxx.jhrewwx }}
        </a-descriptions-item>
        <a-descriptions-item label="监护人二外文名">
          {{ cssbxx.jhrewwm }}
        </a-descriptions-item>
        <a-descriptions-item label="父亲证件种类" :span="1">
          {{ cssbxx.fqcyzjdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="父亲证件号码" :span="3">
          {{ cssbxx.fqzjhm }}
        </a-descriptions-item>
        <a-descriptions-item label="母亲证件种类" :span="1">
          {{ cssbxx.mqcyzjdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="母亲证件号码" :span="3">
          {{ cssbxx.mqzjhm }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
  </a-collapse>
</template>

<script lang="ts" setup name="rkxx-tab">
import { ref, onMounted } from 'vue';
import { apiKsywCssbxxbView } from '#/api';
let activeKey = ref(['1', '2', '3', '4']);
// 定义接收的 props
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const cssbxx = ref<any>({});
onMounted(async () => {
  cssbxx.value = await apiKsywCssbxxbView({ ksywid: props.data.ksywid });
});
</script>
