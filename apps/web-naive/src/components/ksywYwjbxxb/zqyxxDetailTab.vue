<template>
  <a-collapse v-model:active-key="activeKey">
    <a-collapse-panel key="1" header="准迁证业务基本信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="准迁证编号">
          {{ cssbxx.zqzbh }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人公民身份号码">
          {{ cssbxx.sqrgmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人姓名">
          {{ cssbxx.sqrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人联系电话">
          {{ cssbxx.sqrlxdh }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人住址省市县区">
          {{ cssbxx.sqrzzssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人住址详址">
          {{ cssbxx.sqrzzqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人户口机关代码">
          {{ cssbxx.sqrhkdjjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人户口机关名称">
          {{ cssbxx.sqrhkdjjggajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="迁出地省县">
          {{ cssbxx.qcdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="迁出地详址">
          {{ cssbxx.qcdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="迁出地户口机关代码">
          {{ cssbxx.qcdhkdjjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="迁出地户口机关名称">
          {{ cssbxx.qcdhkdjjggajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="迁入地省市县区">
          {{ cssbxx.qrdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="迁入地详址" :span="2">
          {{ cssbxx.qrdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="迁入地户口机关代码">
          {{ cssbxx.qrdhkdjjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="迁入地户口登记机关名称">
          {{ cssbxx.qrdhkdjjggajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="签发机关公安机关机构代码">
          {{ cssbxx.qfjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="签发机关公安机关名称">
          {{ cssbxx.qfjggajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="承办人姓名">
          {{ cssbxx.cbrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="签发日期">
          {{ cssbxx.qfrq }}
        </a-descriptions-item>
        <a-descriptions-item label="变动原因">
          {{ cssbxx.qyldyydm }}
        </a-descriptions-item>
        <a-descriptions-item label="迁移流动原因名称（内部）">
          {{ cssbxx.qyldyymcnb }}
        </a-descriptions-item>
        <a-descriptions-item label="有效期限截止日期">
          {{ cssbxx.yxqjzrq }}
        </a-descriptions-item>
        <a-descriptions-item label="变动范围">
          {{ cssbxx.qyfwdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="受理单位公安机关机构代码">
          {{ cssbxx.sldwgajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="受理地单位名称">
          {{ cssbxx.sldwgajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="受理单位联系电话">
          {{ cssbxx.sldwlxdh }}
        </a-descriptions-item>
        <a-descriptions-item label="受理人姓名">
          {{ cssbxx.slrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="受理时间">
          {{ cssbxx.slsj }}
        </a-descriptions-item>
        <a-descriptions-item label="数据归属单位代码">
          {{ cssbxx.sjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="数据归属单位名称">
          {{ cssbxx.sjgsdwmc }}
        </a-descriptions-item>
        <a-descriptions-item label="采集时间">
          {{ cssbxx.rksj }}
        </a-descriptions-item>
        <a-descriptions-item label="派出所" :span="3">
          {{ cssbxx.pcs }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="3">
          {{ cssbxx.bz }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="2" header="迁移人一">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="与申请人关系">
          {{ cssbxx.qyr1ysqrgxjtgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ cssbxx.qyr1gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ cssbxx.qyr1xm }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ cssbxx.qyr1xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ cssbxx.qyr1csrq }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="3" header="迁移人二">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="与申请人关系">
          {{ cssbxx.qyr2ysqrgxjtgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ cssbxx.qyr2gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ cssbxx.qyr2xm }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ cssbxx.qyr2xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ cssbxx.qyr2csrq }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="4" header="迁移人三">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="与申请人关系">
          {{ cssbxx.qyr3ysqrgxjtgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ cssbxx.qyr3gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ cssbxx.qyr3xm }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ cssbxx.qyr3xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ cssbxx.qyr3csrq }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="5" header="迁移人四">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="与申请人关系">
          {{ cssbxx.qyr4ysqrgxjtgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ cssbxx.qyr4gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ cssbxx.qyr4xm }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ cssbxx.qyr4xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ cssbxx.qyr4csrq }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
  </a-collapse>
</template>

<script lang="ts" setup name="zqyxx-tab">
import { ref, onMounted } from 'vue';
import { apiKsywZqzxxbView } from '#/api';
let activeKey = ref(['1', '2', '3', '4','5']);
// 定义接收的 props
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const cssbxx = ref<any>({});
onMounted(async () => {
  cssbxx.value = await apiKsywZqzxxbView({ ksywid: props.data.ksywid });
});
</script>
