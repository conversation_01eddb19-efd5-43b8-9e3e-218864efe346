<template>
  <a-collapse v-model:active-key="activeKey">
    <a-collapse-panel key="1" header="迁移证信息">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="迁移证编号">
          {{ cssbxx.qyzbh }}
        </a-descriptions-item>
        <a-descriptions-item label="持证人公民身份号码">
          {{ cssbxx.czrgmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="持证人姓名">
          {{ cssbxx.czrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="原住址详址">
          {{ cssbxx.yzzqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="城乡属性">
          {{ cssbxx.yzzcxfldmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="迁往地区划内详细地址">
          {{ cssbxx.qwdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="签发机关公安机关机构代码">
          {{ cssbxx.qfjggajgjgdm }}
        </a-descriptions-item>
        <a-descriptions-item label="签发日期">
          {{ cssbxx.qfrq }}
        </a-descriptions-item>
        <a-descriptions-item label="有效期限截止日期">
          {{ cssbxx.yxqjzrq }}
        </a-descriptions-item>
        <a-descriptions-item label="承办人姓名">
          {{ cssbxx.cbrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="准迁证编号">
          {{ cssbxx.zqzbh }}
        </a-descriptions-item>
        <a-descriptions-item label="变动范围">
          {{ cssbxx.qyfwdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="受理人姓名">
          {{ cssbxx.slrxm }}
        </a-descriptions-item>
        <a-descriptions-item label="受理时间" >
          {{ cssbxx.slsj }}
        </a-descriptions-item>
        <a-descriptions-item label="数据归属单位代码">
          {{ cssbxx.sjgsdwdm }}
        </a-descriptions-item>
        <a-descriptions-item label="采集时间" >
          {{ cssbxx.rksj }}
        </a-descriptions-item>
        <a-descriptions-item label="受理单位" :span="2">
          {{ cssbxx.sldwgajgmc }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="3">
          {{ cssbxx.bz }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
    <a-collapse-panel key="2" header="迁移人一">
      <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="与持证人关系">
          {{ cssbxx.qyr1yczrgxjtgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ cssbxx.qyr1gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ cssbxx.qyr1xm }}
        </a-descriptions-item>
        <a-descriptions-item label="曾用名">
          {{ cssbxx.qyr1cym }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ cssbxx.qyr1xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="民族">
          {{ cssbxx.qyr1mzdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ cssbxx.qyr1csrq }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地国家地区">
          {{ cssbxx.qyr1csdgjhdqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地省市县（区）">
          {{ cssbxx.qyr1csdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地详址">
          {{ cssbxx.qyr1csdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯国家地区">
          {{ cssbxx.qyr1jggjhdqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯省县">
          {{ cssbxx.qyr1jgssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯详址">
          {{ cssbxx.qyr1jgqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="文化程度">
          {{ cssbxx.qyr1xldmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="婚姻状况">
          {{ cssbxx.qyr1hyzkdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="职业">
          {{ cssbxx.qyr1zy }}
        </a-descriptions-item>
        <a-descriptions-item label="居民身份证签发机关">
          {{ cssbxx.qyr1jmsfzqfjg }}
        </a-descriptions-item>
        <a-descriptions-item label="有效期限起始日期">
          {{ cssbxx.qyr1jmsfzyxqxqsrq }}
        </a-descriptions-item>
         <a-descriptions-item label="有效期限截止日期">
          {{ cssbxx.qyr1jmsfzyxqxjzrq }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
     <a-collapse-panel key="3" header="迁移人二">
          <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="与持证人关系">
          {{ cssbxx.qyr2yczrgxjtgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ cssbxx.qyr2gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ cssbxx.qyr2xm }}
        </a-descriptions-item>
        <a-descriptions-item label="曾用名">
          {{ cssbxx.qyr2cym }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ cssbxx.qyr2xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="民族">
          {{ cssbxx.qyr2mzdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ cssbxx.qyr2csrq }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地国家地区">
          {{ cssbxx.qyr2csdgjhdqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地省市县（区）">
          {{ cssbxx.qyr2csdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地详址">
          {{ cssbxx.qyr2csdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯国家地区">
          {{ cssbxx.qyr2jggjhdqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯省县">
          {{ cssbxx.qyr2jgssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯详址">
          {{ cssbxx.qyr2jgqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="文化程度">
          {{ cssbxx.qyr2xldmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="婚姻状况">
          {{ cssbxx.qyr2hyzkdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="职业">
          {{ cssbxx.qyr2zy }}
        </a-descriptions-item>
        <a-descriptions-item label="居民身份证签发机关">
          {{ cssbxx.qyr2jmsfzqfjg }}
        </a-descriptions-item>
        <a-descriptions-item label="有效期限起始日期">
          {{ cssbxx.qyr2jmsfzyxqxqsrq }}
        </a-descriptions-item>
         <a-descriptions-item label="有效期限截止日期">
          {{ cssbxx.qyr2jmsfzyxqxjzrq }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
        <a-collapse-panel key="4" header="迁移人三">
          <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="与持证人关系">
          {{ cssbxx.qyr3yczrgxjtgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ cssbxx.qyr3gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ cssbxx.qyr3xm }}
        </a-descriptions-item>
        <a-descriptions-item label="曾用名">
          {{ cssbxx.qyr3cym }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ cssbxx.qyr3xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="民族">
          {{ cssbxx.qyr3mzdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ cssbxx.qyr3csrq }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地国家地区">
          {{ cssbxx.qyr3csdgjhdqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地省市县（区）">
          {{ cssbxx.qyr3csdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地详址">
          {{ cssbxx.qyr3csdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯国家地区">
          {{ cssbxx.qyr3jggjhdqdmLabel}}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯省县">
          {{ cssbxx.qyr3jgssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯详址">
          {{ cssbxx.qyr3jgqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="文化程度">
          {{ cssbxx.qyr3xldmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="婚姻状况">
          {{ cssbxx.qyr3hyzkdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="职业">
          {{ cssbxx.qyr3zy }}
        </a-descriptions-item>
        <a-descriptions-item label="居民身份证签发机关">
          {{ cssbxx.qyr3jmsfzqfjg }}
        </a-descriptions-item>
        <a-descriptions-item label="有效期限起始日期">
          {{ cssbxx.qyr3jmsfzyxqxqsrq }}
        </a-descriptions-item>
         <a-descriptions-item label="有效期限截止日期">
          {{ cssbxx.qyr3jmsfzyxqxjzrq }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
      <a-collapse-panel key="5" header="迁移人四">
          <a-descriptions label-placement="left" :column="3" size="small" bordered>
        <a-descriptions-item label="与持证人关系">
          {{ cssbxx.qyr4yczrgxjtgxdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="公民身份号码">
          {{ cssbxx.qyr4gmsfhm }}
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          {{ cssbxx.qyr4xm }}
        </a-descriptions-item>
        <a-descriptions-item label="曾用名">
          {{ cssbxx.qyr4cym }}
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          {{ cssbxx.qyr4xbdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="民族">
          {{ cssbxx.qyr4mzdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生日期">
          {{ cssbxx.qyr4csrq }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地国家地区">
          {{ cssbxx.qyr4csdgjhdqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地省市县（区）">
          {{ cssbxx.qyr4csdssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="出生地详址">
          {{ cssbxx.qyr4csdqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯国家地区">
          {{ cssbxx.qyr4jggjhdqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯省县">
          {{ cssbxx.qyr4jgssxqdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="籍贯详址">
          {{ cssbxx.qyr4jgqhnxxdz }}
        </a-descriptions-item>
        <a-descriptions-item label="文化程度">
          {{ cssbxx.qyr4xldmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="婚姻状况">
          {{ cssbxx.qyr4hyzkdmLabel }}
        </a-descriptions-item>
        <a-descriptions-item label="职业">
          {{ cssbxx.qyr4zy }}
        </a-descriptions-item>
        <a-descriptions-item label="居民身份证签发机关">
          {{ cssbxx.qyr4jmsfzqfjg }}
        </a-descriptions-item>
        <a-descriptions-item label="有效期限起始日期">
          {{ cssbxx.qyr4jmsfzyxqxqsrq }}
        </a-descriptions-item>
         <a-descriptions-item label="有效期限截止日期">
          {{ cssbxx.qyr4jmsfzyxqxjzrq }}
        </a-descriptions-item>
      </a-descriptions>
    </a-collapse-panel>
  </a-collapse>
</template>

<script lang="ts" setup name="qyxx-tab">
import { ref, onMounted } from 'vue';
import { apiKsywQyzxxbView } from '#/api';
let activeKey = ref(['1', '2', '3', '4','5']);
// 定义接收的 props
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const cssbxx = ref<any>({});
onMounted(async () => {
  cssbxx.value = await apiKsywQyzxxbView({ ksywid: props.data.ksywid });
});
</script>
