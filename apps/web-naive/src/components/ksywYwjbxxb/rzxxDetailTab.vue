<template>
  <Page class="flex h-full flex-col">
    <Grid class="flex-1"> </Grid>
  </Page>
</template>
<script lang="ts" setup name="rzxx-tab">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import { Page } from '@vben/common-ui';
import { apiKsywRzbPage } from '#/api';
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
// 搜索表单配置
function useColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { title: '业务类型', field: 'ywlxLabel' },
    { title: '业务标志', field: 'ywbz' },
    { title: '入库时间', field: 'rksj', width: 150 },
    { title: '返回标志', field: 'fhbz' },
    { title: '准迁证编号', field: 'zqzbh' },
    { title: '迁移证编号', field: 'qyzbh' },
    { title: '备注', field: 'bz' },
    { title: '返回描述', field: 'qcdssxqdmLabel', width: 150 },
  ];
}
const [Grid] = useVbenVxeGrid({
  showSearchForm: false,
  gridOptions: {
    columns: useFormatterColumns(useColumns()),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          
          const { currentPage, pageSize } = page;
          const result = await apiKsywRzbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: true,
            ywslh:props.data.ywslh
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'nbslid',
    },
    toolbarConfig: {
      search: false,
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
    },
  } as VxeTableGridOptions,
});
</script>
<style lang="scss" scoped></style>
