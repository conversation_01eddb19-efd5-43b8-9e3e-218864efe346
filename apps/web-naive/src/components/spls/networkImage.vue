<script setup lang="ts">
import type { Ref } from 'vue';

import { inject, onMounted, ref } from 'vue';

import { apiGaythtYthSlclList } from '#/api';

const detailData = inject<Ref<any>>('detailData');
const images = ref<{ name: string; url: string }[]>([]);
const hlwclList = ref<any[]>([]);

onMounted(async () => {
  const result = await apiGaythtYthSlclList({
    projectId: detailData.value.ysbxxbh ?? -1,
  });
  hlwclList.value = result;
  images.value = (result?.records || []).map((item) => ({
    url: item.base64Data
      ? `data:image/jpeg;base64,${item.base64Data}`
      : item.fileurl,
    name: item.name || item.clmc || '图片',
  }));
});
</script>
<template>
  <div class="image-grid">
    <a-card
      class="w-full"
      size="small"
      :title="hlwcl.attrname"
      v-for="hlwcl in hlwclList"
      :key="hlwcl.unid"
    >
      <div
        v-if="hlwcl.gaythtYthSlclsjList?.length > 0"
        class="flex flex-nowrap gap-4 overflow-x-auto"
      >
        <a-image-preview-group>
          <div
            v-for="(imgItem, idx) in hlwcl.gaythtYthSlclsjList"
            :key="idx"
            class="image-item shrink-0"
          >
            <a-image
              :src="imgItem.fileurl"
              :alt="imgItem.filename"
              width="200"
              height="200"
            />
            <div class="image-name">{{ imgItem.filename }}</div>
          </div>
        </a-image-preview-group>
      </div>
      <template v-else>
        <a-empty />
      </template>
    </a-card>
  </div>
</template>
<style scoped>
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding: 16px 0;
}

.image-item {
  width: 120px;
  height: 120px;
}

.image-name {
  margin-top: 8px;
  font-size: 14px;
  color: #888;
  text-align: center;
  word-break: break-all;
}

.empty-tip {
  padding: 32px 0;
  font-size: 16px;
  color: #bbb;
}
</style>
