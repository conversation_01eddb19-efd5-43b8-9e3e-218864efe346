<script setup lang="ts">
import { computed, defineAsyncComponent, inject, ref } from 'vue';
import { useRoute } from 'vue-router';

import { useVbenDrawer } from '@vben/common-ui';

import YjtqhlwclDrawer from '#/components/spls/yjtqhlwclDrawer.vue';
import DzsfzjzjExtract from '#/views/business/dzsfz/dzsfzjzjExtract.vue';

const tabs = [
  { label: '材料信息', key: 'materialDetail' },
  { label: '互联网材料网页显示', key: 'netMaterial' },
];

const route = useRoute();
const value = ref(tabs[0].key);
const detailData = inject<any>('detailData');

const tabComponents = {
  materialDetail: defineAsyncComponent(() => import('./materialView.vue')),
  netMaterial: defineAsyncComponent(() => import('./networkImage.vue')),
};

const CurrentTab = computed(() => tabComponents[value.value]);

const editMode = ref(false);

const toggleEditMode = () => {
  editMode.value = !editMode.value;
};

// 一键提取互联网材料
// const data = ref();
const [HlwclDrawer, hlwclDrawerlApi] = useVbenDrawer({
  connectedComponent: YjtqhlwclDrawer,
  destroyOnClose: true,
});

// 打开一键提取互联网材料
const openModal = () => {
  hlwclDrawerlApi
    .setData({
      content: '外部传递的数据 content',
      payload: '外部传递的数据 payload',
    })
    .open();
};

// 创建电子身份证加注件提取组件
const [ExtractDrawer, extractApi] = useVbenDrawer({
  connectedComponent: DzsfzjzjExtract,
  destroyOnClose: true,
});

// 打开电子身份证加注件提取功能
const openExtractDrawer = () => {
  extractApi.setData({
    ywslh: route.params?.id ?? -1,
    sjgsdwdm: detailData.value.sjgsdwdm ?? '',
    lcywlx: detailData.value.lcywlx ?? '',
  });
  extractApi.open();
};
</script>

<template>
  <div>
    <div class="mb-4 flex items-center justify-between">
      <a-segmented
        v-model:value="value"
        block
        :options="tabs.map((tab) => ({ label: tab.label, value: tab.key }))"
        class="tab-segmented"
      />
      <a-space wrap v-show="value === 'materialDetail'">
        <a-button @click="openExtractDrawer" v-if="editMode" size="small">
          引用电子加注件
        </a-button>
        <a-button @click="openModal" v-if="editMode" size="small">
          一键提取互联网材料
        </a-button>
        <a-button @click="toggleEditMode" type="primary" size="small">
          {{ editMode ? '保存并返回' : '修改材料' }}
        </a-button>
      </a-space>
    </div>
    <component :is="CurrentTab" />
    <ExtractDrawer />
    <HlwclDrawer />
  </div>
</template>

<style lang="scss" scoped></style>
