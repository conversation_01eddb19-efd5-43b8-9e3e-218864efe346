<script setup lang="ts">
import { onMounted, ref } from 'vue';
// import { useRoute } from 'vue-router';

// import { apiRktYwslJbView } from '#/api';

// const route = useRoute();
const data = ref<any>({});
// const updateDetailData = inject<(d: any) => void>('updateDetailData');

onMounted(() => {
  // apiRktYwslJbView({ id: route.params.id }).then((response) => {
  //   data.value = response;
  //   if (updateDetailData) {
  //     updateDetailData(data.value);
  //   }
  // });
});
</script>

<template>
  <div class="tab-panel">
    <a-collapse>
      <a-collapse-panel key="1" header="审批业务流水信息">
        <a-descriptions bordered size="small" :column="2">
          <a-descriptions-item label="业务流水号">
            {{ data.ywslh }}
          </a-descriptions-item>
          <a-descriptions-item label="业务类型">
            {{ data.lcywlxLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="办理状态">
            {{ data.blztLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="审批流程">
            {{ data.lcmc }}
          </a-descriptions-item>
          <a-descriptions-item label="公民身份号码">
            {{ data.gmsfhm }}
          </a-descriptions-item>
          <a-descriptions-item label="姓名">{{ data.xm }}</a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
      <a-collapse-panel key="2" header="申请人信息">
        <a-descriptions bordered size="small" :column="2">
          <a-descriptions-item label="申请人姓名">
            {{ data.sqrxm }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人公民身份号码">
            {{ data.sqrgmsfhm }}
          </a-descriptions-item>
          <a-descriptions-item label="申请日期">
            {{ data.sqrq }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人联系电话">
            {{ data.sqrlxdh }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人户口登记机关">
            {{ data.sqrhkdjjg }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人住址省市县（区）">
            {{ data.sqrzzssxq }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人住址详址">
            {{ data.sqrzzxz }}
          </a-descriptions-item>
          <a-descriptions-item label="入立户标志">
            {{ data.rlhbzLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人证件名称">
            {{ data.sqrzjmc }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人证件号码">
            {{ data.sqrzjhm }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人工作单位">
            {{ data.sqrgzdw }}
          </a-descriptions-item>
          <a-descriptions-item label="受理人数量">
            {{ data.slrs }}
          </a-descriptions-item>
          <a-descriptions-item label="申请查询理由">
            {{ data.sqcxly }}
          </a-descriptions-item>
          <a-descriptions-item label="其他申请人">
            {{ data.qtsqr }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人人像比对时间">
            {{ data.sqrrxbdsj }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人人像比对相似度">
            {{ data.sqrrxbdxsd }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人人像比对结果">
            {{ data.sqrrxbdjg }}
          </a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
      <a-collapse-panel key="3" header="办理模式">
        <a-descriptions bordered size="small" :column="2">
          <a-descriptions-item label="办理模式">
            {{ data.spxxlylbLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="打印出件类型">
            {{ data.dycjlxLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="受理单位_公安机关机构代码">
            {{ data.sldwgajgjgdm }}
          </a-descriptions-item>
          <a-descriptions-item label="受理单位_公安机关名称">
            {{ data.sldwgajgmc }}
          </a-descriptions-item>
          <a-descriptions-item label="数据归属单位代码">
            {{ data.sjgsdwdm }}
          </a-descriptions-item>
          <a-descriptions-item label="数据归属单位名称">
            {{ data.sjgsdwmc }}
          </a-descriptions-item>
          <a-descriptions-item label="受理时间">
            {{ data.slsj }}
          </a-descriptions-item>
          <a-descriptions-item label="流程状态/审批是否完成">
            {{ data.lcztLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="审批信息采集方式">
            {{ data.spxxcjfsLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="互联网申请类型">
            {{ data.hlwsqlxLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="政务网大厅名称/政务大厅对应的常口受理点">
            {{ data.zwwdtmc }}
          </a-descriptions-item>
          <a-descriptions-item label="互联网申报来源">
            {{ data.applyfromLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="互联网业务办理类型">
            {{ data.bustypeLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="是否提取互联网材料">
            {{ data.sftqhlwcl }}
          </a-descriptions-item>
          <a-descriptions-item label="户籍事项查询类别">
            {{ data.hjsxcxlbLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="是否跨省协同迁入">
            {{ data.sfksxtqrLabel }}
          </a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
      <a-collapse-panel key="4" header="快递信息">
        <a-descriptions bordered size="small" :column="2">
          <a-descriptions-item label="是否需要邮寄证件">
            {{ data.sfxyyjzjLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="是否已经邮寄证件">
            {{ data.sfyjyjzjLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="收件人姓名">
            {{ data.sjrxm }}
          </a-descriptions-item>
          <a-descriptions-item label="收件人联系电话">
            {{ data.sjrlxdh }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人通讯地址">
            {{ data.sqrtxdz }}
          </a-descriptions-item>
          <a-descriptions-item label="快递单号">
            {{ data.kddh }}
          </a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
      <a-collapse-panel key="5" header="审批信息">
        <a-descriptions bordered size="small" :column="2">
          <a-descriptions-item label="审批单位_公安机关机构代码">
            {{ data.spdwgajgjgdm }}
          </a-descriptions-item>
          <a-descriptions-item label="审批单位_公安机关名称">
            {{ data.spdwgajgmc }}
          </a-descriptions-item>
          <a-descriptions-item label="审批人姓名">
            {{ data.sprxm }}
          </a-descriptions-item>
          <a-descriptions-item label="审批时间">
            {{ data.spsj }}
          </a-descriptions-item>
          <a-descriptions-item label="审批人联系电话">
            {{ data.sprlxdh }}
          </a-descriptions-item>
          <a-descriptions-item label="审批结果代码">
            {{ data.spjgdm }}
          </a-descriptions-item>
          <a-descriptions-item label="审批意见">
            {{ data.spyj }}
          </a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
      <a-collapse-panel key="6" header="业务信息">
        <a-descriptions bordered size="small" :column="2">
          <a-descriptions-item label="受理人姓名">
            {{ data.slrxm }}
          </a-descriptions-item>
          <a-descriptions-item label="受理人联系电话">
            {{ data.slrlxdh }}
          </a-descriptions-item>
          <a-descriptions-item label="业务标题">
            {{ data.lcywbt }}
          </a-descriptions-item>
          <a-descriptions-item label="办理户籍业务id">
            {{ data.blhjywid }}
          </a-descriptions-item>
          <a-descriptions-item label="户籍业务办理时间">
            {{ data.hjywblsj }}
          </a-descriptions-item>
          <a-descriptions-item label="是否生成电子签章户口页">
            {{ data.sfscdzqzhkyLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="是否打印户口簿首页">
            {{ data.sfdyhkbsyLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="是否按照集体户方式打印">
            {{ data.sfazjthfsdyLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="是否打印全户人员">
            {{ data.sfdyqhryLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="是否打印出生原因">
            {{ data.sfdycsyyLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="预申报系统业务文件类型">
            {{ data.ysbxtywwjlxLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="预申报信息编号">
            {{ data.ysbxxbh }}
          </a-descriptions-item>
          <a-descriptions-item label="户主姓名">
            {{ data.hzxm }}
          </a-descriptions-item>
          <a-descriptions-item label="户主公民身份号码">
            {{ data.hzgmsfhm }}
          </a-descriptions-item>
          <a-descriptions-item label="申请表是否标准签名">
            {{ data.sqbsfbzqmLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="常表是否标准签名">
            {{ data.cbsfbzqmLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="备注">{{ data.bz }}</a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
      <a-collapse-panel key="7" header="备案及发送信息">
        <a-descriptions bordered size="small" :column="2">
          <a-descriptions-item label="档案备案标志">
            {{ data.dababz }}
          </a-descriptions-item>
          <a-descriptions-item label="档案备案时间">
            {{ data.dabasj }}
          </a-descriptions-item>
          <a-descriptions-item label="变更一件事上报标志">
            {{ data.bgyjssbbzLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="变更一件事上报时间">
            {{ data.bgyjssbsj }}
          </a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
      <a-collapse-panel key="8" header="满意度评价信息">
        <a-descriptions bordered size="small" :column="2">
          <a-descriptions-item label="评价结果">
            {{ data.pjjgLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="跑了几次评价">
            {{ data.pjpljcLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="评价时间">
            {{ data.pjsj }}
          </a-descriptions-item>
          <a-descriptions-item label="户籍地民警首次审核耗时(分)">
            {{ data.mjscshhs }}
          </a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
      <a-collapse-panel key="9" header="“好差评”系统上报信息">
        <a-descriptions bordered size="small" :column="2">
          <a-descriptions-item label="是否需要上报0否1是">
            {{ data.sbsfxyLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="上报状态">
            {{ data.sbztLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="上报业务类型代码">
            {{ data.sbywlxLabel }}
          </a-descriptions-item>
          <a-descriptions-item label="上报业务名称">
            {{ data.sbywmc }}
          </a-descriptions-item>
          <a-descriptions-item label="上报申报时间">
            {{ data.sbsbsj }}
          </a-descriptions-item>
          <a-descriptions-item label="上报受理时间">
            {{ data.sbslsj }}
          </a-descriptions-item>
          <a-descriptions-item label="上报审核时间">
            {{ data.sbshsj }}
          </a-descriptions-item>
          <a-descriptions-item label="上报办结时间">
            {{ data.sbbjsj }}
          </a-descriptions-item>
          <a-descriptions-item label="上报审批开始时间">
            {{ data.sbspkssj }}
          </a-descriptions-item>
          <a-descriptions-item label="上报审批结束时间">
            {{ data.sbspjssj }}
          </a-descriptions-item>
          <a-descriptions-item label="上报审批人姓名">
            {{ data.sbsprxm }}
          </a-descriptions-item>
          <a-descriptions-item label="上报审批单位代码">
            {{ data.sbsprdwdm }}
          </a-descriptions-item>
          <a-descriptions-item label="上报审批结果代码">
            {{ data.sbspjgdm }}
          </a-descriptions-item>
          <a-descriptions-item label="上报审批意见">
            {{ data.sbspyj }}
          </a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
      <a-collapse-panel key="10" header="人社局联审信息">
        <a-descriptions bordered size="small" :column="2">
          <a-descriptions-item label="高技能证书编号">
            {{ data.gjnzsbh }}
          </a-descriptions-item>
          <a-descriptions-item label="人社局审核结果">
            {{ data.rsjshjg }}
          </a-descriptions-item>
          <a-descriptions-item label="人社局审核理由">
            {{ data.rsjshly }}
          </a-descriptions-item>
          <a-descriptions-item label="人社局审核时间">
            {{ data.rsjshsj }}
          </a-descriptions-item>
          <a-descriptions-item label="人社局审核人">
            {{ data.rsjshr }}
          </a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<style scoped>
.tab-panel {
  padding: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 3%);
}

h2 {
  margin-bottom: 12px;
  font-size: 20px;
  font-weight: 600;
}
</style>
