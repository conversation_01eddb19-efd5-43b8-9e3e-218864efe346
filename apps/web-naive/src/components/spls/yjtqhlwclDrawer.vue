<script setup lang="ts">
import type { Ref } from 'vue';

import { inject, ref } from 'vue';
import { useRoute } from 'vue-router';

import { ColPage, useVbenDrawer } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { Modal } from 'ant-design-vue';

import { defaultFormConfig } from '#/adapter/form';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  apiGaythtYthSlclPage,
  apiGaythtYthSlclsjPage,
  apiRktYwslClqdPage,
  apiRktYwslClysjPageWithFile,
  apiSaveYwslClysjAll,
  apiSaveYwslClysjBatch,
} from '#/api';

const fixedQueryParams = inject<any>('fixedQueryParams');
const detailData = inject<Ref<any>>('detailData');
const unid = ref<any>(-1);
const currRow = ref<any>({});

const route = useRoute();

const onExtractBatchData = () => {
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认提取所有的记录吗？`,
    onOk: async () => {
      await apiSaveYwslClysjAll({
        projid: detailData.value.ysbxxbh ?? -1,
        sqrxm: detailData.value.sqrxm ?? '',
        ywslh: route.params?.id ?? -1,
        lcywlx: fixedQueryParams?.material?.params?.lcywlx ?? '',
        gmsfhm: detailData.value.gmsfhm ?? '',
        sldsjgsdwdm: detailData.value.sldsjgsdwdm ?? '',
      });
      // await gridApi.query();
    },
  });
};

const onExtractData = () => {
  const selectRecords = gridRightApi.grid.getCheckboxRecords();
  const ids = selectRecords.map((item) => item.ywslh);
  selectRecords.forEach((item) => {
    item.attrname = currRow.value.attrname || '';
    item.sqrxm = detailData.value.sqrxm || '';
    item.ywslh = detailData.value.ywslh || '';
    item.lcywlx = detailData.value.lcywlx || '';
    item.gmsfhm = detailData.value.gmsfhm || '';
    item.sldsjgsdwdm = detailData.value.sldsjgsdwdm || '';
  });
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认备案选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await apiSaveYwslClysjBatch({
        gaythtYthSlclsjDTOList: selectRecords,
      });
      // await gridRightApi.query();
    },
  });
};
const data = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  class: 'w-full',
  title: '互联网材料提取',
  placement: 'right',
  destroyOnClose: true,
  showCancelButton: false,
  showConfirmButton: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // 重置数据
      data.value = drawerApi.getData<Record<string, any>>();
      resetForm();
    }
  },
});

// 重置表单
const resetForm = () => {};

const gridEvents: VxeGridListeners<RowType> = {
  cellClick: ({ row }) => {
    currRow.value = row;
    unid.value = row.unid;
    gridRightApi.query({ unid: row.unid });
  },
};

const [Grid] = useVbenVxeGrid({
  gridEvents,
  formOptions: {
    ...defaultFormConfig,
    compact: false,
    actionButtonsReverse: false,
    wrapperClass: '',
    submitButtonOptions: {
      content: '查询',
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
    resetButtonOptions: {
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
  },
  gridOptions: {
    columns: useFormatterColumns(
      fixedQueryParams?.yjtqhlwcl?.useYjtqhlwclLeftColumns(),
    ),
    layouts: ['Top', 'Form', 'Toolbar', 'Table', 'Bottom'],
    height: 'auto',
    scrollY: {
      enabled: true,
      gt: 0,
    },
    showOverflow: true,
    toolbarConfig: {
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
      search: false,
    },
    pagerConfig: {
      enabled: true,
    },
    sortConfig: {
      multiple: false,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage } = page;
          formValues.projid = detailData.value.ysbxxbh ?? -1;
          const result = await apiGaythtYthSlclPage({
            pageNumber: currentPage,
            pageSize: fixedQueryParams.pageSize,
            ...formValues,
            ...formatCascaderValue(formValues, ['ssxq']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'dm',
      isCurrent: true,
      isHover: true,
    },
  } as VxeTableGridOptions,
});

const [RightGrid, gridRightApi] = useVbenVxeGrid({
  formOptions: {
    ...defaultFormConfig,
    compact: false,
    actionButtonsReverse: false,
    wrapperClass: '',
    submitButtonOptions: {
      content: '查询',
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
    resetButtonOptions: {
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
  },
  gridOptions: {
    columns: useFormatterColumns(
      fixedQueryParams?.yjtqhlwcl?.useYjtqhlwclRightColumns(),
    ),
    layouts: ['Top', 'Form', 'Toolbar', 'Table', 'Bottom'],
    height: 'auto',
    scrollY: {
      enabled: true,
      gt: 0,
    },
    showOverflow: true,
    toolbarConfig: {
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
      search: false,
    },
    pagerConfig: {
      enabled: true,
    },
    sortConfig: {
      multiple: false,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage } = page;
          formValues.unid = unid.value;
          const result = await apiGaythtYthSlclsjPage({
            pageNumber: currentPage,
            pageSize: fixedQueryParams.pageSize,
            ...fixedQueryParams.material?.params,
            ...formValues,
            ...formatCascaderValue(formValues, ['ssxq']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'dm',
    },
  } as VxeTableGridOptions,
});

const [LeftBottomGrid] = useVbenVxeGrid({
  formOptions: {
    ...defaultFormConfig,
    compact: false,
    actionButtonsReverse: false,
    wrapperClass: '',
    submitButtonOptions: {
      content: '查询',
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
    resetButtonOptions: {
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
  },
  gridOptions: {
    columns: useFormatterColumns(
      fixedQueryParams?.material?.useMaterialLeftColumns(),
    ),
    layouts: ['Top', 'Form', 'Toolbar', 'Table', 'Bottom'],
    height: 'auto',
    scrollY: {
      enabled: true,
      gt: 0,
    },
    showOverflow: true,
    toolbarConfig: {
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
      search: false,
    },
    pagerConfig: {
      enabled: true,
    },
    sortConfig: {
      multiple: false,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage } = page;
          formValues.ywslh = route.params?.id ?? -1;
          const result = await apiRktYwslClqdPage({
            pageNumber: currentPage,
            pageSize: fixedQueryParams.pageSize,
            ...formValues,
            ...formatCascaderValue(formValues, ['ssxq']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'dm',
    },
  } as VxeTableGridOptions,
});

const [RightBottomGrid] = useVbenVxeGrid({
  formOptions: {
    ...defaultFormConfig,
    compact: false,
    actionButtonsReverse: false,
    wrapperClass: '',
    submitButtonOptions: {
      content: '查询',
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
    resetButtonOptions: {
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
  },
  gridOptions: {
    columns: useFormatterColumns(
      fixedQueryParams?.material?.useMaterialRightColumns(),
    ),
    layouts: ['Top', 'Form', 'Toolbar', 'Table', 'Bottom'],
    height: 'auto',
    scrollY: {
      enabled: true,
      gt: 0,
    },
    showOverflow: true,
    toolbarConfig: {
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
      search: false,
    },
    pagerConfig: {
      enabled: true,
    },
    sortConfig: {
      multiple: false,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage } = page;
          formValues.ywslh = route.params?.id ?? -1;
          const result = await apiRktYwslClysjPageWithFile({
            pageNumber: currentPage,
            pageSize: fixedQueryParams.pageSize,
            ...fixedQueryParams.material?.params,
            ...formValues,
            ...formatCascaderValue(formValues, ['ssxq']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'dm',
    },
  } as VxeTableGridOptions,
});
</script>
<template>
  <div>
    <Drawer append-to-main>
      <!-- <div class="flex-col-center">外部传递数据： {{ data }}</div> -->
      <ColPage>
        <template #left="">
          <Card class="flex-1">
            <Page class="flex-1">
              <div class="vp-raw h-full w-full">
                <Grid>
                  <template #toolbar-tools>
                    <a-button
                      type="primary"
                      @click="onExtractBatchData"
                      size="small"
                    >
                      一键提取
                    </a-button>
                  </template>
                </Grid>
              </div>
            </Page>
          </Card>
        </template>
        <Card>
          <Page>
            <RightGrid>
              <template #toolbar-tools>
                <a-button type="primary" @click="onExtractData" size="small">
                  提取
                </a-button>
              </template>
            </RightGrid>
          </Page>
        </Card>
      </ColPage>
      <!-- ================================== -->
      <!-- <ColPage auto-content-height> -->
      <ColPage>
        <template #left="">
          <Card class="flex-1">
            <Page class="flex-1">
              <div class="vp-raw h-full w-full">
                <LeftBottomGrid />
              </div>
            </Page>
          </Card>
        </template>
        <Card>
          <Page>
            <RightBottomGrid />
          </Page>
        </Card>
      </ColPage>
    </Drawer>
  </div>
</template>
<style lang="scss" scoped></style>
