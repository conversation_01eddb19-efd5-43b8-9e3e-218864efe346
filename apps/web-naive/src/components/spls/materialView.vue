<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { inject, ref } from 'vue';
import { useRoute } from 'vue-router';

import { ColPage, Page } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { PictureOutlined, UnorderedListOutlined } from '@ant-design/icons-vue';
import { Card } from 'ant-design-vue';

import { defaultFormConfig } from '#/adapter/form';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import { apiRktYwslClqdPage, apiRktYwslClysjPageWithFile } from '#/api';

import materialImageGrid from './materialImageGrid.vue';

const fixedQueryParams = inject<any>('fixedQueryParams');

const route = useRoute();

const searchCount = ref(true);
const displayMode = ref('list');

const [Grid] = useVbenVxeGrid({
  formOptions: {
    ...defaultFormConfig,
    compact: false,
    actionButtonsReverse: false,
    wrapperClass: '',
    submitButtonOptions: {
      content: '查询',
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
    resetButtonOptions: {
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
  },
  gridOptions: {
    columns: useFormatterColumns(
      fixedQueryParams?.material?.useMaterialLeftColumns(),
    ),
    layouts: ['Top', 'Form', 'Toolbar', 'Table', 'Bottom'],
    height: 'auto',
    scrollY: {
      enabled: true,
      gt: 0,
    },
    showOverflow: true,
    toolbarConfig: {
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
      search: false,
    },
    pagerConfig: {
      enabled: true,
    },
    sortConfig: {
      multiple: false,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage } = page;
          formValues.ywslh = route.params?.id ?? -1;
          const result = await apiRktYwslClqdPage({
            pageNumber: currentPage,
            pageSize: fixedQueryParams.pageSize,
            ...formValues,
            searchCount: searchCount.value,
            ...formatCascaderValue(formValues, ['ssxq']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'dm',
    },
  } as VxeTableGridOptions,
});

const [RightGrid] = useVbenVxeGrid({
  formOptions: {
    ...defaultFormConfig,
    compact: false,
    actionButtonsReverse: false,
    wrapperClass: '',
    submitButtonOptions: {
      content: '查询',
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
    resetButtonOptions: {
      // @ts-ignore 框架bug
      size: 'small' as any,
      show: false,
    },
  },
  gridOptions: {
    columns: useFormatterColumns(
      fixedQueryParams?.material?.useMaterialRightColumns(),
    ),
    layouts: ['Top', 'Form', 'Toolbar', 'Table', 'Bottom'],
    height: 'auto',
    scrollY: {
      enabled: true,
      gt: 0,
    },
    showOverflow: true,
    toolbarConfig: {
      custom: false,
      export: false,
      // import: true,
      refresh: false,
      zoom: false,
      search: false,
    },
    pagerConfig: {
      enabled: true,
    },
    sortConfig: {
      multiple: false,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage } = page;
          formValues.ywslh = route.params?.id ?? -1;
          const result = await apiRktYwslClysjPageWithFile({
            pageNumber: currentPage,
            pageSize: fixedQueryParams.pageSize,
            ...fixedQueryParams.material?.params,
            ...formValues,
            searchCount: searchCount.value,
            ...formatCascaderValue(formValues, ['ssxq']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'dm',
    },
  } as VxeTableGridOptions,
});
</script>
<template>
  <ColPage auto-content-height>
    <template #left="">
      <Card class="flex-1">
        <Page class="flex-1">
          <div class="vp-raw h-full w-full">
            <Grid>
              <template #submit-before>
                <a-space warp>
                  <a-button
                    type="primary"
                    size="small"
                    @click="batchRecordFiling()"
                  >
                    新增
                  </a-button>
                  <a-button danger size="small"> 删除 </a-button>
                </a-space>
              </template>
            </Grid>
          </div>
        </Page>
      </Card>
    </template>
    <Card>
      <Page>
        <template #extra>
          <div style="display: flex; gap: 16px; align-items: center">
            <a-radio-group v-model:value="displayMode" button-style="solid">
              <a-radio-button value="list">
                <UnorderedListOutlined />
                列表
              </a-radio-button>
              <a-radio-button value="image">
                <PictureOutlined />
                图片
              </a-radio-button>
            </a-radio-group>
          </div>
        </template>
        <component
          :is="displayMode === 'list' ? RightGrid : materialImageGrid"
        />
      </Page>
    </Card>
  </ColPage>
</template>
<style lang="scss" scoped></style>
