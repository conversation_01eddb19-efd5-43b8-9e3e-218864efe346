import type { CascaderProps } from 'ant-design-vue';

import type { ExtendedFormApi } from '@vben/common-ui';

import { ApiComponent } from '@vben/common-ui';

import { apiFwtDmGgYwbmTree, apiListByParent } from '#/api';
import { apiXtXtcsbListByType } from '#/api/system/sysDict';

const isLeafFn = (
  value: string,
  isLeaf: boolean,
  minLevel: number | string,
) => {
  return (
    value.replace(/0+$/, '').length === Number(minLevel) || isLeaf || false
  );
};

const Cascader = defineAsyncComponent(
  () => import('ant-design-vue/es/cascader'),
);
const ApiYwbmCascader: Component = (props, { attrs, slots }) => {
  return h(
    ApiComponent,
    {
      component: Cascader,
      nodeKey: 'value',
      valueField: 'value',
      labelField: 'label',
      modelPropName: 'value',
      optionsPropName: 'options',
      ...props,
      ...attrs,
      api: async () => {
        const result = await apiFwtDmGgYwbmTree();
        return new Promise((resolve) => {
          resolve(result);
        });
      },
    },
    slots,
  );
};
const ApiCascader: Component = (props, { attrs, slots }) => {
  type CascaderParams = {
    abbr?: boolean;
    dataType?: string;
    glmType?: string;
    init?: boolean;
    minLevel: number;
    root?: boolean;
    ssxq?: string;
  };

  const finds = (
    options: any,
    findValue: number | string,
    result: any[] = [],
  ) => {
    for (const option of options) {
      result.push(option.value);
      if (option.value === findValue) {
        return result;
      }
      if (option.children !== undefined && option.children.length > 0) {
        const items = finds(option.children, findValue, result);
        if (items) {
          return result;
        }
      }
      result.pop();
    }
    if (result.length === 0) {
      return [];
    }
  };
  const { glmType, minLevel, dataType } = attrs.params as CascaderParams;
  const loadData: CascaderProps['loadData'] = (selectedOptions) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    if (!targetOption) return;
    if (targetOption?.children && targetOption?.children?.length > 0) return;
    targetOption.loading = true;
    return new Promise<void>((resolve) => {
      apiListByParent({
        ssxq: targetOption.value,
        glmType,
        minLevel,
        dataType,
      })
        .then((res) => {
          targetOption.children = targetOption.children || [];
          targetOption.children?.push(
            ...res.map((v: any) => ({
              origin: { ...v },
              ...v,
              text: v.text,
              value: v.value,
              isLeaf: isLeafFn(v.value, v.isLeaf, minLevel),
            })),
          );
          targetOption.isLeaf = targetOption.children?.length === 0;
        })
        .finally(() => {
          resolve();
        });
    });
  };
  return h(
    ApiComponent,
    {
      component: Cascader,
      nodeKey: 'value',
      api: async (params) => {
        if (attrs.multiple) {
          params.ssxqList = params.ssxq.join(',');
          delete params.ssxq;
        }
        const formApi: ExtendedFormApi = attrs.formApi as any;
        const result = await apiListByParent({
          abbr: true,
          init: true,
          root: true,
          ...params,
        });
        if (formApi) {
          if (attrs.multiple) {
            const innerValues = (attrs.modelValue as string[]).map((v) => {
              const innerValue = finds(result, v as string);
              return innerValue;
            });
            formApi.setFieldValue(attrs.name as string, innerValues);
          } else {
            const innerValue = finds(result, attrs.modelValue as string);
            formApi.setFieldValue(attrs.name as string, innerValue);
          }
        }
        return new Promise((resolve) => {
          resolve(result);
        });
      },
      loadingSlot: 'arrow',
      keyField: 'value',
      labelField: 'text',
      modelPropName: 'value',
      optionsPropName: 'options',
      visibleEvent: 'onVisibleChange',
      remote: true,
      loadData,
      changeOnSelect: true,
      ...props,
      ...attrs,
    },
    slots,
  );
};

const Select = defineAsyncComponent(() => import('ant-design-vue/es/select'));
const ApiSelect: Component = (props, { attrs, slots }) => {
  const { api, params } = props;
  return h(
    ApiComponent,
    {
      component: Select,
      loadingSlot: 'arrow',
      keyField: 'value',
      labelField: 'text',
      modelPropName: 'value',
      optionsPropName: 'options',
      api: api || (() => apiXtXtcsbListByType(params.type)),
      ...props,
      ...attrs,
    },
    slots,
  );
};

const Checkbox = defineAsyncComponent(
  () => import('ant-design-vue/es/checkbox'),
);
const CheckboxGroup = defineAsyncComponent(
  () => import('ant-design-vue/es/checkbox'),
);
const ApiCheckboxGroup: Component = (props, { attrs, slots }) => {
  const { api, params } = props;
  const checkboxGroup = defineComponent({
    inheritAttrs: false,
    name: 'ApiCheckboxGroup',
    setup: (innerProps, ctx) => {
      let defaultSlot: any;
      if (Reflect.has(ctx.slots, 'default')) {
        defaultSlot = ctx.slots.default;
      } else {
        const { options } = ctx.attrs;
        if (Array.isArray(options)) {
          defaultSlot = () =>
            options.map((option) => {
              const { text, value, otherOptions } = option;
              return h(Checkbox, { label: text, value, ...otherOptions });
            });
        }
      }
      return () =>
        h(
          CheckboxGroup,
          { ...innerProps, ...ctx.attrs },
          { default: defaultSlot },
        );
    },
  });

  return h(
    ApiComponent,
    {
      component: checkboxGroup,
      loadingSlot: 'arrow',
      keyField: 'value',
      labelField: 'text',
      modelPropName: 'value',
      optionsPropName: 'options',
      api: api || (() => apiXtXtcsbListByType(params.type)),
      ...props,
      ...attrs,
    },
    slots,
  );
};

const Space = defineAsyncComponent(() => import('ant-design-vue/es/space'));
const Radio = defineAsyncComponent(() => import('ant-design-vue/es/radio'));
const RadioButton = defineAsyncComponent(
  () => import('ant-design-vue/es/radio'),
);
const RadioGroup = defineAsyncComponent(
  () => import('ant-design-vue/es/radio'),
);
const ApiRadioGroup: Component = (props, { attrs, slots }) => {
  const { api, params } = props;
  const radioGroup = defineComponent({
    inheritAttrs: false,
    name: 'ApiCheckboxGroup',
    setup: (innerProps, ctx) => {
      let defaultSlot;
      if (Reflect.has(ctx.slots, 'default')) {
        defaultSlot = ctx.slots.default;
      } else {
        const { options } = ctx.attrs;
        if (Array.isArray(options)) {
          defaultSlot = () =>
            options.map((option) => {
              const { text, value, otherOptions } = option;
              return h(ctx.attrs.isButton ? RadioButton : Radio, {
                label: text,
                value,
                ...otherOptions,
              });
            });
        }
      }
      const groupRender = h(
        RadioGroup,
        { ...innerProps, ...ctx.attrs },
        { default: defaultSlot },
      );
      return () =>
        ctx.attrs.isButton
          ? h(Space, { vertical: true }, () => groupRender)
          : groupRender;
    },
  });
  return h(
    ApiComponent,
    {
      component: radioGroup,
      loadingSlot: 'arrow',
      keyField: 'value',
      labelField: 'text',
      modelPropName: 'value',
      optionsPropName: 'options',
      api: api || (() => apiXtXtcsbListByType(params.type)),
      ...props,
      ...attrs,
    },
    slots,
  );
};

const TreeSelect = defineAsyncComponent(
  () => import('ant-design-vue/es/tree-select'),
);
const ApiTreeSelect: Component = (props, { attrs, slots }) => {
  type CascaderParams = {
    abbr?: boolean;
    dataType?: string;
    glmType?: string;
    init?: boolean;
    minLevel?: number;
    root?: boolean;
    ssxq?: string;
  };
  const defaultCompProps = {
    multiple: true,
    cascade: true,
    treeCheckable: true,
    'allow-checking-not-loaded': true,
    maxTagCount: 1,
  };
  if (!attrs.api) {
    const { glmType, minLevel, dataType } = attrs?.params as CascaderParams;
    return h(
      ApiComponent,
      {
        component: TreeSelect,
        nodeKey: 'value',
        api: (params) =>
          apiListByParent({
            abbr: true,
            init: true,
            root: true,
            ...params,
          }),
        loadingSlot: 'arrow',
        keyField: 'value',
        labelField: 'text',
        modelPropName: 'value',
        optionsPropName: 'options',
        visibleEvent: 'onVisibleChange',
        remote: true,
        onLoad: (option: any) => {
          return new Promise<void>((resolve) => {
            apiListByParent({
              ssxq: option.value,
              glmType,
              minLevel,
              dataType,
            })
              .then((res) => {
                option.children = option.children || [];
                option.children.push(
                  ...res.map((v: any) => ({
                    origin: { ...v },
                    text: v.text,
                    value: v.value,
                    isLeaf: v.isLeaf,
                  })),
                );
                option.isLeaf = option.children.length === 0;
              })
              .finally(() => {
                resolve();
              });
          });
        },
        afterFetch: async (result) => {
          const deleteChildren = (_result: any) => {
            if (!Array.isArray(_result)) return;
            _result.forEach((item) => {
              item.isLeaf = false;
              if (item.children && item.children.length === 0) {
                item.isLeaf = false;
                delete item.children;
              } else {
                deleteChildren(item.children);
              }
            });
          };
          deleteChildren(result);
          return result;
        },
        ...defaultCompProps,
        ...props,
        ...attrs,
      },
      slots,
    );
  }
  return h(
    ApiComponent,
    {
      component: TreeSelect,
      nodeKey: 'value',
      loadingSlot: 'arrow',
      modelPropName: 'value',
      modelValue: 'value',
      optionsPropName: 'treeData',
      visibleEvent: 'onVisibleChange',
      ...defaultCompProps,
      ...props,
      ...attrs,
    },
    slots,
  );
};

export {
  ApiCascader,
  ApiCheckboxGroup,
  ApiRadioGroup,
  ApiSelect,
  ApiTreeSelect,
  ApiYwbmCascader,
};
