import { requestClient } from '../request';

export interface ZjtSlxxbPageResp {
  // 内部受理id
  nbslid: string;
  // 姓名
  xm: string;
  // 公民身份号码
  gmsfhm: string;
  // 受理状态
  slzt: string;
  // 是否申请绿通证
  sfsqltz: string;
  // 绿通证审核结果
  ltzshjg: string;
  //
  lssfzslbz: string;
  // 是否保存照片原图
  sfbczpyt: string;
  //
  hjddzbm: string;
  //
  hjdsjgsdwdm: string;
  //
  hjdsjgsdwmc: string;
  // 操作员姓名
  czyxm: string;
  //
  sldsjgsdwdm: string;
  //
  sldsjgsdwmc: string;
  //
  sldfjsjgsdwdm: string;
  //
  sldfjsjgsdwmc: string;
  // 有效期限起始日期
  yxqxqsrq: string;
  // 有效期限截止日期
  yxqxjzrq: string;
  // 制证类型
  zzlx: string;
  // 性别
  xb: string;
  // 民族
  mz: string;
  // 申请人_联系电话
  sqrlxdh: string;
  // 申领原因
  slyy: string;
  // 收费类型
  sflx: string;
  // 领证方式
  lqfs: string;
  // 受理方式
  slfs: string;
  // 受理方式新
  slfsx: string;
  // 受理号
  slh: string;
  // 是否待挂失证件0否1是
  sfdgszj: string;
  // 收件人联系电话
  sjrlxdh: string;
  // 指纹一指位
  zwyzw: string;
  // 指纹二指位
  zwezw: string;
  // 指纹采集结果代码
  zwcjjgdm: string;
  // 制证信息错误类别
  zzxxcwlb: string;
  // 质量回馈状态
  zlhkzt: string;
  // 分拣批次号
  fjpch: string;
  // 照片设备标识号
  zpsbbsh: string;
  // 照片设备品牌型号编码
  zpsbppxhdm: string;
  // 照片设备品牌型号
  zpsbppxh: string;
  // 绿通证申请时间
  ltzsqsj: string;
}

export interface ZjtSlxxbViewResp {
  // 姓名
  xm: string;
  // 照片ID
  zpid: string;
  // 申请人_公民身份号码
  sqrgmsfhm: string;
  // 公民身份号码
  gmsfhm: string;
  // 性别
  xb: string;
  // 申领原因
  slyy: string;
  // 出生日期
  csrq: string;
  // 领证方式
  lqfs: string;
  // 住址
  zz: string;
  // 民族
  mz: string;
  // 有效期限起始日期
  yxqxqsrq: string;
  // 有效期限截止日期
  yxqxjzrq: string;
  // 是否申请绿通证
  sfsqltz: string;
  // 绿通证申请时间
  ltzsqsj: string;
  // 绿通证申请事由
  ltzsqsy: string;
  // 绿通证审核结果
  ltzshjg: string;
  // 绿通证审批时间
  ltzspsj: string;
  // 绿通证审批人编号
  ltzsprid: string;
  // 绿通证审批人姓名
  ltzsprxm: string;
  // 绿通证申请原因
  ltzsqyy: string;
}

export const apiZjtSlxxbPage = async (params: any) => {
  return requestClient.get('/zjtSlxxb/page', {
    params,
  });
};

export const apiZjtSlxxbView = async (params?: {
  id: number | string;
}): Promise<ZjtSlxxbViewResp> => {
  return requestClient.get('/zjtSlxxb/view', {
    params,
  });
};

// 绿通证区县审核通过 F23353
export const apiApproveLtzQx = async (data: any) => {
  return requestClient.post('/zjtSlxxb/approveLtzQx', {
    ...data,
  });
};

// 绿通证区县审核不通过 F23353
export const apiRejectLtzQx = async (data: any) => {
  return requestClient.post('/zjtSlxxb/rejectLtzQx', {
    ...data,
  });
};

// 绿通证地市审核通过 F23354
export const apiApproveLtzDs = async (data: any) => {
  return requestClient.post('/zjtSlxxb/approveLtzDs', {
    ...data,
  });
};

// 绿通证地市审核不通过 F23354
export const apiRejectLtzDs = async (data: any) => {
  return requestClient.post('/zjtSlxxb/rejectLtzDs', {
    ...data,
  });
};

// 区县审核通过 F23309
export const apiApproveQx = async (data: any) => {
  return requestClient.post('/zjtSlxxb/approveQx', {
    ...data,
  });
};

// 区县审核不通过 F23309
export const apiRejectQx = async (data: any) => {
  return requestClient.post('/zjtSlxxb/rejectQx', {
    ...data,
  });
};

// 区县签发 F23310
export const apiIssueQx = async (data: any) => {
  return requestClient.post('/zjtSlxxb/issueQx', {
    ...data,
  });
};

// 地市审核通过 F23311
export const apiApproveDs = async (data: any) => {
  return requestClient.post('/zjtSlxxb/approveDs', {
    ...data,
  });
};

// 地市审核不通过 F23311
export const apiRejectDs = async (data: any) => {
  return requestClient.post('/zjtSlxxb/rejectDs', {
    ...data,
  });
};

// 分局绿通证每日办理剩余量查询 F23356
export const apiQueryLtzfjsyl = async () => {
  return requestClient.get('/zjtSlxxb/queryLtzfjsyl');
};

// 绿通证申请 F23352
export const apiZjtSlxxbApplyLtz = async (data: any) => {
  return requestClient.post('/zjtSlxxb/applyLtz', {
    ...data,
  });
};

// 查询未审核绿通证数量
export const apiZjtSlxxbCountWshLtz = async (params: any) => {
  return requestClient.get('/zjtSlxxb/countWshLtz', {
    params,
  });
};
