import { requestClient } from '../request';

// 一体化受理材料
export const apiGaythtYthSlclPage = async (params: any) => {
  return requestClient.get('/gaythtYthSlcl/page', {
    params,
  });
};

// 一体化审批材料数据
export const apiGaythtYthSlclsjPage = async (params: any) => {
  return requestClient.get('/gaythtYthSlclsj/page', {
    params,
  });
};

// 列表
export const apiGaythtYthSlclList = async (params: any) => {
  return requestClient.get('/gaythtYthSlcl/list', {
    params,
  });
};

// 一键提取
export const apiSaveYwslClysjAll = async (data: any) => {
  return requestClient.post('/gaythtYthSlcl/saveYwslClysjAll', {
    ...data,
  });
};

// 批量提取 F10102
export const apiSaveYwslClysjBatch = async (data: any) => {
  return requestClient.post('/gaythtYthSlclsj/saveYwslClysjBatch', {
    ...data,
  });
};
