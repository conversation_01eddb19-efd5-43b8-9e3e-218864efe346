import type { pageResp } from '.';

import { requestClient } from '../request';

export interface fwtDmGgResp {
  dmzm: string;
  dmlx: string;
  dmmc: string;
  dmpy: string;
  dmsx: string;
  yxbz: string;
  xgsj: string;
  dmlxmc: string;
  kzbzb: string;
  kzbzc: string;
  kzbzd: string;
  kzbze: string;
  kzbzf: string;
  kzbzg: string;
  [key: string]: any;
}

export const apiFwtDmGgListByDmlx = async (params?: {
  dmlx: string;
}): Promise<pageResp<fwtDmGgResp>> => {
  return requestClient.get('/fwtDmGg/listByDmlx', {
    params,
  });
};

export const apiFwtDmGgYwbmTree = async (params?: {
  dmlx: string;
}): Promise<pageResp<fwtDmGgResp>> => {
  return requestClient.get('/fwtDmGg/ywbmTree', {
    params,
  });
};
