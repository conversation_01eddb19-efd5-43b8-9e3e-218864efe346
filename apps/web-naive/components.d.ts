/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACarousel: typeof import('ant-design-vue/es')['Carousel']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    AImage: typeof import('ant-design-vue/es')['Image']
    AImagePreviewGroup: typeof import('ant-design-vue/es')['ImagePreviewGroup']
    AInput: typeof import('ant-design-vue/es')['Input']
    AModal: typeof import('ant-design-vue/es')['Modal']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASegmented: typeof import('ant-design-vue/es')['Segmented']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    Camera: typeof import('./src/components/camera/index.vue')['default']
    CollectTab: typeof import('./src/components/spls/collectTab.vue')['default']
    copy: typeof import('./src/components/ksywYwjbxxb/qyxxDetailTab copy.vue')['default']
    CsxxTab: typeof import('./src/components/ksxtYwjbxxb/csxxTab.vue')['default']
    DetailTab: typeof import('./src/components/spls/detailTab.vue')['default']
    FilePreviewModal: typeof import('./src/components/zjtSlxxb/filePreviewModal.vue')['default']
    HjlzmsqTab: typeof import('./src/components/ksxtYwjbxxb/hjlzmsqTab.vue')['default']
    JbxxDetailTab: typeof import('./src/components/ksywYwjbxxb/jbxxDetailTab.vue')['default']
    JbxxDetalTab: typeof import('./src/components/ksywYwjbxxb/jbxxDetailTab.vue')['default']
    KsywYwjbxxbDetal: typeof import('./src/components/ksywYwjbxxb/ksywYwjbxxbDetal.vue')['default']
    LogTab: typeof import('./src/components/spls/logTab.vue')['default']
    LtzsqDetailTab: typeof import('./src/components/zjtSlxxb/ltzsqDetailTab.vue')['default']
    MaterialImageGrid: typeof import('./src/components/spls/materialImageGrid.vue')['default']
    MaterialTab: typeof import('./src/components/spls/materialTab.vue')['default']
    MaterialView: typeof import('./src/components/spls/materialView.vue')['default']
    MemberTab: typeof import('./src/components/spls/memberTab.vue')['default']
    MoveoutTab: typeof import('./src/components/spls/moveoutTab.vue')['default']
    NetworkImage: typeof import('./src/components/spls/networkImage.vue')['default']
    PrintTab: typeof import('./src/components/spls/printTab.vue')['default']
    QcsqTab: typeof import('./src/components/ksxtYwjbxxb/qcsqTab.vue')['default']
    QyxxDetailTab: typeof import('./src/components/ksywYwjbxxb/qyxxDetailTab.vue')['default']
    Qyz: typeof import('./src/components/ksywYwjbxxb/qyz.vue')['default']
    RkxxDetailTab: typeof import('./src/components/ksywYwjbxxb/rkxxDetailTab.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RzxxDetail: typeof import('./src/components/ksxtYwjbxxb/rzxxDetailTab.vue')['default']
    RzxxDetailTab: typeof import('./src/components/ksywYwjbxxb/rzxxDetailTab.vue')['default']
    SlxxDetailTab: typeof import('./src/components/zjtSlxxb/slxxDetailTab.vue')['default']
    SqxxDetailTab: typeof import('./src/components/zjtSlxxb/sqxxDetailTab.vue')['default']
    XtfkxxDetailTab: typeof import('./src/components/ksxtYwjbxxb/xtfkxxDetailTab.vue')['default']
    XtfzxxDetailTab: typeof import('./src/components/ksxtYwjbxxb/xtfzxxDetailTab.vue')['default']
    YjtqhlwclDrawer: typeof import('./src/components/spls/yjtqhlwclDrawer.vue')['default']
    ZqyxxDetailTab: typeof import('./src/components/ksywYwjbxxb/zqyxxDetailTab.vue')['default']
  }
}
