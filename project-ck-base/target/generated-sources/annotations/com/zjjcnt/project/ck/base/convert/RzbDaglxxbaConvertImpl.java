package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RzbDaglxxbaDTO;
import com.zjjcnt.project.ck.base.dto.req.RzbDaglxxbaCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RzbDaglxxbaPageReq;
import com.zjjcnt.project.ck.base.dto.req.RzbDaglxxbaUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RzbDaglxxbaCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RzbDaglxxbaPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RzbDaglxxbaViewResp;
import com.zjjcnt.project.ck.base.entity.RzbDaglxxbaDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RzbDaglxxbaConvertImpl implements RzbDaglxxbaConvert {

    @Override
    public RzbDaglxxbaDTO convert(RzbDaglxxbaDO entity) {
        if ( entity == null ) {
            return null;
        }

        RzbDaglxxbaDTO rzbDaglxxbaDTO = new RzbDaglxxbaDTO();

        rzbDaglxxbaDTO.setId( entity.getId() );
        rzbDaglxxbaDTO.setYwslh( entity.getYwslh() );
        rzbDaglxxbaDTO.setDzwjh( entity.getDzwjh() );
        rzbDaglxxbaDTO.setCjsj( entity.getCjsj() );
        rzbDaglxxbaDTO.setFhjg( entity.getFhjg() );
        rzbDaglxxbaDTO.setFhms( entity.getFhms() );
        rzbDaglxxbaDTO.setFhdata( entity.getFhdata() );
        rzbDaglxxbaDTO.setBz( entity.getBz() );
        rzbDaglxxbaDTO.setKssj( entity.getKssj() );
        rzbDaglxxbaDTO.setFssj( entity.getFssj() );
        rzbDaglxxbaDTO.setFhsj( entity.getFhsj() );

        return rzbDaglxxbaDTO;
    }

    @Override
    public RzbDaglxxbaDO convertToDO(RzbDaglxxbaDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzbDaglxxbaDO rzbDaglxxbaDO = new RzbDaglxxbaDO();

        rzbDaglxxbaDO.setId( dto.getId() );
        rzbDaglxxbaDO.setYwslh( dto.getYwslh() );
        rzbDaglxxbaDO.setDzwjh( dto.getDzwjh() );
        rzbDaglxxbaDO.setCjsj( dto.getCjsj() );
        rzbDaglxxbaDO.setFhjg( dto.getFhjg() );
        rzbDaglxxbaDO.setFhms( dto.getFhms() );
        rzbDaglxxbaDO.setFhdata( dto.getFhdata() );
        rzbDaglxxbaDO.setBz( dto.getBz() );
        rzbDaglxxbaDO.setKssj( dto.getKssj() );
        rzbDaglxxbaDO.setFssj( dto.getFssj() );
        rzbDaglxxbaDO.setFhsj( dto.getFhsj() );

        return rzbDaglxxbaDO;
    }

    @Override
    public RzbDaglxxbaDTO convertToDTO(RzbDaglxxbaPageReq req) {
        if ( req == null ) {
            return null;
        }

        RzbDaglxxbaDTO rzbDaglxxbaDTO = new RzbDaglxxbaDTO();

        rzbDaglxxbaDTO.setYwslh( req.getYwslh() );
        rzbDaglxxbaDTO.setDzwjh( req.getDzwjh() );
        rzbDaglxxbaDTO.setCjsj( req.getCjsj() );
        rzbDaglxxbaDTO.setFhjg( req.getFhjg() );
        rzbDaglxxbaDTO.setFhms( req.getFhms() );
        rzbDaglxxbaDTO.setFhdata( req.getFhdata() );
        rzbDaglxxbaDTO.setBz( req.getBz() );
        rzbDaglxxbaDTO.setKssj( req.getKssj() );
        rzbDaglxxbaDTO.setFssj( req.getFssj() );
        rzbDaglxxbaDTO.setFhsj( req.getFhsj() );

        return rzbDaglxxbaDTO;
    }

    @Override
    public RzbDaglxxbaDTO convertToDTO(RzbDaglxxbaCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RzbDaglxxbaDTO rzbDaglxxbaDTO = new RzbDaglxxbaDTO();

        rzbDaglxxbaDTO.setYwslh( req.getYwslh() );
        rzbDaglxxbaDTO.setDzwjh( req.getDzwjh() );
        rzbDaglxxbaDTO.setCjsj( req.getCjsj() );
        rzbDaglxxbaDTO.setFhjg( req.getFhjg() );
        rzbDaglxxbaDTO.setFhms( req.getFhms() );
        rzbDaglxxbaDTO.setFhdata( req.getFhdata() );
        rzbDaglxxbaDTO.setBz( req.getBz() );
        rzbDaglxxbaDTO.setKssj( req.getKssj() );
        rzbDaglxxbaDTO.setFssj( req.getFssj() );
        rzbDaglxxbaDTO.setFhsj( req.getFhsj() );

        return rzbDaglxxbaDTO;
    }

    @Override
    public RzbDaglxxbaDTO convertToDTO(RzbDaglxxbaUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RzbDaglxxbaDTO rzbDaglxxbaDTO = new RzbDaglxxbaDTO();

        rzbDaglxxbaDTO.setId( req.getId() );
        rzbDaglxxbaDTO.setYwslh( req.getYwslh() );
        rzbDaglxxbaDTO.setDzwjh( req.getDzwjh() );
        rzbDaglxxbaDTO.setFhjg( req.getFhjg() );
        rzbDaglxxbaDTO.setFhms( req.getFhms() );
        rzbDaglxxbaDTO.setFhdata( req.getFhdata() );
        rzbDaglxxbaDTO.setBz( req.getBz() );
        rzbDaglxxbaDTO.setKssj( req.getKssj() );
        rzbDaglxxbaDTO.setFssj( req.getFssj() );
        rzbDaglxxbaDTO.setFhsj( req.getFhsj() );

        return rzbDaglxxbaDTO;
    }

    @Override
    public RzbDaglxxbaPageResp convertToPageResp(RzbDaglxxbaDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzbDaglxxbaPageResp rzbDaglxxbaPageResp = new RzbDaglxxbaPageResp();

        rzbDaglxxbaPageResp.setId( dto.getId() );
        rzbDaglxxbaPageResp.setYwslh( dto.getYwslh() );
        rzbDaglxxbaPageResp.setDzwjh( dto.getDzwjh() );
        rzbDaglxxbaPageResp.setFhjg( dto.getFhjg() );
        rzbDaglxxbaPageResp.setFhms( dto.getFhms() );
        rzbDaglxxbaPageResp.setFhdata( dto.getFhdata() );
        rzbDaglxxbaPageResp.setBz( dto.getBz() );
        rzbDaglxxbaPageResp.setKssj( dto.getKssj() );
        rzbDaglxxbaPageResp.setFssj( dto.getFssj() );
        rzbDaglxxbaPageResp.setFhsj( dto.getFhsj() );

        return rzbDaglxxbaPageResp;
    }

    @Override
    public RzbDaglxxbaViewResp convertToViewResp(RzbDaglxxbaDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzbDaglxxbaViewResp rzbDaglxxbaViewResp = new RzbDaglxxbaViewResp();

        rzbDaglxxbaViewResp.setId( dto.getId() );
        rzbDaglxxbaViewResp.setYwslh( dto.getYwslh() );
        rzbDaglxxbaViewResp.setDzwjh( dto.getDzwjh() );
        rzbDaglxxbaViewResp.setFhjg( dto.getFhjg() );
        rzbDaglxxbaViewResp.setFhms( dto.getFhms() );
        rzbDaglxxbaViewResp.setFhdata( dto.getFhdata() );
        rzbDaglxxbaViewResp.setBz( dto.getBz() );
        rzbDaglxxbaViewResp.setKssj( dto.getKssj() );
        rzbDaglxxbaViewResp.setFssj( dto.getFssj() );
        rzbDaglxxbaViewResp.setFhsj( dto.getFhsj() );

        return rzbDaglxxbaViewResp;
    }

    @Override
    public RzbDaglxxbaCreateResp convertToCreateResp(RzbDaglxxbaDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzbDaglxxbaCreateResp rzbDaglxxbaCreateResp = new RzbDaglxxbaCreateResp();

        rzbDaglxxbaCreateResp.setId( dto.getId() );
        rzbDaglxxbaCreateResp.setYwslh( dto.getYwslh() );
        rzbDaglxxbaCreateResp.setDzwjh( dto.getDzwjh() );
        rzbDaglxxbaCreateResp.setFhjg( dto.getFhjg() );
        rzbDaglxxbaCreateResp.setFhms( dto.getFhms() );
        rzbDaglxxbaCreateResp.setFhdata( dto.getFhdata() );
        rzbDaglxxbaCreateResp.setBz( dto.getBz() );
        rzbDaglxxbaCreateResp.setKssj( dto.getKssj() );
        rzbDaglxxbaCreateResp.setFssj( dto.getFssj() );
        rzbDaglxxbaCreateResp.setFhsj( dto.getFhsj() );

        return rzbDaglxxbaCreateResp;
    }
}
