package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.GaythtZwjhFsrwbDTO;
import com.zjjcnt.project.ck.base.entity.GaythtZwjhFsrwbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:45+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class GaythtZwjhFsrwbConvertImpl implements GaythtZwjhFsrwbConvert {

    @Override
    public GaythtZwjhFsrwbDTO convert(GaythtZwjhFsrwbDO entity) {
        if ( entity == null ) {
            return null;
        }

        GaythtZwjhFsrwbDTO gaythtZwjhFsrwbDTO = new GaythtZwjhFsrwbDTO();

        gaythtZwjhFsrwbDTO.setId( entity.getId() );
        gaythtZwjhFsrwbDTO.setProjectid( entity.getProjectid() );
        gaythtZwjhFsrwbDTO.setSjly( entity.getSjly() );
        gaythtZwjhFsrwbDTO.setDfsjls( entity.getDfsjls() );
        gaythtZwjhFsrwbDTO.setYfsjls( entity.getYfsjls() );
        gaythtZwjhFsrwbDTO.setFsbz( entity.getFsbz() );
        gaythtZwjhFsrwbDTO.setCjsj( entity.getCjsj() );
        gaythtZwjhFsrwbDTO.setFspxsj( entity.getFspxsj() );

        return gaythtZwjhFsrwbDTO;
    }

    @Override
    public GaythtZwjhFsrwbDO convertToDO(GaythtZwjhFsrwbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        GaythtZwjhFsrwbDO gaythtZwjhFsrwbDO = new GaythtZwjhFsrwbDO();

        gaythtZwjhFsrwbDO.setId( dto.getId() );
        gaythtZwjhFsrwbDO.setProjectid( dto.getProjectid() );
        gaythtZwjhFsrwbDO.setSjly( dto.getSjly() );
        gaythtZwjhFsrwbDO.setDfsjls( dto.getDfsjls() );
        gaythtZwjhFsrwbDO.setYfsjls( dto.getYfsjls() );
        gaythtZwjhFsrwbDO.setFsbz( dto.getFsbz() );
        gaythtZwjhFsrwbDO.setCjsj( dto.getCjsj() );
        gaythtZwjhFsrwbDO.setFspxsj( dto.getFspxsj() );

        return gaythtZwjhFsrwbDO;
    }
}
