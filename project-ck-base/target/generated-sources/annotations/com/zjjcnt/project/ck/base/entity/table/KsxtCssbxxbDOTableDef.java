package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class KsxtCssbxxbDOTableDef extends TableDef {

    /**
     * 跨省协同出生申报信息表DO

 <AUTHOR>
 @date 2025-08-05 13:59:15
 @see com.zjjcnt.project.ck.base.dto.KsxtCssbxxbDTO
     */
    public static final KsxtCssbxxbDOTableDef KSXT_CSSBXXB_DO = new KsxtCssbxxbDOTableDef();

    /**
     * 名
     */
    public final QueryColumn M = new QueryColumn(this, "m");

    /**
     * 姓
     */
    public final QueryColumn X = new QueryColumn(this, "x");

    /**
     * 姓名
     */
    public final QueryColumn XM = new QueryColumn(this, "xm");

    /**
     * 比对结果
     */
    public final QueryColumn BDJG = new QueryColumn(this, "bdjg");

    /**
     * 比对描述
     */
    public final QueryColumn BDMS = new QueryColumn(this, "bdms");

    /**
     * 比对时间
     */
    public final QueryColumn BDSJ = new QueryColumn(this, "bdsj");

    /**
     * 出生日期
     */
    public final QueryColumn CSRQ = new QueryColumn(this, "csrq");

    /**
     * 出生时间
     */
    public final QueryColumn CSSJ = new QueryColumn(this, "cssj");

    /**
     * 父亲_姓名
     */
    public final QueryColumn FQXM = new QueryColumn(this, "fqxm");

    /**
     * 户主_姓名
     */
    public final QueryColumn HZXM = new QueryColumn(this, "hzxm");

    /**
     * 母亲_姓名
     */
    public final QueryColumn MQXM = new QueryColumn(this, "mqxm");

    /**
     * 民族
     */
    public final QueryColumn MZDM = new QueryColumn(this, "mzdm");

    /**
     * 入库时间
     */
    public final QueryColumn RKSJ = new QueryColumn(this, "rksj");

    /**
     * 受理时间
     */
    public final QueryColumn SLSJ = new QueryColumn(this, "slsj");

    /**
     * 性别
     */
    public final QueryColumn XBDM = new QueryColumn(this, "xbdm");

    /**
     * 血型
     */
    public final QueryColumn XXDM = new QueryColumn(this, "xxdm");

    /**
     * 比对人ID
     */
    public final QueryColumn BDRID = new QueryColumn(this, "bdrid");

    /**
     * 比对人IP
     */
    public final QueryColumn BDRIP = new QueryColumn(this, "bdrip");

    /**
     * 比对人姓名
     */
    public final QueryColumn BDRXM = new QueryColumn(this, "bdrxm");

    /**
     * 申报人_姓名
     */
    public final QueryColumn SBRXM = new QueryColumn(this, "sbrxm");

    /**
     * 受理人ID
     */
    public final QueryColumn SLRID = new QueryColumn(this, "slrid");

    /**
     * 受理人_姓名
     */
    public final QueryColumn SLRXM = new QueryColumn(this, "slrxm");

    /**
     * 出生证明编号
     */
    public final QueryColumn CSZMBH = new QueryColumn(this, "cszmbh");

    /**
     * 父亲_证件号码
     */
    public final QueryColumn FQZJHM = new QueryColumn(this, "fqzjhm");

    /**
     * 监护人二_姓名
     */
    public final QueryColumn JHREXM = new QueryColumn(this, "jhrexm");

    /**
     * 监护人一_姓名
     */
    public final QueryColumn JHRYXM = new QueryColumn(this, "jhryxm");

    /**
     * 跨省业务ID
     */
    public final QueryColumn KSXTID = new QueryColumn(this, "ksxtid");

    /**
     * 母亲_证件号码
     */
    public final QueryColumn MQZJHM = new QueryColumn(this, "mqzjhm");

    /**
     * 业务协同区域范围代码
     */
    public final QueryColumn QYFWDM = new QueryColumn(this, "qyfwdm");

    /**
     * 归档配置版本号
     */
    public final QueryColumn GDPZBBH = new QueryColumn(this, "gdpzbbh");

    /**
     * 监护人二_外文名
     */
    public final QueryColumn JHREWWM = new QueryColumn(this, "jhrewwm");

    /**
     * 监护人二_外文姓
     */
    public final QueryColumn JHREWWX = new QueryColumn(this, "jhrewwx");

    /**
     * 监护人一_外文名
     */
    public final QueryColumn JHRYWWM = new QueryColumn(this, "jhrywwm");

    /**
     * 监护人一_外文姓
     */
    public final QueryColumn JHRYWWX = new QueryColumn(this, "jhrywwx");

    /**
     * 申报人_联系电话
     */
    public final QueryColumn SBRLXDH = new QueryColumn(this, "sbrlxdh");

    /**
     * 受理地_联系电话
     */
    public final QueryColumn SLDLXDH = new QueryColumn(this, "sldlxdh");

    /**
     * 与户主关系
     */
    public final QueryColumn YHZGXDM = new QueryColumn(this, "yhzgxdm");

    /**
     * 出生登记类别
     */
    public final QueryColumn CSDJLBDM = new QueryColumn(this, "csdjlbdm");

    /**
     * 父亲_证件种类
     */
    public final QueryColumn FQCYZJDM = new QueryColumn(this, "fqcyzjdm");

    /**
     * 父亲_公民身份号码
     */
    public final QueryColumn FQGMSFHM = new QueryColumn(this, "fqgmsfhm");

    /**
     * 户主_公民身份号码
     */
    public final QueryColumn HZGMSFHM = new QueryColumn(this, "hzgmsfhm");

    /**
     * 籍贯_省市县（区）
     */
    public final QueryColumn JGSSXQDM = new QueryColumn(this, "jgssxqdm");

    /**
     * 监护人二_联系电话
     */
    public final QueryColumn JHRELXDH = new QueryColumn(this, "jhrelxdh");

    /**
     * 监护人二_证件号码
     */
    public final QueryColumn JHREZJHM = new QueryColumn(this, "jhrezjhm");

    /**
     * 监护人一_联系电话
     */
    public final QueryColumn JHRYLXDH = new QueryColumn(this, "jhrylxdh");

    /**
     * 监护人一_证件号码
     */
    public final QueryColumn JHRYZJHM = new QueryColumn(this, "jhryzjhm");

    /**
     * 跨省出生业务ID
     */
    public final QueryColumn KSXTCSID = new QueryColumn(this, "ksxtcsid");

    /**
     * 母亲_证件种类
     */
    public final QueryColumn MQCYZJDM = new QueryColumn(this, "mqcyzjdm");

    /**
     * 母亲_公民身份号码
     */
    public final QueryColumn MQGMSFHM = new QueryColumn(this, "mqgmsfhm");

    /**
     * 判断标志是否落户
     */
    public final QueryColumn PDBZSFLH = new QueryColumn(this, "pdbzsflh");

    /**
     * 人口信息级别
     */
    public final QueryColumn RKXXJBDM = new QueryColumn(this, "rkxxjbdm");

    /**
     * 出生地_省市县（区）
     */
    public final QueryColumn CSDSSXQDM = new QueryColumn(this, "csdssxqdm");

    /**
     * 籍贯_国家（地区）
     */
    public final QueryColumn JGGJHDQDM = new QueryColumn(this, "jggjhdqdm");

    /**
     * 籍贯_区划内详细地址
     */
    public final QueryColumn JGQHNXXDZ = new QueryColumn(this, "jgqhnxxdz");

    /**
     * 申报人_公民身份号码
     */
    public final QueryColumn SBRGMSFHM = new QueryColumn(this, "sbrgmsfhm");

    /**
     * 受理地_公安机关名称
     */
    public final QueryColumn SLDGAJGMC = new QueryColumn(this, "sldgajgmc");

    /**
     * 现居住地址_省市县（区）
     */
    public final QueryColumn XZZSSXQDM = new QueryColumn(this, "xzzssxqdm");

    /**
     * 出生地_国家（地区）
     */
    public final QueryColumn CSDGJHDQDM = new QueryColumn(this, "csdgjhdqdm");

    /**
     * 出生地_区划内详细地址
     */
    public final QueryColumn CSDQHNXXDZ = new QueryColumn(this, "csdqhnxxdz");

    /**
     * 监护人二_证件种类
     */
    public final QueryColumn JHRECYZJDM = new QueryColumn(this, "jhrecyzjdm");

    /**
     * 监护人二_公民身份号码
     */
    public final QueryColumn JHREGMSFHM = new QueryColumn(this, "jhregmsfhm");

    /**
     * 监护人二_监护关系
     */
    public final QueryColumn JHREJHGXDM = new QueryColumn(this, "jhrejhgxdm");

    /**
     * 监护人一_证件种类
     */
    public final QueryColumn JHRYCYZJDM = new QueryColumn(this, "jhrycyzjdm");

    /**
     * 监护人一_公民身份号码
     */
    public final QueryColumn JHRYGMSFHM = new QueryColumn(this, "jhrygmsfhm");

    /**
     * 监护人一_监护关系
     */
    public final QueryColumn JHRYJHGXDM = new QueryColumn(this, "jhryjhgxdm");

    /**
     * 现居住地址_区划内详细地址
     */
    public final QueryColumn XZZQHNXXDZ = new QueryColumn(this, "xzzqhnxxdz");

    /**
     * 治安管理业务类别代码
     */
    public final QueryColumn ZAGLYWLBDM = new QueryColumn(this, "zaglywlbdm");

    /**
     * 治安管理业务协同编号
     */
    public final QueryColumn ZAGLYWXTBH = new QueryColumn(this, "zaglywxtbh");

    /**
     * 户籍地_数据归属单位代码
     */
    public final QueryColumn HJDSJGSDWDM = new QueryColumn(this, "hjdsjgsdwdm");

    /**
     * 户籍地_数据归属单位名称
     */
    public final QueryColumn HJDSJGSDWMC = new QueryColumn(this, "hjdsjgsdwmc");

    /**
     * 判断标志出生证明黑名单
     */
    public final QueryColumn PDBZCSZMHMD = new QueryColumn(this, "pdbzcszmhmd");

    /**
     * 受理地_公安机关机构代码
     */
    public final QueryColumn SLDGAJGJGDM = new QueryColumn(this, "sldgajgjgdm");

    /**
     * 受理地_数据归属单位代码
     */
    public final QueryColumn SLDSJGSDWDM = new QueryColumn(this, "sldsjgsdwdm");

    /**
     * 受理地_数据归属单位名称
     */
    public final QueryColumn SLDSJGSDWMC = new QueryColumn(this, "sldsjgsdwmc");

    /**
     * 治安管理政务服务事项编码
     */
    public final QueryColumn ZAGLZWFWSXBM = new QueryColumn(this, "zaglzwfwsxbm");

    /**
     * 申报人_家庭关系
     */
    public final QueryColumn SBRYCSRGXJTGXDM = new QueryColumn(this, "sbrycsrgxjtgxdm");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{M, X, XM, BDJG, BDMS, BDSJ, CSRQ, CSSJ, FQXM, HZXM, MQXM, MZDM, RKSJ, SLSJ, XBDM, XXDM, BDRID, BDRIP, BDRXM, SBRXM, SLRID, SLRXM, CSZMBH, FQZJHM, JHREXM, JHRYXM, KSXTID, MQZJHM, QYFWDM, GDPZBBH, JHREWWM, JHREWWX, JHRYWWM, JHRYWWX, SBRLXDH, SLDLXDH, YHZGXDM, CSDJLBDM, FQCYZJDM, FQGMSFHM, HZGMSFHM, JGSSXQDM, JHRELXDH, JHREZJHM, JHRYLXDH, JHRYZJHM, KSXTCSID, MQCYZJDM, MQGMSFHM, PDBZSFLH, RKXXJBDM, CSDSSXQDM, JGGJHDQDM, JGQHNXXDZ, SBRGMSFHM, SLDGAJGMC, XZZSSXQDM, CSDGJHDQDM, CSDQHNXXDZ, JHRECYZJDM, JHREGMSFHM, JHREJHGXDM, JHRYCYZJDM, JHRYGMSFHM, JHRYJHGXDM, XZZQHNXXDZ, ZAGLYWLBDM, ZAGLYWXTBH, HJDSJGSDWDM, HJDSJGSDWMC, PDBZCSZMHMD, SLDGAJGJGDM, SLDSJGSDWDM, SLDSJGSDWMC, ZAGLZWFWSXBM, SBRYCSRGXJTGXDM};

    public KsxtCssbxxbDOTableDef() {
        super("", "ksxt_cssbxxb");
    }

    private KsxtCssbxxbDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public KsxtCssbxxbDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new KsxtCssbxxbDOTableDef("", "ksxt_cssbxxb", alias));
    }

}
