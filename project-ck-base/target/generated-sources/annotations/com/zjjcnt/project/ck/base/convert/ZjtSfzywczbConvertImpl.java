package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtSfzywczbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtSfzywczbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSfzywczbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSfzywczbViewResp;
import com.zjjcnt.project.ck.base.entity.ZjtSfzywczbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:41+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtSfzywczbConvertImpl implements ZjtSfzywczbConvert {

    @Override
    public ZjtSfzywczbDTO convert(ZjtSfzywczbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtSfzywczbDTO zjtSfzywczbDTO = new ZjtSfzywczbDTO();

        zjtSfzywczbDTO.setId( entity.getId() );
        zjtSfzywczbDTO.setZjywid( entity.getZjywid() );
        zjtSfzywczbDTO.setYwslh( entity.getYwslh() );
        zjtSfzywczbDTO.setSlh( entity.getSlh() );
        zjtSfzywczbDTO.setYwbz( entity.getYwbz() );
        zjtSfzywczbDTO.setSlzt( entity.getSlzt() );
        zjtSfzywczbDTO.setCzyid( entity.getCzyid() );
        zjtSfzywczbDTO.setCzyxm( entity.getCzyxm() );
        zjtSfzywczbDTO.setCzsj( entity.getCzsj() );
        zjtSfzywczbDTO.setCzip( entity.getCzip() );
        zjtSfzywczbDTO.setCzydwdm( entity.getCzydwdm() );
        zjtSfzywczbDTO.setCzydwmc( entity.getCzydwmc() );
        zjtSfzywczbDTO.setBz( entity.getBz() );

        return zjtSfzywczbDTO;
    }

    @Override
    public ZjtSfzywczbDO convertToDO(ZjtSfzywczbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtSfzywczbDO zjtSfzywczbDO = new ZjtSfzywczbDO();

        zjtSfzywczbDO.setId( dto.getId() );
        zjtSfzywczbDO.setZjywid( dto.getZjywid() );
        zjtSfzywczbDO.setYwslh( dto.getYwslh() );
        zjtSfzywczbDO.setSlh( dto.getSlh() );
        zjtSfzywczbDO.setYwbz( dto.getYwbz() );
        zjtSfzywczbDO.setSlzt( dto.getSlzt() );
        zjtSfzywczbDO.setCzyid( dto.getCzyid() );
        zjtSfzywczbDO.setCzyxm( dto.getCzyxm() );
        zjtSfzywczbDO.setCzsj( dto.getCzsj() );
        zjtSfzywczbDO.setCzip( dto.getCzip() );
        zjtSfzywczbDO.setCzydwdm( dto.getCzydwdm() );
        zjtSfzywczbDO.setCzydwmc( dto.getCzydwmc() );
        zjtSfzywczbDO.setBz( dto.getBz() );

        return zjtSfzywczbDO;
    }

    @Override
    public ZjtSfzywczbDTO convertToDTO(ZjtSfzywczbPageReq req) {
        if ( req == null ) {
            return null;
        }

        ZjtSfzywczbDTO zjtSfzywczbDTO = new ZjtSfzywczbDTO();

        zjtSfzywczbDTO.setSlh( req.getSlh() );

        return zjtSfzywczbDTO;
    }

    @Override
    public ZjtSfzywczbPageResp convertToPageResp(ZjtSfzywczbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtSfzywczbPageResp zjtSfzywczbPageResp = new ZjtSfzywczbPageResp();

        zjtSfzywczbPageResp.setZjywid( dto.getZjywid() );
        zjtSfzywczbPageResp.setYwslh( dto.getYwslh() );
        zjtSfzywczbPageResp.setSlh( dto.getSlh() );
        zjtSfzywczbPageResp.setYwbz( dto.getYwbz() );
        zjtSfzywczbPageResp.setSlzt( dto.getSlzt() );
        zjtSfzywczbPageResp.setCzyid( dto.getCzyid() );
        zjtSfzywczbPageResp.setCzyxm( dto.getCzyxm() );
        zjtSfzywczbPageResp.setCzsj( dto.getCzsj() );
        zjtSfzywczbPageResp.setCzip( dto.getCzip() );
        zjtSfzywczbPageResp.setCzydwdm( dto.getCzydwdm() );
        zjtSfzywczbPageResp.setCzydwmc( dto.getCzydwmc() );
        zjtSfzywczbPageResp.setBz( dto.getBz() );

        return zjtSfzywczbPageResp;
    }

    @Override
    public ZjtSfzywczbViewResp convertToViewResp(ZjtSfzywczbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtSfzywczbViewResp zjtSfzywczbViewResp = new ZjtSfzywczbViewResp();

        zjtSfzywczbViewResp.setZjywid( dto.getZjywid() );
        zjtSfzywczbViewResp.setYwslh( dto.getYwslh() );
        zjtSfzywczbViewResp.setSlh( dto.getSlh() );
        zjtSfzywczbViewResp.setYwbz( dto.getYwbz() );
        zjtSfzywczbViewResp.setSlzt( dto.getSlzt() );
        zjtSfzywczbViewResp.setCzyid( dto.getCzyid() );
        zjtSfzywczbViewResp.setCzyxm( dto.getCzyxm() );
        zjtSfzywczbViewResp.setCzsj( dto.getCzsj() );
        zjtSfzywczbViewResp.setCzip( dto.getCzip() );
        zjtSfzywczbViewResp.setCzydwdm( dto.getCzydwdm() );
        zjtSfzywczbViewResp.setCzydwmc( dto.getCzydwmc() );
        zjtSfzywczbViewResp.setBz( dto.getBz() );

        return zjtSfzywczbViewResp;
    }
}
