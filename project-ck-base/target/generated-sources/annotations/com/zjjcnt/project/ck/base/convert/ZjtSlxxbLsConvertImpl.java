package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtSlxxbLsDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtSlxxbLsPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSlxxbLsPageResp;
import com.zjjcnt.project.ck.base.entity.ZjtSlxxbLsDO;
import java.util.Arrays;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:42+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtSlxxbLsConvertImpl implements ZjtSlxxbLsConvert {

    @Override
    public ZjtSlxxbLsDTO convert(ZjtSlxxbLsDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtSlxxbLsDTO zjtSlxxbLsDTO = new ZjtSlxxbLsDTO();

        zjtSlxxbLsDTO.setId( entity.getId() );
        zjtSlxxbLsDTO.setNbslid( entity.getNbslid() );
        zjtSlxxbLsDTO.setYwslh( entity.getYwslh() );
        zjtSlxxbLsDTO.setNbsfzid( entity.getNbsfzid() );
        zjtSlxxbLsDTO.setZpid( entity.getZpid() );
        zjtSlxxbLsDTO.setSlh( entity.getSlh() );
        zjtSlxxbLsDTO.setRyid( entity.getRyid() );
        zjtSlxxbLsDTO.setRynbid( entity.getRynbid() );
        zjtSlxxbLsDTO.setQfjg( entity.getQfjg() );
        zjtSlxxbLsDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        zjtSlxxbLsDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        zjtSlxxbLsDTO.setZz( entity.getZz() );
        zjtSlxxbLsDTO.setHjdzqhnxxdz( entity.getHjdzqhnxxdz() );
        zjtSlxxbLsDTO.setSlyy( entity.getSlyy() );
        zjtSlxxbLsDTO.setZzlx( entity.getZzlx() );
        zjtSlxxbLsDTO.setLqfs( entity.getLqfs() );
        zjtSlxxbLsDTO.setSflx( entity.getSflx() );
        zjtSlxxbLsDTO.setSfje( entity.getSfje() );
        zjtSlxxbLsDTO.setSjblsh( entity.getSjblsh() );
        zjtSlxxbLsDTO.setSlzt( entity.getSlzt() );
        zjtSlxxbLsDTO.setTbbz( entity.getTbbz() );
        zjtSlxxbLsDTO.setQyfwdm( entity.getQyfwdm() );
        zjtSlxxbLsDTO.setSyzbz( entity.getSyzbz() );
        zjtSlxxbLsDTO.setDzzdbz( entity.getDzzdbz() );
        zjtSlxxbLsDTO.setGmsfhm( entity.getGmsfhm() );
        zjtSlxxbLsDTO.setXm( entity.getXm() );
        zjtSlxxbLsDTO.setXb( entity.getXb() );
        zjtSlxxbLsDTO.setMz( entity.getMz() );
        zjtSlxxbLsDTO.setMzfjxdm( entity.getMzfjxdm() );
        zjtSlxxbLsDTO.setCsrq( entity.getCsrq() );
        zjtSlxxbLsDTO.setCsdssxq( entity.getCsdssxq() );
        zjtSlxxbLsDTO.setMlpnbid( entity.getMlpnbid() );
        zjtSlxxbLsDTO.setSsxq( entity.getSsxq() );
        zjtSlxxbLsDTO.setJlx( entity.getJlx() );
        zjtSlxxbLsDTO.setMlph( entity.getMlph() );
        zjtSlxxbLsDTO.setMlxz( entity.getMlxz() );
        zjtSlxxbLsDTO.setPcs( entity.getPcs() );
        zjtSlxxbLsDTO.setZrq( entity.getZrq() );
        zjtSlxxbLsDTO.setXzjd( entity.getXzjd() );
        zjtSlxxbLsDTO.setJcwh( entity.getJcwh() );
        zjtSlxxbLsDTO.setPxh( entity.getPxh() );
        zjtSlxxbLsDTO.setYwbz( entity.getYwbz() );
        zjtSlxxbLsDTO.setCzyid( entity.getCzyid() );
        zjtSlxxbLsDTO.setCzsj( entity.getCzsj() );
        zjtSlxxbLsDTO.setDwdm( entity.getDwdm() );
        zjtSlxxbLsDTO.setSjrxm( entity.getSjrxm() );
        zjtSlxxbLsDTO.setSjrlxdh( entity.getSjrlxdh() );
        zjtSlxxbLsDTO.setSjryb( entity.getSjryb() );
        zjtSlxxbLsDTO.setSjrssxq( entity.getSjrssxq() );
        zjtSlxxbLsDTO.setSjrxz( entity.getSjrxz() );
        zjtSlxxbLsDTO.setSjrtxdz( entity.getSjrtxdz() );
        zjtSlxxbLsDTO.setZzxxcwlb( entity.getZzxxcwlb() );
        zjtSlxxbLsDTO.setCwms( entity.getCwms() );
        zjtSlxxbLsDTO.setJydw( entity.getJydw() );
        zjtSlxxbLsDTO.setJyrxm( entity.getJyrxm() );
        zjtSlxxbLsDTO.setJyrq( entity.getJyrq() );
        zjtSlxxbLsDTO.setCldw( entity.getCldw() );
        zjtSlxxbLsDTO.setClqk( entity.getClqk() );
        zjtSlxxbLsDTO.setClrq( entity.getClrq() );
        zjtSlxxbLsDTO.setZlhkzt( entity.getZlhkzt() );
        zjtSlxxbLsDTO.setHksj( entity.getHksj() );
        zjtSlxxbLsDTO.setBwbha( entity.getBwbha() );
        zjtSlxxbLsDTO.setBwbhb( entity.getBwbhb() );
        zjtSlxxbLsDTO.setShrq( entity.getShrq() );
        zjtSlxxbLsDTO.setStjssj( entity.getStjssj() );
        zjtSlxxbLsDTO.setBwbhc( entity.getBwbhc() );
        zjtSlxxbLsDTO.setFjpch( entity.getFjpch() );
        zjtSlxxbLsDTO.setRlbdid( entity.getRlbdid() );
        zjtSlxxbLsDTO.setRlbdbz( entity.getRlbdbz() );
        zjtSlxxbLsDTO.setRlbdsj( entity.getRlbdsj() );
        zjtSlxxbLsDTO.setZwyzw( entity.getZwyzw() );
        zjtSlxxbLsDTO.setZwyzcjg( entity.getZwyzcjg() );
        zjtSlxxbLsDTO.setZwezw( entity.getZwezw() );
        zjtSlxxbLsDTO.setZwezcjg( entity.getZwezcjg() );
        zjtSlxxbLsDTO.setZwcjjgdm( entity.getZwcjjgdm() );
        zjtSlxxbLsDTO.setSzyczkdm( entity.getSzyczkdm() );
        zjtSlxxbLsDTO.setSfzwzj( entity.getSfzwzj() );
        zjtSlxxbLsDTO.setDztbbz( entity.getDztbbz() );
        zjtSlxxbLsDTO.setDztbsj( entity.getDztbsj() );
        zjtSlxxbLsDTO.setDzsjbbh( entity.getDzsjbbh() );
        zjtSlxxbLsDTO.setSlfs( entity.getSlfs() );
        zjtSlxxbLsDTO.setSlfsx( entity.getSlfsx() );
        zjtSlxxbLsDTO.setZwtxid( entity.getZwtxid() );
        zjtSlxxbLsDTO.setCzyxm( entity.getCzyxm() );
        zjtSlxxbLsDTO.setHjddzbm( entity.getHjddzbm() );
        zjtSlxxbLsDTO.setLssfzslbz( entity.getLssfzslbz() );
        zjtSlxxbLsDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        zjtSlxxbLsDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        zjtSlxxbLsDTO.setCzydwmc( entity.getCzydwmc() );
        zjtSlxxbLsDTO.setZjzdssxq( entity.getZjzdssxq() );
        zjtSlxxbLsDTO.setZjzdxz( entity.getZjzdxz() );
        zjtSlxxbLsDTO.setLqrq( entity.getLqrq() );
        zjtSlxxbLsDTO.setLqrxm( entity.getLqrxm() );
        zjtSlxxbLsDTO.setLqrsfhm( entity.getLqrsfhm() );
        zjtSlxxbLsDTO.setLqrzpid( entity.getLqrzpid() );
        zjtSlxxbLsDTO.setZjddrq( entity.getZjddrq() );
        zjtSlxxbLsDTO.setLzczrid( entity.getLzczrid() );
        zjtSlxxbLsDTO.setLzczrxm( entity.getLzczrxm() );
        zjtSlxxbLsDTO.setLzczrdwdm( entity.getLzczrdwdm() );
        zjtSlxxbLsDTO.setLzczrdwmc( entity.getLzczrdwmc() );
        zjtSlxxbLsDTO.setShrxm( entity.getShrxm() );
        zjtSlxxbLsDTO.setShdw( entity.getShdw() );
        zjtSlxxbLsDTO.setShqk( entity.getShqk() );
        zjtSlxxbLsDTO.setQfrq( entity.getQfrq() );
        zjtSlxxbLsDTO.setQfrid( entity.getQfrid() );
        zjtSlxxbLsDTO.setQfrxm( entity.getQfrxm() );
        zjtSlxxbLsDTO.setQfdwjgdm( entity.getQfdwjgdm() );
        zjtSlxxbLsDTO.setQfdw( entity.getQfdw() );
        zjtSlxxbLsDTO.setDsshrq( entity.getDsshrq() );
        zjtSlxxbLsDTO.setDsshrxm( entity.getDsshrxm() );
        zjtSlxxbLsDTO.setDsshdw( entity.getDsshdw() );
        zjtSlxxbLsDTO.setDsshqk( entity.getDsshqk() );
        zjtSlxxbLsDTO.setRwid( entity.getRwid() );
        zjtSlxxbLsDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        zjtSlxxbLsDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        zjtSlxxbLsDTO.setSldfjsjgsdwdm( entity.getSldfjsjgsdwdm() );
        zjtSlxxbLsDTO.setSldfjsjgsdwmc( entity.getSldfjsjgsdwmc() );
        zjtSlxxbLsDTO.setShdwdm( entity.getShdwdm() );
        zjtSlxxbLsDTO.setCldwdm( entity.getCldwdm() );
        zjtSlxxbLsDTO.setDsshdwdm( entity.getDsshdwdm() );
        zjtSlxxbLsDTO.setShrid( entity.getShrid() );
        zjtSlxxbLsDTO.setDsshrid( entity.getDsshrid() );
        zjtSlxxbLsDTO.setSfdjh( entity.getSfdjh() );
        zjtSlxxbLsDTO.setRwzxrzbh( entity.getRwzxrzbh() );
        zjtSlxxbLsDTO.setRwddsj( entity.getRwddsj() );
        zjtSlxxbLsDTO.setSlyckrxsfbd( entity.getSlyckrxsfbd() );
        zjtSlxxbLsDTO.setRxbdkssj( entity.getRxbdkssj() );
        zjtSlxxbLsDTO.setRxbdhs( entity.getRxbdhs() );
        zjtSlxxbLsDTO.setRxbdxsd( entity.getRxbdxsd() );
        zjtSlxxbLsDTO.setRxbdkbh( entity.getRxbdkbh() );
        zjtSlxxbLsDTO.setRxbdjg( entity.getRxbdjg() );
        zjtSlxxbLsDTO.setSlylszwsfbd( entity.getSlylszwsfbd() );
        zjtSlxxbLsDTO.setZwybdjg( entity.getZwybdjg() );
        zjtSlxxbLsDTO.setZwybdxsd( entity.getZwybdxsd() );
        zjtSlxxbLsDTO.setZwebdjg( entity.getZwebdjg() );
        zjtSlxxbLsDTO.setZwebdxsd( entity.getZwebdxsd() );
        zjtSlxxbLsDTO.setLzszwsfhy( entity.getLzszwsfhy() );
        zjtSlxxbLsDTO.setLzszwyhyjg( entity.getLzszwyhyjg() );
        zjtSlxxbLsDTO.setLzszwyhyxsd( entity.getLzszwyhyxsd() );
        zjtSlxxbLsDTO.setLzszwehyjg( entity.getLzszwehyjg() );
        zjtSlxxbLsDTO.setLzszwehyxsd( entity.getLzszwehyxsd() );
        zjtSlxxbLsDTO.setLzssfjhjz( entity.getLzssfjhjz() );
        zjtSlxxbLsDTO.setSlylszwbdsm( entity.getSlylszwbdsm() );
        zjtSlxxbLsDTO.setLzszwbdsm( entity.getLzszwbdsm() );
        zjtSlxxbLsDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        zjtSlxxbLsDTO.setSqrxm( entity.getSqrxm() );
        zjtSlxxbLsDTO.setSqrlxdh( entity.getSqrlxdh() );
        zjtSlxxbLsDTO.setJzqsrq( entity.getJzqsrq() );
        zjtSlxxbLsDTO.setCzylxdh( entity.getCzylxdh() );
        zjtSlxxbLsDTO.setShrlxdh( entity.getShrlxdh() );
        zjtSlxxbLsDTO.setDsshrlxdh( entity.getDsshrlxdh() );
        zjtSlxxbLsDTO.setZfcje( entity.getZfcje() );
        zjtSlxxbLsDTO.setQxfcje( entity.getQxfcje() );
        zjtSlxxbLsDTO.setDsfcje( entity.getDsfcje() );
        zjtSlxxbLsDTO.setZxfcje( entity.getZxfcje() );
        zjtSlxxbLsDTO.setYzkddh( entity.getYzkddh() );
        zjtSlxxbLsDTO.setSpdz1( entity.getSpdz1() );
        zjtSlxxbLsDTO.setSfmsbswzp( entity.getSfmsbswzp() );
        zjtSlxxbLsDTO.setHlwsqid( entity.getHlwsqid() );
        zjtSlxxbLsDTO.setUsername( entity.getUsername() );
        zjtSlxxbLsDTO.setPjjg( entity.getPjjg() );
        zjtSlxxbLsDTO.setPjpljc( entity.getPjpljc() );
        zjtSlxxbLsDTO.setPjsj( entity.getPjsj() );
        zjtSlxxbLsDTO.setFwdx( entity.getFwdx() );
        zjtSlxxbLsDTO.setSfdgszj( entity.getSfdgszj() );
        zjtSlxxbLsDTO.setBz( entity.getBz() );
        zjtSlxxbLsDTO.setSlshjdz( entity.getSlshjdz() );
        zjtSlxxbLsDTO.setKsywlsh( entity.getKsywlsh() );
        zjtSlxxbLsDTO.setKsfsbz( entity.getKsfsbz() );
        zjtSlxxbLsDTO.setKsfssj( entity.getKsfssj() );
        byte[] base64zp = entity.getBase64zp();
        if ( base64zp != null ) {
            zjtSlxxbLsDTO.setBase64zp( Arrays.copyOf( base64zp, base64zp.length ) );
        }
        zjtSlxxbLsDTO.setZplsid( entity.getZplsid() );
        zjtSlxxbLsDTO.setZpcjlx( entity.getZpcjlx() );
        zjtSlxxbLsDTO.setZpsbbsh( entity.getZpsbbsh() );
        zjtSlxxbLsDTO.setZpsbppxhdm( entity.getZpsbppxhdm() );
        zjtSlxxbLsDTO.setZpsbppxh( entity.getZpsbppxh() );
        zjtSlxxbLsDTO.setZpytbid( entity.getZpytbid() );
        zjtSlxxbLsDTO.setSfbczpyt( entity.getSfbczpyt() );
        zjtSlxxbLsDTO.setLxcjsj( entity.getLxcjsj() );
        zjtSlxxbLsDTO.setLxczymc( entity.getLxczymc() );
        zjtSlxxbLsDTO.setLxsldw( entity.getLxsldw() );
        zjtSlxxbLsDTO.setZpsbbh( entity.getZpsbbh() );
        zjtSlxxbLsDTO.setZpsbzcdw( entity.getZpsbzcdw() );
        zjtSlxxbLsDTO.setZpsbscgs( entity.getZpsbscgs() );
        zjtSlxxbLsDTO.setZpsbxsgs( entity.getZpsbxsgs() );
        zjtSlxxbLsDTO.setBzsbbh( entity.getBzsbbh() );
        zjtSlxxbLsDTO.setBzsbzcdw( entity.getBzsbzcdw() );
        zjtSlxxbLsDTO.setBzsbppxhdm( entity.getBzsbppxhdm() );
        zjtSlxxbLsDTO.setBzsbppxh( entity.getBzsbppxh() );
        zjtSlxxbLsDTO.setBzsbscgs( entity.getBzsbscgs() );
        zjtSlxxbLsDTO.setBzsbxsgs( entity.getBzsbxsgs() );
        zjtSlxxbLsDTO.setJjlxrxm( entity.getJjlxrxm() );
        zjtSlxxbLsDTO.setJjlxrdh( entity.getJjlxrdh() );
        zjtSlxxbLsDTO.setJjlxryslrgx( entity.getJjlxryslrgx() );
        zjtSlxxbLsDTO.setZaglywxtbh( entity.getZaglywxtbh() );
        zjtSlxxbLsDTO.setZaglzwfwsxbm( entity.getZaglzwfwsxbm() );
        zjtSlxxbLsDTO.setZaglywlbdm( entity.getZaglywlbdm() );
        zjtSlxxbLsDTO.setXmmzwz( entity.getXmmzwz() );
        zjtSlxxbLsDTO.setXbmzwz( entity.getXbmzwz() );
        zjtSlxxbLsDTO.setMzmzwz( entity.getMzmzwz() );
        zjtSlxxbLsDTO.setMzfjxmzwz( entity.getMzfjxmzwz() );
        zjtSlxxbLsDTO.setZzhymzwz( entity.getZzhymzwz() );
        zjtSlxxbLsDTO.setZzhemzwz( entity.getZzhemzwz() );
        zjtSlxxbLsDTO.setZzhsmzwz( entity.getZzhsmzwz() );
        zjtSlxxbLsDTO.setQfjgmzwz( entity.getQfjgmzwz() );
        zjtSlxxbLsDTO.setCzyip( entity.getCzyip() );
        zjtSlxxbLsDTO.setZwcjqid( entity.getZwcjqid() );
        zjtSlxxbLsDTO.setSfsqltz( entity.getSfsqltz() );
        zjtSlxxbLsDTO.setLtzshjg( entity.getLtzshjg() );
        zjtSlxxbLsDTO.setLtzspsj( entity.getLtzspsj() );
        zjtSlxxbLsDTO.setLtzsprid( entity.getLtzsprid() );
        zjtSlxxbLsDTO.setLtzsprxm( entity.getLtzsprxm() );
        zjtSlxxbLsDTO.setLtzsqsy( entity.getLtzsqsy() );

        return zjtSlxxbLsDTO;
    }

    @Override
    public ZjtSlxxbLsDO convertToDO(ZjtSlxxbLsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtSlxxbLsDO zjtSlxxbLsDO = new ZjtSlxxbLsDO();

        zjtSlxxbLsDO.setId( dto.getId() );
        zjtSlxxbLsDO.setNbslid( dto.getNbslid() );
        zjtSlxxbLsDO.setYwslh( dto.getYwslh() );
        zjtSlxxbLsDO.setNbsfzid( dto.getNbsfzid() );
        zjtSlxxbLsDO.setZpid( dto.getZpid() );
        zjtSlxxbLsDO.setSlh( dto.getSlh() );
        zjtSlxxbLsDO.setRyid( dto.getRyid() );
        zjtSlxxbLsDO.setRynbid( dto.getRynbid() );
        zjtSlxxbLsDO.setQfjg( dto.getQfjg() );
        zjtSlxxbLsDO.setYxqxqsrq( dto.getYxqxqsrq() );
        zjtSlxxbLsDO.setYxqxjzrq( dto.getYxqxjzrq() );
        zjtSlxxbLsDO.setZz( dto.getZz() );
        zjtSlxxbLsDO.setHjdzqhnxxdz( dto.getHjdzqhnxxdz() );
        zjtSlxxbLsDO.setSlyy( dto.getSlyy() );
        zjtSlxxbLsDO.setZzlx( dto.getZzlx() );
        zjtSlxxbLsDO.setLqfs( dto.getLqfs() );
        zjtSlxxbLsDO.setSflx( dto.getSflx() );
        zjtSlxxbLsDO.setSfje( dto.getSfje() );
        zjtSlxxbLsDO.setSjblsh( dto.getSjblsh() );
        zjtSlxxbLsDO.setSlzt( dto.getSlzt() );
        zjtSlxxbLsDO.setTbbz( dto.getTbbz() );
        zjtSlxxbLsDO.setQyfwdm( dto.getQyfwdm() );
        zjtSlxxbLsDO.setSyzbz( dto.getSyzbz() );
        zjtSlxxbLsDO.setDzzdbz( dto.getDzzdbz() );
        zjtSlxxbLsDO.setGmsfhm( dto.getGmsfhm() );
        zjtSlxxbLsDO.setXm( dto.getXm() );
        zjtSlxxbLsDO.setXb( dto.getXb() );
        zjtSlxxbLsDO.setMz( dto.getMz() );
        zjtSlxxbLsDO.setMzfjxdm( dto.getMzfjxdm() );
        zjtSlxxbLsDO.setCsrq( dto.getCsrq() );
        zjtSlxxbLsDO.setCsdssxq( dto.getCsdssxq() );
        zjtSlxxbLsDO.setMlpnbid( dto.getMlpnbid() );
        zjtSlxxbLsDO.setSsxq( dto.getSsxq() );
        zjtSlxxbLsDO.setJlx( dto.getJlx() );
        zjtSlxxbLsDO.setMlph( dto.getMlph() );
        zjtSlxxbLsDO.setMlxz( dto.getMlxz() );
        zjtSlxxbLsDO.setPcs( dto.getPcs() );
        zjtSlxxbLsDO.setZrq( dto.getZrq() );
        zjtSlxxbLsDO.setXzjd( dto.getXzjd() );
        zjtSlxxbLsDO.setJcwh( dto.getJcwh() );
        zjtSlxxbLsDO.setPxh( dto.getPxh() );
        zjtSlxxbLsDO.setYwbz( dto.getYwbz() );
        zjtSlxxbLsDO.setCzyid( dto.getCzyid() );
        zjtSlxxbLsDO.setCzsj( dto.getCzsj() );
        zjtSlxxbLsDO.setDwdm( dto.getDwdm() );
        zjtSlxxbLsDO.setSjrxm( dto.getSjrxm() );
        zjtSlxxbLsDO.setSjrlxdh( dto.getSjrlxdh() );
        zjtSlxxbLsDO.setSjryb( dto.getSjryb() );
        zjtSlxxbLsDO.setSjrssxq( dto.getSjrssxq() );
        zjtSlxxbLsDO.setSjrxz( dto.getSjrxz() );
        zjtSlxxbLsDO.setSjrtxdz( dto.getSjrtxdz() );
        zjtSlxxbLsDO.setZzxxcwlb( dto.getZzxxcwlb() );
        zjtSlxxbLsDO.setCwms( dto.getCwms() );
        zjtSlxxbLsDO.setJydw( dto.getJydw() );
        zjtSlxxbLsDO.setJyrxm( dto.getJyrxm() );
        zjtSlxxbLsDO.setJyrq( dto.getJyrq() );
        zjtSlxxbLsDO.setCldw( dto.getCldw() );
        zjtSlxxbLsDO.setClqk( dto.getClqk() );
        zjtSlxxbLsDO.setClrq( dto.getClrq() );
        zjtSlxxbLsDO.setZlhkzt( dto.getZlhkzt() );
        zjtSlxxbLsDO.setHksj( dto.getHksj() );
        zjtSlxxbLsDO.setBwbha( dto.getBwbha() );
        zjtSlxxbLsDO.setBwbhb( dto.getBwbhb() );
        zjtSlxxbLsDO.setShrq( dto.getShrq() );
        zjtSlxxbLsDO.setStjssj( dto.getStjssj() );
        zjtSlxxbLsDO.setBwbhc( dto.getBwbhc() );
        zjtSlxxbLsDO.setFjpch( dto.getFjpch() );
        zjtSlxxbLsDO.setRlbdid( dto.getRlbdid() );
        zjtSlxxbLsDO.setRlbdbz( dto.getRlbdbz() );
        zjtSlxxbLsDO.setRlbdsj( dto.getRlbdsj() );
        zjtSlxxbLsDO.setZwyzw( dto.getZwyzw() );
        zjtSlxxbLsDO.setZwyzcjg( dto.getZwyzcjg() );
        zjtSlxxbLsDO.setZwezw( dto.getZwezw() );
        zjtSlxxbLsDO.setZwezcjg( dto.getZwezcjg() );
        zjtSlxxbLsDO.setZwcjjgdm( dto.getZwcjjgdm() );
        zjtSlxxbLsDO.setSzyczkdm( dto.getSzyczkdm() );
        zjtSlxxbLsDO.setSfzwzj( dto.getSfzwzj() );
        zjtSlxxbLsDO.setDztbbz( dto.getDztbbz() );
        zjtSlxxbLsDO.setDztbsj( dto.getDztbsj() );
        zjtSlxxbLsDO.setDzsjbbh( dto.getDzsjbbh() );
        zjtSlxxbLsDO.setSlfs( dto.getSlfs() );
        zjtSlxxbLsDO.setSlfsx( dto.getSlfsx() );
        zjtSlxxbLsDO.setZwtxid( dto.getZwtxid() );
        zjtSlxxbLsDO.setCzyxm( dto.getCzyxm() );
        zjtSlxxbLsDO.setHjddzbm( dto.getHjddzbm() );
        zjtSlxxbLsDO.setLssfzslbz( dto.getLssfzslbz() );
        zjtSlxxbLsDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtSlxxbLsDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtSlxxbLsDO.setCzydwmc( dto.getCzydwmc() );
        zjtSlxxbLsDO.setZjzdssxq( dto.getZjzdssxq() );
        zjtSlxxbLsDO.setZjzdxz( dto.getZjzdxz() );
        zjtSlxxbLsDO.setLqrq( dto.getLqrq() );
        zjtSlxxbLsDO.setLqrxm( dto.getLqrxm() );
        zjtSlxxbLsDO.setLqrsfhm( dto.getLqrsfhm() );
        zjtSlxxbLsDO.setLqrzpid( dto.getLqrzpid() );
        zjtSlxxbLsDO.setZjddrq( dto.getZjddrq() );
        zjtSlxxbLsDO.setLzczrid( dto.getLzczrid() );
        zjtSlxxbLsDO.setLzczrxm( dto.getLzczrxm() );
        zjtSlxxbLsDO.setLzczrdwdm( dto.getLzczrdwdm() );
        zjtSlxxbLsDO.setLzczrdwmc( dto.getLzczrdwmc() );
        zjtSlxxbLsDO.setShrxm( dto.getShrxm() );
        zjtSlxxbLsDO.setShdw( dto.getShdw() );
        zjtSlxxbLsDO.setShqk( dto.getShqk() );
        zjtSlxxbLsDO.setQfrq( dto.getQfrq() );
        zjtSlxxbLsDO.setQfrid( dto.getQfrid() );
        zjtSlxxbLsDO.setQfrxm( dto.getQfrxm() );
        zjtSlxxbLsDO.setQfdwjgdm( dto.getQfdwjgdm() );
        zjtSlxxbLsDO.setQfdw( dto.getQfdw() );
        zjtSlxxbLsDO.setDsshrq( dto.getDsshrq() );
        zjtSlxxbLsDO.setDsshrxm( dto.getDsshrxm() );
        zjtSlxxbLsDO.setDsshdw( dto.getDsshdw() );
        zjtSlxxbLsDO.setDsshqk( dto.getDsshqk() );
        zjtSlxxbLsDO.setRwid( dto.getRwid() );
        zjtSlxxbLsDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtSlxxbLsDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtSlxxbLsDO.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtSlxxbLsDO.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtSlxxbLsDO.setShdwdm( dto.getShdwdm() );
        zjtSlxxbLsDO.setCldwdm( dto.getCldwdm() );
        zjtSlxxbLsDO.setDsshdwdm( dto.getDsshdwdm() );
        zjtSlxxbLsDO.setShrid( dto.getShrid() );
        zjtSlxxbLsDO.setDsshrid( dto.getDsshrid() );
        zjtSlxxbLsDO.setSfdjh( dto.getSfdjh() );
        zjtSlxxbLsDO.setRwzxrzbh( dto.getRwzxrzbh() );
        zjtSlxxbLsDO.setRwddsj( dto.getRwddsj() );
        zjtSlxxbLsDO.setSlyckrxsfbd( dto.getSlyckrxsfbd() );
        zjtSlxxbLsDO.setRxbdkssj( dto.getRxbdkssj() );
        zjtSlxxbLsDO.setRxbdhs( dto.getRxbdhs() );
        zjtSlxxbLsDO.setRxbdxsd( dto.getRxbdxsd() );
        zjtSlxxbLsDO.setRxbdkbh( dto.getRxbdkbh() );
        zjtSlxxbLsDO.setRxbdjg( dto.getRxbdjg() );
        zjtSlxxbLsDO.setSlylszwsfbd( dto.getSlylszwsfbd() );
        zjtSlxxbLsDO.setZwybdjg( dto.getZwybdjg() );
        zjtSlxxbLsDO.setZwybdxsd( dto.getZwybdxsd() );
        zjtSlxxbLsDO.setZwebdjg( dto.getZwebdjg() );
        zjtSlxxbLsDO.setZwebdxsd( dto.getZwebdxsd() );
        zjtSlxxbLsDO.setLzszwsfhy( dto.getLzszwsfhy() );
        zjtSlxxbLsDO.setLzszwyhyjg( dto.getLzszwyhyjg() );
        zjtSlxxbLsDO.setLzszwyhyxsd( dto.getLzszwyhyxsd() );
        zjtSlxxbLsDO.setLzszwehyjg( dto.getLzszwehyjg() );
        zjtSlxxbLsDO.setLzszwehyxsd( dto.getLzszwehyxsd() );
        zjtSlxxbLsDO.setLzssfjhjz( dto.getLzssfjhjz() );
        zjtSlxxbLsDO.setSlylszwbdsm( dto.getSlylszwbdsm() );
        zjtSlxxbLsDO.setLzszwbdsm( dto.getLzszwbdsm() );
        zjtSlxxbLsDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        zjtSlxxbLsDO.setSqrxm( dto.getSqrxm() );
        zjtSlxxbLsDO.setSqrlxdh( dto.getSqrlxdh() );
        zjtSlxxbLsDO.setJzqsrq( dto.getJzqsrq() );
        zjtSlxxbLsDO.setCzylxdh( dto.getCzylxdh() );
        zjtSlxxbLsDO.setShrlxdh( dto.getShrlxdh() );
        zjtSlxxbLsDO.setDsshrlxdh( dto.getDsshrlxdh() );
        zjtSlxxbLsDO.setZfcje( dto.getZfcje() );
        zjtSlxxbLsDO.setQxfcje( dto.getQxfcje() );
        zjtSlxxbLsDO.setDsfcje( dto.getDsfcje() );
        zjtSlxxbLsDO.setZxfcje( dto.getZxfcje() );
        zjtSlxxbLsDO.setYzkddh( dto.getYzkddh() );
        zjtSlxxbLsDO.setSpdz1( dto.getSpdz1() );
        zjtSlxxbLsDO.setSfmsbswzp( dto.getSfmsbswzp() );
        zjtSlxxbLsDO.setHlwsqid( dto.getHlwsqid() );
        zjtSlxxbLsDO.setUsername( dto.getUsername() );
        zjtSlxxbLsDO.setPjjg( dto.getPjjg() );
        zjtSlxxbLsDO.setPjpljc( dto.getPjpljc() );
        zjtSlxxbLsDO.setPjsj( dto.getPjsj() );
        zjtSlxxbLsDO.setFwdx( dto.getFwdx() );
        zjtSlxxbLsDO.setSfdgszj( dto.getSfdgszj() );
        zjtSlxxbLsDO.setBz( dto.getBz() );
        zjtSlxxbLsDO.setSlshjdz( dto.getSlshjdz() );
        zjtSlxxbLsDO.setKsywlsh( dto.getKsywlsh() );
        zjtSlxxbLsDO.setKsfsbz( dto.getKsfsbz() );
        zjtSlxxbLsDO.setKsfssj( dto.getKsfssj() );
        byte[] base64zp = dto.getBase64zp();
        if ( base64zp != null ) {
            zjtSlxxbLsDO.setBase64zp( Arrays.copyOf( base64zp, base64zp.length ) );
        }
        zjtSlxxbLsDO.setZplsid( dto.getZplsid() );
        zjtSlxxbLsDO.setZpcjlx( dto.getZpcjlx() );
        zjtSlxxbLsDO.setZpsbbsh( dto.getZpsbbsh() );
        zjtSlxxbLsDO.setZpsbppxhdm( dto.getZpsbppxhdm() );
        zjtSlxxbLsDO.setZpsbppxh( dto.getZpsbppxh() );
        zjtSlxxbLsDO.setZpytbid( dto.getZpytbid() );
        zjtSlxxbLsDO.setSfbczpyt( dto.getSfbczpyt() );
        zjtSlxxbLsDO.setLxcjsj( dto.getLxcjsj() );
        zjtSlxxbLsDO.setLxczymc( dto.getLxczymc() );
        zjtSlxxbLsDO.setLxsldw( dto.getLxsldw() );
        zjtSlxxbLsDO.setZpsbbh( dto.getZpsbbh() );
        zjtSlxxbLsDO.setZpsbzcdw( dto.getZpsbzcdw() );
        zjtSlxxbLsDO.setZpsbscgs( dto.getZpsbscgs() );
        zjtSlxxbLsDO.setZpsbxsgs( dto.getZpsbxsgs() );
        zjtSlxxbLsDO.setBzsbbh( dto.getBzsbbh() );
        zjtSlxxbLsDO.setBzsbzcdw( dto.getBzsbzcdw() );
        zjtSlxxbLsDO.setBzsbppxhdm( dto.getBzsbppxhdm() );
        zjtSlxxbLsDO.setBzsbppxh( dto.getBzsbppxh() );
        zjtSlxxbLsDO.setBzsbscgs( dto.getBzsbscgs() );
        zjtSlxxbLsDO.setBzsbxsgs( dto.getBzsbxsgs() );
        zjtSlxxbLsDO.setJjlxrxm( dto.getJjlxrxm() );
        zjtSlxxbLsDO.setJjlxrdh( dto.getJjlxrdh() );
        zjtSlxxbLsDO.setJjlxryslrgx( dto.getJjlxryslrgx() );
        zjtSlxxbLsDO.setZaglywxtbh( dto.getZaglywxtbh() );
        zjtSlxxbLsDO.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        zjtSlxxbLsDO.setZaglywlbdm( dto.getZaglywlbdm() );
        zjtSlxxbLsDO.setXmmzwz( dto.getXmmzwz() );
        zjtSlxxbLsDO.setXbmzwz( dto.getXbmzwz() );
        zjtSlxxbLsDO.setMzmzwz( dto.getMzmzwz() );
        zjtSlxxbLsDO.setMzfjxmzwz( dto.getMzfjxmzwz() );
        zjtSlxxbLsDO.setZzhymzwz( dto.getZzhymzwz() );
        zjtSlxxbLsDO.setZzhemzwz( dto.getZzhemzwz() );
        zjtSlxxbLsDO.setZzhsmzwz( dto.getZzhsmzwz() );
        zjtSlxxbLsDO.setQfjgmzwz( dto.getQfjgmzwz() );
        zjtSlxxbLsDO.setCzyip( dto.getCzyip() );
        zjtSlxxbLsDO.setZwcjqid( dto.getZwcjqid() );
        zjtSlxxbLsDO.setSfsqltz( dto.getSfsqltz() );
        zjtSlxxbLsDO.setLtzshjg( dto.getLtzshjg() );
        zjtSlxxbLsDO.setLtzspsj( dto.getLtzspsj() );
        zjtSlxxbLsDO.setLtzsprid( dto.getLtzsprid() );
        zjtSlxxbLsDO.setLtzsprxm( dto.getLtzsprxm() );
        zjtSlxxbLsDO.setLtzsqsy( dto.getLtzsqsy() );

        return zjtSlxxbLsDO;
    }

    @Override
    public ZjtSlxxbLsDTO convertToDTO(ZjtSlxxbLsPageReq req) {
        if ( req == null ) {
            return null;
        }

        ZjtSlxxbLsDTO zjtSlxxbLsDTO = new ZjtSlxxbLsDTO();

        zjtSlxxbLsDTO.setGmsfhm( req.getGmsfhm() );

        return zjtSlxxbLsDTO;
    }

    @Override
    public ZjtSlxxbLsPageResp convertToPageResp(ZjtSlxxbLsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtSlxxbLsPageResp zjtSlxxbLsPageResp = new ZjtSlxxbLsPageResp();

        zjtSlxxbLsPageResp.setNbslid( dto.getNbslid() );
        zjtSlxxbLsPageResp.setYwslh( dto.getYwslh() );
        zjtSlxxbLsPageResp.setNbsfzid( dto.getNbsfzid() );
        zjtSlxxbLsPageResp.setZpid( dto.getZpid() );
        zjtSlxxbLsPageResp.setSlh( dto.getSlh() );
        zjtSlxxbLsPageResp.setRyid( dto.getRyid() );
        zjtSlxxbLsPageResp.setRynbid( dto.getRynbid() );
        zjtSlxxbLsPageResp.setQfjg( dto.getQfjg() );
        zjtSlxxbLsPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        zjtSlxxbLsPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        zjtSlxxbLsPageResp.setZz( dto.getZz() );
        zjtSlxxbLsPageResp.setHjdzqhnxxdz( dto.getHjdzqhnxxdz() );
        zjtSlxxbLsPageResp.setSlyy( dto.getSlyy() );
        zjtSlxxbLsPageResp.setZzlx( dto.getZzlx() );
        zjtSlxxbLsPageResp.setLqfs( dto.getLqfs() );
        zjtSlxxbLsPageResp.setSflx( dto.getSflx() );
        zjtSlxxbLsPageResp.setSfje( dto.getSfje() );
        zjtSlxxbLsPageResp.setSjblsh( dto.getSjblsh() );
        zjtSlxxbLsPageResp.setSlzt( dto.getSlzt() );
        zjtSlxxbLsPageResp.setTbbz( dto.getTbbz() );
        zjtSlxxbLsPageResp.setQyfwdm( dto.getQyfwdm() );
        zjtSlxxbLsPageResp.setSyzbz( dto.getSyzbz() );
        zjtSlxxbLsPageResp.setDzzdbz( dto.getDzzdbz() );
        zjtSlxxbLsPageResp.setGmsfhm( dto.getGmsfhm() );
        zjtSlxxbLsPageResp.setXm( dto.getXm() );
        zjtSlxxbLsPageResp.setXb( dto.getXb() );
        zjtSlxxbLsPageResp.setMz( dto.getMz() );
        zjtSlxxbLsPageResp.setMzfjxdm( dto.getMzfjxdm() );
        zjtSlxxbLsPageResp.setCsrq( dto.getCsrq() );
        zjtSlxxbLsPageResp.setCsdssxq( dto.getCsdssxq() );
        zjtSlxxbLsPageResp.setMlpnbid( dto.getMlpnbid() );
        zjtSlxxbLsPageResp.setSsxq( dto.getSsxq() );
        zjtSlxxbLsPageResp.setJlx( dto.getJlx() );
        zjtSlxxbLsPageResp.setMlph( dto.getMlph() );
        zjtSlxxbLsPageResp.setMlxz( dto.getMlxz() );
        zjtSlxxbLsPageResp.setPcs( dto.getPcs() );
        zjtSlxxbLsPageResp.setZrq( dto.getZrq() );
        zjtSlxxbLsPageResp.setXzjd( dto.getXzjd() );
        zjtSlxxbLsPageResp.setJcwh( dto.getJcwh() );
        zjtSlxxbLsPageResp.setPxh( dto.getPxh() );
        zjtSlxxbLsPageResp.setYwbz( dto.getYwbz() );
        zjtSlxxbLsPageResp.setCzyid( dto.getCzyid() );
        zjtSlxxbLsPageResp.setCzsj( dto.getCzsj() );
        zjtSlxxbLsPageResp.setDwdm( dto.getDwdm() );
        zjtSlxxbLsPageResp.setSjrxm( dto.getSjrxm() );
        zjtSlxxbLsPageResp.setSjrlxdh( dto.getSjrlxdh() );
        zjtSlxxbLsPageResp.setSjryb( dto.getSjryb() );
        zjtSlxxbLsPageResp.setSjrssxq( dto.getSjrssxq() );
        zjtSlxxbLsPageResp.setSjrxz( dto.getSjrxz() );
        zjtSlxxbLsPageResp.setSjrtxdz( dto.getSjrtxdz() );
        zjtSlxxbLsPageResp.setZzxxcwlb( dto.getZzxxcwlb() );
        zjtSlxxbLsPageResp.setCwms( dto.getCwms() );
        zjtSlxxbLsPageResp.setJydw( dto.getJydw() );
        zjtSlxxbLsPageResp.setJyrxm( dto.getJyrxm() );
        zjtSlxxbLsPageResp.setJyrq( dto.getJyrq() );
        zjtSlxxbLsPageResp.setCldw( dto.getCldw() );
        zjtSlxxbLsPageResp.setClqk( dto.getClqk() );
        zjtSlxxbLsPageResp.setClrq( dto.getClrq() );
        zjtSlxxbLsPageResp.setZlhkzt( dto.getZlhkzt() );
        zjtSlxxbLsPageResp.setHksj( dto.getHksj() );
        zjtSlxxbLsPageResp.setBwbha( dto.getBwbha() );
        zjtSlxxbLsPageResp.setBwbhb( dto.getBwbhb() );
        zjtSlxxbLsPageResp.setShrq( dto.getShrq() );
        zjtSlxxbLsPageResp.setStjssj( dto.getStjssj() );
        zjtSlxxbLsPageResp.setBwbhc( dto.getBwbhc() );
        zjtSlxxbLsPageResp.setFjpch( dto.getFjpch() );
        zjtSlxxbLsPageResp.setRlbdid( dto.getRlbdid() );
        zjtSlxxbLsPageResp.setRlbdbz( dto.getRlbdbz() );
        zjtSlxxbLsPageResp.setRlbdsj( dto.getRlbdsj() );
        zjtSlxxbLsPageResp.setZwyzw( dto.getZwyzw() );
        zjtSlxxbLsPageResp.setZwyzcjg( dto.getZwyzcjg() );
        zjtSlxxbLsPageResp.setZwezw( dto.getZwezw() );
        zjtSlxxbLsPageResp.setZwezcjg( dto.getZwezcjg() );
        zjtSlxxbLsPageResp.setZwcjjgdm( dto.getZwcjjgdm() );
        zjtSlxxbLsPageResp.setSzyczkdm( dto.getSzyczkdm() );
        zjtSlxxbLsPageResp.setSfzwzj( dto.getSfzwzj() );
        zjtSlxxbLsPageResp.setDztbbz( dto.getDztbbz() );
        zjtSlxxbLsPageResp.setDztbsj( dto.getDztbsj() );
        zjtSlxxbLsPageResp.setDzsjbbh( dto.getDzsjbbh() );
        zjtSlxxbLsPageResp.setSlfs( dto.getSlfs() );
        zjtSlxxbLsPageResp.setSlfsx( dto.getSlfsx() );
        zjtSlxxbLsPageResp.setZwtxid( dto.getZwtxid() );
        zjtSlxxbLsPageResp.setCzyxm( dto.getCzyxm() );
        zjtSlxxbLsPageResp.setHjddzbm( dto.getHjddzbm() );
        zjtSlxxbLsPageResp.setLssfzslbz( dto.getLssfzslbz() );
        zjtSlxxbLsPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtSlxxbLsPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtSlxxbLsPageResp.setCzydwmc( dto.getCzydwmc() );
        zjtSlxxbLsPageResp.setZjzdssxq( dto.getZjzdssxq() );
        zjtSlxxbLsPageResp.setZjzdxz( dto.getZjzdxz() );
        zjtSlxxbLsPageResp.setLqrq( dto.getLqrq() );
        zjtSlxxbLsPageResp.setLqrxm( dto.getLqrxm() );
        zjtSlxxbLsPageResp.setLqrsfhm( dto.getLqrsfhm() );
        zjtSlxxbLsPageResp.setLqrzpid( dto.getLqrzpid() );
        zjtSlxxbLsPageResp.setZjddrq( dto.getZjddrq() );
        zjtSlxxbLsPageResp.setLzczrid( dto.getLzczrid() );
        zjtSlxxbLsPageResp.setLzczrxm( dto.getLzczrxm() );
        zjtSlxxbLsPageResp.setLzczrdwdm( dto.getLzczrdwdm() );
        zjtSlxxbLsPageResp.setLzczrdwmc( dto.getLzczrdwmc() );
        zjtSlxxbLsPageResp.setShrxm( dto.getShrxm() );
        zjtSlxxbLsPageResp.setShdw( dto.getShdw() );
        zjtSlxxbLsPageResp.setShqk( dto.getShqk() );
        zjtSlxxbLsPageResp.setQfrq( dto.getQfrq() );
        zjtSlxxbLsPageResp.setQfrid( dto.getQfrid() );
        zjtSlxxbLsPageResp.setQfrxm( dto.getQfrxm() );
        zjtSlxxbLsPageResp.setQfdwjgdm( dto.getQfdwjgdm() );
        zjtSlxxbLsPageResp.setQfdw( dto.getQfdw() );
        zjtSlxxbLsPageResp.setDsshrq( dto.getDsshrq() );
        zjtSlxxbLsPageResp.setDsshrxm( dto.getDsshrxm() );
        zjtSlxxbLsPageResp.setDsshdw( dto.getDsshdw() );
        zjtSlxxbLsPageResp.setDsshqk( dto.getDsshqk() );
        zjtSlxxbLsPageResp.setRwid( dto.getRwid() );
        zjtSlxxbLsPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtSlxxbLsPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtSlxxbLsPageResp.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtSlxxbLsPageResp.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtSlxxbLsPageResp.setShdwdm( dto.getShdwdm() );
        zjtSlxxbLsPageResp.setCldwdm( dto.getCldwdm() );
        zjtSlxxbLsPageResp.setDsshdwdm( dto.getDsshdwdm() );
        zjtSlxxbLsPageResp.setShrid( dto.getShrid() );
        zjtSlxxbLsPageResp.setDsshrid( dto.getDsshrid() );
        zjtSlxxbLsPageResp.setSfdjh( dto.getSfdjh() );
        zjtSlxxbLsPageResp.setRwzxrzbh( dto.getRwzxrzbh() );
        zjtSlxxbLsPageResp.setRwddsj( dto.getRwddsj() );
        zjtSlxxbLsPageResp.setSlyckrxsfbd( dto.getSlyckrxsfbd() );
        zjtSlxxbLsPageResp.setRxbdkssj( dto.getRxbdkssj() );
        zjtSlxxbLsPageResp.setRxbdhs( dto.getRxbdhs() );
        zjtSlxxbLsPageResp.setRxbdxsd( dto.getRxbdxsd() );
        zjtSlxxbLsPageResp.setRxbdkbh( dto.getRxbdkbh() );
        zjtSlxxbLsPageResp.setRxbdjg( dto.getRxbdjg() );
        zjtSlxxbLsPageResp.setSlylszwsfbd( dto.getSlylszwsfbd() );
        zjtSlxxbLsPageResp.setZwybdjg( dto.getZwybdjg() );
        zjtSlxxbLsPageResp.setZwybdxsd( dto.getZwybdxsd() );
        zjtSlxxbLsPageResp.setZwebdjg( dto.getZwebdjg() );
        zjtSlxxbLsPageResp.setZwebdxsd( dto.getZwebdxsd() );
        zjtSlxxbLsPageResp.setLzszwsfhy( dto.getLzszwsfhy() );
        zjtSlxxbLsPageResp.setLzszwyhyjg( dto.getLzszwyhyjg() );
        zjtSlxxbLsPageResp.setLzszwyhyxsd( dto.getLzszwyhyxsd() );
        zjtSlxxbLsPageResp.setLzszwehyjg( dto.getLzszwehyjg() );
        zjtSlxxbLsPageResp.setLzszwehyxsd( dto.getLzszwehyxsd() );
        zjtSlxxbLsPageResp.setLzssfjhjz( dto.getLzssfjhjz() );
        zjtSlxxbLsPageResp.setSlylszwbdsm( dto.getSlylszwbdsm() );
        zjtSlxxbLsPageResp.setLzszwbdsm( dto.getLzszwbdsm() );
        zjtSlxxbLsPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        zjtSlxxbLsPageResp.setSqrxm( dto.getSqrxm() );
        zjtSlxxbLsPageResp.setSqrlxdh( dto.getSqrlxdh() );
        zjtSlxxbLsPageResp.setJzqsrq( dto.getJzqsrq() );
        zjtSlxxbLsPageResp.setCzylxdh( dto.getCzylxdh() );
        zjtSlxxbLsPageResp.setShrlxdh( dto.getShrlxdh() );
        zjtSlxxbLsPageResp.setDsshrlxdh( dto.getDsshrlxdh() );
        zjtSlxxbLsPageResp.setZfcje( dto.getZfcje() );
        zjtSlxxbLsPageResp.setQxfcje( dto.getQxfcje() );
        zjtSlxxbLsPageResp.setDsfcje( dto.getDsfcje() );
        zjtSlxxbLsPageResp.setZxfcje( dto.getZxfcje() );
        zjtSlxxbLsPageResp.setYzkddh( dto.getYzkddh() );
        zjtSlxxbLsPageResp.setSpdz1( dto.getSpdz1() );
        zjtSlxxbLsPageResp.setSfmsbswzp( dto.getSfmsbswzp() );
        zjtSlxxbLsPageResp.setHlwsqid( dto.getHlwsqid() );
        zjtSlxxbLsPageResp.setUsername( dto.getUsername() );
        zjtSlxxbLsPageResp.setPjjg( dto.getPjjg() );
        zjtSlxxbLsPageResp.setPjpljc( dto.getPjpljc() );
        zjtSlxxbLsPageResp.setPjsj( dto.getPjsj() );
        zjtSlxxbLsPageResp.setFwdx( dto.getFwdx() );
        zjtSlxxbLsPageResp.setSfdgszj( dto.getSfdgszj() );
        zjtSlxxbLsPageResp.setBz( dto.getBz() );
        zjtSlxxbLsPageResp.setSlshjdz( dto.getSlshjdz() );
        zjtSlxxbLsPageResp.setKsywlsh( dto.getKsywlsh() );
        zjtSlxxbLsPageResp.setKsfsbz( dto.getKsfsbz() );
        zjtSlxxbLsPageResp.setKsfssj( dto.getKsfssj() );
        byte[] base64zp = dto.getBase64zp();
        if ( base64zp != null ) {
            zjtSlxxbLsPageResp.setBase64zp( Arrays.copyOf( base64zp, base64zp.length ) );
        }
        zjtSlxxbLsPageResp.setZplsid( dto.getZplsid() );
        zjtSlxxbLsPageResp.setZpcjlx( dto.getZpcjlx() );
        zjtSlxxbLsPageResp.setZpsbbsh( dto.getZpsbbsh() );
        zjtSlxxbLsPageResp.setZpsbppxhdm( dto.getZpsbppxhdm() );
        zjtSlxxbLsPageResp.setZpsbppxh( dto.getZpsbppxh() );
        zjtSlxxbLsPageResp.setZpytbid( dto.getZpytbid() );
        zjtSlxxbLsPageResp.setSfbczpyt( dto.getSfbczpyt() );
        zjtSlxxbLsPageResp.setLxcjsj( dto.getLxcjsj() );
        zjtSlxxbLsPageResp.setLxczymc( dto.getLxczymc() );
        zjtSlxxbLsPageResp.setLxsldw( dto.getLxsldw() );
        zjtSlxxbLsPageResp.setZpsbbh( dto.getZpsbbh() );
        zjtSlxxbLsPageResp.setZpsbzcdw( dto.getZpsbzcdw() );
        zjtSlxxbLsPageResp.setZpsbscgs( dto.getZpsbscgs() );
        zjtSlxxbLsPageResp.setZpsbxsgs( dto.getZpsbxsgs() );
        zjtSlxxbLsPageResp.setBzsbbh( dto.getBzsbbh() );
        zjtSlxxbLsPageResp.setBzsbzcdw( dto.getBzsbzcdw() );
        zjtSlxxbLsPageResp.setBzsbppxhdm( dto.getBzsbppxhdm() );
        zjtSlxxbLsPageResp.setBzsbppxh( dto.getBzsbppxh() );
        zjtSlxxbLsPageResp.setBzsbscgs( dto.getBzsbscgs() );
        zjtSlxxbLsPageResp.setBzsbxsgs( dto.getBzsbxsgs() );
        zjtSlxxbLsPageResp.setJjlxrxm( dto.getJjlxrxm() );
        zjtSlxxbLsPageResp.setJjlxrdh( dto.getJjlxrdh() );
        zjtSlxxbLsPageResp.setJjlxryslrgx( dto.getJjlxryslrgx() );
        zjtSlxxbLsPageResp.setZaglywxtbh( dto.getZaglywxtbh() );
        zjtSlxxbLsPageResp.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        zjtSlxxbLsPageResp.setZaglywlbdm( dto.getZaglywlbdm() );
        zjtSlxxbLsPageResp.setXmmzwz( dto.getXmmzwz() );
        zjtSlxxbLsPageResp.setXbmzwz( dto.getXbmzwz() );
        zjtSlxxbLsPageResp.setMzmzwz( dto.getMzmzwz() );
        zjtSlxxbLsPageResp.setMzfjxmzwz( dto.getMzfjxmzwz() );
        zjtSlxxbLsPageResp.setZzhymzwz( dto.getZzhymzwz() );
        zjtSlxxbLsPageResp.setZzhemzwz( dto.getZzhemzwz() );
        zjtSlxxbLsPageResp.setZzhsmzwz( dto.getZzhsmzwz() );
        zjtSlxxbLsPageResp.setQfjgmzwz( dto.getQfjgmzwz() );
        zjtSlxxbLsPageResp.setCzyip( dto.getCzyip() );
        zjtSlxxbLsPageResp.setZwcjqid( dto.getZwcjqid() );
        zjtSlxxbLsPageResp.setSfsqltz( dto.getSfsqltz() );
        zjtSlxxbLsPageResp.setLtzshjg( dto.getLtzshjg() );
        zjtSlxxbLsPageResp.setLtzspsj( dto.getLtzspsj() );
        zjtSlxxbLsPageResp.setLtzsprid( dto.getLtzsprid() );
        zjtSlxxbLsPageResp.setLtzsprxm( dto.getLtzsprxm() );
        zjtSlxxbLsPageResp.setLtzsqsy( dto.getLtzsqsy() );

        return zjtSlxxbLsPageResp;
    }
}
