package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjywWjSjDTO;
import com.zjjcnt.project.ck.base.entity.HjywWjSjDO;
import java.util.Arrays;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:45+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjywWjSjConvertImpl implements HjywWjSjConvert {

    @Override
    public HjywWjSjDTO convert(HjywWjSjDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjywWjSjDTO hjywWjSjDTO = new HjywWjSjDTO();

        hjywWjSjDTO.setId( entity.getId() );
        hjywWjSjDTO.setWjsjbh( entity.getWjsjbh() );
        byte[] wjsj = entity.getWjsj();
        if ( wjsj != null ) {
            hjywWjSjDTO.setWjsj( Arrays.copyOf( wjsj, wjsj.length ) );
        }

        return hjywWjSjDTO;
    }

    @Override
    public HjywWjSjDO convertToDO(HjywWjSjDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjywWjSjDO hjywWjSjDO = new HjywWjSjDO();

        hjywWjSjDO.setId( dto.getId() );
        hjywWjSjDO.setWjsjbh( dto.getWjsjbh() );
        byte[] wjsj = dto.getWjsj();
        if ( wjsj != null ) {
            hjywWjSjDO.setWjsj( Arrays.copyOf( wjsj, wjsj.length ) );
        }

        return hjywWjSjDO;
    }
}
