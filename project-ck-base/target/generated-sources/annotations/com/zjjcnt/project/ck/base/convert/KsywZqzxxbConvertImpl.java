package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsywZqzxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsywZqzxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsywZqzxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsywZqzxxbViewResp;
import com.zjjcnt.project.ck.base.entity.KsywZqzxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:45+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsywZqzxxbConvertImpl implements KsywZqzxxbConvert {

    @Override
    public KsywZqzxxbDTO convert(KsywZqzxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsywZqzxxbDTO ksywZqzxxbDTO = new KsywZqzxxbDTO();

        ksywZqzxxbDTO.setId( entity.getId() );
        ksywZqzxxbDTO.setKszqzid( entity.getKszqzid() );
        ksywZqzxxbDTO.setKsywid( entity.getKsywid() );
        ksywZqzxxbDTO.setYwlsh( entity.getYwlsh() );
        ksywZqzxxbDTO.setZqzbh( entity.getZqzbh() );
        ksywZqzxxbDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        ksywZqzxxbDTO.setSqrxm( entity.getSqrxm() );
        ksywZqzxxbDTO.setSqrlxdh( entity.getSqrlxdh() );
        ksywZqzxxbDTO.setSqrzzssxqdm( entity.getSqrzzssxqdm() );
        ksywZqzxxbDTO.setSqrzzqhnxxdz( entity.getSqrzzqhnxxdz() );
        ksywZqzxxbDTO.setSqrhkdjjggajgjgdm( entity.getSqrhkdjjggajgjgdm() );
        ksywZqzxxbDTO.setSqrhkdjjggajgmc( entity.getSqrhkdjjggajgmc() );
        ksywZqzxxbDTO.setQcdssxqdm( entity.getQcdssxqdm() );
        ksywZqzxxbDTO.setQcdqhnxxdz( entity.getQcdqhnxxdz() );
        ksywZqzxxbDTO.setQcdhkdjjggajgjgdm( entity.getQcdhkdjjggajgjgdm() );
        ksywZqzxxbDTO.setQcdhkdjjggajgmc( entity.getQcdhkdjjggajgmc() );
        ksywZqzxxbDTO.setQrdssxqdm( entity.getQrdssxqdm() );
        ksywZqzxxbDTO.setQrdqhnxxdz( entity.getQrdqhnxxdz() );
        ksywZqzxxbDTO.setQrdhkdjjggajgjgdm( entity.getQrdhkdjjggajgjgdm() );
        ksywZqzxxbDTO.setQrdhkdjjggajgmc( entity.getQrdhkdjjggajgmc() );
        ksywZqzxxbDTO.setQfjggajgjgdm( entity.getQfjggajgjgdm() );
        ksywZqzxxbDTO.setQfjggajgmc( entity.getQfjggajgmc() );
        ksywZqzxxbDTO.setCbrxm( entity.getCbrxm() );
        ksywZqzxxbDTO.setQfrq( entity.getQfrq() );
        ksywZqzxxbDTO.setBz( entity.getBz() );
        ksywZqzxxbDTO.setQyldyydm( entity.getQyldyydm() );
        ksywZqzxxbDTO.setQyldyymcnb( entity.getQyldyymcnb() );
        ksywZqzxxbDTO.setYxqjzrq( entity.getYxqjzrq() );
        ksywZqzxxbDTO.setQyfwdm( entity.getQyfwdm() );
        ksywZqzxxbDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        ksywZqzxxbDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        ksywZqzxxbDTO.setSldwlxdh( entity.getSldwlxdh() );
        ksywZqzxxbDTO.setSlrxm( entity.getSlrxm() );
        ksywZqzxxbDTO.setSlsj( entity.getSlsj() );
        ksywZqzxxbDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        ksywZqzxxbDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        ksywZqzxxbDTO.setQyr1ysqrgxjtgxdm( entity.getQyr1ysqrgxjtgxdm() );
        ksywZqzxxbDTO.setQyr1gmsfhm( entity.getQyr1gmsfhm() );
        ksywZqzxxbDTO.setQyr1xm( entity.getQyr1xm() );
        ksywZqzxxbDTO.setQyr1xbdm( entity.getQyr1xbdm() );
        ksywZqzxxbDTO.setQyr1csrq( entity.getQyr1csrq() );
        ksywZqzxxbDTO.setQyr2ysqrgxjtgxdm( entity.getQyr2ysqrgxjtgxdm() );
        ksywZqzxxbDTO.setQyr2gmsfhm( entity.getQyr2gmsfhm() );
        ksywZqzxxbDTO.setQyr2xm( entity.getQyr2xm() );
        ksywZqzxxbDTO.setQyr2xbdm( entity.getQyr2xbdm() );
        ksywZqzxxbDTO.setQyr2csrq( entity.getQyr2csrq() );
        ksywZqzxxbDTO.setQyr3ysqrgxjtgxdm( entity.getQyr3ysqrgxjtgxdm() );
        ksywZqzxxbDTO.setQyr3gmsfhm( entity.getQyr3gmsfhm() );
        ksywZqzxxbDTO.setQyr3xm( entity.getQyr3xm() );
        ksywZqzxxbDTO.setQyr3xbdm( entity.getQyr3xbdm() );
        ksywZqzxxbDTO.setQyr3csrq( entity.getQyr3csrq() );
        ksywZqzxxbDTO.setQyr4ysqrgxjtgxdm( entity.getQyr4ysqrgxjtgxdm() );
        ksywZqzxxbDTO.setQyr4gmsfhm( entity.getQyr4gmsfhm() );
        ksywZqzxxbDTO.setQyr4xm( entity.getQyr4xm() );
        ksywZqzxxbDTO.setQyr4xbdm( entity.getQyr4xbdm() );
        ksywZqzxxbDTO.setQyr4csrq( entity.getQyr4csrq() );
        ksywZqzxxbDTO.setRksj( entity.getRksj() );
        ksywZqzxxbDTO.setPcs( entity.getPcs() );

        return ksywZqzxxbDTO;
    }

    @Override
    public KsywZqzxxbDO convertToDO(KsywZqzxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywZqzxxbDO ksywZqzxxbDO = new KsywZqzxxbDO();

        ksywZqzxxbDO.setId( dto.getId() );
        ksywZqzxxbDO.setKszqzid( dto.getKszqzid() );
        ksywZqzxxbDO.setKsywid( dto.getKsywid() );
        ksywZqzxxbDO.setYwlsh( dto.getYwlsh() );
        ksywZqzxxbDO.setZqzbh( dto.getZqzbh() );
        ksywZqzxxbDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksywZqzxxbDO.setSqrxm( dto.getSqrxm() );
        ksywZqzxxbDO.setSqrlxdh( dto.getSqrlxdh() );
        ksywZqzxxbDO.setSqrzzssxqdm( dto.getSqrzzssxqdm() );
        ksywZqzxxbDO.setSqrzzqhnxxdz( dto.getSqrzzqhnxxdz() );
        ksywZqzxxbDO.setSqrhkdjjggajgjgdm( dto.getSqrhkdjjggajgjgdm() );
        ksywZqzxxbDO.setSqrhkdjjggajgmc( dto.getSqrhkdjjggajgmc() );
        ksywZqzxxbDO.setQcdssxqdm( dto.getQcdssxqdm() );
        ksywZqzxxbDO.setQcdqhnxxdz( dto.getQcdqhnxxdz() );
        ksywZqzxxbDO.setQcdhkdjjggajgjgdm( dto.getQcdhkdjjggajgjgdm() );
        ksywZqzxxbDO.setQcdhkdjjggajgmc( dto.getQcdhkdjjggajgmc() );
        ksywZqzxxbDO.setQrdssxqdm( dto.getQrdssxqdm() );
        ksywZqzxxbDO.setQrdqhnxxdz( dto.getQrdqhnxxdz() );
        ksywZqzxxbDO.setQrdhkdjjggajgjgdm( dto.getQrdhkdjjggajgjgdm() );
        ksywZqzxxbDO.setQrdhkdjjggajgmc( dto.getQrdhkdjjggajgmc() );
        ksywZqzxxbDO.setQfjggajgjgdm( dto.getQfjggajgjgdm() );
        ksywZqzxxbDO.setQfjggajgmc( dto.getQfjggajgmc() );
        ksywZqzxxbDO.setCbrxm( dto.getCbrxm() );
        ksywZqzxxbDO.setQfrq( dto.getQfrq() );
        ksywZqzxxbDO.setBz( dto.getBz() );
        ksywZqzxxbDO.setQyldyydm( dto.getQyldyydm() );
        ksywZqzxxbDO.setQyldyymcnb( dto.getQyldyymcnb() );
        ksywZqzxxbDO.setYxqjzrq( dto.getYxqjzrq() );
        ksywZqzxxbDO.setQyfwdm( dto.getQyfwdm() );
        ksywZqzxxbDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        ksywZqzxxbDO.setSldwgajgmc( dto.getSldwgajgmc() );
        ksywZqzxxbDO.setSldwlxdh( dto.getSldwlxdh() );
        ksywZqzxxbDO.setSlrxm( dto.getSlrxm() );
        ksywZqzxxbDO.setSlsj( dto.getSlsj() );
        ksywZqzxxbDO.setSjgsdwdm( dto.getSjgsdwdm() );
        ksywZqzxxbDO.setSjgsdwmc( dto.getSjgsdwmc() );
        ksywZqzxxbDO.setQyr1ysqrgxjtgxdm( dto.getQyr1ysqrgxjtgxdm() );
        ksywZqzxxbDO.setQyr1gmsfhm( dto.getQyr1gmsfhm() );
        ksywZqzxxbDO.setQyr1xm( dto.getQyr1xm() );
        ksywZqzxxbDO.setQyr1xbdm( dto.getQyr1xbdm() );
        ksywZqzxxbDO.setQyr1csrq( dto.getQyr1csrq() );
        ksywZqzxxbDO.setQyr2ysqrgxjtgxdm( dto.getQyr2ysqrgxjtgxdm() );
        ksywZqzxxbDO.setQyr2gmsfhm( dto.getQyr2gmsfhm() );
        ksywZqzxxbDO.setQyr2xm( dto.getQyr2xm() );
        ksywZqzxxbDO.setQyr2xbdm( dto.getQyr2xbdm() );
        ksywZqzxxbDO.setQyr2csrq( dto.getQyr2csrq() );
        ksywZqzxxbDO.setQyr3ysqrgxjtgxdm( dto.getQyr3ysqrgxjtgxdm() );
        ksywZqzxxbDO.setQyr3gmsfhm( dto.getQyr3gmsfhm() );
        ksywZqzxxbDO.setQyr3xm( dto.getQyr3xm() );
        ksywZqzxxbDO.setQyr3xbdm( dto.getQyr3xbdm() );
        ksywZqzxxbDO.setQyr3csrq( dto.getQyr3csrq() );
        ksywZqzxxbDO.setQyr4ysqrgxjtgxdm( dto.getQyr4ysqrgxjtgxdm() );
        ksywZqzxxbDO.setQyr4gmsfhm( dto.getQyr4gmsfhm() );
        ksywZqzxxbDO.setQyr4xm( dto.getQyr4xm() );
        ksywZqzxxbDO.setQyr4xbdm( dto.getQyr4xbdm() );
        ksywZqzxxbDO.setQyr4csrq( dto.getQyr4csrq() );
        ksywZqzxxbDO.setRksj( dto.getRksj() );
        ksywZqzxxbDO.setPcs( dto.getPcs() );

        return ksywZqzxxbDO;
    }

    @Override
    public KsywZqzxxbDTO convertToDTO(KsywZqzxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsywZqzxxbDTO ksywZqzxxbDTO = new KsywZqzxxbDTO();

        ksywZqzxxbDTO.setKsywid( req.getKsywid() );
        ksywZqzxxbDTO.setYwlsh( req.getYwlsh() );
        ksywZqzxxbDTO.setZqzbh( req.getZqzbh() );
        ksywZqzxxbDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        ksywZqzxxbDTO.setSqrxm( req.getSqrxm() );
        ksywZqzxxbDTO.setSqrlxdh( req.getSqrlxdh() );
        ksywZqzxxbDTO.setSqrzzssxqdm( req.getSqrzzssxqdm() );
        ksywZqzxxbDTO.setSqrzzqhnxxdz( req.getSqrzzqhnxxdz() );
        ksywZqzxxbDTO.setSqrhkdjjggajgjgdm( req.getSqrhkdjjggajgjgdm() );
        ksywZqzxxbDTO.setSqrhkdjjggajgmc( req.getSqrhkdjjggajgmc() );
        ksywZqzxxbDTO.setQcdssxqdm( req.getQcdssxqdm() );
        ksywZqzxxbDTO.setQcdqhnxxdz( req.getQcdqhnxxdz() );
        ksywZqzxxbDTO.setQcdhkdjjggajgjgdm( req.getQcdhkdjjggajgjgdm() );
        ksywZqzxxbDTO.setQcdhkdjjggajgmc( req.getQcdhkdjjggajgmc() );
        ksywZqzxxbDTO.setQrdssxqdm( req.getQrdssxqdm() );
        ksywZqzxxbDTO.setQrdqhnxxdz( req.getQrdqhnxxdz() );
        ksywZqzxxbDTO.setQrdhkdjjggajgjgdm( req.getQrdhkdjjggajgjgdm() );
        ksywZqzxxbDTO.setQrdhkdjjggajgmc( req.getQrdhkdjjggajgmc() );
        ksywZqzxxbDTO.setQfjggajgjgdm( req.getQfjggajgjgdm() );
        ksywZqzxxbDTO.setQfjggajgmc( req.getQfjggajgmc() );
        ksywZqzxxbDTO.setCbrxm( req.getCbrxm() );
        ksywZqzxxbDTO.setQfrq( req.getQfrq() );
        ksywZqzxxbDTO.setBz( req.getBz() );
        ksywZqzxxbDTO.setQyldyydm( req.getQyldyydm() );
        ksywZqzxxbDTO.setQyldyymcnb( req.getQyldyymcnb() );
        ksywZqzxxbDTO.setYxqjzrq( req.getYxqjzrq() );
        ksywZqzxxbDTO.setQyfwdm( req.getQyfwdm() );
        ksywZqzxxbDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        ksywZqzxxbDTO.setSldwgajgmc( req.getSldwgajgmc() );
        ksywZqzxxbDTO.setSldwlxdh( req.getSldwlxdh() );
        ksywZqzxxbDTO.setSlrxm( req.getSlrxm() );
        ksywZqzxxbDTO.setSlsj( req.getSlsj() );
        ksywZqzxxbDTO.setSjgsdwdm( req.getSjgsdwdm() );
        ksywZqzxxbDTO.setSjgsdwmc( req.getSjgsdwmc() );
        ksywZqzxxbDTO.setQyr1ysqrgxjtgxdm( req.getQyr1ysqrgxjtgxdm() );
        ksywZqzxxbDTO.setQyr1gmsfhm( req.getQyr1gmsfhm() );
        ksywZqzxxbDTO.setQyr1xm( req.getQyr1xm() );
        ksywZqzxxbDTO.setQyr1xbdm( req.getQyr1xbdm() );
        ksywZqzxxbDTO.setQyr1csrq( req.getQyr1csrq() );
        ksywZqzxxbDTO.setQyr2ysqrgxjtgxdm( req.getQyr2ysqrgxjtgxdm() );
        ksywZqzxxbDTO.setQyr2gmsfhm( req.getQyr2gmsfhm() );
        ksywZqzxxbDTO.setQyr2xm( req.getQyr2xm() );
        ksywZqzxxbDTO.setQyr2xbdm( req.getQyr2xbdm() );
        ksywZqzxxbDTO.setQyr2csrq( req.getQyr2csrq() );
        ksywZqzxxbDTO.setQyr3ysqrgxjtgxdm( req.getQyr3ysqrgxjtgxdm() );
        ksywZqzxxbDTO.setQyr3gmsfhm( req.getQyr3gmsfhm() );
        ksywZqzxxbDTO.setQyr3xm( req.getQyr3xm() );
        ksywZqzxxbDTO.setQyr3xbdm( req.getQyr3xbdm() );
        ksywZqzxxbDTO.setQyr3csrq( req.getQyr3csrq() );
        ksywZqzxxbDTO.setQyr4ysqrgxjtgxdm( req.getQyr4ysqrgxjtgxdm() );
        ksywZqzxxbDTO.setQyr4gmsfhm( req.getQyr4gmsfhm() );
        ksywZqzxxbDTO.setQyr4xm( req.getQyr4xm() );
        ksywZqzxxbDTO.setQyr4xbdm( req.getQyr4xbdm() );
        ksywZqzxxbDTO.setQyr4csrq( req.getQyr4csrq() );
        ksywZqzxxbDTO.setRksj( req.getRksj() );
        ksywZqzxxbDTO.setPcs( req.getPcs() );

        return ksywZqzxxbDTO;
    }

    @Override
    public KsywZqzxxbPageResp convertToPageResp(KsywZqzxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywZqzxxbPageResp ksywZqzxxbPageResp = new KsywZqzxxbPageResp();

        ksywZqzxxbPageResp.setKszqzid( dto.getKszqzid() );
        ksywZqzxxbPageResp.setKsywid( dto.getKsywid() );
        ksywZqzxxbPageResp.setYwlsh( dto.getYwlsh() );
        ksywZqzxxbPageResp.setZqzbh( dto.getZqzbh() );
        ksywZqzxxbPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksywZqzxxbPageResp.setSqrxm( dto.getSqrxm() );
        ksywZqzxxbPageResp.setSqrlxdh( dto.getSqrlxdh() );
        ksywZqzxxbPageResp.setSqrzzssxqdm( dto.getSqrzzssxqdm() );
        ksywZqzxxbPageResp.setSqrzzqhnxxdz( dto.getSqrzzqhnxxdz() );
        ksywZqzxxbPageResp.setSqrhkdjjggajgjgdm( dto.getSqrhkdjjggajgjgdm() );
        ksywZqzxxbPageResp.setSqrhkdjjggajgmc( dto.getSqrhkdjjggajgmc() );
        ksywZqzxxbPageResp.setQcdssxqdm( dto.getQcdssxqdm() );
        ksywZqzxxbPageResp.setQcdqhnxxdz( dto.getQcdqhnxxdz() );
        ksywZqzxxbPageResp.setQcdhkdjjggajgjgdm( dto.getQcdhkdjjggajgjgdm() );
        ksywZqzxxbPageResp.setQcdhkdjjggajgmc( dto.getQcdhkdjjggajgmc() );
        ksywZqzxxbPageResp.setQrdssxqdm( dto.getQrdssxqdm() );
        ksywZqzxxbPageResp.setQrdqhnxxdz( dto.getQrdqhnxxdz() );
        ksywZqzxxbPageResp.setQrdhkdjjggajgjgdm( dto.getQrdhkdjjggajgjgdm() );
        ksywZqzxxbPageResp.setQrdhkdjjggajgmc( dto.getQrdhkdjjggajgmc() );
        ksywZqzxxbPageResp.setQfjggajgjgdm( dto.getQfjggajgjgdm() );
        ksywZqzxxbPageResp.setQfjggajgmc( dto.getQfjggajgmc() );
        ksywZqzxxbPageResp.setCbrxm( dto.getCbrxm() );
        ksywZqzxxbPageResp.setQfrq( dto.getQfrq() );
        ksywZqzxxbPageResp.setBz( dto.getBz() );
        ksywZqzxxbPageResp.setQyldyydm( dto.getQyldyydm() );
        ksywZqzxxbPageResp.setQyldyymcnb( dto.getQyldyymcnb() );
        ksywZqzxxbPageResp.setYxqjzrq( dto.getYxqjzrq() );
        ksywZqzxxbPageResp.setQyfwdm( dto.getQyfwdm() );
        ksywZqzxxbPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        ksywZqzxxbPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        ksywZqzxxbPageResp.setSldwlxdh( dto.getSldwlxdh() );
        ksywZqzxxbPageResp.setSlrxm( dto.getSlrxm() );
        ksywZqzxxbPageResp.setSlsj( dto.getSlsj() );
        ksywZqzxxbPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        ksywZqzxxbPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        ksywZqzxxbPageResp.setQyr1ysqrgxjtgxdm( dto.getQyr1ysqrgxjtgxdm() );
        ksywZqzxxbPageResp.setQyr1gmsfhm( dto.getQyr1gmsfhm() );
        ksywZqzxxbPageResp.setQyr1xm( dto.getQyr1xm() );
        ksywZqzxxbPageResp.setQyr1xbdm( dto.getQyr1xbdm() );
        ksywZqzxxbPageResp.setQyr1csrq( dto.getQyr1csrq() );
        ksywZqzxxbPageResp.setQyr2ysqrgxjtgxdm( dto.getQyr2ysqrgxjtgxdm() );
        ksywZqzxxbPageResp.setQyr2gmsfhm( dto.getQyr2gmsfhm() );
        ksywZqzxxbPageResp.setQyr2xm( dto.getQyr2xm() );
        ksywZqzxxbPageResp.setQyr2xbdm( dto.getQyr2xbdm() );
        ksywZqzxxbPageResp.setQyr2csrq( dto.getQyr2csrq() );
        ksywZqzxxbPageResp.setQyr3ysqrgxjtgxdm( dto.getQyr3ysqrgxjtgxdm() );
        ksywZqzxxbPageResp.setQyr3gmsfhm( dto.getQyr3gmsfhm() );
        ksywZqzxxbPageResp.setQyr3xm( dto.getQyr3xm() );
        ksywZqzxxbPageResp.setQyr3xbdm( dto.getQyr3xbdm() );
        ksywZqzxxbPageResp.setQyr3csrq( dto.getQyr3csrq() );
        ksywZqzxxbPageResp.setQyr4ysqrgxjtgxdm( dto.getQyr4ysqrgxjtgxdm() );
        ksywZqzxxbPageResp.setQyr4gmsfhm( dto.getQyr4gmsfhm() );
        ksywZqzxxbPageResp.setQyr4xm( dto.getQyr4xm() );
        ksywZqzxxbPageResp.setQyr4xbdm( dto.getQyr4xbdm() );
        ksywZqzxxbPageResp.setQyr4csrq( dto.getQyr4csrq() );
        ksywZqzxxbPageResp.setRksj( dto.getRksj() );
        ksywZqzxxbPageResp.setPcs( dto.getPcs() );

        return ksywZqzxxbPageResp;
    }

    @Override
    public KsywZqzxxbViewResp convertToViewResp(KsywZqzxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywZqzxxbViewResp ksywZqzxxbViewResp = new KsywZqzxxbViewResp();

        ksywZqzxxbViewResp.setKszqzid( dto.getKszqzid() );
        ksywZqzxxbViewResp.setKsywid( dto.getKsywid() );
        ksywZqzxxbViewResp.setYwlsh( dto.getYwlsh() );
        ksywZqzxxbViewResp.setZqzbh( dto.getZqzbh() );
        ksywZqzxxbViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksywZqzxxbViewResp.setSqrxm( dto.getSqrxm() );
        ksywZqzxxbViewResp.setSqrlxdh( dto.getSqrlxdh() );
        ksywZqzxxbViewResp.setSqrzzssxqdm( dto.getSqrzzssxqdm() );
        ksywZqzxxbViewResp.setSqrzzqhnxxdz( dto.getSqrzzqhnxxdz() );
        ksywZqzxxbViewResp.setSqrhkdjjggajgjgdm( dto.getSqrhkdjjggajgjgdm() );
        ksywZqzxxbViewResp.setSqrhkdjjggajgmc( dto.getSqrhkdjjggajgmc() );
        ksywZqzxxbViewResp.setQcdssxqdm( dto.getQcdssxqdm() );
        ksywZqzxxbViewResp.setQcdqhnxxdz( dto.getQcdqhnxxdz() );
        ksywZqzxxbViewResp.setQcdhkdjjggajgjgdm( dto.getQcdhkdjjggajgjgdm() );
        ksywZqzxxbViewResp.setQcdhkdjjggajgmc( dto.getQcdhkdjjggajgmc() );
        ksywZqzxxbViewResp.setQrdssxqdm( dto.getQrdssxqdm() );
        ksywZqzxxbViewResp.setQrdqhnxxdz( dto.getQrdqhnxxdz() );
        ksywZqzxxbViewResp.setQrdhkdjjggajgjgdm( dto.getQrdhkdjjggajgjgdm() );
        ksywZqzxxbViewResp.setQrdhkdjjggajgmc( dto.getQrdhkdjjggajgmc() );
        ksywZqzxxbViewResp.setQfjggajgjgdm( dto.getQfjggajgjgdm() );
        ksywZqzxxbViewResp.setQfjggajgmc( dto.getQfjggajgmc() );
        ksywZqzxxbViewResp.setCbrxm( dto.getCbrxm() );
        ksywZqzxxbViewResp.setQfrq( dto.getQfrq() );
        ksywZqzxxbViewResp.setBz( dto.getBz() );
        ksywZqzxxbViewResp.setQyldyydm( dto.getQyldyydm() );
        ksywZqzxxbViewResp.setQyldyymcnb( dto.getQyldyymcnb() );
        ksywZqzxxbViewResp.setYxqjzrq( dto.getYxqjzrq() );
        ksywZqzxxbViewResp.setQyfwdm( dto.getQyfwdm() );
        ksywZqzxxbViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        ksywZqzxxbViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        ksywZqzxxbViewResp.setSldwlxdh( dto.getSldwlxdh() );
        ksywZqzxxbViewResp.setSlrxm( dto.getSlrxm() );
        ksywZqzxxbViewResp.setSlsj( dto.getSlsj() );
        ksywZqzxxbViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        ksywZqzxxbViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        ksywZqzxxbViewResp.setQyr1ysqrgxjtgxdm( dto.getQyr1ysqrgxjtgxdm() );
        ksywZqzxxbViewResp.setQyr1gmsfhm( dto.getQyr1gmsfhm() );
        ksywZqzxxbViewResp.setQyr1xm( dto.getQyr1xm() );
        ksywZqzxxbViewResp.setQyr1xbdm( dto.getQyr1xbdm() );
        ksywZqzxxbViewResp.setQyr1csrq( dto.getQyr1csrq() );
        ksywZqzxxbViewResp.setQyr2ysqrgxjtgxdm( dto.getQyr2ysqrgxjtgxdm() );
        ksywZqzxxbViewResp.setQyr2gmsfhm( dto.getQyr2gmsfhm() );
        ksywZqzxxbViewResp.setQyr2xm( dto.getQyr2xm() );
        ksywZqzxxbViewResp.setQyr2xbdm( dto.getQyr2xbdm() );
        ksywZqzxxbViewResp.setQyr2csrq( dto.getQyr2csrq() );
        ksywZqzxxbViewResp.setQyr3ysqrgxjtgxdm( dto.getQyr3ysqrgxjtgxdm() );
        ksywZqzxxbViewResp.setQyr3gmsfhm( dto.getQyr3gmsfhm() );
        ksywZqzxxbViewResp.setQyr3xm( dto.getQyr3xm() );
        ksywZqzxxbViewResp.setQyr3xbdm( dto.getQyr3xbdm() );
        ksywZqzxxbViewResp.setQyr3csrq( dto.getQyr3csrq() );
        ksywZqzxxbViewResp.setQyr4ysqrgxjtgxdm( dto.getQyr4ysqrgxjtgxdm() );
        ksywZqzxxbViewResp.setQyr4gmsfhm( dto.getQyr4gmsfhm() );
        ksywZqzxxbViewResp.setQyr4xm( dto.getQyr4xm() );
        ksywZqzxxbViewResp.setQyr4xbdm( dto.getQyr4xbdm() );
        ksywZqzxxbViewResp.setQyr4csrq( dto.getQyr4csrq() );
        ksywZqzxxbViewResp.setRksj( dto.getRksj() );
        ksywZqzxxbViewResp.setPcs( dto.getPcs() );

        return ksywZqzxxbViewResp;
    }
}
