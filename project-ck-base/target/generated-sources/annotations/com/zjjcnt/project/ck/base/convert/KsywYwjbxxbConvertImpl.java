package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsywYwjbxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsywYwjbxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsywYwjbxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsywYwjbxxbViewResp;
import com.zjjcnt.project.ck.base.entity.KsywYwjbxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:43+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsywYwjbxxbConvertImpl implements KsywYwjbxxbConvert {

    @Override
    public KsywYwjbxxbDTO convert(KsywYwjbxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsywYwjbxxbDTO ksywYwjbxxbDTO = new KsywYwjbxxbDTO();

        ksywYwjbxxbDTO.setId( entity.getId() );
        ksywYwjbxxbDTO.setKsywid( entity.getKsywid() );
        ksywYwjbxxbDTO.setPcs( entity.getPcs() );
        ksywYwjbxxbDTO.setYwlx( entity.getYwlx() );
        ksywYwjbxxbDTO.setYwlysf( entity.getYwlysf() );
        ksywYwjbxxbDTO.setYwslh( entity.getYwslh() );
        ksywYwjbxxbDTO.setZqzbh( entity.getZqzbh() );
        ksywYwjbxxbDTO.setQyzbh( entity.getQyzbh() );
        ksywYwjbxxbDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        ksywYwjbxxbDTO.setSqrxm( entity.getSqrxm() );
        ksywYwjbxxbDTO.setSqrlxdh( entity.getSqrlxdh() );
        ksywYwjbxxbDTO.setQcdssxqdm( entity.getQcdssxqdm() );
        ksywYwjbxxbDTO.setQrdssxqdm( entity.getQrdssxqdm() );
        ksywYwjbxxbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        ksywYwjbxxbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        ksywYwjbxxbDTO.setHjdlxdh( entity.getHjdlxdh() );
        ksywYwjbxxbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        ksywYwjbxxbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        ksywYwjbxxbDTO.setSldlxdh( entity.getSldlxdh() );
        ksywYwjbxxbDTO.setBlzt( entity.getBlzt() );
        ksywYwjbxxbDTO.setRksj( entity.getRksj() );
        ksywYwjbxxbDTO.setShjg( entity.getShjg() );
        ksywYwjbxxbDTO.setShms( entity.getShms() );
        ksywYwjbxxbDTO.setShsj( entity.getShsj() );
        ksywYwjbxxbDTO.setShrxm( entity.getShrxm() );
        ksywYwjbxxbDTO.setYwblsj( entity.getYwblsj() );
        ksywYwjbxxbDTO.setYwblrxm( entity.getYwblrxm() );
        ksywYwjbxxbDTO.setJssj( entity.getJssj() );
        ksywYwjbxxbDTO.setSpywlsh( entity.getSpywlsh() );
        ksywYwjbxxbDTO.setSpywlx( entity.getSpywlx() );
        ksywYwjbxxbDTO.setMsg( entity.getMsg() );
        ksywYwjbxxbDTO.setSjrxm( entity.getSjrxm() );
        ksywYwjbxxbDTO.setSjrgmsfhm( entity.getSjrgmsfhm() );
        ksywYwjbxxbDTO.setSjrlxdh( entity.getSjrlxdh() );
        ksywYwjbxxbDTO.setSjdzssxqdm( entity.getSjdzssxqdm() );
        ksywYwjbxxbDTO.setSjdzqhnxxdz( entity.getSjdzqhnxxdz() );

        return ksywYwjbxxbDTO;
    }

    @Override
    public KsywYwjbxxbDO convertToDO(KsywYwjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywYwjbxxbDO ksywYwjbxxbDO = new KsywYwjbxxbDO();

        ksywYwjbxxbDO.setId( dto.getId() );
        ksywYwjbxxbDO.setKsywid( dto.getKsywid() );
        ksywYwjbxxbDO.setPcs( dto.getPcs() );
        ksywYwjbxxbDO.setYwlx( dto.getYwlx() );
        ksywYwjbxxbDO.setYwlysf( dto.getYwlysf() );
        ksywYwjbxxbDO.setYwslh( dto.getYwslh() );
        ksywYwjbxxbDO.setZqzbh( dto.getZqzbh() );
        ksywYwjbxxbDO.setQyzbh( dto.getQyzbh() );
        ksywYwjbxxbDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksywYwjbxxbDO.setSqrxm( dto.getSqrxm() );
        ksywYwjbxxbDO.setSqrlxdh( dto.getSqrlxdh() );
        ksywYwjbxxbDO.setQcdssxqdm( dto.getQcdssxqdm() );
        ksywYwjbxxbDO.setQrdssxqdm( dto.getQrdssxqdm() );
        ksywYwjbxxbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksywYwjbxxbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksywYwjbxxbDO.setHjdlxdh( dto.getHjdlxdh() );
        ksywYwjbxxbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksywYwjbxxbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksywYwjbxxbDO.setSldlxdh( dto.getSldlxdh() );
        ksywYwjbxxbDO.setBlzt( dto.getBlzt() );
        ksywYwjbxxbDO.setRksj( dto.getRksj() );
        ksywYwjbxxbDO.setShjg( dto.getShjg() );
        ksywYwjbxxbDO.setShms( dto.getShms() );
        ksywYwjbxxbDO.setShsj( dto.getShsj() );
        ksywYwjbxxbDO.setShrxm( dto.getShrxm() );
        ksywYwjbxxbDO.setYwblsj( dto.getYwblsj() );
        ksywYwjbxxbDO.setYwblrxm( dto.getYwblrxm() );
        ksywYwjbxxbDO.setJssj( dto.getJssj() );
        ksywYwjbxxbDO.setSpywlsh( dto.getSpywlsh() );
        ksywYwjbxxbDO.setSpywlx( dto.getSpywlx() );
        ksywYwjbxxbDO.setMsg( dto.getMsg() );
        ksywYwjbxxbDO.setSjrxm( dto.getSjrxm() );
        ksywYwjbxxbDO.setSjrgmsfhm( dto.getSjrgmsfhm() );
        ksywYwjbxxbDO.setSjrlxdh( dto.getSjrlxdh() );
        ksywYwjbxxbDO.setSjdzssxqdm( dto.getSjdzssxqdm() );
        ksywYwjbxxbDO.setSjdzqhnxxdz( dto.getSjdzqhnxxdz() );

        return ksywYwjbxxbDO;
    }

    @Override
    public KsywYwjbxxbDTO convertToDTO(KsywYwjbxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsywYwjbxxbDTO ksywYwjbxxbDTO = new KsywYwjbxxbDTO();

        ksywYwjbxxbDTO.setPcs( req.getPcs() );
        ksywYwjbxxbDTO.setYwlx( req.getYwlx() );
        ksywYwjbxxbDTO.setYwlysf( req.getYwlysf() );
        ksywYwjbxxbDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        ksywYwjbxxbDTO.setSqrxm( req.getSqrxm() );
        ksywYwjbxxbDTO.setBlzt( req.getBlzt() );
        ksywYwjbxxbDTO.setRksjStart( req.getRksjStart() );
        ksywYwjbxxbDTO.setRksjEnd( req.getRksjEnd() );

        return ksywYwjbxxbDTO;
    }

    @Override
    public KsywYwjbxxbPageResp convertToPageResp(KsywYwjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywYwjbxxbPageResp ksywYwjbxxbPageResp = new KsywYwjbxxbPageResp();

        ksywYwjbxxbPageResp.setKsywid( dto.getKsywid() );
        ksywYwjbxxbPageResp.setPcs( dto.getPcs() );
        ksywYwjbxxbPageResp.setYwlx( dto.getYwlx() );
        ksywYwjbxxbPageResp.setYwlysf( dto.getYwlysf() );
        ksywYwjbxxbPageResp.setYwslh( dto.getYwslh() );
        ksywYwjbxxbPageResp.setZqzbh( dto.getZqzbh() );
        ksywYwjbxxbPageResp.setQyzbh( dto.getQyzbh() );
        ksywYwjbxxbPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksywYwjbxxbPageResp.setSqrxm( dto.getSqrxm() );
        ksywYwjbxxbPageResp.setSqrlxdh( dto.getSqrlxdh() );
        ksywYwjbxxbPageResp.setQcdssxqdm( dto.getQcdssxqdm() );
        ksywYwjbxxbPageResp.setQrdssxqdm( dto.getQrdssxqdm() );
        ksywYwjbxxbPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksywYwjbxxbPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksywYwjbxxbPageResp.setHjdlxdh( dto.getHjdlxdh() );
        ksywYwjbxxbPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksywYwjbxxbPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksywYwjbxxbPageResp.setSldlxdh( dto.getSldlxdh() );
        ksywYwjbxxbPageResp.setBlzt( dto.getBlzt() );
        ksywYwjbxxbPageResp.setRksj( dto.getRksj() );
        ksywYwjbxxbPageResp.setShjg( dto.getShjg() );
        ksywYwjbxxbPageResp.setShms( dto.getShms() );
        ksywYwjbxxbPageResp.setShsj( dto.getShsj() );
        ksywYwjbxxbPageResp.setShrxm( dto.getShrxm() );
        ksywYwjbxxbPageResp.setYwblsj( dto.getYwblsj() );
        ksywYwjbxxbPageResp.setYwblrxm( dto.getYwblrxm() );
        ksywYwjbxxbPageResp.setJssj( dto.getJssj() );
        ksywYwjbxxbPageResp.setSpywlsh( dto.getSpywlsh() );
        ksywYwjbxxbPageResp.setSpywlx( dto.getSpywlx() );
        ksywYwjbxxbPageResp.setMsg( dto.getMsg() );
        ksywYwjbxxbPageResp.setSjrxm( dto.getSjrxm() );
        ksywYwjbxxbPageResp.setSjrgmsfhm( dto.getSjrgmsfhm() );
        ksywYwjbxxbPageResp.setSjrlxdh( dto.getSjrlxdh() );
        ksywYwjbxxbPageResp.setSjdzssxqdm( dto.getSjdzssxqdm() );
        ksywYwjbxxbPageResp.setSjdzqhnxxdz( dto.getSjdzqhnxxdz() );

        return ksywYwjbxxbPageResp;
    }

    @Override
    public KsywYwjbxxbViewResp convertToViewResp(KsywYwjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywYwjbxxbViewResp ksywYwjbxxbViewResp = new KsywYwjbxxbViewResp();

        ksywYwjbxxbViewResp.setKsywid( dto.getKsywid() );
        ksywYwjbxxbViewResp.setPcs( dto.getPcs() );
        ksywYwjbxxbViewResp.setYwlx( dto.getYwlx() );
        ksywYwjbxxbViewResp.setYwlysf( dto.getYwlysf() );
        ksywYwjbxxbViewResp.setYwslh( dto.getYwslh() );
        ksywYwjbxxbViewResp.setZqzbh( dto.getZqzbh() );
        ksywYwjbxxbViewResp.setQyzbh( dto.getQyzbh() );
        ksywYwjbxxbViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksywYwjbxxbViewResp.setSqrxm( dto.getSqrxm() );
        ksywYwjbxxbViewResp.setSqrlxdh( dto.getSqrlxdh() );
        ksywYwjbxxbViewResp.setQcdssxqdm( dto.getQcdssxqdm() );
        ksywYwjbxxbViewResp.setQrdssxqdm( dto.getQrdssxqdm() );
        ksywYwjbxxbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksywYwjbxxbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksywYwjbxxbViewResp.setHjdlxdh( dto.getHjdlxdh() );
        ksywYwjbxxbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksywYwjbxxbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksywYwjbxxbViewResp.setSldlxdh( dto.getSldlxdh() );
        ksywYwjbxxbViewResp.setBlzt( dto.getBlzt() );
        ksywYwjbxxbViewResp.setRksj( dto.getRksj() );
        ksywYwjbxxbViewResp.setShjg( dto.getShjg() );
        ksywYwjbxxbViewResp.setShms( dto.getShms() );
        ksywYwjbxxbViewResp.setShsj( dto.getShsj() );
        ksywYwjbxxbViewResp.setShrxm( dto.getShrxm() );
        ksywYwjbxxbViewResp.setYwblsj( dto.getYwblsj() );
        ksywYwjbxxbViewResp.setYwblrxm( dto.getYwblrxm() );
        ksywYwjbxxbViewResp.setJssj( dto.getJssj() );
        ksywYwjbxxbViewResp.setSpywlsh( dto.getSpywlsh() );
        ksywYwjbxxbViewResp.setSpywlx( dto.getSpywlx() );
        ksywYwjbxxbViewResp.setMsg( dto.getMsg() );
        ksywYwjbxxbViewResp.setSjrxm( dto.getSjrxm() );
        ksywYwjbxxbViewResp.setSjrgmsfhm( dto.getSjrgmsfhm() );
        ksywYwjbxxbViewResp.setSjrlxdh( dto.getSjrlxdh() );
        ksywYwjbxxbViewResp.setSjdzssxqdm( dto.getSjdzssxqdm() );
        ksywYwjbxxbViewResp.setSjdzqhnxxdz( dto.getSjdzqhnxxdz() );

        return ksywYwjbxxbViewResp;
    }
}
