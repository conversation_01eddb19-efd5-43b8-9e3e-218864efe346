package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.JkWjSjDTO;
import com.zjjcnt.project.ck.base.entity.JkWjSjDO;
import java.util.Arrays;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class JkWjSjConvertImpl implements JkWjSjConvert {

    @Override
    public JkWjSjDTO convert(JkWjSjDO entity) {
        if ( entity == null ) {
            return null;
        }

        byte[] data = null;
        String type = null;

        JkWjSjDTO jkWjSjDTO = new JkWjSjDTO( data, type );

        jkWjSjDTO.setId( entity.getId() );
        jkWjSjDTO.setWjsjbh( entity.getWjsjbh() );
        byte[] wjsj = entity.getWjsj();
        if ( wjsj != null ) {
            jkWjSjDTO.setWjsj( Arrays.copyOf( wjsj, wjsj.length ) );
        }
        jkWjSjDTO.setCjsj( entity.getCjsj() );
        jkWjSjDTO.setBz( entity.getBz() );
        jkWjSjDTO.setFiletype( entity.getFiletype() );
        jkWjSjDTO.setSize( entity.getSize() );

        return jkWjSjDTO;
    }

    @Override
    public JkWjSjDO convertToDO(JkWjSjDTO dto) {
        if ( dto == null ) {
            return null;
        }

        JkWjSjDO jkWjSjDO = new JkWjSjDO();

        jkWjSjDO.setId( dto.getId() );
        jkWjSjDO.setWjsjbh( dto.getWjsjbh() );
        byte[] wjsj = dto.getWjsj();
        if ( wjsj != null ) {
            jkWjSjDO.setWjsj( Arrays.copyOf( wjsj, wjsj.length ) );
        }
        jkWjSjDO.setCjsj( dto.getCjsj() );
        jkWjSjDO.setBz( dto.getBz() );
        jkWjSjDO.setFiletype( dto.getFiletype() );
        jkWjSjDO.setSize( dto.getSize() );

        return jkWjSjDO;
    }
}
