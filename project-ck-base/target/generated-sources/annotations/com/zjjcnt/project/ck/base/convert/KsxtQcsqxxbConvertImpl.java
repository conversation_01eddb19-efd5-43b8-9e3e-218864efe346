package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsxtQcsqxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtQcsqxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtQcsqxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtQcsqxxbViewResp;
import com.zjjcnt.project.ck.base.entity.KsxtQcsqxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsxtQcsqxxbConvertImpl implements KsxtQcsqxxbConvert {

    @Override
    public KsxtQcsqxxbDTO convert(KsxtQcsqxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsxtQcsqxxbDTO ksxtQcsqxxbDTO = new KsxtQcsqxxbDTO();

        ksxtQcsqxxbDTO.setId( entity.getId() );
        ksxtQcsqxxbDTO.setKsxtqcsqid( entity.getKsxtqcsqid() );
        ksxtQcsqxxbDTO.setKsxtid( entity.getKsxtid() );
        ksxtQcsqxxbDTO.setZaglywxtbh( entity.getZaglywxtbh() );
        ksxtQcsqxxbDTO.setZaglywlbdm( entity.getZaglywlbdm() );
        ksxtQcsqxxbDTO.setZaglzwfwsxbm( entity.getZaglzwfwsxbm() );
        ksxtQcsqxxbDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        ksxtQcsqxxbDTO.setSqrxm( entity.getSqrxm() );
        ksxtQcsqxxbDTO.setSqrlxdh( entity.getSqrlxdh() );
        ksxtQcsqxxbDTO.setSqrzzssxqdm( entity.getSqrzzssxqdm() );
        ksxtQcsqxxbDTO.setSqrzzqhnxxdz( entity.getSqrzzqhnxxdz() );
        ksxtQcsqxxbDTO.setSqrhkdjjggajgjgdm( entity.getSqrhkdjjggajgjgdm() );
        ksxtQcsqxxbDTO.setSqrhkdjjggajgmc( entity.getSqrhkdjjggajgmc() );
        ksxtQcsqxxbDTO.setQcdssxqdm( entity.getQcdssxqdm() );
        ksxtQcsqxxbDTO.setQcdqhnxxdz( entity.getQcdqhnxxdz() );
        ksxtQcsqxxbDTO.setQcdhkdjjggajgjgdm( entity.getQcdhkdjjggajgjgdm() );
        ksxtQcsqxxbDTO.setQcdhkdjjggajgmc( entity.getQcdhkdjjggajgmc() );
        ksxtQcsqxxbDTO.setQcdsjgsdwdm( entity.getQcdsjgsdwdm() );
        ksxtQcsqxxbDTO.setQcdsjgsdwmc( entity.getQcdsjgsdwmc() );
        ksxtQcsqxxbDTO.setQrdssxqdm( entity.getQrdssxqdm() );
        ksxtQcsqxxbDTO.setQrdqhnxxdz( entity.getQrdqhnxxdz() );
        ksxtQcsqxxbDTO.setQrdhkdjjggajgjgdm( entity.getQrdhkdjjggajgjgdm() );
        ksxtQcsqxxbDTO.setQrdhkdjjggajgmc( entity.getQrdhkdjjggajgmc() );
        ksxtQcsqxxbDTO.setZqzbh( entity.getZqzbh() );
        ksxtQcsqxxbDTO.setZqzqfjggajgjgdm( entity.getZqzqfjggajgjgdm() );
        ksxtQcsqxxbDTO.setZqzqfjggajgmc( entity.getZqzqfjggajgmc() );
        ksxtQcsqxxbDTO.setZqzqfrq( entity.getZqzqfrq() );
        ksxtQcsqxxbDTO.setZqzyxqjzrq( entity.getZqzyxqjzrq() );
        ksxtQcsqxxbDTO.setDzzqdzzzbz( entity.getDzzqdzzzbz() );
        ksxtQcsqxxbDTO.setDzzqzagldzzzbh( entity.getDzzqzagldzzzbh() );
        ksxtQcsqxxbDTO.setBz( entity.getBz() );
        ksxtQcsqxxbDTO.setQyldyydm( entity.getQyldyydm() );
        ksxtQcsqxxbDTO.setYsqrgxjtgxdm( entity.getYsqrgxjtgxdm() );
        ksxtQcsqxxbDTO.setGmsfhm( entity.getGmsfhm() );
        ksxtQcsqxxbDTO.setXm( entity.getXm() );
        ksxtQcsqxxbDTO.setXbdm( entity.getXbdm() );
        ksxtQcsqxxbDTO.setCsrq( entity.getCsrq() );
        ksxtQcsqxxbDTO.setSldgajgjgdm( entity.getSldgajgjgdm() );
        ksxtQcsqxxbDTO.setSldgajgmc( entity.getSldgajgmc() );
        ksxtQcsqxxbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        ksxtQcsqxxbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        ksxtQcsqxxbDTO.setSldlxdh( entity.getSldlxdh() );
        ksxtQcsqxxbDTO.setSlrxm( entity.getSlrxm() );
        ksxtQcsqxxbDTO.setSlsj( entity.getSlsj() );
        ksxtQcsqxxbDTO.setGdpzbbh( entity.getGdpzbbh() );
        ksxtQcsqxxbDTO.setQyfwdm( entity.getQyfwdm() );
        ksxtQcsqxxbDTO.setFsdwsjgsdwdm( entity.getFsdwsjgsdwdm() );
        ksxtQcsqxxbDTO.setFsdwsjgsdwmc( entity.getFsdwsjgsdwmc() );
        ksxtQcsqxxbDTO.setJsdwsjgsdwdm( entity.getJsdwsjgsdwdm() );
        ksxtQcsqxxbDTO.setJsdwsjgsdwmc( entity.getJsdwsjgsdwmc() );
        ksxtQcsqxxbDTO.setRksj( entity.getRksj() );
        ksxtQcsqxxbDTO.setHjqcbz( entity.getHjqcbz() );
        ksxtQcsqxxbDTO.setSpywslh( entity.getSpywslh() );
        ksxtQcsqxxbDTO.setSpywslsj( entity.getSpywslsj() );

        return ksxtQcsqxxbDTO;
    }

    @Override
    public KsxtQcsqxxbDO convertToDO(KsxtQcsqxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtQcsqxxbDO ksxtQcsqxxbDO = new KsxtQcsqxxbDO();

        ksxtQcsqxxbDO.setId( dto.getId() );
        ksxtQcsqxxbDO.setKsxtqcsqid( dto.getKsxtqcsqid() );
        ksxtQcsqxxbDO.setKsxtid( dto.getKsxtid() );
        ksxtQcsqxxbDO.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtQcsqxxbDO.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtQcsqxxbDO.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        ksxtQcsqxxbDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksxtQcsqxxbDO.setSqrxm( dto.getSqrxm() );
        ksxtQcsqxxbDO.setSqrlxdh( dto.getSqrlxdh() );
        ksxtQcsqxxbDO.setSqrzzssxqdm( dto.getSqrzzssxqdm() );
        ksxtQcsqxxbDO.setSqrzzqhnxxdz( dto.getSqrzzqhnxxdz() );
        ksxtQcsqxxbDO.setSqrhkdjjggajgjgdm( dto.getSqrhkdjjggajgjgdm() );
        ksxtQcsqxxbDO.setSqrhkdjjggajgmc( dto.getSqrhkdjjggajgmc() );
        ksxtQcsqxxbDO.setQcdssxqdm( dto.getQcdssxqdm() );
        ksxtQcsqxxbDO.setQcdqhnxxdz( dto.getQcdqhnxxdz() );
        ksxtQcsqxxbDO.setQcdhkdjjggajgjgdm( dto.getQcdhkdjjggajgjgdm() );
        ksxtQcsqxxbDO.setQcdhkdjjggajgmc( dto.getQcdhkdjjggajgmc() );
        ksxtQcsqxxbDO.setQcdsjgsdwdm( dto.getQcdsjgsdwdm() );
        ksxtQcsqxxbDO.setQcdsjgsdwmc( dto.getQcdsjgsdwmc() );
        ksxtQcsqxxbDO.setQrdssxqdm( dto.getQrdssxqdm() );
        ksxtQcsqxxbDO.setQrdqhnxxdz( dto.getQrdqhnxxdz() );
        ksxtQcsqxxbDO.setQrdhkdjjggajgjgdm( dto.getQrdhkdjjggajgjgdm() );
        ksxtQcsqxxbDO.setQrdhkdjjggajgmc( dto.getQrdhkdjjggajgmc() );
        ksxtQcsqxxbDO.setZqzbh( dto.getZqzbh() );
        ksxtQcsqxxbDO.setZqzqfjggajgjgdm( dto.getZqzqfjggajgjgdm() );
        ksxtQcsqxxbDO.setZqzqfjggajgmc( dto.getZqzqfjggajgmc() );
        ksxtQcsqxxbDO.setZqzqfrq( dto.getZqzqfrq() );
        ksxtQcsqxxbDO.setZqzyxqjzrq( dto.getZqzyxqjzrq() );
        ksxtQcsqxxbDO.setDzzqdzzzbz( dto.getDzzqdzzzbz() );
        ksxtQcsqxxbDO.setDzzqzagldzzzbh( dto.getDzzqzagldzzzbh() );
        ksxtQcsqxxbDO.setBz( dto.getBz() );
        ksxtQcsqxxbDO.setQyldyydm( dto.getQyldyydm() );
        ksxtQcsqxxbDO.setYsqrgxjtgxdm( dto.getYsqrgxjtgxdm() );
        ksxtQcsqxxbDO.setGmsfhm( dto.getGmsfhm() );
        ksxtQcsqxxbDO.setXm( dto.getXm() );
        ksxtQcsqxxbDO.setXbdm( dto.getXbdm() );
        ksxtQcsqxxbDO.setCsrq( dto.getCsrq() );
        ksxtQcsqxxbDO.setSldgajgjgdm( dto.getSldgajgjgdm() );
        ksxtQcsqxxbDO.setSldgajgmc( dto.getSldgajgmc() );
        ksxtQcsqxxbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtQcsqxxbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtQcsqxxbDO.setSldlxdh( dto.getSldlxdh() );
        ksxtQcsqxxbDO.setSlrxm( dto.getSlrxm() );
        ksxtQcsqxxbDO.setSlsj( dto.getSlsj() );
        ksxtQcsqxxbDO.setGdpzbbh( dto.getGdpzbbh() );
        ksxtQcsqxxbDO.setQyfwdm( dto.getQyfwdm() );
        ksxtQcsqxxbDO.setFsdwsjgsdwdm( dto.getFsdwsjgsdwdm() );
        ksxtQcsqxxbDO.setFsdwsjgsdwmc( dto.getFsdwsjgsdwmc() );
        ksxtQcsqxxbDO.setJsdwsjgsdwdm( dto.getJsdwsjgsdwdm() );
        ksxtQcsqxxbDO.setJsdwsjgsdwmc( dto.getJsdwsjgsdwmc() );
        ksxtQcsqxxbDO.setRksj( dto.getRksj() );
        ksxtQcsqxxbDO.setHjqcbz( dto.getHjqcbz() );
        ksxtQcsqxxbDO.setSpywslh( dto.getSpywslh() );
        ksxtQcsqxxbDO.setSpywslsj( dto.getSpywslsj() );

        return ksxtQcsqxxbDO;
    }

    @Override
    public KsxtQcsqxxbDTO convertToDTO(KsxtQcsqxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsxtQcsqxxbDTO ksxtQcsqxxbDTO = new KsxtQcsqxxbDTO();

        ksxtQcsqxxbDTO.setKsxtid( req.getKsxtid() );

        return ksxtQcsqxxbDTO;
    }

    @Override
    public KsxtQcsqxxbPageResp convertToPageResp(KsxtQcsqxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtQcsqxxbPageResp ksxtQcsqxxbPageResp = new KsxtQcsqxxbPageResp();

        ksxtQcsqxxbPageResp.setKsxtqcsqid( dto.getKsxtqcsqid() );
        ksxtQcsqxxbPageResp.setKsxtid( dto.getKsxtid() );
        ksxtQcsqxxbPageResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtQcsqxxbPageResp.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtQcsqxxbPageResp.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        ksxtQcsqxxbPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksxtQcsqxxbPageResp.setSqrxm( dto.getSqrxm() );
        ksxtQcsqxxbPageResp.setSqrlxdh( dto.getSqrlxdh() );
        ksxtQcsqxxbPageResp.setSqrzzssxqdm( dto.getSqrzzssxqdm() );
        ksxtQcsqxxbPageResp.setSqrzzqhnxxdz( dto.getSqrzzqhnxxdz() );
        ksxtQcsqxxbPageResp.setSqrhkdjjggajgjgdm( dto.getSqrhkdjjggajgjgdm() );
        ksxtQcsqxxbPageResp.setSqrhkdjjggajgmc( dto.getSqrhkdjjggajgmc() );
        ksxtQcsqxxbPageResp.setQcdssxqdm( dto.getQcdssxqdm() );
        ksxtQcsqxxbPageResp.setQcdqhnxxdz( dto.getQcdqhnxxdz() );
        ksxtQcsqxxbPageResp.setQcdhkdjjggajgjgdm( dto.getQcdhkdjjggajgjgdm() );
        ksxtQcsqxxbPageResp.setQcdhkdjjggajgmc( dto.getQcdhkdjjggajgmc() );
        ksxtQcsqxxbPageResp.setQcdsjgsdwdm( dto.getQcdsjgsdwdm() );
        ksxtQcsqxxbPageResp.setQcdsjgsdwmc( dto.getQcdsjgsdwmc() );
        ksxtQcsqxxbPageResp.setQrdssxqdm( dto.getQrdssxqdm() );
        ksxtQcsqxxbPageResp.setQrdqhnxxdz( dto.getQrdqhnxxdz() );
        ksxtQcsqxxbPageResp.setQrdhkdjjggajgjgdm( dto.getQrdhkdjjggajgjgdm() );
        ksxtQcsqxxbPageResp.setQrdhkdjjggajgmc( dto.getQrdhkdjjggajgmc() );
        ksxtQcsqxxbPageResp.setZqzbh( dto.getZqzbh() );
        ksxtQcsqxxbPageResp.setZqzqfjggajgjgdm( dto.getZqzqfjggajgjgdm() );
        ksxtQcsqxxbPageResp.setZqzqfjggajgmc( dto.getZqzqfjggajgmc() );
        ksxtQcsqxxbPageResp.setZqzqfrq( dto.getZqzqfrq() );
        ksxtQcsqxxbPageResp.setZqzyxqjzrq( dto.getZqzyxqjzrq() );
        ksxtQcsqxxbPageResp.setDzzqdzzzbz( dto.getDzzqdzzzbz() );
        ksxtQcsqxxbPageResp.setDzzqzagldzzzbh( dto.getDzzqzagldzzzbh() );
        ksxtQcsqxxbPageResp.setBz( dto.getBz() );
        ksxtQcsqxxbPageResp.setQyldyydm( dto.getQyldyydm() );
        ksxtQcsqxxbPageResp.setYsqrgxjtgxdm( dto.getYsqrgxjtgxdm() );
        ksxtQcsqxxbPageResp.setGmsfhm( dto.getGmsfhm() );
        ksxtQcsqxxbPageResp.setXm( dto.getXm() );
        ksxtQcsqxxbPageResp.setXbdm( dto.getXbdm() );
        ksxtQcsqxxbPageResp.setCsrq( dto.getCsrq() );
        ksxtQcsqxxbPageResp.setSldgajgjgdm( dto.getSldgajgjgdm() );
        ksxtQcsqxxbPageResp.setSldgajgmc( dto.getSldgajgmc() );
        ksxtQcsqxxbPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtQcsqxxbPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtQcsqxxbPageResp.setSldlxdh( dto.getSldlxdh() );
        ksxtQcsqxxbPageResp.setSlrxm( dto.getSlrxm() );
        ksxtQcsqxxbPageResp.setSlsj( dto.getSlsj() );
        ksxtQcsqxxbPageResp.setGdpzbbh( dto.getGdpzbbh() );
        ksxtQcsqxxbPageResp.setQyfwdm( dto.getQyfwdm() );
        ksxtQcsqxxbPageResp.setFsdwsjgsdwdm( dto.getFsdwsjgsdwdm() );
        ksxtQcsqxxbPageResp.setFsdwsjgsdwmc( dto.getFsdwsjgsdwmc() );
        ksxtQcsqxxbPageResp.setJsdwsjgsdwdm( dto.getJsdwsjgsdwdm() );
        ksxtQcsqxxbPageResp.setJsdwsjgsdwmc( dto.getJsdwsjgsdwmc() );
        ksxtQcsqxxbPageResp.setRksj( dto.getRksj() );
        ksxtQcsqxxbPageResp.setHjqcbz( dto.getHjqcbz() );
        ksxtQcsqxxbPageResp.setSpywslh( dto.getSpywslh() );
        ksxtQcsqxxbPageResp.setSpywslsj( dto.getSpywslsj() );

        return ksxtQcsqxxbPageResp;
    }

    @Override
    public KsxtQcsqxxbViewResp convertToViewResp(KsxtQcsqxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtQcsqxxbViewResp ksxtQcsqxxbViewResp = new KsxtQcsqxxbViewResp();

        ksxtQcsqxxbViewResp.setKsxtqcsqid( dto.getKsxtqcsqid() );
        ksxtQcsqxxbViewResp.setKsxtid( dto.getKsxtid() );
        ksxtQcsqxxbViewResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtQcsqxxbViewResp.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtQcsqxxbViewResp.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        ksxtQcsqxxbViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksxtQcsqxxbViewResp.setSqrxm( dto.getSqrxm() );
        ksxtQcsqxxbViewResp.setSqrlxdh( dto.getSqrlxdh() );
        ksxtQcsqxxbViewResp.setSqrzzssxqdm( dto.getSqrzzssxqdm() );
        ksxtQcsqxxbViewResp.setSqrzzqhnxxdz( dto.getSqrzzqhnxxdz() );
        ksxtQcsqxxbViewResp.setSqrhkdjjggajgjgdm( dto.getSqrhkdjjggajgjgdm() );
        ksxtQcsqxxbViewResp.setSqrhkdjjggajgmc( dto.getSqrhkdjjggajgmc() );
        ksxtQcsqxxbViewResp.setQcdssxqdm( dto.getQcdssxqdm() );
        ksxtQcsqxxbViewResp.setQcdqhnxxdz( dto.getQcdqhnxxdz() );
        ksxtQcsqxxbViewResp.setQcdhkdjjggajgjgdm( dto.getQcdhkdjjggajgjgdm() );
        ksxtQcsqxxbViewResp.setQcdhkdjjggajgmc( dto.getQcdhkdjjggajgmc() );
        ksxtQcsqxxbViewResp.setQcdsjgsdwdm( dto.getQcdsjgsdwdm() );
        ksxtQcsqxxbViewResp.setQcdsjgsdwmc( dto.getQcdsjgsdwmc() );
        ksxtQcsqxxbViewResp.setQrdssxqdm( dto.getQrdssxqdm() );
        ksxtQcsqxxbViewResp.setQrdqhnxxdz( dto.getQrdqhnxxdz() );
        ksxtQcsqxxbViewResp.setQrdhkdjjggajgjgdm( dto.getQrdhkdjjggajgjgdm() );
        ksxtQcsqxxbViewResp.setQrdhkdjjggajgmc( dto.getQrdhkdjjggajgmc() );
        ksxtQcsqxxbViewResp.setZqzbh( dto.getZqzbh() );
        ksxtQcsqxxbViewResp.setZqzqfjggajgjgdm( dto.getZqzqfjggajgjgdm() );
        ksxtQcsqxxbViewResp.setZqzqfjggajgmc( dto.getZqzqfjggajgmc() );
        ksxtQcsqxxbViewResp.setZqzqfrq( dto.getZqzqfrq() );
        ksxtQcsqxxbViewResp.setZqzyxqjzrq( dto.getZqzyxqjzrq() );
        ksxtQcsqxxbViewResp.setDzzqdzzzbz( dto.getDzzqdzzzbz() );
        ksxtQcsqxxbViewResp.setDzzqzagldzzzbh( dto.getDzzqzagldzzzbh() );
        ksxtQcsqxxbViewResp.setBz( dto.getBz() );
        ksxtQcsqxxbViewResp.setQyldyydm( dto.getQyldyydm() );
        ksxtQcsqxxbViewResp.setYsqrgxjtgxdm( dto.getYsqrgxjtgxdm() );
        ksxtQcsqxxbViewResp.setGmsfhm( dto.getGmsfhm() );
        ksxtQcsqxxbViewResp.setXm( dto.getXm() );
        ksxtQcsqxxbViewResp.setXbdm( dto.getXbdm() );
        ksxtQcsqxxbViewResp.setCsrq( dto.getCsrq() );
        ksxtQcsqxxbViewResp.setSldgajgjgdm( dto.getSldgajgjgdm() );
        ksxtQcsqxxbViewResp.setSldgajgmc( dto.getSldgajgmc() );
        ksxtQcsqxxbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtQcsqxxbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtQcsqxxbViewResp.setSldlxdh( dto.getSldlxdh() );
        ksxtQcsqxxbViewResp.setSlrxm( dto.getSlrxm() );
        ksxtQcsqxxbViewResp.setSlsj( dto.getSlsj() );
        ksxtQcsqxxbViewResp.setGdpzbbh( dto.getGdpzbbh() );
        ksxtQcsqxxbViewResp.setQyfwdm( dto.getQyfwdm() );
        ksxtQcsqxxbViewResp.setFsdwsjgsdwdm( dto.getFsdwsjgsdwdm() );
        ksxtQcsqxxbViewResp.setFsdwsjgsdwmc( dto.getFsdwsjgsdwmc() );
        ksxtQcsqxxbViewResp.setJsdwsjgsdwdm( dto.getJsdwsjgsdwdm() );
        ksxtQcsqxxbViewResp.setJsdwsjgsdwmc( dto.getJsdwsjgsdwmc() );
        ksxtQcsqxxbViewResp.setRksj( dto.getRksj() );
        ksxtQcsqxxbViewResp.setHjqcbz( dto.getHjqcbz() );
        ksxtQcsqxxbViewResp.setSpywslh( dto.getSpywslh() );
        ksxtQcsqxxbViewResp.setSpywslsj( dto.getSpywslsj() );

        return ksxtQcsqxxbViewResp;
    }
}
