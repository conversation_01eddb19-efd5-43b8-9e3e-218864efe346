package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsxtCssbxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtCssbxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtCssbxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtCssbxxbViewResp;
import com.zjjcnt.project.ck.base.entity.KsxtCssbxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:45+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsxtCssbxxbConvertImpl implements KsxtCssbxxbConvert {

    @Override
    public KsxtCssbxxbDTO convert(KsxtCssbxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsxtCssbxxbDTO ksxtCssbxxbDTO = new KsxtCssbxxbDTO();

        ksxtCssbxxbDTO.setId( entity.getId() );
        ksxtCssbxxbDTO.setKsxtcsid( entity.getKsxtcsid() );
        ksxtCssbxxbDTO.setKsxtid( entity.getKsxtid() );
        ksxtCssbxxbDTO.setZaglywxtbh( entity.getZaglywxtbh() );
        ksxtCssbxxbDTO.setXm( entity.getXm() );
        ksxtCssbxxbDTO.setX( entity.getX() );
        ksxtCssbxxbDTO.setM( entity.getM() );
        ksxtCssbxxbDTO.setXbdm( entity.getXbdm() );
        ksxtCssbxxbDTO.setMzdm( entity.getMzdm() );
        ksxtCssbxxbDTO.setCsrq( entity.getCsrq() );
        ksxtCssbxxbDTO.setCssj( entity.getCssj() );
        ksxtCssbxxbDTO.setCsdgjhdqdm( entity.getCsdgjhdqdm() );
        ksxtCssbxxbDTO.setCsdssxqdm( entity.getCsdssxqdm() );
        ksxtCssbxxbDTO.setCsdqhnxxdz( entity.getCsdqhnxxdz() );
        ksxtCssbxxbDTO.setJggjhdqdm( entity.getJggjhdqdm() );
        ksxtCssbxxbDTO.setJgssxqdm( entity.getJgssxqdm() );
        ksxtCssbxxbDTO.setJgqhnxxdz( entity.getJgqhnxxdz() );
        ksxtCssbxxbDTO.setXxdm( entity.getXxdm() );
        ksxtCssbxxbDTO.setRkxxjbdm( entity.getRkxxjbdm() );
        ksxtCssbxxbDTO.setCsdjlbdm( entity.getCsdjlbdm() );
        ksxtCssbxxbDTO.setCszmbh( entity.getCszmbh() );
        ksxtCssbxxbDTO.setYhzgxdm( entity.getYhzgxdm() );
        ksxtCssbxxbDTO.setHzxm( entity.getHzxm() );
        ksxtCssbxxbDTO.setHzgmsfhm( entity.getHzgmsfhm() );
        ksxtCssbxxbDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        ksxtCssbxxbDTO.setFqxm( entity.getFqxm() );
        ksxtCssbxxbDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        ksxtCssbxxbDTO.setFqzjhm( entity.getFqzjhm() );
        ksxtCssbxxbDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        ksxtCssbxxbDTO.setMqxm( entity.getMqxm() );
        ksxtCssbxxbDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        ksxtCssbxxbDTO.setMqzjhm( entity.getMqzjhm() );
        ksxtCssbxxbDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        ksxtCssbxxbDTO.setJhryxm( entity.getJhryxm() );
        ksxtCssbxxbDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        ksxtCssbxxbDTO.setJhryzjhm( entity.getJhryzjhm() );
        ksxtCssbxxbDTO.setJhrywwx( entity.getJhrywwx() );
        ksxtCssbxxbDTO.setJhrywwm( entity.getJhrywwm() );
        ksxtCssbxxbDTO.setJhryjhgxdm( entity.getJhryjhgxdm() );
        ksxtCssbxxbDTO.setJhrylxdh( entity.getJhrylxdh() );
        ksxtCssbxxbDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        ksxtCssbxxbDTO.setJhrexm( entity.getJhrexm() );
        ksxtCssbxxbDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        ksxtCssbxxbDTO.setJhrezjhm( entity.getJhrezjhm() );
        ksxtCssbxxbDTO.setJhrewwx( entity.getJhrewwx() );
        ksxtCssbxxbDTO.setJhrewwm( entity.getJhrewwm() );
        ksxtCssbxxbDTO.setJhrejhgxdm( entity.getJhrejhgxdm() );
        ksxtCssbxxbDTO.setJhrelxdh( entity.getJhrelxdh() );
        ksxtCssbxxbDTO.setSbrgmsfhm( entity.getSbrgmsfhm() );
        ksxtCssbxxbDTO.setSbrxm( entity.getSbrxm() );
        ksxtCssbxxbDTO.setSbrlxdh( entity.getSbrlxdh() );
        ksxtCssbxxbDTO.setSbrycsrgxjtgxdm( entity.getSbrycsrgxjtgxdm() );
        ksxtCssbxxbDTO.setXzzssxqdm( entity.getXzzssxqdm() );
        ksxtCssbxxbDTO.setXzzqhnxxdz( entity.getXzzqhnxxdz() );
        ksxtCssbxxbDTO.setSldgajgjgdm( entity.getSldgajgjgdm() );
        ksxtCssbxxbDTO.setSldgajgmc( entity.getSldgajgmc() );
        ksxtCssbxxbDTO.setSldlxdh( entity.getSldlxdh() );
        ksxtCssbxxbDTO.setSlrid( entity.getSlrid() );
        ksxtCssbxxbDTO.setSlrxm( entity.getSlrxm() );
        ksxtCssbxxbDTO.setSlsj( entity.getSlsj() );
        ksxtCssbxxbDTO.setZaglzwfwsxbm( entity.getZaglzwfwsxbm() );
        ksxtCssbxxbDTO.setZaglywlbdm( entity.getZaglywlbdm() );
        ksxtCssbxxbDTO.setGdpzbbh( entity.getGdpzbbh() );
        ksxtCssbxxbDTO.setQyfwdm( entity.getQyfwdm() );
        ksxtCssbxxbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        ksxtCssbxxbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        ksxtCssbxxbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        ksxtCssbxxbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        ksxtCssbxxbDTO.setRksj( entity.getRksj() );
        ksxtCssbxxbDTO.setPdbzsflh( entity.getPdbzsflh() );
        ksxtCssbxxbDTO.setPdbzcszmhmd( entity.getPdbzcszmhmd() );
        ksxtCssbxxbDTO.setBdjg( entity.getBdjg() );
        ksxtCssbxxbDTO.setBdms( entity.getBdms() );
        ksxtCssbxxbDTO.setBdsj( entity.getBdsj() );
        ksxtCssbxxbDTO.setBdrid( entity.getBdrid() );
        ksxtCssbxxbDTO.setBdrxm( entity.getBdrxm() );
        ksxtCssbxxbDTO.setBdrip( entity.getBdrip() );

        return ksxtCssbxxbDTO;
    }

    @Override
    public KsxtCssbxxbDO convertToDO(KsxtCssbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtCssbxxbDO ksxtCssbxxbDO = new KsxtCssbxxbDO();

        ksxtCssbxxbDO.setId( dto.getId() );
        ksxtCssbxxbDO.setKsxtcsid( dto.getKsxtcsid() );
        ksxtCssbxxbDO.setKsxtid( dto.getKsxtid() );
        ksxtCssbxxbDO.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtCssbxxbDO.setXm( dto.getXm() );
        ksxtCssbxxbDO.setX( dto.getX() );
        ksxtCssbxxbDO.setM( dto.getM() );
        ksxtCssbxxbDO.setXbdm( dto.getXbdm() );
        ksxtCssbxxbDO.setMzdm( dto.getMzdm() );
        ksxtCssbxxbDO.setCsrq( dto.getCsrq() );
        ksxtCssbxxbDO.setCssj( dto.getCssj() );
        ksxtCssbxxbDO.setCsdgjhdqdm( dto.getCsdgjhdqdm() );
        ksxtCssbxxbDO.setCsdssxqdm( dto.getCsdssxqdm() );
        ksxtCssbxxbDO.setCsdqhnxxdz( dto.getCsdqhnxxdz() );
        ksxtCssbxxbDO.setJggjhdqdm( dto.getJggjhdqdm() );
        ksxtCssbxxbDO.setJgssxqdm( dto.getJgssxqdm() );
        ksxtCssbxxbDO.setJgqhnxxdz( dto.getJgqhnxxdz() );
        ksxtCssbxxbDO.setXxdm( dto.getXxdm() );
        ksxtCssbxxbDO.setRkxxjbdm( dto.getRkxxjbdm() );
        ksxtCssbxxbDO.setCsdjlbdm( dto.getCsdjlbdm() );
        ksxtCssbxxbDO.setCszmbh( dto.getCszmbh() );
        ksxtCssbxxbDO.setYhzgxdm( dto.getYhzgxdm() );
        ksxtCssbxxbDO.setHzxm( dto.getHzxm() );
        ksxtCssbxxbDO.setHzgmsfhm( dto.getHzgmsfhm() );
        ksxtCssbxxbDO.setFqgmsfhm( dto.getFqgmsfhm() );
        ksxtCssbxxbDO.setFqxm( dto.getFqxm() );
        ksxtCssbxxbDO.setFqcyzjdm( dto.getFqcyzjdm() );
        ksxtCssbxxbDO.setFqzjhm( dto.getFqzjhm() );
        ksxtCssbxxbDO.setMqgmsfhm( dto.getMqgmsfhm() );
        ksxtCssbxxbDO.setMqxm( dto.getMqxm() );
        ksxtCssbxxbDO.setMqcyzjdm( dto.getMqcyzjdm() );
        ksxtCssbxxbDO.setMqzjhm( dto.getMqzjhm() );
        ksxtCssbxxbDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        ksxtCssbxxbDO.setJhryxm( dto.getJhryxm() );
        ksxtCssbxxbDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        ksxtCssbxxbDO.setJhryzjhm( dto.getJhryzjhm() );
        ksxtCssbxxbDO.setJhrywwx( dto.getJhrywwx() );
        ksxtCssbxxbDO.setJhrywwm( dto.getJhrywwm() );
        ksxtCssbxxbDO.setJhryjhgxdm( dto.getJhryjhgxdm() );
        ksxtCssbxxbDO.setJhrylxdh( dto.getJhrylxdh() );
        ksxtCssbxxbDO.setJhregmsfhm( dto.getJhregmsfhm() );
        ksxtCssbxxbDO.setJhrexm( dto.getJhrexm() );
        ksxtCssbxxbDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        ksxtCssbxxbDO.setJhrezjhm( dto.getJhrezjhm() );
        ksxtCssbxxbDO.setJhrewwx( dto.getJhrewwx() );
        ksxtCssbxxbDO.setJhrewwm( dto.getJhrewwm() );
        ksxtCssbxxbDO.setJhrejhgxdm( dto.getJhrejhgxdm() );
        ksxtCssbxxbDO.setJhrelxdh( dto.getJhrelxdh() );
        ksxtCssbxxbDO.setSbrgmsfhm( dto.getSbrgmsfhm() );
        ksxtCssbxxbDO.setSbrxm( dto.getSbrxm() );
        ksxtCssbxxbDO.setSbrlxdh( dto.getSbrlxdh() );
        ksxtCssbxxbDO.setSbrycsrgxjtgxdm( dto.getSbrycsrgxjtgxdm() );
        ksxtCssbxxbDO.setXzzssxqdm( dto.getXzzssxqdm() );
        ksxtCssbxxbDO.setXzzqhnxxdz( dto.getXzzqhnxxdz() );
        ksxtCssbxxbDO.setSldgajgjgdm( dto.getSldgajgjgdm() );
        ksxtCssbxxbDO.setSldgajgmc( dto.getSldgajgmc() );
        ksxtCssbxxbDO.setSldlxdh( dto.getSldlxdh() );
        ksxtCssbxxbDO.setSlrid( dto.getSlrid() );
        ksxtCssbxxbDO.setSlrxm( dto.getSlrxm() );
        ksxtCssbxxbDO.setSlsj( dto.getSlsj() );
        ksxtCssbxxbDO.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        ksxtCssbxxbDO.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtCssbxxbDO.setGdpzbbh( dto.getGdpzbbh() );
        ksxtCssbxxbDO.setQyfwdm( dto.getQyfwdm() );
        ksxtCssbxxbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtCssbxxbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtCssbxxbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtCssbxxbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtCssbxxbDO.setRksj( dto.getRksj() );
        ksxtCssbxxbDO.setPdbzsflh( dto.getPdbzsflh() );
        ksxtCssbxxbDO.setPdbzcszmhmd( dto.getPdbzcszmhmd() );
        ksxtCssbxxbDO.setBdjg( dto.getBdjg() );
        ksxtCssbxxbDO.setBdms( dto.getBdms() );
        ksxtCssbxxbDO.setBdsj( dto.getBdsj() );
        ksxtCssbxxbDO.setBdrid( dto.getBdrid() );
        ksxtCssbxxbDO.setBdrxm( dto.getBdrxm() );
        ksxtCssbxxbDO.setBdrip( dto.getBdrip() );

        return ksxtCssbxxbDO;
    }

    @Override
    public KsxtCssbxxbDTO convertToDTO(KsxtCssbxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsxtCssbxxbDTO ksxtCssbxxbDTO = new KsxtCssbxxbDTO();

        ksxtCssbxxbDTO.setKsxtid( req.getKsxtid() );

        return ksxtCssbxxbDTO;
    }

    @Override
    public KsxtCssbxxbPageResp convertToPageResp(KsxtCssbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtCssbxxbPageResp ksxtCssbxxbPageResp = new KsxtCssbxxbPageResp();

        ksxtCssbxxbPageResp.setKsxtcsid( dto.getKsxtcsid() );
        ksxtCssbxxbPageResp.setKsxtid( dto.getKsxtid() );
        ksxtCssbxxbPageResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtCssbxxbPageResp.setXm( dto.getXm() );
        ksxtCssbxxbPageResp.setX( dto.getX() );
        ksxtCssbxxbPageResp.setM( dto.getM() );
        ksxtCssbxxbPageResp.setXbdm( dto.getXbdm() );
        ksxtCssbxxbPageResp.setMzdm( dto.getMzdm() );
        ksxtCssbxxbPageResp.setCsrq( dto.getCsrq() );
        ksxtCssbxxbPageResp.setCssj( dto.getCssj() );
        ksxtCssbxxbPageResp.setCsdgjhdqdm( dto.getCsdgjhdqdm() );
        ksxtCssbxxbPageResp.setCsdssxqdm( dto.getCsdssxqdm() );
        ksxtCssbxxbPageResp.setCsdqhnxxdz( dto.getCsdqhnxxdz() );
        ksxtCssbxxbPageResp.setJggjhdqdm( dto.getJggjhdqdm() );
        ksxtCssbxxbPageResp.setJgssxqdm( dto.getJgssxqdm() );
        ksxtCssbxxbPageResp.setJgqhnxxdz( dto.getJgqhnxxdz() );
        ksxtCssbxxbPageResp.setXxdm( dto.getXxdm() );
        ksxtCssbxxbPageResp.setRkxxjbdm( dto.getRkxxjbdm() );
        ksxtCssbxxbPageResp.setCsdjlbdm( dto.getCsdjlbdm() );
        ksxtCssbxxbPageResp.setCszmbh( dto.getCszmbh() );
        ksxtCssbxxbPageResp.setYhzgxdm( dto.getYhzgxdm() );
        ksxtCssbxxbPageResp.setHzxm( dto.getHzxm() );
        ksxtCssbxxbPageResp.setHzgmsfhm( dto.getHzgmsfhm() );
        ksxtCssbxxbPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        ksxtCssbxxbPageResp.setFqxm( dto.getFqxm() );
        ksxtCssbxxbPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        ksxtCssbxxbPageResp.setFqzjhm( dto.getFqzjhm() );
        ksxtCssbxxbPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        ksxtCssbxxbPageResp.setMqxm( dto.getMqxm() );
        ksxtCssbxxbPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        ksxtCssbxxbPageResp.setMqzjhm( dto.getMqzjhm() );
        ksxtCssbxxbPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        ksxtCssbxxbPageResp.setJhryxm( dto.getJhryxm() );
        ksxtCssbxxbPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        ksxtCssbxxbPageResp.setJhryzjhm( dto.getJhryzjhm() );
        ksxtCssbxxbPageResp.setJhrywwx( dto.getJhrywwx() );
        ksxtCssbxxbPageResp.setJhrywwm( dto.getJhrywwm() );
        ksxtCssbxxbPageResp.setJhryjhgxdm( dto.getJhryjhgxdm() );
        ksxtCssbxxbPageResp.setJhrylxdh( dto.getJhrylxdh() );
        ksxtCssbxxbPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        ksxtCssbxxbPageResp.setJhrexm( dto.getJhrexm() );
        ksxtCssbxxbPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        ksxtCssbxxbPageResp.setJhrezjhm( dto.getJhrezjhm() );
        ksxtCssbxxbPageResp.setJhrewwx( dto.getJhrewwx() );
        ksxtCssbxxbPageResp.setJhrewwm( dto.getJhrewwm() );
        ksxtCssbxxbPageResp.setJhrejhgxdm( dto.getJhrejhgxdm() );
        ksxtCssbxxbPageResp.setJhrelxdh( dto.getJhrelxdh() );
        ksxtCssbxxbPageResp.setSbrgmsfhm( dto.getSbrgmsfhm() );
        ksxtCssbxxbPageResp.setSbrxm( dto.getSbrxm() );
        ksxtCssbxxbPageResp.setSbrlxdh( dto.getSbrlxdh() );
        ksxtCssbxxbPageResp.setSbrycsrgxjtgxdm( dto.getSbrycsrgxjtgxdm() );
        ksxtCssbxxbPageResp.setXzzssxqdm( dto.getXzzssxqdm() );
        ksxtCssbxxbPageResp.setXzzqhnxxdz( dto.getXzzqhnxxdz() );
        ksxtCssbxxbPageResp.setSldgajgjgdm( dto.getSldgajgjgdm() );
        ksxtCssbxxbPageResp.setSldgajgmc( dto.getSldgajgmc() );
        ksxtCssbxxbPageResp.setSldlxdh( dto.getSldlxdh() );
        ksxtCssbxxbPageResp.setSlrid( dto.getSlrid() );
        ksxtCssbxxbPageResp.setSlrxm( dto.getSlrxm() );
        ksxtCssbxxbPageResp.setSlsj( dto.getSlsj() );
        ksxtCssbxxbPageResp.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        ksxtCssbxxbPageResp.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtCssbxxbPageResp.setGdpzbbh( dto.getGdpzbbh() );
        ksxtCssbxxbPageResp.setQyfwdm( dto.getQyfwdm() );
        ksxtCssbxxbPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtCssbxxbPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtCssbxxbPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtCssbxxbPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtCssbxxbPageResp.setRksj( dto.getRksj() );
        ksxtCssbxxbPageResp.setPdbzsflh( dto.getPdbzsflh() );
        ksxtCssbxxbPageResp.setPdbzcszmhmd( dto.getPdbzcszmhmd() );
        ksxtCssbxxbPageResp.setBdjg( dto.getBdjg() );
        ksxtCssbxxbPageResp.setBdms( dto.getBdms() );
        ksxtCssbxxbPageResp.setBdsj( dto.getBdsj() );
        ksxtCssbxxbPageResp.setBdrid( dto.getBdrid() );
        ksxtCssbxxbPageResp.setBdrxm( dto.getBdrxm() );
        ksxtCssbxxbPageResp.setBdrip( dto.getBdrip() );

        return ksxtCssbxxbPageResp;
    }

    @Override
    public KsxtCssbxxbViewResp convertToViewResp(KsxtCssbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtCssbxxbViewResp ksxtCssbxxbViewResp = new KsxtCssbxxbViewResp();

        ksxtCssbxxbViewResp.setKsxtcsid( dto.getKsxtcsid() );
        ksxtCssbxxbViewResp.setKsxtid( dto.getKsxtid() );
        ksxtCssbxxbViewResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtCssbxxbViewResp.setXm( dto.getXm() );
        ksxtCssbxxbViewResp.setX( dto.getX() );
        ksxtCssbxxbViewResp.setM( dto.getM() );
        ksxtCssbxxbViewResp.setXbdm( dto.getXbdm() );
        ksxtCssbxxbViewResp.setMzdm( dto.getMzdm() );
        ksxtCssbxxbViewResp.setCsrq( dto.getCsrq() );
        ksxtCssbxxbViewResp.setCssj( dto.getCssj() );
        ksxtCssbxxbViewResp.setCsdgjhdqdm( dto.getCsdgjhdqdm() );
        ksxtCssbxxbViewResp.setCsdssxqdm( dto.getCsdssxqdm() );
        ksxtCssbxxbViewResp.setCsdqhnxxdz( dto.getCsdqhnxxdz() );
        ksxtCssbxxbViewResp.setJggjhdqdm( dto.getJggjhdqdm() );
        ksxtCssbxxbViewResp.setJgssxqdm( dto.getJgssxqdm() );
        ksxtCssbxxbViewResp.setJgqhnxxdz( dto.getJgqhnxxdz() );
        ksxtCssbxxbViewResp.setXxdm( dto.getXxdm() );
        ksxtCssbxxbViewResp.setRkxxjbdm( dto.getRkxxjbdm() );
        ksxtCssbxxbViewResp.setCsdjlbdm( dto.getCsdjlbdm() );
        ksxtCssbxxbViewResp.setCszmbh( dto.getCszmbh() );
        ksxtCssbxxbViewResp.setYhzgxdm( dto.getYhzgxdm() );
        ksxtCssbxxbViewResp.setHzxm( dto.getHzxm() );
        ksxtCssbxxbViewResp.setHzgmsfhm( dto.getHzgmsfhm() );
        ksxtCssbxxbViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        ksxtCssbxxbViewResp.setFqxm( dto.getFqxm() );
        ksxtCssbxxbViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        ksxtCssbxxbViewResp.setFqzjhm( dto.getFqzjhm() );
        ksxtCssbxxbViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        ksxtCssbxxbViewResp.setMqxm( dto.getMqxm() );
        ksxtCssbxxbViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        ksxtCssbxxbViewResp.setMqzjhm( dto.getMqzjhm() );
        ksxtCssbxxbViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        ksxtCssbxxbViewResp.setJhryxm( dto.getJhryxm() );
        ksxtCssbxxbViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        ksxtCssbxxbViewResp.setJhryzjhm( dto.getJhryzjhm() );
        ksxtCssbxxbViewResp.setJhrywwx( dto.getJhrywwx() );
        ksxtCssbxxbViewResp.setJhrywwm( dto.getJhrywwm() );
        ksxtCssbxxbViewResp.setJhryjhgxdm( dto.getJhryjhgxdm() );
        ksxtCssbxxbViewResp.setJhrylxdh( dto.getJhrylxdh() );
        ksxtCssbxxbViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        ksxtCssbxxbViewResp.setJhrexm( dto.getJhrexm() );
        ksxtCssbxxbViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        ksxtCssbxxbViewResp.setJhrezjhm( dto.getJhrezjhm() );
        ksxtCssbxxbViewResp.setJhrewwx( dto.getJhrewwx() );
        ksxtCssbxxbViewResp.setJhrewwm( dto.getJhrewwm() );
        ksxtCssbxxbViewResp.setJhrejhgxdm( dto.getJhrejhgxdm() );
        ksxtCssbxxbViewResp.setJhrelxdh( dto.getJhrelxdh() );
        ksxtCssbxxbViewResp.setSbrgmsfhm( dto.getSbrgmsfhm() );
        ksxtCssbxxbViewResp.setSbrxm( dto.getSbrxm() );
        ksxtCssbxxbViewResp.setSbrlxdh( dto.getSbrlxdh() );
        ksxtCssbxxbViewResp.setSbrycsrgxjtgxdm( dto.getSbrycsrgxjtgxdm() );
        ksxtCssbxxbViewResp.setXzzssxqdm( dto.getXzzssxqdm() );
        ksxtCssbxxbViewResp.setXzzqhnxxdz( dto.getXzzqhnxxdz() );
        ksxtCssbxxbViewResp.setSldgajgjgdm( dto.getSldgajgjgdm() );
        ksxtCssbxxbViewResp.setSldgajgmc( dto.getSldgajgmc() );
        ksxtCssbxxbViewResp.setSldlxdh( dto.getSldlxdh() );
        ksxtCssbxxbViewResp.setSlrid( dto.getSlrid() );
        ksxtCssbxxbViewResp.setSlrxm( dto.getSlrxm() );
        ksxtCssbxxbViewResp.setSlsj( dto.getSlsj() );
        ksxtCssbxxbViewResp.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        ksxtCssbxxbViewResp.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtCssbxxbViewResp.setGdpzbbh( dto.getGdpzbbh() );
        ksxtCssbxxbViewResp.setQyfwdm( dto.getQyfwdm() );
        ksxtCssbxxbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtCssbxxbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtCssbxxbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtCssbxxbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtCssbxxbViewResp.setRksj( dto.getRksj() );
        ksxtCssbxxbViewResp.setPdbzsflh( dto.getPdbzsflh() );
        ksxtCssbxxbViewResp.setPdbzcszmhmd( dto.getPdbzcszmhmd() );
        ksxtCssbxxbViewResp.setBdjg( dto.getBdjg() );
        ksxtCssbxxbViewResp.setBdms( dto.getBdms() );
        ksxtCssbxxbViewResp.setBdsj( dto.getBdsj() );
        ksxtCssbxxbViewResp.setBdrid( dto.getBdrid() );
        ksxtCssbxxbViewResp.setBdrxm( dto.getBdrxm() );
        ksxtCssbxxbViewResp.setBdrip( dto.getBdrip() );

        return ksxtCssbxxbViewResp;
    }
}
