package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.GaythtYthSlclDTO;
import com.zjjcnt.project.ck.base.dto.GaythtYthSlclsjDTO;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclSaveYwslClysjReq;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclsjCreateReq;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclsjPageReq;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclsjUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclsjCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclsjPageResp;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclsjViewResp;
import com.zjjcnt.project.ck.base.entity.GaythtYthSlclsjDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:40+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class GaythtYthSlclsjConvertImpl implements GaythtYthSlclsjConvert {

    @Override
    public GaythtYthSlclsjDTO convert(GaythtYthSlclsjDO entity) {
        if ( entity == null ) {
            return null;
        }

        GaythtYthSlclsjDTO gaythtYthSlclsjDTO = new GaythtYthSlclsjDTO();

        gaythtYthSlclsjDTO.setId( entity.getId() );
        gaythtYthSlclsjDTO.setSlclsjid( entity.getSlclsjid() );
        gaythtYthSlclsjDTO.setUnid( entity.getUnid() );
        gaythtYthSlclsjDTO.setFilename( entity.getFilename() );
        gaythtYthSlclsjDTO.setFileurl( entity.getFileurl() );
        gaythtYthSlclsjDTO.setWjcflb( entity.getWjcflb() );
        gaythtYthSlclsjDTO.setWjdx( entity.getWjdx() );
        gaythtYthSlclsjDTO.setWjlx( entity.getWjlx() );
        gaythtYthSlclsjDTO.setWjsjdz( entity.getWjsjdz() );
        gaythtYthSlclsjDTO.setHash( entity.getHash() );
        gaythtYthSlclsjDTO.setWjdata( entity.getWjdata() );
        gaythtYthSlclsjDTO.setCjr( entity.getCjr() );
        gaythtYthSlclsjDTO.setCjrip( entity.getCjrip() );
        gaythtYthSlclsjDTO.setCjsj( entity.getCjsj() );
        gaythtYthSlclsjDTO.setXgr( entity.getXgr() );
        gaythtYthSlclsjDTO.setXgrip( entity.getXgrip() );
        gaythtYthSlclsjDTO.setXgsj( entity.getXgsj() );
        gaythtYthSlclsjDTO.setYxbz( entity.getYxbz() );
        gaythtYthSlclsjDTO.setExtend( entity.getExtend() );
        gaythtYthSlclsjDTO.setExtend2( entity.getExtend2() );
        gaythtYthSlclsjDTO.setExtend3( entity.getExtend3() );
        gaythtYthSlclsjDTO.setExtend4( entity.getExtend4() );
        gaythtYthSlclsjDTO.setExtend5( entity.getExtend5() );

        return gaythtYthSlclsjDTO;
    }

    @Override
    public GaythtYthSlclsjDO convertToDO(GaythtYthSlclsjDTO dto) {
        if ( dto == null ) {
            return null;
        }

        GaythtYthSlclsjDO gaythtYthSlclsjDO = new GaythtYthSlclsjDO();

        gaythtYthSlclsjDO.setId( dto.getId() );
        gaythtYthSlclsjDO.setSlclsjid( dto.getSlclsjid() );
        gaythtYthSlclsjDO.setUnid( dto.getUnid() );
        gaythtYthSlclsjDO.setFilename( dto.getFilename() );
        gaythtYthSlclsjDO.setFileurl( dto.getFileurl() );
        gaythtYthSlclsjDO.setWjcflb( dto.getWjcflb() );
        gaythtYthSlclsjDO.setWjdx( dto.getWjdx() );
        gaythtYthSlclsjDO.setWjlx( dto.getWjlx() );
        gaythtYthSlclsjDO.setWjsjdz( dto.getWjsjdz() );
        gaythtYthSlclsjDO.setHash( dto.getHash() );
        gaythtYthSlclsjDO.setWjdata( dto.getWjdata() );
        gaythtYthSlclsjDO.setCjr( dto.getCjr() );
        gaythtYthSlclsjDO.setCjrip( dto.getCjrip() );
        gaythtYthSlclsjDO.setCjsj( dto.getCjsj() );
        gaythtYthSlclsjDO.setXgr( dto.getXgr() );
        gaythtYthSlclsjDO.setXgrip( dto.getXgrip() );
        gaythtYthSlclsjDO.setXgsj( dto.getXgsj() );
        gaythtYthSlclsjDO.setYxbz( dto.getYxbz() );
        gaythtYthSlclsjDO.setExtend( dto.getExtend() );
        gaythtYthSlclsjDO.setExtend2( dto.getExtend2() );
        gaythtYthSlclsjDO.setExtend3( dto.getExtend3() );
        gaythtYthSlclsjDO.setExtend4( dto.getExtend4() );
        gaythtYthSlclsjDO.setExtend5( dto.getExtend5() );

        return gaythtYthSlclsjDO;
    }

    @Override
    public GaythtYthSlclsjDTO convertToDTO(GaythtYthSlclsjPageReq req) {
        if ( req == null ) {
            return null;
        }

        GaythtYthSlclsjDTO gaythtYthSlclsjDTO = new GaythtYthSlclsjDTO();

        gaythtYthSlclsjDTO.setUnid( req.getUnid() );
        gaythtYthSlclsjDTO.setFilename( req.getFilename() );
        gaythtYthSlclsjDTO.setFileurl( req.getFileurl() );
        gaythtYthSlclsjDTO.setWjcflb( req.getWjcflb() );
        gaythtYthSlclsjDTO.setWjdx( req.getWjdx() );
        gaythtYthSlclsjDTO.setWjlx( req.getWjlx() );
        gaythtYthSlclsjDTO.setWjsjdz( req.getWjsjdz() );
        gaythtYthSlclsjDTO.setHash( req.getHash() );
        gaythtYthSlclsjDTO.setWjdata( req.getWjdata() );
        gaythtYthSlclsjDTO.setCjr( req.getCjr() );
        gaythtYthSlclsjDTO.setCjrip( req.getCjrip() );
        gaythtYthSlclsjDTO.setCjsj( req.getCjsj() );
        gaythtYthSlclsjDTO.setXgr( req.getXgr() );
        gaythtYthSlclsjDTO.setXgrip( req.getXgrip() );
        gaythtYthSlclsjDTO.setXgsj( req.getXgsj() );
        gaythtYthSlclsjDTO.setYxbz( req.getYxbz() );
        gaythtYthSlclsjDTO.setExtend( req.getExtend() );
        gaythtYthSlclsjDTO.setExtend2( req.getExtend2() );
        gaythtYthSlclsjDTO.setExtend3( req.getExtend3() );
        gaythtYthSlclsjDTO.setExtend4( req.getExtend4() );
        gaythtYthSlclsjDTO.setExtend5( req.getExtend5() );

        return gaythtYthSlclsjDTO;
    }

    @Override
    public GaythtYthSlclsjDTO convertToDTO(GaythtYthSlclsjCreateReq req) {
        if ( req == null ) {
            return null;
        }

        GaythtYthSlclsjDTO gaythtYthSlclsjDTO = new GaythtYthSlclsjDTO();

        gaythtYthSlclsjDTO.setUnid( req.getUnid() );
        gaythtYthSlclsjDTO.setFilename( req.getFilename() );
        gaythtYthSlclsjDTO.setFileurl( req.getFileurl() );
        gaythtYthSlclsjDTO.setWjcflb( req.getWjcflb() );
        gaythtYthSlclsjDTO.setWjdx( req.getWjdx() );
        gaythtYthSlclsjDTO.setWjlx( req.getWjlx() );
        gaythtYthSlclsjDTO.setWjsjdz( req.getWjsjdz() );
        gaythtYthSlclsjDTO.setHash( req.getHash() );
        gaythtYthSlclsjDTO.setWjdata( req.getWjdata() );
        gaythtYthSlclsjDTO.setCjr( req.getCjr() );
        gaythtYthSlclsjDTO.setCjrip( req.getCjrip() );
        gaythtYthSlclsjDTO.setCjsj( req.getCjsj() );
        gaythtYthSlclsjDTO.setXgr( req.getXgr() );
        gaythtYthSlclsjDTO.setXgrip( req.getXgrip() );
        gaythtYthSlclsjDTO.setXgsj( req.getXgsj() );
        gaythtYthSlclsjDTO.setYxbz( req.getYxbz() );
        gaythtYthSlclsjDTO.setExtend( req.getExtend() );
        gaythtYthSlclsjDTO.setExtend2( req.getExtend2() );
        gaythtYthSlclsjDTO.setExtend3( req.getExtend3() );
        gaythtYthSlclsjDTO.setExtend4( req.getExtend4() );
        gaythtYthSlclsjDTO.setExtend5( req.getExtend5() );

        return gaythtYthSlclsjDTO;
    }

    @Override
    public GaythtYthSlclsjDTO convertToDTO(GaythtYthSlclsjUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        GaythtYthSlclsjDTO gaythtYthSlclsjDTO = new GaythtYthSlclsjDTO();

        gaythtYthSlclsjDTO.setSlclsjid( req.getSlclsjid() );
        gaythtYthSlclsjDTO.setUnid( req.getUnid() );
        gaythtYthSlclsjDTO.setFilename( req.getFilename() );
        gaythtYthSlclsjDTO.setFileurl( req.getFileurl() );
        gaythtYthSlclsjDTO.setWjcflb( req.getWjcflb() );
        gaythtYthSlclsjDTO.setWjdx( req.getWjdx() );
        gaythtYthSlclsjDTO.setWjlx( req.getWjlx() );
        gaythtYthSlclsjDTO.setWjsjdz( req.getWjsjdz() );
        gaythtYthSlclsjDTO.setHash( req.getHash() );
        gaythtYthSlclsjDTO.setWjdata( req.getWjdata() );
        gaythtYthSlclsjDTO.setExtend( req.getExtend() );
        gaythtYthSlclsjDTO.setExtend2( req.getExtend2() );
        gaythtYthSlclsjDTO.setExtend3( req.getExtend3() );
        gaythtYthSlclsjDTO.setExtend4( req.getExtend4() );
        gaythtYthSlclsjDTO.setExtend5( req.getExtend5() );

        return gaythtYthSlclsjDTO;
    }

    @Override
    public GaythtYthSlclsjPageResp convertToPageResp(GaythtYthSlclsjDTO dto) {
        if ( dto == null ) {
            return null;
        }

        GaythtYthSlclsjPageResp gaythtYthSlclsjPageResp = new GaythtYthSlclsjPageResp();

        gaythtYthSlclsjPageResp.setSlclsjid( dto.getSlclsjid() );
        gaythtYthSlclsjPageResp.setUnid( dto.getUnid() );
        gaythtYthSlclsjPageResp.setFilename( dto.getFilename() );
        gaythtYthSlclsjPageResp.setWjlx( dto.getWjlx() );
        gaythtYthSlclsjPageResp.setWjdx( dto.getWjdx() );
        gaythtYthSlclsjPageResp.setFileurl( dto.getFileurl() );
        gaythtYthSlclsjPageResp.setWjcflb( dto.getWjcflb() );
        gaythtYthSlclsjPageResp.setWjsjdz( dto.getWjsjdz() );
        gaythtYthSlclsjPageResp.setHash( dto.getHash() );
        gaythtYthSlclsjPageResp.setWjdata( dto.getWjdata() );
        gaythtYthSlclsjPageResp.setExtend( dto.getExtend() );
        gaythtYthSlclsjPageResp.setExtend2( dto.getExtend2() );
        gaythtYthSlclsjPageResp.setExtend3( dto.getExtend3() );
        gaythtYthSlclsjPageResp.setExtend4( dto.getExtend4() );
        gaythtYthSlclsjPageResp.setExtend5( dto.getExtend5() );
        gaythtYthSlclsjPageResp.setYxbz( dto.getYxbz() );

        return gaythtYthSlclsjPageResp;
    }

    @Override
    public GaythtYthSlclsjViewResp convertToViewResp(GaythtYthSlclsjDTO dto) {
        if ( dto == null ) {
            return null;
        }

        GaythtYthSlclsjViewResp gaythtYthSlclsjViewResp = new GaythtYthSlclsjViewResp();

        gaythtYthSlclsjViewResp.setSlclsjid( dto.getSlclsjid() );
        gaythtYthSlclsjViewResp.setUnid( dto.getUnid() );
        gaythtYthSlclsjViewResp.setFilename( dto.getFilename() );
        gaythtYthSlclsjViewResp.setFileurl( dto.getFileurl() );
        gaythtYthSlclsjViewResp.setWjcflb( dto.getWjcflb() );
        gaythtYthSlclsjViewResp.setWjdx( dto.getWjdx() );
        gaythtYthSlclsjViewResp.setWjlx( dto.getWjlx() );
        gaythtYthSlclsjViewResp.setWjsjdz( dto.getWjsjdz() );
        gaythtYthSlclsjViewResp.setHash( dto.getHash() );
        gaythtYthSlclsjViewResp.setWjdata( dto.getWjdata() );
        gaythtYthSlclsjViewResp.setExtend( dto.getExtend() );
        gaythtYthSlclsjViewResp.setExtend2( dto.getExtend2() );
        gaythtYthSlclsjViewResp.setExtend3( dto.getExtend3() );
        gaythtYthSlclsjViewResp.setExtend4( dto.getExtend4() );
        gaythtYthSlclsjViewResp.setExtend5( dto.getExtend5() );

        return gaythtYthSlclsjViewResp;
    }

    @Override
    public GaythtYthSlclsjCreateResp convertToCreateResp(GaythtYthSlclsjDTO dto) {
        if ( dto == null ) {
            return null;
        }

        GaythtYthSlclsjCreateResp gaythtYthSlclsjCreateResp = new GaythtYthSlclsjCreateResp();

        gaythtYthSlclsjCreateResp.setSlclsjid( dto.getSlclsjid() );
        gaythtYthSlclsjCreateResp.setUnid( dto.getUnid() );
        gaythtYthSlclsjCreateResp.setFilename( dto.getFilename() );
        gaythtYthSlclsjCreateResp.setFileurl( dto.getFileurl() );
        gaythtYthSlclsjCreateResp.setWjcflb( dto.getWjcflb() );
        gaythtYthSlclsjCreateResp.setWjdx( dto.getWjdx() );
        gaythtYthSlclsjCreateResp.setWjlx( dto.getWjlx() );
        gaythtYthSlclsjCreateResp.setWjsjdz( dto.getWjsjdz() );
        gaythtYthSlclsjCreateResp.setHash( dto.getHash() );
        gaythtYthSlclsjCreateResp.setWjdata( dto.getWjdata() );
        gaythtYthSlclsjCreateResp.setExtend( dto.getExtend() );
        gaythtYthSlclsjCreateResp.setExtend2( dto.getExtend2() );
        gaythtYthSlclsjCreateResp.setExtend3( dto.getExtend3() );
        gaythtYthSlclsjCreateResp.setExtend4( dto.getExtend4() );
        gaythtYthSlclsjCreateResp.setExtend5( dto.getExtend5() );

        return gaythtYthSlclsjCreateResp;
    }

    @Override
    public GaythtYthSlclDTO convertToDTO(GaythtYthSlclSaveYwslClysjReq req) {
        if ( req == null ) {
            return null;
        }

        GaythtYthSlclDTO gaythtYthSlclDTO = new GaythtYthSlclDTO();

        gaythtYthSlclDTO.setProjid( req.getProjid() );
        gaythtYthSlclDTO.setSqrxm( req.getSqrxm() );
        gaythtYthSlclDTO.setYwslh( req.getYwslh() );
        gaythtYthSlclDTO.setLcywlx( req.getLcywlx() );
        gaythtYthSlclDTO.setGmsfhm( req.getGmsfhm() );
        gaythtYthSlclDTO.setSldsjgsdwdm( req.getSldsjgsdwdm() );

        return gaythtYthSlclDTO;
    }
}
