package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.GaythtZwjhFsrwmxbDTO;
import com.zjjcnt.project.ck.base.entity.GaythtZwjhFsrwmxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:45+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class GaythtZwjhFsrwmxbConvertImpl implements GaythtZwjhFsrwmxbConvert {

    @Override
    public GaythtZwjhFsrwmxbDTO convert(GaythtZwjhFsrwmxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        GaythtZwjhFsrwmxbDTO gaythtZwjhFsrwmxbDTO = new GaythtZwjhFsrwmxbDTO();

        gaythtZwjhFsrwmxbDTO.setId( entity.getId() );
        gaythtZwjhFsrwmxbDTO.setFsrwmxid( entity.getFsrwmxid() );
        gaythtZwjhFsrwmxbDTO.setProjectid( entity.getProjectid() );
        gaythtZwjhFsrwmxbDTO.setSendtype( entity.getSendtype() );
        gaythtZwjhFsrwmxbDTO.setSendorder( entity.getSendorder() );
        gaythtZwjhFsrwmxbDTO.setSystemid( entity.getSystemid() );
        gaythtZwjhFsrwmxbDTO.setAreacode( entity.getAreacode() );
        gaythtZwjhFsrwmxbDTO.setDeptcode( entity.getDeptcode() );
        gaythtZwjhFsrwmxbDTO.setDeptname( entity.getDeptname() );
        gaythtZwjhFsrwmxbDTO.setBljg( entity.getBljg() );
        gaythtZwjhFsrwmxbDTO.setBlms( entity.getBlms() );
        gaythtZwjhFsrwmxbDTO.setBlsj( entity.getBlsj() );
        gaythtZwjhFsrwmxbDTO.setExtinfo( entity.getExtinfo() );
        gaythtZwjhFsrwmxbDTO.setReceptionvo( entity.getReceptionvo() );
        gaythtZwjhFsrwmxbDTO.setAttachmentvo( entity.getAttachmentvo() );
        gaythtZwjhFsrwmxbDTO.setMemo( entity.getMemo() );
        gaythtZwjhFsrwmxbDTO.setRemark( entity.getRemark() );
        gaythtZwjhFsrwmxbDTO.setReason( entity.getReason() );
        gaythtZwjhFsrwmxbDTO.setType( entity.getType() );
        gaythtZwjhFsrwmxbDTO.setPausetime( entity.getPausetime() );
        gaythtZwjhFsrwmxbDTO.setRecovertime( entity.getRecovertime() );
        gaythtZwjhFsrwmxbDTO.setOperatorname( entity.getOperatorname() );
        gaythtZwjhFsrwmxbDTO.setOperatoruid( entity.getOperatoruid() );
        gaythtZwjhFsrwmxbDTO.setApplycashchannel( entity.getApplycashchannel() );
        gaythtZwjhFsrwmxbDTO.setPaynoticeorderid( entity.getPaynoticeorderid() );
        gaythtZwjhFsrwmxbDTO.setApplypaytype( entity.getApplypaytype() );
        gaythtZwjhFsrwmxbDTO.setNeedqrcodepay( entity.getNeedqrcodepay() );
        gaythtZwjhFsrwmxbDTO.setUrl( entity.getUrl() );
        gaythtZwjhFsrwmxbDTO.setOutorderno( entity.getOutorderno() );
        gaythtZwjhFsrwmxbDTO.setNoticepaydate( entity.getNoticepaydate() );
        gaythtZwjhFsrwmxbDTO.setNoticeno( entity.getNoticeno() );
        gaythtZwjhFsrwmxbDTO.setConfirmtype( entity.getConfirmtype() );
        gaythtZwjhFsrwmxbDTO.setUserconfirmmode( entity.getUserconfirmmode() );
        gaythtZwjhFsrwmxbDTO.setSignednotice( entity.getSignednotice() );
        gaythtZwjhFsrwmxbDTO.setCountersignature( entity.getCountersignature() );
        gaythtZwjhFsrwmxbDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        gaythtZwjhFsrwmxbDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        gaythtZwjhFsrwmxbDTO.setFsbz( entity.getFsbz() );
        gaythtZwjhFsrwmxbDTO.setFssj( entity.getFssj() );
        gaythtZwjhFsrwmxbDTO.setFhjg( entity.getFhjg() );
        gaythtZwjhFsrwmxbDTO.setFhms( entity.getFhms() );
        gaythtZwjhFsrwmxbDTO.setFhdata( entity.getFhdata() );
        gaythtZwjhFsrwmxbDTO.setCjsj( entity.getCjsj() );
        gaythtZwjhFsrwmxbDTO.setCjr( entity.getCjr() );
        gaythtZwjhFsrwmxbDTO.setCjrid( entity.getCjrid() );
        gaythtZwjhFsrwmxbDTO.setCjrip( entity.getCjrip() );

        return gaythtZwjhFsrwmxbDTO;
    }

    @Override
    public GaythtZwjhFsrwmxbDO convertToDO(GaythtZwjhFsrwmxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        GaythtZwjhFsrwmxbDO gaythtZwjhFsrwmxbDO = new GaythtZwjhFsrwmxbDO();

        gaythtZwjhFsrwmxbDO.setId( dto.getId() );
        gaythtZwjhFsrwmxbDO.setFsrwmxid( dto.getFsrwmxid() );
        gaythtZwjhFsrwmxbDO.setProjectid( dto.getProjectid() );
        gaythtZwjhFsrwmxbDO.setSendtype( dto.getSendtype() );
        gaythtZwjhFsrwmxbDO.setSendorder( dto.getSendorder() );
        gaythtZwjhFsrwmxbDO.setSystemid( dto.getSystemid() );
        gaythtZwjhFsrwmxbDO.setAreacode( dto.getAreacode() );
        gaythtZwjhFsrwmxbDO.setDeptcode( dto.getDeptcode() );
        gaythtZwjhFsrwmxbDO.setDeptname( dto.getDeptname() );
        gaythtZwjhFsrwmxbDO.setBljg( dto.getBljg() );
        gaythtZwjhFsrwmxbDO.setBlms( dto.getBlms() );
        gaythtZwjhFsrwmxbDO.setBlsj( dto.getBlsj() );
        gaythtZwjhFsrwmxbDO.setExtinfo( dto.getExtinfo() );
        gaythtZwjhFsrwmxbDO.setReceptionvo( dto.getReceptionvo() );
        gaythtZwjhFsrwmxbDO.setAttachmentvo( dto.getAttachmentvo() );
        gaythtZwjhFsrwmxbDO.setMemo( dto.getMemo() );
        gaythtZwjhFsrwmxbDO.setRemark( dto.getRemark() );
        gaythtZwjhFsrwmxbDO.setReason( dto.getReason() );
        gaythtZwjhFsrwmxbDO.setType( dto.getType() );
        gaythtZwjhFsrwmxbDO.setPausetime( dto.getPausetime() );
        gaythtZwjhFsrwmxbDO.setRecovertime( dto.getRecovertime() );
        gaythtZwjhFsrwmxbDO.setOperatorname( dto.getOperatorname() );
        gaythtZwjhFsrwmxbDO.setOperatoruid( dto.getOperatoruid() );
        gaythtZwjhFsrwmxbDO.setApplycashchannel( dto.getApplycashchannel() );
        gaythtZwjhFsrwmxbDO.setPaynoticeorderid( dto.getPaynoticeorderid() );
        gaythtZwjhFsrwmxbDO.setApplypaytype( dto.getApplypaytype() );
        gaythtZwjhFsrwmxbDO.setNeedqrcodepay( dto.getNeedqrcodepay() );
        gaythtZwjhFsrwmxbDO.setUrl( dto.getUrl() );
        gaythtZwjhFsrwmxbDO.setOutorderno( dto.getOutorderno() );
        gaythtZwjhFsrwmxbDO.setNoticepaydate( dto.getNoticepaydate() );
        gaythtZwjhFsrwmxbDO.setNoticeno( dto.getNoticeno() );
        gaythtZwjhFsrwmxbDO.setConfirmtype( dto.getConfirmtype() );
        gaythtZwjhFsrwmxbDO.setUserconfirmmode( dto.getUserconfirmmode() );
        gaythtZwjhFsrwmxbDO.setSignednotice( dto.getSignednotice() );
        gaythtZwjhFsrwmxbDO.setCountersignature( dto.getCountersignature() );
        gaythtZwjhFsrwmxbDO.setSjgsdwdm( dto.getSjgsdwdm() );
        gaythtZwjhFsrwmxbDO.setSjgsdwmc( dto.getSjgsdwmc() );
        gaythtZwjhFsrwmxbDO.setFsbz( dto.getFsbz() );
        gaythtZwjhFsrwmxbDO.setFssj( dto.getFssj() );
        gaythtZwjhFsrwmxbDO.setFhjg( dto.getFhjg() );
        gaythtZwjhFsrwmxbDO.setFhms( dto.getFhms() );
        gaythtZwjhFsrwmxbDO.setFhdata( dto.getFhdata() );
        gaythtZwjhFsrwmxbDO.setCjsj( dto.getCjsj() );
        gaythtZwjhFsrwmxbDO.setCjr( dto.getCjr() );
        gaythtZwjhFsrwmxbDO.setCjrid( dto.getCjrid() );
        gaythtZwjhFsrwmxbDO.setCjrip( dto.getCjrip() );

        return gaythtZwjhFsrwmxbDO;
    }
}
