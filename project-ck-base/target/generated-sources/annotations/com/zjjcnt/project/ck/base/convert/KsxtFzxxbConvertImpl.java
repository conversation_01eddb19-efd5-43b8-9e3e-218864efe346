package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsxtFzxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtFzxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtFzxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtFzxxbViewResp;
import com.zjjcnt.project.ck.base.entity.KsxtFzxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:43+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsxtFzxxbConvertImpl implements KsxtFzxxbConvert {

    @Override
    public KsxtFzxxbDTO convert(KsxtFzxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsxtFzxxbDTO ksxtFzxxbDTO = new KsxtFzxxbDTO();

        ksxtFzxxbDTO.setId( entity.getId() );
        ksxtFzxxbDTO.setKsxtfzxxid( entity.getKsxtfzxxid() );
        ksxtFzxxbDTO.setYwbm( entity.getYwbm() );
        ksxtFzxxbDTO.setKsxtid( entity.getKsxtid() );
        ksxtFzxxbDTO.setZaglywxtbh( entity.getZaglywxtbh() );
        ksxtFzxxbDTO.setZaglywlbdm( entity.getZaglywlbdm() );
        ksxtFzxxbDTO.setBt( entity.getBt() );
        ksxtFzxxbDTO.setKstbxthjdm( entity.getKstbxthjdm() );
        ksxtFzxxbDTO.setKstbxxlxdm( entity.getKstbxxlxdm() );
        ksxtFzxxbDTO.setJyqk( entity.getJyqk() );
        ksxtFzxxbDTO.setClbz( entity.getClbz() );
        ksxtFzxxbDTO.setCzyid( entity.getCzyid() );
        ksxtFzxxbDTO.setCzyxm( entity.getCzyxm() );
        ksxtFzxxbDTO.setCzyip( entity.getCzyip() );
        ksxtFzxxbDTO.setQyfwdm( entity.getQyfwdm() );
        ksxtFzxxbDTO.setFsdwsjgsdwdm( entity.getFsdwsjgsdwdm() );
        ksxtFzxxbDTO.setFsdwsjgsdwmc( entity.getFsdwsjgsdwmc() );
        ksxtFzxxbDTO.setJsdwsjgsdwdm( entity.getJsdwsjgsdwdm() );
        ksxtFzxxbDTO.setJsdwsjgsdwmc( entity.getJsdwsjgsdwmc() );
        ksxtFzxxbDTO.setRksj( entity.getRksj() );
        ksxtFzxxbDTO.setYdrxm( entity.getYdrxm() );
        ksxtFzxxbDTO.setYdsj( entity.getYdsj() );

        return ksxtFzxxbDTO;
    }

    @Override
    public KsxtFzxxbDO convertToDO(KsxtFzxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtFzxxbDO ksxtFzxxbDO = new KsxtFzxxbDO();

        ksxtFzxxbDO.setId( dto.getId() );
        ksxtFzxxbDO.setKsxtfzxxid( dto.getKsxtfzxxid() );
        ksxtFzxxbDO.setYwbm( dto.getYwbm() );
        ksxtFzxxbDO.setKsxtid( dto.getKsxtid() );
        ksxtFzxxbDO.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtFzxxbDO.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtFzxxbDO.setBt( dto.getBt() );
        ksxtFzxxbDO.setKstbxthjdm( dto.getKstbxthjdm() );
        ksxtFzxxbDO.setKstbxxlxdm( dto.getKstbxxlxdm() );
        ksxtFzxxbDO.setJyqk( dto.getJyqk() );
        ksxtFzxxbDO.setClbz( dto.getClbz() );
        ksxtFzxxbDO.setCzyid( dto.getCzyid() );
        ksxtFzxxbDO.setCzyxm( dto.getCzyxm() );
        ksxtFzxxbDO.setCzyip( dto.getCzyip() );
        ksxtFzxxbDO.setQyfwdm( dto.getQyfwdm() );
        ksxtFzxxbDO.setFsdwsjgsdwdm( dto.getFsdwsjgsdwdm() );
        ksxtFzxxbDO.setFsdwsjgsdwmc( dto.getFsdwsjgsdwmc() );
        ksxtFzxxbDO.setJsdwsjgsdwdm( dto.getJsdwsjgsdwdm() );
        ksxtFzxxbDO.setJsdwsjgsdwmc( dto.getJsdwsjgsdwmc() );
        ksxtFzxxbDO.setRksj( dto.getRksj() );
        ksxtFzxxbDO.setYdrxm( dto.getYdrxm() );
        ksxtFzxxbDO.setYdsj( dto.getYdsj() );

        return ksxtFzxxbDO;
    }

    @Override
    public KsxtFzxxbDTO convertToDTO(KsxtFzxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsxtFzxxbDTO ksxtFzxxbDTO = new KsxtFzxxbDTO();

        ksxtFzxxbDTO.setKsxtid( req.getKsxtid() );

        return ksxtFzxxbDTO;
    }

    @Override
    public KsxtFzxxbPageResp convertToPageResp(KsxtFzxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtFzxxbPageResp ksxtFzxxbPageResp = new KsxtFzxxbPageResp();

        ksxtFzxxbPageResp.setKsxtfzxxid( dto.getKsxtfzxxid() );
        ksxtFzxxbPageResp.setYwbm( dto.getYwbm() );
        ksxtFzxxbPageResp.setKsxtid( dto.getKsxtid() );
        ksxtFzxxbPageResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtFzxxbPageResp.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtFzxxbPageResp.setBt( dto.getBt() );
        ksxtFzxxbPageResp.setKstbxthjdm( dto.getKstbxthjdm() );
        ksxtFzxxbPageResp.setKstbxxlxdm( dto.getKstbxxlxdm() );
        ksxtFzxxbPageResp.setJyqk( dto.getJyqk() );
        ksxtFzxxbPageResp.setClbz( dto.getClbz() );
        ksxtFzxxbPageResp.setCzyid( dto.getCzyid() );
        ksxtFzxxbPageResp.setCzyxm( dto.getCzyxm() );
        ksxtFzxxbPageResp.setCzyip( dto.getCzyip() );
        ksxtFzxxbPageResp.setQyfwdm( dto.getQyfwdm() );
        ksxtFzxxbPageResp.setFsdwsjgsdwdm( dto.getFsdwsjgsdwdm() );
        ksxtFzxxbPageResp.setFsdwsjgsdwmc( dto.getFsdwsjgsdwmc() );
        ksxtFzxxbPageResp.setJsdwsjgsdwdm( dto.getJsdwsjgsdwdm() );
        ksxtFzxxbPageResp.setJsdwsjgsdwmc( dto.getJsdwsjgsdwmc() );
        ksxtFzxxbPageResp.setRksj( dto.getRksj() );
        ksxtFzxxbPageResp.setYdrxm( dto.getYdrxm() );
        ksxtFzxxbPageResp.setYdsj( dto.getYdsj() );

        return ksxtFzxxbPageResp;
    }

    @Override
    public KsxtFzxxbViewResp convertToViewResp(KsxtFzxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtFzxxbViewResp ksxtFzxxbViewResp = new KsxtFzxxbViewResp();

        ksxtFzxxbViewResp.setKsxtfzxxid( dto.getKsxtfzxxid() );
        ksxtFzxxbViewResp.setYwbm( dto.getYwbm() );
        ksxtFzxxbViewResp.setKsxtid( dto.getKsxtid() );
        ksxtFzxxbViewResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtFzxxbViewResp.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtFzxxbViewResp.setBt( dto.getBt() );
        ksxtFzxxbViewResp.setKstbxthjdm( dto.getKstbxthjdm() );
        ksxtFzxxbViewResp.setKstbxxlxdm( dto.getKstbxxlxdm() );
        ksxtFzxxbViewResp.setJyqk( dto.getJyqk() );
        ksxtFzxxbViewResp.setClbz( dto.getClbz() );
        ksxtFzxxbViewResp.setCzyid( dto.getCzyid() );
        ksxtFzxxbViewResp.setCzyxm( dto.getCzyxm() );
        ksxtFzxxbViewResp.setCzyip( dto.getCzyip() );
        ksxtFzxxbViewResp.setQyfwdm( dto.getQyfwdm() );
        ksxtFzxxbViewResp.setFsdwsjgsdwdm( dto.getFsdwsjgsdwdm() );
        ksxtFzxxbViewResp.setFsdwsjgsdwmc( dto.getFsdwsjgsdwmc() );
        ksxtFzxxbViewResp.setJsdwsjgsdwdm( dto.getJsdwsjgsdwdm() );
        ksxtFzxxbViewResp.setJsdwsjgsdwmc( dto.getJsdwsjgsdwmc() );
        ksxtFzxxbViewResp.setRksj( dto.getRksj() );
        ksxtFzxxbViewResp.setYdrxm( dto.getYdrxm() );
        ksxtFzxxbViewResp.setYdsj( dto.getYdsj() );

        return ksxtFzxxbViewResp;
    }
}
