package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsxtYwjbxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtYwjbxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtYwjbxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtYwjbxxbViewResp;
import com.zjjcnt.project.ck.base.entity.KsxtYwjbxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:45+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsxtYwjbxxbConvertImpl implements KsxtYwjbxxbConvert {

    @Override
    public KsxtYwjbxxbDTO convert(KsxtYwjbxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsxtYwjbxxbDTO ksxtYwjbxxbDTO = new KsxtYwjbxxbDTO();

        ksxtYwjbxxbDTO.setId( entity.getId() );
        ksxtYwjbxxbDTO.setKsxtid( entity.getKsxtid() );
        ksxtYwjbxxbDTO.setZaglywxtbh( entity.getZaglywxtbh() );
        ksxtYwjbxxbDTO.setZaglywlbdm( entity.getZaglywlbdm() );
        ksxtYwjbxxbDTO.setZaglzwfwsxbm( entity.getZaglzwfwsxbm() );
        ksxtYwjbxxbDTO.setYwlx( entity.getYwlx() );
        ksxtYwjbxxbDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        ksxtYwjbxxbDTO.setSqrxm( entity.getSqrxm() );
        ksxtYwjbxxbDTO.setSqrlxdh( entity.getSqrlxdh() );
        ksxtYwjbxxbDTO.setHzgmsfhm( entity.getHzgmsfhm() );
        ksxtYwjbxxbDTO.setHzxm( entity.getHzxm() );
        ksxtYwjbxxbDTO.setQyfwdm( entity.getQyfwdm() );
        ksxtYwjbxxbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        ksxtYwjbxxbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        ksxtYwjbxxbDTO.setHjdlxdh( entity.getHjdlxdh() );
        ksxtYwjbxxbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        ksxtYwjbxxbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        ksxtYwjbxxbDTO.setSldlxdh( entity.getSldlxdh() );
        ksxtYwjbxxbDTO.setSlrid( entity.getSlrid() );
        ksxtYwjbxxbDTO.setSlrxm( entity.getSlrxm() );
        ksxtYwjbxxbDTO.setSlsj( entity.getSlsj() );
        ksxtYwjbxxbDTO.setRksj( entity.getRksj() );
        ksxtYwjbxxbDTO.setBlzt( entity.getBlzt() );
        ksxtYwjbxxbDTO.setSpywslh( entity.getSpywslh() );
        ksxtYwjbxxbDTO.setSpywlx( entity.getSpywlx() );
        ksxtYwjbxxbDTO.setYwblsj( entity.getYwblsj() );
        ksxtYwjbxxbDTO.setYwblrxm( entity.getYwblrxm() );
        ksxtYwjbxxbDTO.setShjg( entity.getShjg() );
        ksxtYwjbxxbDTO.setShms( entity.getShms() );
        ksxtYwjbxxbDTO.setShrid( entity.getShrid() );
        ksxtYwjbxxbDTO.setShrxm( entity.getShrxm() );
        ksxtYwjbxxbDTO.setShsj( entity.getShsj() );
        ksxtYwjbxxbDTO.setBjsj( entity.getBjsj() );
        ksxtYwjbxxbDTO.setSfxykdbz( entity.getSfxykdbz() );
        ksxtYwjbxxbDTO.setSjrxm( entity.getSjrxm() );
        ksxtYwjbxxbDTO.setSjrgmsfhm( entity.getSjrgmsfhm() );
        ksxtYwjbxxbDTO.setSjrlxdh( entity.getSjrlxdh() );
        ksxtYwjbxxbDTO.setSjdzssxqdm( entity.getSjdzssxqdm() );
        ksxtYwjbxxbDTO.setSjdzqhnxxdz( entity.getSjdzqhnxxdz() );

        return ksxtYwjbxxbDTO;
    }

    @Override
    public KsxtYwjbxxbDO convertToDO(KsxtYwjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtYwjbxxbDO ksxtYwjbxxbDO = new KsxtYwjbxxbDO();

        ksxtYwjbxxbDO.setId( dto.getId() );
        ksxtYwjbxxbDO.setKsxtid( dto.getKsxtid() );
        ksxtYwjbxxbDO.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtYwjbxxbDO.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtYwjbxxbDO.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        ksxtYwjbxxbDO.setYwlx( dto.getYwlx() );
        ksxtYwjbxxbDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksxtYwjbxxbDO.setSqrxm( dto.getSqrxm() );
        ksxtYwjbxxbDO.setSqrlxdh( dto.getSqrlxdh() );
        ksxtYwjbxxbDO.setHzgmsfhm( dto.getHzgmsfhm() );
        ksxtYwjbxxbDO.setHzxm( dto.getHzxm() );
        ksxtYwjbxxbDO.setQyfwdm( dto.getQyfwdm() );
        ksxtYwjbxxbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtYwjbxxbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtYwjbxxbDO.setHjdlxdh( dto.getHjdlxdh() );
        ksxtYwjbxxbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtYwjbxxbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtYwjbxxbDO.setSldlxdh( dto.getSldlxdh() );
        ksxtYwjbxxbDO.setSlrid( dto.getSlrid() );
        ksxtYwjbxxbDO.setSlrxm( dto.getSlrxm() );
        ksxtYwjbxxbDO.setSlsj( dto.getSlsj() );
        ksxtYwjbxxbDO.setRksj( dto.getRksj() );
        ksxtYwjbxxbDO.setBlzt( dto.getBlzt() );
        ksxtYwjbxxbDO.setSpywslh( dto.getSpywslh() );
        ksxtYwjbxxbDO.setSpywlx( dto.getSpywlx() );
        ksxtYwjbxxbDO.setYwblsj( dto.getYwblsj() );
        ksxtYwjbxxbDO.setYwblrxm( dto.getYwblrxm() );
        ksxtYwjbxxbDO.setShjg( dto.getShjg() );
        ksxtYwjbxxbDO.setShms( dto.getShms() );
        ksxtYwjbxxbDO.setShrid( dto.getShrid() );
        ksxtYwjbxxbDO.setShrxm( dto.getShrxm() );
        ksxtYwjbxxbDO.setShsj( dto.getShsj() );
        ksxtYwjbxxbDO.setBjsj( dto.getBjsj() );
        ksxtYwjbxxbDO.setSfxykdbz( dto.getSfxykdbz() );
        ksxtYwjbxxbDO.setSjrxm( dto.getSjrxm() );
        ksxtYwjbxxbDO.setSjrgmsfhm( dto.getSjrgmsfhm() );
        ksxtYwjbxxbDO.setSjrlxdh( dto.getSjrlxdh() );
        ksxtYwjbxxbDO.setSjdzssxqdm( dto.getSjdzssxqdm() );
        ksxtYwjbxxbDO.setSjdzqhnxxdz( dto.getSjdzqhnxxdz() );

        return ksxtYwjbxxbDO;
    }

    @Override
    public KsxtYwjbxxbDTO convertToDTO(KsxtYwjbxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsxtYwjbxxbDTO ksxtYwjbxxbDTO = new KsxtYwjbxxbDTO();

        ksxtYwjbxxbDTO.setYwlx( req.getYwlx() );
        ksxtYwjbxxbDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        ksxtYwjbxxbDTO.setSqrxm( req.getSqrxm() );
        ksxtYwjbxxbDTO.setHjdsjgsdwdm( req.getHjdsjgsdwdm() );
        ksxtYwjbxxbDTO.setSldsjgsdwdm( req.getSldsjgsdwdm() );
        ksxtYwjbxxbDTO.setBlzt( req.getBlzt() );
        ksxtYwjbxxbDTO.setSpywlx( req.getSpywlx() );
        ksxtYwjbxxbDTO.setSlsjStart( req.getSlsjStart() );
        ksxtYwjbxxbDTO.setSlsjEnd( req.getSlsjEnd() );

        return ksxtYwjbxxbDTO;
    }

    @Override
    public KsxtYwjbxxbPageResp convertToPageResp(KsxtYwjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtYwjbxxbPageResp ksxtYwjbxxbPageResp = new KsxtYwjbxxbPageResp();

        ksxtYwjbxxbPageResp.setKsxtid( dto.getKsxtid() );
        ksxtYwjbxxbPageResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtYwjbxxbPageResp.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtYwjbxxbPageResp.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        ksxtYwjbxxbPageResp.setYwlx( dto.getYwlx() );
        ksxtYwjbxxbPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksxtYwjbxxbPageResp.setSqrxm( dto.getSqrxm() );
        ksxtYwjbxxbPageResp.setSqrlxdh( dto.getSqrlxdh() );
        ksxtYwjbxxbPageResp.setHzgmsfhm( dto.getHzgmsfhm() );
        ksxtYwjbxxbPageResp.setHzxm( dto.getHzxm() );
        ksxtYwjbxxbPageResp.setQyfwdm( dto.getQyfwdm() );
        ksxtYwjbxxbPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtYwjbxxbPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtYwjbxxbPageResp.setHjdlxdh( dto.getHjdlxdh() );
        ksxtYwjbxxbPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtYwjbxxbPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtYwjbxxbPageResp.setSldlxdh( dto.getSldlxdh() );
        ksxtYwjbxxbPageResp.setSlrid( dto.getSlrid() );
        ksxtYwjbxxbPageResp.setSlrxm( dto.getSlrxm() );
        ksxtYwjbxxbPageResp.setSlsj( dto.getSlsj() );
        ksxtYwjbxxbPageResp.setRksj( dto.getRksj() );
        ksxtYwjbxxbPageResp.setBlzt( dto.getBlzt() );
        ksxtYwjbxxbPageResp.setSpywslh( dto.getSpywslh() );
        ksxtYwjbxxbPageResp.setSpywlx( dto.getSpywlx() );
        ksxtYwjbxxbPageResp.setYwblsj( dto.getYwblsj() );
        ksxtYwjbxxbPageResp.setYwblrxm( dto.getYwblrxm() );
        ksxtYwjbxxbPageResp.setShjg( dto.getShjg() );
        ksxtYwjbxxbPageResp.setShms( dto.getShms() );
        ksxtYwjbxxbPageResp.setShrid( dto.getShrid() );
        ksxtYwjbxxbPageResp.setShrxm( dto.getShrxm() );
        ksxtYwjbxxbPageResp.setShsj( dto.getShsj() );
        ksxtYwjbxxbPageResp.setBjsj( dto.getBjsj() );
        ksxtYwjbxxbPageResp.setSfxykdbz( dto.getSfxykdbz() );
        ksxtYwjbxxbPageResp.setSjrxm( dto.getSjrxm() );
        ksxtYwjbxxbPageResp.setSjrgmsfhm( dto.getSjrgmsfhm() );
        ksxtYwjbxxbPageResp.setSjrlxdh( dto.getSjrlxdh() );
        ksxtYwjbxxbPageResp.setSjdzssxqdm( dto.getSjdzssxqdm() );
        ksxtYwjbxxbPageResp.setSjdzqhnxxdz( dto.getSjdzqhnxxdz() );

        return ksxtYwjbxxbPageResp;
    }

    @Override
    public KsxtYwjbxxbViewResp convertToViewResp(KsxtYwjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtYwjbxxbViewResp ksxtYwjbxxbViewResp = new KsxtYwjbxxbViewResp();

        ksxtYwjbxxbViewResp.setKsxtid( dto.getKsxtid() );
        ksxtYwjbxxbViewResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtYwjbxxbViewResp.setZaglywlbdm( dto.getZaglywlbdm() );
        ksxtYwjbxxbViewResp.setZaglzwfwsxbm( dto.getZaglzwfwsxbm() );
        ksxtYwjbxxbViewResp.setYwlx( dto.getYwlx() );
        ksxtYwjbxxbViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ksxtYwjbxxbViewResp.setSqrxm( dto.getSqrxm() );
        ksxtYwjbxxbViewResp.setSqrlxdh( dto.getSqrlxdh() );
        ksxtYwjbxxbViewResp.setHzgmsfhm( dto.getHzgmsfhm() );
        ksxtYwjbxxbViewResp.setHzxm( dto.getHzxm() );
        ksxtYwjbxxbViewResp.setQyfwdm( dto.getQyfwdm() );
        ksxtYwjbxxbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtYwjbxxbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtYwjbxxbViewResp.setHjdlxdh( dto.getHjdlxdh() );
        ksxtYwjbxxbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtYwjbxxbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtYwjbxxbViewResp.setSldlxdh( dto.getSldlxdh() );
        ksxtYwjbxxbViewResp.setSlrid( dto.getSlrid() );
        ksxtYwjbxxbViewResp.setSlrxm( dto.getSlrxm() );
        ksxtYwjbxxbViewResp.setSlsj( dto.getSlsj() );
        ksxtYwjbxxbViewResp.setRksj( dto.getRksj() );
        ksxtYwjbxxbViewResp.setBlzt( dto.getBlzt() );
        ksxtYwjbxxbViewResp.setSpywslh( dto.getSpywslh() );
        ksxtYwjbxxbViewResp.setSpywlx( dto.getSpywlx() );
        ksxtYwjbxxbViewResp.setYwblsj( dto.getYwblsj() );
        ksxtYwjbxxbViewResp.setYwblrxm( dto.getYwblrxm() );
        ksxtYwjbxxbViewResp.setShjg( dto.getShjg() );
        ksxtYwjbxxbViewResp.setShms( dto.getShms() );
        ksxtYwjbxxbViewResp.setShrid( dto.getShrid() );
        ksxtYwjbxxbViewResp.setShrxm( dto.getShrxm() );
        ksxtYwjbxxbViewResp.setShsj( dto.getShsj() );
        ksxtYwjbxxbViewResp.setBjsj( dto.getBjsj() );
        ksxtYwjbxxbViewResp.setSfxykdbz( dto.getSfxykdbz() );
        ksxtYwjbxxbViewResp.setSjrxm( dto.getSjrxm() );
        ksxtYwjbxxbViewResp.setSjrgmsfhm( dto.getSjrgmsfhm() );
        ksxtYwjbxxbViewResp.setSjrlxdh( dto.getSjrlxdh() );
        ksxtYwjbxxbViewResp.setSjdzssxqdm( dto.getSjdzssxqdm() );
        ksxtYwjbxxbViewResp.setSjdzqhnxxdz( dto.getSjdzqhnxxdz() );

        return ksxtYwjbxxbViewResp;
    }
}
