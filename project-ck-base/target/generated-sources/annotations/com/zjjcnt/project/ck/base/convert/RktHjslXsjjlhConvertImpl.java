package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktHjslXsjjlhDTO;
import com.zjjcnt.project.ck.base.dto.req.RktHjslXsjjlhCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslXsjjlhPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslXsjjlhUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslXsjjlhCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslXsjjlhPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslXsjjlhViewResp;
import com.zjjcnt.project.ck.base.entity.RktHjslXsjjlhDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:47+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktHjslXsjjlhConvertImpl implements RktHjslXsjjlhConvert {

    @Override
    public RktHjslXsjjlhDTO convert(RktHjslXsjjlhDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktHjslXsjjlhDTO rktHjslXsjjlhDTO = new RktHjslXsjjlhDTO();

        rktHjslXsjjlhDTO.setId( entity.getId() );
        rktHjslXsjjlhDTO.setYwslnbbh( entity.getYwslnbbh() );
        rktHjslXsjjlhDTO.setHhnbid( entity.getHhnbid() );
        rktHjslXsjjlhDTO.setYwslh( entity.getYwslh() );
        rktHjslXsjjlhDTO.setHh( entity.getHh() );
        rktHjslXsjjlhDTO.setHhid( entity.getHhid() );
        rktHjslXsjjlhDTO.setHlx( entity.getHlx() );
        rktHjslXsjjlhDTO.setHmc( entity.getHmc() );
        rktHjslXsjjlhDTO.setHjddzbm( entity.getHjddzbm() );
        rktHjslXsjjlhDTO.setHjdssxq( entity.getHjdssxq() );
        rktHjslXsjjlhDTO.setHjdxxdz( entity.getHjdxxdz() );
        rktHjslXsjjlhDTO.setHjdrhyzbs( entity.getHjdrhyzbs() );
        rktHjslXsjjlhDTO.setJlx( entity.getJlx() );
        rktHjslXsjjlhDTO.setMlph( entity.getMlph() );
        rktHjslXsjjlhDTO.setMlxz( entity.getMlxz() );
        rktHjslXsjjlhDTO.setLcywlx( entity.getLcywlx() );
        rktHjslXsjjlhDTO.setLcdyid( entity.getLcdyid() );
        rktHjslXsjjlhDTO.setLcmc( entity.getLcmc() );
        rktHjslXsjjlhDTO.setLcslid( entity.getLcslid() );
        rktHjslXsjjlhDTO.setLcywbt( entity.getLcywbt() );
        rktHjslXsjjlhDTO.setLcrwjd( entity.getLcrwjd() );
        rktHjslXsjjlhDTO.setLczt( entity.getLczt() );
        rktHjslXsjjlhDTO.setBlzt( entity.getBlzt() );
        rktHjslXsjjlhDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        rktHjslXsjjlhDTO.setSqrxm( entity.getSqrxm() );
        rktHjslXsjjlhDTO.setSqrxb( entity.getSqrxb() );
        rktHjslXsjjlhDTO.setSqrlxdh( entity.getSqrlxdh() );
        rktHjslXsjjlhDTO.setSqrybdrgx( entity.getSqrybdrgx() );
        rktHjslXsjjlhDTO.setSqrzzssxq( entity.getSqrzzssxq() );
        rktHjslXsjjlhDTO.setSqrzzxz( entity.getSqrzzxz() );
        rktHjslXsjjlhDTO.setSqrhkdjjg( entity.getSqrhkdjjg() );
        rktHjslXsjjlhDTO.setSqrq( entity.getSqrq() );
        rktHjslXsjjlhDTO.setSpdwgajgjgdm( entity.getSpdwgajgjgdm() );
        rktHjslXsjjlhDTO.setSpdwgajgmc( entity.getSpdwgajgmc() );
        rktHjslXsjjlhDTO.setSprxm( entity.getSprxm() );
        rktHjslXsjjlhDTO.setSpsj( entity.getSpsj() );
        rktHjslXsjjlhDTO.setSpjgdm( entity.getSpjgdm() );
        rktHjslXsjjlhDTO.setSpyj( entity.getSpyj() );
        rktHjslXsjjlhDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        rktHjslXsjjlhDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        rktHjslXsjjlhDTO.setSlrxm( entity.getSlrxm() );
        rktHjslXsjjlhDTO.setSlsj( entity.getSlsj() );
        rktHjslXsjjlhDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        rktHjslXsjjlhDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        rktHjslXsjjlhDTO.setJcwh( entity.getJcwh() );
        rktHjslXsjjlhDTO.setBz( entity.getBz() );
        rktHjslXsjjlhDTO.setXh( entity.getXh() );
        rktHjslXsjjlhDTO.setHkxz( entity.getHkxz() );
        rktHjslXsjjlhDTO.setHb( entity.getHb() );
        rktHjslXsjjlhDTO.setYhzgx( entity.getYhzgx() );
        rktHjslXsjjlhDTO.setCxsx( entity.getCxsx() );
        rktHjslXsjjlhDTO.setGmsfhm( entity.getGmsfhm() );
        rktHjslXsjjlhDTO.setXm( entity.getXm() );
        rktHjslXsjjlhDTO.setX( entity.getX() );
        rktHjslXsjjlhDTO.setM( entity.getM() );
        rktHjslXsjjlhDTO.setCym( entity.getCym() );
        rktHjslXsjjlhDTO.setXmpy( entity.getXmpy() );
        rktHjslXsjjlhDTO.setCympy( entity.getCympy() );
        rktHjslXsjjlhDTO.setXb( entity.getXb() );
        rktHjslXsjjlhDTO.setMz( entity.getMz() );
        rktHjslXsjjlhDTO.setJggjdq( entity.getJggjdq() );
        rktHjslXsjjlhDTO.setJgssxq( entity.getJgssxq() );
        rktHjslXsjjlhDTO.setJgxz( entity.getJgxz() );
        rktHjslXsjjlhDTO.setCsrq( entity.getCsrq() );
        rktHjslXsjjlhDTO.setCssj( entity.getCssj() );
        rktHjslXsjjlhDTO.setCsdgjdq( entity.getCsdgjdq() );
        rktHjslXsjjlhDTO.setCsdssxq( entity.getCsdssxq() );
        rktHjslXsjjlhDTO.setCsdxz( entity.getCsdxz() );
        rktHjslXsjjlhDTO.setWhcd( entity.getWhcd() );
        rktHjslXsjjlhDTO.setHyzk( entity.getHyzk() );
        rktHjslXsjjlhDTO.setCyzkdwbm( entity.getCyzkdwbm() );
        rktHjslXsjjlhDTO.setCyzkdwmc( entity.getCyzkdwmc() );
        rktHjslXsjjlhDTO.setZy( entity.getZy() );
        rktHjslXsjjlhDTO.setZylb( entity.getZylb() );
        rktHjslXsjjlhDTO.setZjxy( entity.getZjxy() );
        rktHjslXsjjlhDTO.setSg( entity.getSg() );
        rktHjslXsjjlhDTO.setXx( entity.getXx() );
        rktHjslXsjjlhDTO.setByzk( entity.getByzk() );
        rktHjslXsjjlhDTO.setXxjb( entity.getXxjb() );
        rktHjslXsjjlhDTO.setLxdh( entity.getLxdh() );
        rktHjslXsjjlhDTO.setFqxm( entity.getFqxm() );
        rktHjslXsjjlhDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        rktHjslXsjjlhDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        rktHjslXsjjlhDTO.setFqzjhm( entity.getFqzjhm() );
        rktHjslXsjjlhDTO.setFqwwx( entity.getFqwwx() );
        rktHjslXsjjlhDTO.setFqwwm( entity.getFqwwm() );
        rktHjslXsjjlhDTO.setMqxm( entity.getMqxm() );
        rktHjslXsjjlhDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        rktHjslXsjjlhDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        rktHjslXsjjlhDTO.setMqzjhm( entity.getMqzjhm() );
        rktHjslXsjjlhDTO.setMqwwx( entity.getMqwwx() );
        rktHjslXsjjlhDTO.setMqwwm( entity.getMqwwm() );
        rktHjslXsjjlhDTO.setPoxm( entity.getPoxm() );
        rktHjslXsjjlhDTO.setPogmsfhm( entity.getPogmsfhm() );
        rktHjslXsjjlhDTO.setPocyzjdm( entity.getPocyzjdm() );
        rktHjslXsjjlhDTO.setPozjhm( entity.getPozjhm() );
        rktHjslXsjjlhDTO.setPowwx( entity.getPowwx() );
        rktHjslXsjjlhDTO.setPowwm( entity.getPowwm() );
        rktHjslXsjjlhDTO.setJhryxm( entity.getJhryxm() );
        rktHjslXsjjlhDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        rktHjslXsjjlhDTO.setJhryjhgx( entity.getJhryjhgx() );
        rktHjslXsjjlhDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        rktHjslXsjjlhDTO.setJhryzjhm( entity.getJhryzjhm() );
        rktHjslXsjjlhDTO.setJhrywwx( entity.getJhrywwx() );
        rktHjslXsjjlhDTO.setJhrywwm( entity.getJhrywwm() );
        rktHjslXsjjlhDTO.setJhrylxdh( entity.getJhrylxdh() );
        rktHjslXsjjlhDTO.setJhrexm( entity.getJhrexm() );
        rktHjslXsjjlhDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        rktHjslXsjjlhDTO.setJhrejhgx( entity.getJhrejhgx() );
        rktHjslXsjjlhDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        rktHjslXsjjlhDTO.setJhrezjhm( entity.getJhrezjhm() );
        rktHjslXsjjlhDTO.setJhrewwx( entity.getJhrewwx() );
        rktHjslXsjjlhDTO.setJhrewwm( entity.getJhrewwm() );
        rktHjslXsjjlhDTO.setJhrelxdh( entity.getJhrelxdh() );
        rktHjslXsjjlhDTO.setZjlb( entity.getZjlb() );
        rktHjslXsjjlhDTO.setQfjg( entity.getQfjg() );
        rktHjslXsjjlhDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        rktHjslXsjjlhDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        rktHjslXsjjlhDTO.setFxqhkszdssxqdm( entity.getFxqhkszdssxqdm() );
        rktHjslXsjjlhDTO.setFxqhkszdqhnxxdz( entity.getFxqhkszdqhnxxdz() );
        rktHjslXsjjlhDTO.setFxljszdssxqdm( entity.getFxljszdssxqdm() );
        rktHjslXsjjlhDTO.setFxljszdqhnxxdz( entity.getFxljszdqhnxxdz() );
        rktHjslXsjjlhDTO.setBdfw( entity.getBdfw() );
        rktHjslXsjjlhDTO.setBlhjywid( entity.getBlhjywid() );
        rktHjslXsjjlhDTO.setHjywblsj( entity.getHjywblsj() );
        rktHjslXsjjlhDTO.setQtssxq( entity.getQtssxq() );
        rktHjslXsjjlhDTO.setQtzz( entity.getQtzz() );

        return rktHjslXsjjlhDTO;
    }

    @Override
    public RktHjslXsjjlhDO convertToDO(RktHjslXsjjlhDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslXsjjlhDO rktHjslXsjjlhDO = new RktHjslXsjjlhDO();

        rktHjslXsjjlhDO.setId( dto.getId() );
        rktHjslXsjjlhDO.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslXsjjlhDO.setHhnbid( dto.getHhnbid() );
        rktHjslXsjjlhDO.setYwslh( dto.getYwslh() );
        rktHjslXsjjlhDO.setHh( dto.getHh() );
        rktHjslXsjjlhDO.setHhid( dto.getHhid() );
        rktHjslXsjjlhDO.setHlx( dto.getHlx() );
        rktHjslXsjjlhDO.setHmc( dto.getHmc() );
        rktHjslXsjjlhDO.setHjddzbm( dto.getHjddzbm() );
        rktHjslXsjjlhDO.setHjdssxq( dto.getHjdssxq() );
        rktHjslXsjjlhDO.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslXsjjlhDO.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslXsjjlhDO.setJlx( dto.getJlx() );
        rktHjslXsjjlhDO.setMlph( dto.getMlph() );
        rktHjslXsjjlhDO.setMlxz( dto.getMlxz() );
        rktHjslXsjjlhDO.setLcywlx( dto.getLcywlx() );
        rktHjslXsjjlhDO.setLcdyid( dto.getLcdyid() );
        rktHjslXsjjlhDO.setLcmc( dto.getLcmc() );
        rktHjslXsjjlhDO.setLcslid( dto.getLcslid() );
        rktHjslXsjjlhDO.setLcywbt( dto.getLcywbt() );
        rktHjslXsjjlhDO.setLcrwjd( dto.getLcrwjd() );
        rktHjslXsjjlhDO.setLczt( dto.getLczt() );
        rktHjslXsjjlhDO.setBlzt( dto.getBlzt() );
        rktHjslXsjjlhDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslXsjjlhDO.setSqrxm( dto.getSqrxm() );
        rktHjslXsjjlhDO.setSqrxb( dto.getSqrxb() );
        rktHjslXsjjlhDO.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslXsjjlhDO.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslXsjjlhDO.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslXsjjlhDO.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslXsjjlhDO.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslXsjjlhDO.setSqrq( dto.getSqrq() );
        rktHjslXsjjlhDO.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslXsjjlhDO.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslXsjjlhDO.setSprxm( dto.getSprxm() );
        rktHjslXsjjlhDO.setSpsj( dto.getSpsj() );
        rktHjslXsjjlhDO.setSpjgdm( dto.getSpjgdm() );
        rktHjslXsjjlhDO.setSpyj( dto.getSpyj() );
        rktHjslXsjjlhDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslXsjjlhDO.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslXsjjlhDO.setSlrxm( dto.getSlrxm() );
        rktHjslXsjjlhDO.setSlsj( dto.getSlsj() );
        rktHjslXsjjlhDO.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslXsjjlhDO.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslXsjjlhDO.setJcwh( dto.getJcwh() );
        rktHjslXsjjlhDO.setBz( dto.getBz() );
        rktHjslXsjjlhDO.setXh( dto.getXh() );
        rktHjslXsjjlhDO.setHkxz( dto.getHkxz() );
        rktHjslXsjjlhDO.setHb( dto.getHb() );
        rktHjslXsjjlhDO.setYhzgx( dto.getYhzgx() );
        rktHjslXsjjlhDO.setCxsx( dto.getCxsx() );
        rktHjslXsjjlhDO.setGmsfhm( dto.getGmsfhm() );
        rktHjslXsjjlhDO.setXm( dto.getXm() );
        rktHjslXsjjlhDO.setX( dto.getX() );
        rktHjslXsjjlhDO.setM( dto.getM() );
        rktHjslXsjjlhDO.setCym( dto.getCym() );
        rktHjslXsjjlhDO.setXmpy( dto.getXmpy() );
        rktHjslXsjjlhDO.setCympy( dto.getCympy() );
        rktHjslXsjjlhDO.setXb( dto.getXb() );
        rktHjslXsjjlhDO.setMz( dto.getMz() );
        rktHjslXsjjlhDO.setJggjdq( dto.getJggjdq() );
        rktHjslXsjjlhDO.setJgssxq( dto.getJgssxq() );
        rktHjslXsjjlhDO.setJgxz( dto.getJgxz() );
        rktHjslXsjjlhDO.setCsrq( dto.getCsrq() );
        rktHjslXsjjlhDO.setCssj( dto.getCssj() );
        rktHjslXsjjlhDO.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslXsjjlhDO.setCsdssxq( dto.getCsdssxq() );
        rktHjslXsjjlhDO.setCsdxz( dto.getCsdxz() );
        rktHjslXsjjlhDO.setWhcd( dto.getWhcd() );
        rktHjslXsjjlhDO.setHyzk( dto.getHyzk() );
        rktHjslXsjjlhDO.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslXsjjlhDO.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslXsjjlhDO.setZy( dto.getZy() );
        rktHjslXsjjlhDO.setZylb( dto.getZylb() );
        rktHjslXsjjlhDO.setZjxy( dto.getZjxy() );
        rktHjslXsjjlhDO.setSg( dto.getSg() );
        rktHjslXsjjlhDO.setXx( dto.getXx() );
        rktHjslXsjjlhDO.setByzk( dto.getByzk() );
        rktHjslXsjjlhDO.setXxjb( dto.getXxjb() );
        rktHjslXsjjlhDO.setLxdh( dto.getLxdh() );
        rktHjslXsjjlhDO.setFqxm( dto.getFqxm() );
        rktHjslXsjjlhDO.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslXsjjlhDO.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslXsjjlhDO.setFqzjhm( dto.getFqzjhm() );
        rktHjslXsjjlhDO.setFqwwx( dto.getFqwwx() );
        rktHjslXsjjlhDO.setFqwwm( dto.getFqwwm() );
        rktHjslXsjjlhDO.setMqxm( dto.getMqxm() );
        rktHjslXsjjlhDO.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslXsjjlhDO.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslXsjjlhDO.setMqzjhm( dto.getMqzjhm() );
        rktHjslXsjjlhDO.setMqwwx( dto.getMqwwx() );
        rktHjslXsjjlhDO.setMqwwm( dto.getMqwwm() );
        rktHjslXsjjlhDO.setPoxm( dto.getPoxm() );
        rktHjslXsjjlhDO.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslXsjjlhDO.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslXsjjlhDO.setPozjhm( dto.getPozjhm() );
        rktHjslXsjjlhDO.setPowwx( dto.getPowwx() );
        rktHjslXsjjlhDO.setPowwm( dto.getPowwm() );
        rktHjslXsjjlhDO.setJhryxm( dto.getJhryxm() );
        rktHjslXsjjlhDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslXsjjlhDO.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslXsjjlhDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslXsjjlhDO.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslXsjjlhDO.setJhrywwx( dto.getJhrywwx() );
        rktHjslXsjjlhDO.setJhrywwm( dto.getJhrywwm() );
        rktHjslXsjjlhDO.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslXsjjlhDO.setJhrexm( dto.getJhrexm() );
        rktHjslXsjjlhDO.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslXsjjlhDO.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslXsjjlhDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslXsjjlhDO.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslXsjjlhDO.setJhrewwx( dto.getJhrewwx() );
        rktHjslXsjjlhDO.setJhrewwm( dto.getJhrewwm() );
        rktHjslXsjjlhDO.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslXsjjlhDO.setZjlb( dto.getZjlb() );
        rktHjslXsjjlhDO.setQfjg( dto.getQfjg() );
        rktHjslXsjjlhDO.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslXsjjlhDO.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslXsjjlhDO.setFxqhkszdssxqdm( dto.getFxqhkszdssxqdm() );
        rktHjslXsjjlhDO.setFxqhkszdqhnxxdz( dto.getFxqhkszdqhnxxdz() );
        rktHjslXsjjlhDO.setFxljszdssxqdm( dto.getFxljszdssxqdm() );
        rktHjslXsjjlhDO.setFxljszdqhnxxdz( dto.getFxljszdqhnxxdz() );
        rktHjslXsjjlhDO.setBdfw( dto.getBdfw() );
        rktHjslXsjjlhDO.setBlhjywid( dto.getBlhjywid() );
        rktHjslXsjjlhDO.setHjywblsj( dto.getHjywblsj() );
        rktHjslXsjjlhDO.setQtssxq( dto.getQtssxq() );
        rktHjslXsjjlhDO.setQtzz( dto.getQtzz() );

        return rktHjslXsjjlhDO;
    }

    @Override
    public RktHjslXsjjlhDTO convertToDTO(RktHjslXsjjlhPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslXsjjlhDTO rktHjslXsjjlhDTO = new RktHjslXsjjlhDTO();

        rktHjslXsjjlhDTO.setHhnbid( req.getHhnbid() );
        rktHjslXsjjlhDTO.setYwslh( req.getYwslh() );
        rktHjslXsjjlhDTO.setHh( req.getHh() );
        rktHjslXsjjlhDTO.setHhid( req.getHhid() );
        rktHjslXsjjlhDTO.setHlx( req.getHlx() );
        rktHjslXsjjlhDTO.setHmc( req.getHmc() );
        rktHjslXsjjlhDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslXsjjlhDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslXsjjlhDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslXsjjlhDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslXsjjlhDTO.setJlx( req.getJlx() );
        rktHjslXsjjlhDTO.setMlph( req.getMlph() );
        rktHjslXsjjlhDTO.setMlxz( req.getMlxz() );
        rktHjslXsjjlhDTO.setLcywlx( req.getLcywlx() );
        rktHjslXsjjlhDTO.setLcdyid( req.getLcdyid() );
        rktHjslXsjjlhDTO.setLcmc( req.getLcmc() );
        rktHjslXsjjlhDTO.setLcslid( req.getLcslid() );
        rktHjslXsjjlhDTO.setLcywbt( req.getLcywbt() );
        rktHjslXsjjlhDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslXsjjlhDTO.setLczt( req.getLczt() );
        rktHjslXsjjlhDTO.setBlzt( req.getBlzt() );
        rktHjslXsjjlhDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslXsjjlhDTO.setSqrxm( req.getSqrxm() );
        rktHjslXsjjlhDTO.setSqrxb( req.getSqrxb() );
        rktHjslXsjjlhDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslXsjjlhDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslXsjjlhDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslXsjjlhDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslXsjjlhDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslXsjjlhDTO.setSqrq( req.getSqrq() );
        rktHjslXsjjlhDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslXsjjlhDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslXsjjlhDTO.setSprxm( req.getSprxm() );
        rktHjslXsjjlhDTO.setSpsj( req.getSpsj() );
        rktHjslXsjjlhDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslXsjjlhDTO.setSpyj( req.getSpyj() );
        rktHjslXsjjlhDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslXsjjlhDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslXsjjlhDTO.setSlrxm( req.getSlrxm() );
        rktHjslXsjjlhDTO.setSlsj( req.getSlsj() );
        rktHjslXsjjlhDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslXsjjlhDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslXsjjlhDTO.setJcwh( req.getJcwh() );
        rktHjslXsjjlhDTO.setBz( req.getBz() );
        rktHjslXsjjlhDTO.setXh( req.getXh() );
        rktHjslXsjjlhDTO.setHkxz( req.getHkxz() );
        rktHjslXsjjlhDTO.setHb( req.getHb() );
        rktHjslXsjjlhDTO.setYhzgx( req.getYhzgx() );
        rktHjslXsjjlhDTO.setCxsx( req.getCxsx() );
        rktHjslXsjjlhDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslXsjjlhDTO.setXm( req.getXm() );
        rktHjslXsjjlhDTO.setX( req.getX() );
        rktHjslXsjjlhDTO.setM( req.getM() );
        rktHjslXsjjlhDTO.setCym( req.getCym() );
        rktHjslXsjjlhDTO.setXmpy( req.getXmpy() );
        rktHjslXsjjlhDTO.setCympy( req.getCympy() );
        rktHjslXsjjlhDTO.setXb( req.getXb() );
        rktHjslXsjjlhDTO.setMz( req.getMz() );
        rktHjslXsjjlhDTO.setJggjdq( req.getJggjdq() );
        rktHjslXsjjlhDTO.setJgssxq( req.getJgssxq() );
        rktHjslXsjjlhDTO.setJgxz( req.getJgxz() );
        rktHjslXsjjlhDTO.setCsrq( req.getCsrq() );
        rktHjslXsjjlhDTO.setCssj( req.getCssj() );
        rktHjslXsjjlhDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslXsjjlhDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslXsjjlhDTO.setCsdxz( req.getCsdxz() );
        rktHjslXsjjlhDTO.setWhcd( req.getWhcd() );
        rktHjslXsjjlhDTO.setHyzk( req.getHyzk() );
        rktHjslXsjjlhDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslXsjjlhDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslXsjjlhDTO.setZy( req.getZy() );
        rktHjslXsjjlhDTO.setZylb( req.getZylb() );
        rktHjslXsjjlhDTO.setZjxy( req.getZjxy() );
        rktHjslXsjjlhDTO.setSg( req.getSg() );
        rktHjslXsjjlhDTO.setXx( req.getXx() );
        rktHjslXsjjlhDTO.setByzk( req.getByzk() );
        rktHjslXsjjlhDTO.setXxjb( req.getXxjb() );
        rktHjslXsjjlhDTO.setLxdh( req.getLxdh() );
        rktHjslXsjjlhDTO.setFqxm( req.getFqxm() );
        rktHjslXsjjlhDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslXsjjlhDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslXsjjlhDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslXsjjlhDTO.setFqwwx( req.getFqwwx() );
        rktHjslXsjjlhDTO.setFqwwm( req.getFqwwm() );
        rktHjslXsjjlhDTO.setMqxm( req.getMqxm() );
        rktHjslXsjjlhDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslXsjjlhDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslXsjjlhDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslXsjjlhDTO.setMqwwx( req.getMqwwx() );
        rktHjslXsjjlhDTO.setMqwwm( req.getMqwwm() );
        rktHjslXsjjlhDTO.setPoxm( req.getPoxm() );
        rktHjslXsjjlhDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslXsjjlhDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslXsjjlhDTO.setPozjhm( req.getPozjhm() );
        rktHjslXsjjlhDTO.setPowwx( req.getPowwx() );
        rktHjslXsjjlhDTO.setPowwm( req.getPowwm() );
        rktHjslXsjjlhDTO.setJhryxm( req.getJhryxm() );
        rktHjslXsjjlhDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslXsjjlhDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslXsjjlhDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslXsjjlhDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslXsjjlhDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslXsjjlhDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslXsjjlhDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslXsjjlhDTO.setJhrexm( req.getJhrexm() );
        rktHjslXsjjlhDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslXsjjlhDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslXsjjlhDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslXsjjlhDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslXsjjlhDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslXsjjlhDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslXsjjlhDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslXsjjlhDTO.setZjlb( req.getZjlb() );
        rktHjslXsjjlhDTO.setQfjg( req.getQfjg() );
        rktHjslXsjjlhDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslXsjjlhDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslXsjjlhDTO.setFxqhkszdssxqdm( req.getFxqhkszdssxqdm() );
        rktHjslXsjjlhDTO.setFxqhkszdqhnxxdz( req.getFxqhkszdqhnxxdz() );
        rktHjslXsjjlhDTO.setFxljszdssxqdm( req.getFxljszdssxqdm() );
        rktHjslXsjjlhDTO.setFxljszdqhnxxdz( req.getFxljszdqhnxxdz() );
        rktHjslXsjjlhDTO.setBdfw( req.getBdfw() );
        rktHjslXsjjlhDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslXsjjlhDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslXsjjlhDTO.setQtssxq( req.getQtssxq() );
        rktHjslXsjjlhDTO.setQtzz( req.getQtzz() );

        return rktHjslXsjjlhDTO;
    }

    @Override
    public RktHjslXsjjlhDTO convertToDTO(RktHjslXsjjlhCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslXsjjlhDTO rktHjslXsjjlhDTO = new RktHjslXsjjlhDTO();

        rktHjslXsjjlhDTO.setHhnbid( req.getHhnbid() );
        rktHjslXsjjlhDTO.setYwslh( req.getYwslh() );
        rktHjslXsjjlhDTO.setHh( req.getHh() );
        rktHjslXsjjlhDTO.setHhid( req.getHhid() );
        rktHjslXsjjlhDTO.setHlx( req.getHlx() );
        rktHjslXsjjlhDTO.setHmc( req.getHmc() );
        rktHjslXsjjlhDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslXsjjlhDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslXsjjlhDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslXsjjlhDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslXsjjlhDTO.setJlx( req.getJlx() );
        rktHjslXsjjlhDTO.setMlph( req.getMlph() );
        rktHjslXsjjlhDTO.setMlxz( req.getMlxz() );
        rktHjslXsjjlhDTO.setLcywlx( req.getLcywlx() );
        rktHjslXsjjlhDTO.setLcdyid( req.getLcdyid() );
        rktHjslXsjjlhDTO.setLcmc( req.getLcmc() );
        rktHjslXsjjlhDTO.setLcslid( req.getLcslid() );
        rktHjslXsjjlhDTO.setLcywbt( req.getLcywbt() );
        rktHjslXsjjlhDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslXsjjlhDTO.setLczt( req.getLczt() );
        rktHjslXsjjlhDTO.setBlzt( req.getBlzt() );
        rktHjslXsjjlhDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslXsjjlhDTO.setSqrxm( req.getSqrxm() );
        rktHjslXsjjlhDTO.setSqrxb( req.getSqrxb() );
        rktHjslXsjjlhDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslXsjjlhDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslXsjjlhDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslXsjjlhDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslXsjjlhDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslXsjjlhDTO.setSqrq( req.getSqrq() );
        rktHjslXsjjlhDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslXsjjlhDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslXsjjlhDTO.setSprxm( req.getSprxm() );
        rktHjslXsjjlhDTO.setSpsj( req.getSpsj() );
        rktHjslXsjjlhDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslXsjjlhDTO.setSpyj( req.getSpyj() );
        rktHjslXsjjlhDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslXsjjlhDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslXsjjlhDTO.setSlrxm( req.getSlrxm() );
        rktHjslXsjjlhDTO.setSlsj( req.getSlsj() );
        rktHjslXsjjlhDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslXsjjlhDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslXsjjlhDTO.setJcwh( req.getJcwh() );
        rktHjslXsjjlhDTO.setBz( req.getBz() );
        rktHjslXsjjlhDTO.setXh( req.getXh() );
        rktHjslXsjjlhDTO.setHkxz( req.getHkxz() );
        rktHjslXsjjlhDTO.setHb( req.getHb() );
        rktHjslXsjjlhDTO.setYhzgx( req.getYhzgx() );
        rktHjslXsjjlhDTO.setCxsx( req.getCxsx() );
        rktHjslXsjjlhDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslXsjjlhDTO.setXm( req.getXm() );
        rktHjslXsjjlhDTO.setX( req.getX() );
        rktHjslXsjjlhDTO.setM( req.getM() );
        rktHjslXsjjlhDTO.setCym( req.getCym() );
        rktHjslXsjjlhDTO.setXmpy( req.getXmpy() );
        rktHjslXsjjlhDTO.setCympy( req.getCympy() );
        rktHjslXsjjlhDTO.setXb( req.getXb() );
        rktHjslXsjjlhDTO.setMz( req.getMz() );
        rktHjslXsjjlhDTO.setJggjdq( req.getJggjdq() );
        rktHjslXsjjlhDTO.setJgssxq( req.getJgssxq() );
        rktHjslXsjjlhDTO.setJgxz( req.getJgxz() );
        rktHjslXsjjlhDTO.setCsrq( req.getCsrq() );
        rktHjslXsjjlhDTO.setCssj( req.getCssj() );
        rktHjslXsjjlhDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslXsjjlhDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslXsjjlhDTO.setCsdxz( req.getCsdxz() );
        rktHjslXsjjlhDTO.setWhcd( req.getWhcd() );
        rktHjslXsjjlhDTO.setHyzk( req.getHyzk() );
        rktHjslXsjjlhDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslXsjjlhDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslXsjjlhDTO.setZy( req.getZy() );
        rktHjslXsjjlhDTO.setZylb( req.getZylb() );
        rktHjslXsjjlhDTO.setZjxy( req.getZjxy() );
        rktHjslXsjjlhDTO.setSg( req.getSg() );
        rktHjslXsjjlhDTO.setXx( req.getXx() );
        rktHjslXsjjlhDTO.setByzk( req.getByzk() );
        rktHjslXsjjlhDTO.setXxjb( req.getXxjb() );
        rktHjslXsjjlhDTO.setLxdh( req.getLxdh() );
        rktHjslXsjjlhDTO.setFqxm( req.getFqxm() );
        rktHjslXsjjlhDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslXsjjlhDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslXsjjlhDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslXsjjlhDTO.setFqwwx( req.getFqwwx() );
        rktHjslXsjjlhDTO.setFqwwm( req.getFqwwm() );
        rktHjslXsjjlhDTO.setMqxm( req.getMqxm() );
        rktHjslXsjjlhDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslXsjjlhDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslXsjjlhDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslXsjjlhDTO.setMqwwx( req.getMqwwx() );
        rktHjslXsjjlhDTO.setMqwwm( req.getMqwwm() );
        rktHjslXsjjlhDTO.setPoxm( req.getPoxm() );
        rktHjslXsjjlhDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslXsjjlhDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslXsjjlhDTO.setPozjhm( req.getPozjhm() );
        rktHjslXsjjlhDTO.setPowwx( req.getPowwx() );
        rktHjslXsjjlhDTO.setPowwm( req.getPowwm() );
        rktHjslXsjjlhDTO.setJhryxm( req.getJhryxm() );
        rktHjslXsjjlhDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslXsjjlhDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslXsjjlhDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslXsjjlhDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslXsjjlhDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslXsjjlhDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslXsjjlhDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslXsjjlhDTO.setJhrexm( req.getJhrexm() );
        rktHjslXsjjlhDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslXsjjlhDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslXsjjlhDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslXsjjlhDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslXsjjlhDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslXsjjlhDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslXsjjlhDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslXsjjlhDTO.setZjlb( req.getZjlb() );
        rktHjslXsjjlhDTO.setQfjg( req.getQfjg() );
        rktHjslXsjjlhDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslXsjjlhDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslXsjjlhDTO.setFxqhkszdssxqdm( req.getFxqhkszdssxqdm() );
        rktHjslXsjjlhDTO.setFxqhkszdqhnxxdz( req.getFxqhkszdqhnxxdz() );
        rktHjslXsjjlhDTO.setFxljszdssxqdm( req.getFxljszdssxqdm() );
        rktHjslXsjjlhDTO.setFxljszdqhnxxdz( req.getFxljszdqhnxxdz() );
        rktHjslXsjjlhDTO.setBdfw( req.getBdfw() );
        rktHjslXsjjlhDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslXsjjlhDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslXsjjlhDTO.setQtssxq( req.getQtssxq() );
        rktHjslXsjjlhDTO.setQtzz( req.getQtzz() );

        return rktHjslXsjjlhDTO;
    }

    @Override
    public RktHjslXsjjlhDTO convertToDTO(RktHjslXsjjlhUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslXsjjlhDTO rktHjslXsjjlhDTO = new RktHjslXsjjlhDTO();

        rktHjslXsjjlhDTO.setYwslnbbh( req.getYwslnbbh() );
        rktHjslXsjjlhDTO.setHhnbid( req.getHhnbid() );
        rktHjslXsjjlhDTO.setYwslh( req.getYwslh() );
        rktHjslXsjjlhDTO.setHh( req.getHh() );
        rktHjslXsjjlhDTO.setHhid( req.getHhid() );
        rktHjslXsjjlhDTO.setHlx( req.getHlx() );
        rktHjslXsjjlhDTO.setHmc( req.getHmc() );
        rktHjslXsjjlhDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslXsjjlhDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslXsjjlhDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslXsjjlhDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslXsjjlhDTO.setJlx( req.getJlx() );
        rktHjslXsjjlhDTO.setMlph( req.getMlph() );
        rktHjslXsjjlhDTO.setMlxz( req.getMlxz() );
        rktHjslXsjjlhDTO.setLcywlx( req.getLcywlx() );
        rktHjslXsjjlhDTO.setLcdyid( req.getLcdyid() );
        rktHjslXsjjlhDTO.setLcmc( req.getLcmc() );
        rktHjslXsjjlhDTO.setLcslid( req.getLcslid() );
        rktHjslXsjjlhDTO.setLcywbt( req.getLcywbt() );
        rktHjslXsjjlhDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslXsjjlhDTO.setLczt( req.getLczt() );
        rktHjslXsjjlhDTO.setBlzt( req.getBlzt() );
        rktHjslXsjjlhDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslXsjjlhDTO.setSqrxm( req.getSqrxm() );
        rktHjslXsjjlhDTO.setSqrxb( req.getSqrxb() );
        rktHjslXsjjlhDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslXsjjlhDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslXsjjlhDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslXsjjlhDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslXsjjlhDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslXsjjlhDTO.setSqrq( req.getSqrq() );
        rktHjslXsjjlhDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslXsjjlhDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslXsjjlhDTO.setSprxm( req.getSprxm() );
        rktHjslXsjjlhDTO.setSpsj( req.getSpsj() );
        rktHjslXsjjlhDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslXsjjlhDTO.setSpyj( req.getSpyj() );
        rktHjslXsjjlhDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslXsjjlhDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslXsjjlhDTO.setSlrxm( req.getSlrxm() );
        rktHjslXsjjlhDTO.setSlsj( req.getSlsj() );
        rktHjslXsjjlhDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslXsjjlhDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslXsjjlhDTO.setJcwh( req.getJcwh() );
        rktHjslXsjjlhDTO.setBz( req.getBz() );
        rktHjslXsjjlhDTO.setXh( req.getXh() );
        rktHjslXsjjlhDTO.setHkxz( req.getHkxz() );
        rktHjslXsjjlhDTO.setHb( req.getHb() );
        rktHjslXsjjlhDTO.setYhzgx( req.getYhzgx() );
        rktHjslXsjjlhDTO.setCxsx( req.getCxsx() );
        rktHjslXsjjlhDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslXsjjlhDTO.setXm( req.getXm() );
        rktHjslXsjjlhDTO.setX( req.getX() );
        rktHjslXsjjlhDTO.setM( req.getM() );
        rktHjslXsjjlhDTO.setCym( req.getCym() );
        rktHjslXsjjlhDTO.setXmpy( req.getXmpy() );
        rktHjslXsjjlhDTO.setCympy( req.getCympy() );
        rktHjslXsjjlhDTO.setXb( req.getXb() );
        rktHjslXsjjlhDTO.setMz( req.getMz() );
        rktHjslXsjjlhDTO.setJggjdq( req.getJggjdq() );
        rktHjslXsjjlhDTO.setJgssxq( req.getJgssxq() );
        rktHjslXsjjlhDTO.setJgxz( req.getJgxz() );
        rktHjslXsjjlhDTO.setCsrq( req.getCsrq() );
        rktHjslXsjjlhDTO.setCssj( req.getCssj() );
        rktHjslXsjjlhDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslXsjjlhDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslXsjjlhDTO.setCsdxz( req.getCsdxz() );
        rktHjslXsjjlhDTO.setWhcd( req.getWhcd() );
        rktHjslXsjjlhDTO.setHyzk( req.getHyzk() );
        rktHjslXsjjlhDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslXsjjlhDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslXsjjlhDTO.setZy( req.getZy() );
        rktHjslXsjjlhDTO.setZylb( req.getZylb() );
        rktHjslXsjjlhDTO.setZjxy( req.getZjxy() );
        rktHjslXsjjlhDTO.setSg( req.getSg() );
        rktHjslXsjjlhDTO.setXx( req.getXx() );
        rktHjslXsjjlhDTO.setByzk( req.getByzk() );
        rktHjslXsjjlhDTO.setXxjb( req.getXxjb() );
        rktHjslXsjjlhDTO.setLxdh( req.getLxdh() );
        rktHjslXsjjlhDTO.setFqxm( req.getFqxm() );
        rktHjslXsjjlhDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslXsjjlhDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslXsjjlhDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslXsjjlhDTO.setFqwwx( req.getFqwwx() );
        rktHjslXsjjlhDTO.setFqwwm( req.getFqwwm() );
        rktHjslXsjjlhDTO.setMqxm( req.getMqxm() );
        rktHjslXsjjlhDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslXsjjlhDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslXsjjlhDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslXsjjlhDTO.setMqwwx( req.getMqwwx() );
        rktHjslXsjjlhDTO.setMqwwm( req.getMqwwm() );
        rktHjslXsjjlhDTO.setPoxm( req.getPoxm() );
        rktHjslXsjjlhDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslXsjjlhDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslXsjjlhDTO.setPozjhm( req.getPozjhm() );
        rktHjslXsjjlhDTO.setPowwx( req.getPowwx() );
        rktHjslXsjjlhDTO.setPowwm( req.getPowwm() );
        rktHjslXsjjlhDTO.setJhryxm( req.getJhryxm() );
        rktHjslXsjjlhDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslXsjjlhDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslXsjjlhDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslXsjjlhDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslXsjjlhDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslXsjjlhDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslXsjjlhDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslXsjjlhDTO.setJhrexm( req.getJhrexm() );
        rktHjslXsjjlhDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslXsjjlhDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslXsjjlhDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslXsjjlhDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslXsjjlhDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslXsjjlhDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslXsjjlhDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslXsjjlhDTO.setZjlb( req.getZjlb() );
        rktHjslXsjjlhDTO.setQfjg( req.getQfjg() );
        rktHjslXsjjlhDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslXsjjlhDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslXsjjlhDTO.setFxqhkszdssxqdm( req.getFxqhkszdssxqdm() );
        rktHjslXsjjlhDTO.setFxqhkszdqhnxxdz( req.getFxqhkszdqhnxxdz() );
        rktHjslXsjjlhDTO.setFxljszdssxqdm( req.getFxljszdssxqdm() );
        rktHjslXsjjlhDTO.setFxljszdqhnxxdz( req.getFxljszdqhnxxdz() );
        rktHjslXsjjlhDTO.setBdfw( req.getBdfw() );
        rktHjslXsjjlhDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslXsjjlhDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslXsjjlhDTO.setQtssxq( req.getQtssxq() );
        rktHjslXsjjlhDTO.setQtzz( req.getQtzz() );

        return rktHjslXsjjlhDTO;
    }

    @Override
    public RktHjslXsjjlhPageResp convertToPageResp(RktHjslXsjjlhDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslXsjjlhPageResp rktHjslXsjjlhPageResp = new RktHjslXsjjlhPageResp();

        rktHjslXsjjlhPageResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslXsjjlhPageResp.setHhnbid( dto.getHhnbid() );
        rktHjslXsjjlhPageResp.setYwslh( dto.getYwslh() );
        rktHjslXsjjlhPageResp.setHh( dto.getHh() );
        rktHjslXsjjlhPageResp.setHhid( dto.getHhid() );
        rktHjslXsjjlhPageResp.setHlx( dto.getHlx() );
        rktHjslXsjjlhPageResp.setHmc( dto.getHmc() );
        rktHjslXsjjlhPageResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslXsjjlhPageResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslXsjjlhPageResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslXsjjlhPageResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslXsjjlhPageResp.setJlx( dto.getJlx() );
        rktHjslXsjjlhPageResp.setMlph( dto.getMlph() );
        rktHjslXsjjlhPageResp.setMlxz( dto.getMlxz() );
        rktHjslXsjjlhPageResp.setLcywlx( dto.getLcywlx() );
        rktHjslXsjjlhPageResp.setLcdyid( dto.getLcdyid() );
        rktHjslXsjjlhPageResp.setLcmc( dto.getLcmc() );
        rktHjslXsjjlhPageResp.setLcslid( dto.getLcslid() );
        rktHjslXsjjlhPageResp.setLcywbt( dto.getLcywbt() );
        rktHjslXsjjlhPageResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslXsjjlhPageResp.setLczt( dto.getLczt() );
        rktHjslXsjjlhPageResp.setBlzt( dto.getBlzt() );
        rktHjslXsjjlhPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslXsjjlhPageResp.setSqrxm( dto.getSqrxm() );
        rktHjslXsjjlhPageResp.setSqrxb( dto.getSqrxb() );
        rktHjslXsjjlhPageResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslXsjjlhPageResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslXsjjlhPageResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslXsjjlhPageResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslXsjjlhPageResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslXsjjlhPageResp.setSqrq( dto.getSqrq() );
        rktHjslXsjjlhPageResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslXsjjlhPageResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslXsjjlhPageResp.setSprxm( dto.getSprxm() );
        rktHjslXsjjlhPageResp.setSpsj( dto.getSpsj() );
        rktHjslXsjjlhPageResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslXsjjlhPageResp.setSpyj( dto.getSpyj() );
        rktHjslXsjjlhPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslXsjjlhPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslXsjjlhPageResp.setSlrxm( dto.getSlrxm() );
        rktHjslXsjjlhPageResp.setSlsj( dto.getSlsj() );
        rktHjslXsjjlhPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslXsjjlhPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslXsjjlhPageResp.setJcwh( dto.getJcwh() );
        rktHjslXsjjlhPageResp.setBz( dto.getBz() );
        rktHjslXsjjlhPageResp.setXh( dto.getXh() );
        rktHjslXsjjlhPageResp.setHkxz( dto.getHkxz() );
        rktHjslXsjjlhPageResp.setHb( dto.getHb() );
        rktHjslXsjjlhPageResp.setYhzgx( dto.getYhzgx() );
        rktHjslXsjjlhPageResp.setCxsx( dto.getCxsx() );
        rktHjslXsjjlhPageResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslXsjjlhPageResp.setXm( dto.getXm() );
        rktHjslXsjjlhPageResp.setX( dto.getX() );
        rktHjslXsjjlhPageResp.setM( dto.getM() );
        rktHjslXsjjlhPageResp.setCym( dto.getCym() );
        rktHjslXsjjlhPageResp.setXmpy( dto.getXmpy() );
        rktHjslXsjjlhPageResp.setCympy( dto.getCympy() );
        rktHjslXsjjlhPageResp.setXb( dto.getXb() );
        rktHjslXsjjlhPageResp.setMz( dto.getMz() );
        rktHjslXsjjlhPageResp.setJggjdq( dto.getJggjdq() );
        rktHjslXsjjlhPageResp.setJgssxq( dto.getJgssxq() );
        rktHjslXsjjlhPageResp.setJgxz( dto.getJgxz() );
        rktHjslXsjjlhPageResp.setCsrq( dto.getCsrq() );
        rktHjslXsjjlhPageResp.setCssj( dto.getCssj() );
        rktHjslXsjjlhPageResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslXsjjlhPageResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslXsjjlhPageResp.setCsdxz( dto.getCsdxz() );
        rktHjslXsjjlhPageResp.setWhcd( dto.getWhcd() );
        rktHjslXsjjlhPageResp.setHyzk( dto.getHyzk() );
        rktHjslXsjjlhPageResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslXsjjlhPageResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslXsjjlhPageResp.setZy( dto.getZy() );
        rktHjslXsjjlhPageResp.setZylb( dto.getZylb() );
        rktHjslXsjjlhPageResp.setZjxy( dto.getZjxy() );
        rktHjslXsjjlhPageResp.setSg( dto.getSg() );
        rktHjslXsjjlhPageResp.setXx( dto.getXx() );
        rktHjslXsjjlhPageResp.setByzk( dto.getByzk() );
        rktHjslXsjjlhPageResp.setXxjb( dto.getXxjb() );
        rktHjslXsjjlhPageResp.setLxdh( dto.getLxdh() );
        rktHjslXsjjlhPageResp.setFqxm( dto.getFqxm() );
        rktHjslXsjjlhPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslXsjjlhPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslXsjjlhPageResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslXsjjlhPageResp.setFqwwx( dto.getFqwwx() );
        rktHjslXsjjlhPageResp.setFqwwm( dto.getFqwwm() );
        rktHjslXsjjlhPageResp.setMqxm( dto.getMqxm() );
        rktHjslXsjjlhPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslXsjjlhPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslXsjjlhPageResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslXsjjlhPageResp.setMqwwx( dto.getMqwwx() );
        rktHjslXsjjlhPageResp.setMqwwm( dto.getMqwwm() );
        rktHjslXsjjlhPageResp.setPoxm( dto.getPoxm() );
        rktHjslXsjjlhPageResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslXsjjlhPageResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslXsjjlhPageResp.setPozjhm( dto.getPozjhm() );
        rktHjslXsjjlhPageResp.setPowwx( dto.getPowwx() );
        rktHjslXsjjlhPageResp.setPowwm( dto.getPowwm() );
        rktHjslXsjjlhPageResp.setJhryxm( dto.getJhryxm() );
        rktHjslXsjjlhPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslXsjjlhPageResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslXsjjlhPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslXsjjlhPageResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslXsjjlhPageResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslXsjjlhPageResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslXsjjlhPageResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslXsjjlhPageResp.setJhrexm( dto.getJhrexm() );
        rktHjslXsjjlhPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslXsjjlhPageResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslXsjjlhPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslXsjjlhPageResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslXsjjlhPageResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslXsjjlhPageResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslXsjjlhPageResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslXsjjlhPageResp.setZjlb( dto.getZjlb() );
        rktHjslXsjjlhPageResp.setQfjg( dto.getQfjg() );
        rktHjslXsjjlhPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslXsjjlhPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslXsjjlhPageResp.setFxqhkszdssxqdm( dto.getFxqhkszdssxqdm() );
        rktHjslXsjjlhPageResp.setFxqhkszdqhnxxdz( dto.getFxqhkszdqhnxxdz() );
        rktHjslXsjjlhPageResp.setFxljszdssxqdm( dto.getFxljszdssxqdm() );
        rktHjslXsjjlhPageResp.setFxljszdqhnxxdz( dto.getFxljszdqhnxxdz() );
        rktHjslXsjjlhPageResp.setBdfw( dto.getBdfw() );
        rktHjslXsjjlhPageResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslXsjjlhPageResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslXsjjlhPageResp.setQtssxq( dto.getQtssxq() );
        rktHjslXsjjlhPageResp.setQtzz( dto.getQtzz() );

        return rktHjslXsjjlhPageResp;
    }

    @Override
    public RktHjslXsjjlhViewResp convertToViewResp(RktHjslXsjjlhDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslXsjjlhViewResp rktHjslXsjjlhViewResp = new RktHjslXsjjlhViewResp();

        rktHjslXsjjlhViewResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslXsjjlhViewResp.setHhnbid( dto.getHhnbid() );
        rktHjslXsjjlhViewResp.setYwslh( dto.getYwslh() );
        rktHjslXsjjlhViewResp.setHh( dto.getHh() );
        rktHjslXsjjlhViewResp.setHhid( dto.getHhid() );
        rktHjslXsjjlhViewResp.setHlx( dto.getHlx() );
        rktHjslXsjjlhViewResp.setHmc( dto.getHmc() );
        rktHjslXsjjlhViewResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslXsjjlhViewResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslXsjjlhViewResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslXsjjlhViewResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslXsjjlhViewResp.setJlx( dto.getJlx() );
        rktHjslXsjjlhViewResp.setMlph( dto.getMlph() );
        rktHjslXsjjlhViewResp.setMlxz( dto.getMlxz() );
        rktHjslXsjjlhViewResp.setLcywlx( dto.getLcywlx() );
        rktHjslXsjjlhViewResp.setLcdyid( dto.getLcdyid() );
        rktHjslXsjjlhViewResp.setLcmc( dto.getLcmc() );
        rktHjslXsjjlhViewResp.setLcslid( dto.getLcslid() );
        rktHjslXsjjlhViewResp.setLcywbt( dto.getLcywbt() );
        rktHjslXsjjlhViewResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslXsjjlhViewResp.setLczt( dto.getLczt() );
        rktHjslXsjjlhViewResp.setBlzt( dto.getBlzt() );
        rktHjslXsjjlhViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslXsjjlhViewResp.setSqrxm( dto.getSqrxm() );
        rktHjslXsjjlhViewResp.setSqrxb( dto.getSqrxb() );
        rktHjslXsjjlhViewResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslXsjjlhViewResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslXsjjlhViewResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslXsjjlhViewResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslXsjjlhViewResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslXsjjlhViewResp.setSqrq( dto.getSqrq() );
        rktHjslXsjjlhViewResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslXsjjlhViewResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslXsjjlhViewResp.setSprxm( dto.getSprxm() );
        rktHjslXsjjlhViewResp.setSpsj( dto.getSpsj() );
        rktHjslXsjjlhViewResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslXsjjlhViewResp.setSpyj( dto.getSpyj() );
        rktHjslXsjjlhViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslXsjjlhViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslXsjjlhViewResp.setSlrxm( dto.getSlrxm() );
        rktHjslXsjjlhViewResp.setSlsj( dto.getSlsj() );
        rktHjslXsjjlhViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslXsjjlhViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslXsjjlhViewResp.setJcwh( dto.getJcwh() );
        rktHjslXsjjlhViewResp.setBz( dto.getBz() );
        rktHjslXsjjlhViewResp.setXh( dto.getXh() );
        rktHjslXsjjlhViewResp.setHkxz( dto.getHkxz() );
        rktHjslXsjjlhViewResp.setHb( dto.getHb() );
        rktHjslXsjjlhViewResp.setYhzgx( dto.getYhzgx() );
        rktHjslXsjjlhViewResp.setCxsx( dto.getCxsx() );
        rktHjslXsjjlhViewResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslXsjjlhViewResp.setXm( dto.getXm() );
        rktHjslXsjjlhViewResp.setX( dto.getX() );
        rktHjslXsjjlhViewResp.setM( dto.getM() );
        rktHjslXsjjlhViewResp.setCym( dto.getCym() );
        rktHjslXsjjlhViewResp.setXmpy( dto.getXmpy() );
        rktHjslXsjjlhViewResp.setCympy( dto.getCympy() );
        rktHjslXsjjlhViewResp.setXb( dto.getXb() );
        rktHjslXsjjlhViewResp.setMz( dto.getMz() );
        rktHjslXsjjlhViewResp.setJggjdq( dto.getJggjdq() );
        rktHjslXsjjlhViewResp.setJgssxq( dto.getJgssxq() );
        rktHjslXsjjlhViewResp.setJgxz( dto.getJgxz() );
        rktHjslXsjjlhViewResp.setCsrq( dto.getCsrq() );
        rktHjslXsjjlhViewResp.setCssj( dto.getCssj() );
        rktHjslXsjjlhViewResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslXsjjlhViewResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslXsjjlhViewResp.setCsdxz( dto.getCsdxz() );
        rktHjslXsjjlhViewResp.setWhcd( dto.getWhcd() );
        rktHjslXsjjlhViewResp.setHyzk( dto.getHyzk() );
        rktHjslXsjjlhViewResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslXsjjlhViewResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslXsjjlhViewResp.setZy( dto.getZy() );
        rktHjslXsjjlhViewResp.setZylb( dto.getZylb() );
        rktHjslXsjjlhViewResp.setZjxy( dto.getZjxy() );
        rktHjslXsjjlhViewResp.setSg( dto.getSg() );
        rktHjslXsjjlhViewResp.setXx( dto.getXx() );
        rktHjslXsjjlhViewResp.setByzk( dto.getByzk() );
        rktHjslXsjjlhViewResp.setXxjb( dto.getXxjb() );
        rktHjslXsjjlhViewResp.setLxdh( dto.getLxdh() );
        rktHjslXsjjlhViewResp.setFqxm( dto.getFqxm() );
        rktHjslXsjjlhViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslXsjjlhViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslXsjjlhViewResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslXsjjlhViewResp.setFqwwx( dto.getFqwwx() );
        rktHjslXsjjlhViewResp.setFqwwm( dto.getFqwwm() );
        rktHjslXsjjlhViewResp.setMqxm( dto.getMqxm() );
        rktHjslXsjjlhViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslXsjjlhViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslXsjjlhViewResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslXsjjlhViewResp.setMqwwx( dto.getMqwwx() );
        rktHjslXsjjlhViewResp.setMqwwm( dto.getMqwwm() );
        rktHjslXsjjlhViewResp.setPoxm( dto.getPoxm() );
        rktHjslXsjjlhViewResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslXsjjlhViewResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslXsjjlhViewResp.setPozjhm( dto.getPozjhm() );
        rktHjslXsjjlhViewResp.setPowwx( dto.getPowwx() );
        rktHjslXsjjlhViewResp.setPowwm( dto.getPowwm() );
        rktHjslXsjjlhViewResp.setJhryxm( dto.getJhryxm() );
        rktHjslXsjjlhViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslXsjjlhViewResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslXsjjlhViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslXsjjlhViewResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslXsjjlhViewResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslXsjjlhViewResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslXsjjlhViewResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslXsjjlhViewResp.setJhrexm( dto.getJhrexm() );
        rktHjslXsjjlhViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslXsjjlhViewResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslXsjjlhViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslXsjjlhViewResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslXsjjlhViewResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslXsjjlhViewResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslXsjjlhViewResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslXsjjlhViewResp.setZjlb( dto.getZjlb() );
        rktHjslXsjjlhViewResp.setQfjg( dto.getQfjg() );
        rktHjslXsjjlhViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslXsjjlhViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslXsjjlhViewResp.setFxqhkszdssxqdm( dto.getFxqhkszdssxqdm() );
        rktHjslXsjjlhViewResp.setFxqhkszdqhnxxdz( dto.getFxqhkszdqhnxxdz() );
        rktHjslXsjjlhViewResp.setFxljszdssxqdm( dto.getFxljszdssxqdm() );
        rktHjslXsjjlhViewResp.setFxljszdqhnxxdz( dto.getFxljszdqhnxxdz() );
        rktHjslXsjjlhViewResp.setBdfw( dto.getBdfw() );
        rktHjslXsjjlhViewResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslXsjjlhViewResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslXsjjlhViewResp.setQtssxq( dto.getQtssxq() );
        rktHjslXsjjlhViewResp.setQtzz( dto.getQtzz() );

        return rktHjslXsjjlhViewResp;
    }

    @Override
    public RktHjslXsjjlhCreateResp convertToCreateResp(RktHjslXsjjlhDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslXsjjlhCreateResp rktHjslXsjjlhCreateResp = new RktHjslXsjjlhCreateResp();

        rktHjslXsjjlhCreateResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslXsjjlhCreateResp.setHhnbid( dto.getHhnbid() );
        rktHjslXsjjlhCreateResp.setYwslh( dto.getYwslh() );
        rktHjslXsjjlhCreateResp.setHh( dto.getHh() );
        rktHjslXsjjlhCreateResp.setHhid( dto.getHhid() );
        rktHjslXsjjlhCreateResp.setHlx( dto.getHlx() );
        rktHjslXsjjlhCreateResp.setHmc( dto.getHmc() );
        rktHjslXsjjlhCreateResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslXsjjlhCreateResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslXsjjlhCreateResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslXsjjlhCreateResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslXsjjlhCreateResp.setJlx( dto.getJlx() );
        rktHjslXsjjlhCreateResp.setMlph( dto.getMlph() );
        rktHjslXsjjlhCreateResp.setMlxz( dto.getMlxz() );
        rktHjslXsjjlhCreateResp.setLcywlx( dto.getLcywlx() );
        rktHjslXsjjlhCreateResp.setLcdyid( dto.getLcdyid() );
        rktHjslXsjjlhCreateResp.setLcmc( dto.getLcmc() );
        rktHjslXsjjlhCreateResp.setLcslid( dto.getLcslid() );
        rktHjslXsjjlhCreateResp.setLcywbt( dto.getLcywbt() );
        rktHjslXsjjlhCreateResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslXsjjlhCreateResp.setLczt( dto.getLczt() );
        rktHjslXsjjlhCreateResp.setBlzt( dto.getBlzt() );
        rktHjslXsjjlhCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslXsjjlhCreateResp.setSqrxm( dto.getSqrxm() );
        rktHjslXsjjlhCreateResp.setSqrxb( dto.getSqrxb() );
        rktHjslXsjjlhCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslXsjjlhCreateResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslXsjjlhCreateResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslXsjjlhCreateResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslXsjjlhCreateResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslXsjjlhCreateResp.setSqrq( dto.getSqrq() );
        rktHjslXsjjlhCreateResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslXsjjlhCreateResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslXsjjlhCreateResp.setSprxm( dto.getSprxm() );
        rktHjslXsjjlhCreateResp.setSpsj( dto.getSpsj() );
        rktHjslXsjjlhCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslXsjjlhCreateResp.setSpyj( dto.getSpyj() );
        rktHjslXsjjlhCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslXsjjlhCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslXsjjlhCreateResp.setSlrxm( dto.getSlrxm() );
        rktHjslXsjjlhCreateResp.setSlsj( dto.getSlsj() );
        rktHjslXsjjlhCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslXsjjlhCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslXsjjlhCreateResp.setJcwh( dto.getJcwh() );
        rktHjslXsjjlhCreateResp.setBz( dto.getBz() );
        rktHjslXsjjlhCreateResp.setXh( dto.getXh() );
        rktHjslXsjjlhCreateResp.setHkxz( dto.getHkxz() );
        rktHjslXsjjlhCreateResp.setHb( dto.getHb() );
        rktHjslXsjjlhCreateResp.setYhzgx( dto.getYhzgx() );
        rktHjslXsjjlhCreateResp.setCxsx( dto.getCxsx() );
        rktHjslXsjjlhCreateResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslXsjjlhCreateResp.setXm( dto.getXm() );
        rktHjslXsjjlhCreateResp.setX( dto.getX() );
        rktHjslXsjjlhCreateResp.setM( dto.getM() );
        rktHjslXsjjlhCreateResp.setCym( dto.getCym() );
        rktHjslXsjjlhCreateResp.setXmpy( dto.getXmpy() );
        rktHjslXsjjlhCreateResp.setCympy( dto.getCympy() );
        rktHjslXsjjlhCreateResp.setXb( dto.getXb() );
        rktHjslXsjjlhCreateResp.setMz( dto.getMz() );
        rktHjslXsjjlhCreateResp.setJggjdq( dto.getJggjdq() );
        rktHjslXsjjlhCreateResp.setJgssxq( dto.getJgssxq() );
        rktHjslXsjjlhCreateResp.setJgxz( dto.getJgxz() );
        rktHjslXsjjlhCreateResp.setCsrq( dto.getCsrq() );
        rktHjslXsjjlhCreateResp.setCssj( dto.getCssj() );
        rktHjslXsjjlhCreateResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslXsjjlhCreateResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslXsjjlhCreateResp.setCsdxz( dto.getCsdxz() );
        rktHjslXsjjlhCreateResp.setWhcd( dto.getWhcd() );
        rktHjslXsjjlhCreateResp.setHyzk( dto.getHyzk() );
        rktHjslXsjjlhCreateResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslXsjjlhCreateResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslXsjjlhCreateResp.setZy( dto.getZy() );
        rktHjslXsjjlhCreateResp.setZylb( dto.getZylb() );
        rktHjslXsjjlhCreateResp.setZjxy( dto.getZjxy() );
        rktHjslXsjjlhCreateResp.setSg( dto.getSg() );
        rktHjslXsjjlhCreateResp.setXx( dto.getXx() );
        rktHjslXsjjlhCreateResp.setByzk( dto.getByzk() );
        rktHjslXsjjlhCreateResp.setXxjb( dto.getXxjb() );
        rktHjslXsjjlhCreateResp.setLxdh( dto.getLxdh() );
        rktHjslXsjjlhCreateResp.setFqxm( dto.getFqxm() );
        rktHjslXsjjlhCreateResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslXsjjlhCreateResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslXsjjlhCreateResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslXsjjlhCreateResp.setFqwwx( dto.getFqwwx() );
        rktHjslXsjjlhCreateResp.setFqwwm( dto.getFqwwm() );
        rktHjslXsjjlhCreateResp.setMqxm( dto.getMqxm() );
        rktHjslXsjjlhCreateResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslXsjjlhCreateResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslXsjjlhCreateResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslXsjjlhCreateResp.setMqwwx( dto.getMqwwx() );
        rktHjslXsjjlhCreateResp.setMqwwm( dto.getMqwwm() );
        rktHjslXsjjlhCreateResp.setPoxm( dto.getPoxm() );
        rktHjslXsjjlhCreateResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslXsjjlhCreateResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslXsjjlhCreateResp.setPozjhm( dto.getPozjhm() );
        rktHjslXsjjlhCreateResp.setPowwx( dto.getPowwx() );
        rktHjslXsjjlhCreateResp.setPowwm( dto.getPowwm() );
        rktHjslXsjjlhCreateResp.setJhryxm( dto.getJhryxm() );
        rktHjslXsjjlhCreateResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslXsjjlhCreateResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslXsjjlhCreateResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslXsjjlhCreateResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslXsjjlhCreateResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslXsjjlhCreateResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslXsjjlhCreateResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslXsjjlhCreateResp.setJhrexm( dto.getJhrexm() );
        rktHjslXsjjlhCreateResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslXsjjlhCreateResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslXsjjlhCreateResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslXsjjlhCreateResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslXsjjlhCreateResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslXsjjlhCreateResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslXsjjlhCreateResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslXsjjlhCreateResp.setZjlb( dto.getZjlb() );
        rktHjslXsjjlhCreateResp.setQfjg( dto.getQfjg() );
        rktHjslXsjjlhCreateResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslXsjjlhCreateResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslXsjjlhCreateResp.setFxqhkszdssxqdm( dto.getFxqhkszdssxqdm() );
        rktHjslXsjjlhCreateResp.setFxqhkszdqhnxxdz( dto.getFxqhkszdqhnxxdz() );
        rktHjslXsjjlhCreateResp.setFxljszdssxqdm( dto.getFxljszdssxqdm() );
        rktHjslXsjjlhCreateResp.setFxljszdqhnxxdz( dto.getFxljszdqhnxxdz() );
        rktHjslXsjjlhCreateResp.setBdfw( dto.getBdfw() );
        rktHjslXsjjlhCreateResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslXsjjlhCreateResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslXsjjlhCreateResp.setQtssxq( dto.getQtssxq() );
        rktHjslXsjjlhCreateResp.setQtzz( dto.getQtzz() );

        return rktHjslXsjjlhCreateResp;
    }
}
