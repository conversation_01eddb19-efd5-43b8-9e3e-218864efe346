package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktHjslQrDTO;
import com.zjjcnt.project.ck.base.dto.req.RktHjslQrCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslQrPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslQrUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslQrCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslQrPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslQrViewResp;
import com.zjjcnt.project.ck.base.entity.RktHjslQrDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:46+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktHjslQrConvertImpl implements RktHjslQrConvert {

    @Override
    public RktHjslQrDTO convert(RktHjslQrDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktHjslQrDTO rktHjslQrDTO = new RktHjslQrDTO();

        rktHjslQrDTO.setId( entity.getId() );
        rktHjslQrDTO.setYwslnbbh( entity.getYwslnbbh() );
        rktHjslQrDTO.setYwslh( entity.getYwslh() );
        rktHjslQrDTO.setHmc( entity.getHmc() );
        rktHjslQrDTO.setHjddzbm( entity.getHjddzbm() );
        rktHjslQrDTO.setHjdssxq( entity.getHjdssxq() );
        rktHjslQrDTO.setHjdxxdz( entity.getHjdxxdz() );
        rktHjslQrDTO.setHjdrhyzbs( entity.getHjdrhyzbs() );
        rktHjslQrDTO.setJlx( entity.getJlx() );
        rktHjslQrDTO.setMlph( entity.getMlph() );
        rktHjslQrDTO.setMlxz( entity.getMlxz() );
        rktHjslQrDTO.setHhnbid( entity.getHhnbid() );
        rktHjslQrDTO.setQrqhlx( entity.getQrqhlx() );
        rktHjslQrDTO.setQrqhhnbid( entity.getQrqhhnbid() );
        rktHjslQrDTO.setQrqhhid( entity.getQrqhhid() );
        rktHjslQrDTO.setRynbid( entity.getRynbid() );
        rktHjslQrDTO.setSfzqr( entity.getSfzqr() );
        rktHjslQrDTO.setLcdyid( entity.getLcdyid() );
        rktHjslQrDTO.setLcmc( entity.getLcmc() );
        rktHjslQrDTO.setSpyj( entity.getSpyj() );
        rktHjslQrDTO.setXh( entity.getXh() );
        rktHjslQrDTO.setZjlb( entity.getZjlb() );
        rktHjslQrDTO.setQfjg( entity.getQfjg() );
        rktHjslQrDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        rktHjslQrDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        rktHjslQrDTO.setZqzbh( entity.getZqzbh() );
        rktHjslQrDTO.setQyldyy( entity.getQyldyy() );
        rktHjslQrDTO.setBdfw( entity.getBdfw() );
        rktHjslQrDTO.setQyyyxflb( entity.getQyyyxflb() );
        rktHjslQrDTO.setQyyybcsm( entity.getQyyybcsm() );
        rktHjslQrDTO.setBtkrgmsfhm( entity.getBtkrgmsfhm() );
        rktHjslQrDTO.setBtkrxm( entity.getBtkrxm() );
        rktHjslQrDTO.setBtkrytkrgx( entity.getBtkrytkrgx() );
        rktHjslQrDTO.setQyrysqrgx( entity.getQyrysqrgx() );
        rktHjslQrDTO.setQrqcxsx( entity.getQrqcxsx() );
        rktHjslQrDTO.setCxzhyy( entity.getCxzhyy() );
        rktHjslQrDTO.setPdbzzyjszc( entity.getPdbzzyjszc() );
        rktHjslQrDTO.setPdbzjndj( entity.getPdbzjndj() );
        rktHjslQrDTO.setPdbzncjdzzyxbys( entity.getPdbzncjdzzyxbys() );
        rktHjslQrDTO.setPdbzjjqx( entity.getPdbzjjqx() );
        rktHjslQrDTO.setPdbzzczjyhjzwnys( entity.getPdbzzczjyhjzwnys() );
        rktHjslQrDTO.setQrqhb( entity.getQrqhb() );
        rktHjslQrDTO.setRyid( entity.getRyid() );
        rktHjslQrDTO.setZqlx( entity.getZqlx() );
        rktHjslQrDTO.setYhjddzbm( entity.getYhjddzbm() );
        rktHjslQrDTO.setYhjdssxq( entity.getYhjdssxq() );
        rktHjslQrDTO.setYhjdxxdz( entity.getYhjdxxdz() );
        rktHjslQrDTO.setYhjszddjjgdm( entity.getYhjszddjjgdm() );
        rktHjslQrDTO.setYhjszddjjgmc( entity.getYhjszddjjgmc() );
        rktHjslQrDTO.setYhjdjwzrq( entity.getYhjdjwzrq() );
        rktHjslQrDTO.setYhjdjcwh( entity.getYhjdjcwh() );
        rktHjslQrDTO.setYhjdpcs( entity.getYhjdpcs() );
        rktHjslQrDTO.setYhjdjlx( entity.getYhjdjlx() );
        rktHjslQrDTO.setYhjdxzjd( entity.getYhjdxzjd() );
        rktHjslQrDTO.setHh( entity.getHh() );
        rktHjslQrDTO.setHhid( entity.getHhid() );
        rktHjslQrDTO.setHlx( entity.getHlx() );
        rktHjslQrDTO.setHb( entity.getHb() );
        rktHjslQrDTO.setYhzgx( entity.getYhzgx() );
        rktHjslQrDTO.setHkxz( entity.getHkxz() );
        rktHjslQrDTO.setCxsx( entity.getCxsx() );
        rktHjslQrDTO.setGmsfhm( entity.getGmsfhm() );
        rktHjslQrDTO.setXm( entity.getXm() );
        rktHjslQrDTO.setX( entity.getX() );
        rktHjslQrDTO.setM( entity.getM() );
        rktHjslQrDTO.setCym( entity.getCym() );
        rktHjslQrDTO.setXmpy( entity.getXmpy() );
        rktHjslQrDTO.setCympy( entity.getCympy() );
        rktHjslQrDTO.setXb( entity.getXb() );
        rktHjslQrDTO.setMz( entity.getMz() );
        rktHjslQrDTO.setJggjdq( entity.getJggjdq() );
        rktHjslQrDTO.setJgssxq( entity.getJgssxq() );
        rktHjslQrDTO.setJgxz( entity.getJgxz() );
        rktHjslQrDTO.setCsrq( entity.getCsrq() );
        rktHjslQrDTO.setCssj( entity.getCssj() );
        rktHjslQrDTO.setCsdgjdq( entity.getCsdgjdq() );
        rktHjslQrDTO.setCsdssxq( entity.getCsdssxq() );
        rktHjslQrDTO.setCsdxz( entity.getCsdxz() );
        rktHjslQrDTO.setWhcd( entity.getWhcd() );
        rktHjslQrDTO.setHyzk( entity.getHyzk() );
        rktHjslQrDTO.setCyzkdwbm( entity.getCyzkdwbm() );
        rktHjslQrDTO.setCyzkdwmc( entity.getCyzkdwmc() );
        rktHjslQrDTO.setZy( entity.getZy() );
        rktHjslQrDTO.setZylb( entity.getZylb() );
        rktHjslQrDTO.setZjxy( entity.getZjxy() );
        rktHjslQrDTO.setSg( entity.getSg() );
        rktHjslQrDTO.setXx( entity.getXx() );
        rktHjslQrDTO.setByzk( entity.getByzk() );
        rktHjslQrDTO.setXxjb( entity.getXxjb() );
        rktHjslQrDTO.setLxdh( entity.getLxdh() );
        rktHjslQrDTO.setFqxm( entity.getFqxm() );
        rktHjslQrDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        rktHjslQrDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        rktHjslQrDTO.setFqzjhm( entity.getFqzjhm() );
        rktHjslQrDTO.setFqwwx( entity.getFqwwx() );
        rktHjslQrDTO.setFqwwm( entity.getFqwwm() );
        rktHjslQrDTO.setMqxm( entity.getMqxm() );
        rktHjslQrDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        rktHjslQrDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        rktHjslQrDTO.setMqzjhm( entity.getMqzjhm() );
        rktHjslQrDTO.setMqwwx( entity.getMqwwx() );
        rktHjslQrDTO.setMqwwm( entity.getMqwwm() );
        rktHjslQrDTO.setPoxm( entity.getPoxm() );
        rktHjslQrDTO.setPogmsfhm( entity.getPogmsfhm() );
        rktHjslQrDTO.setPocyzjdm( entity.getPocyzjdm() );
        rktHjslQrDTO.setPozjhm( entity.getPozjhm() );
        rktHjslQrDTO.setPowwx( entity.getPowwx() );
        rktHjslQrDTO.setPowwm( entity.getPowwm() );
        rktHjslQrDTO.setJhryxm( entity.getJhryxm() );
        rktHjslQrDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        rktHjslQrDTO.setJhryjhgx( entity.getJhryjhgx() );
        rktHjslQrDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        rktHjslQrDTO.setJhryzjhm( entity.getJhryzjhm() );
        rktHjslQrDTO.setJhrywwx( entity.getJhrywwx() );
        rktHjslQrDTO.setJhrywwm( entity.getJhrywwm() );
        rktHjslQrDTO.setJhrylxdh( entity.getJhrylxdh() );
        rktHjslQrDTO.setJhrexm( entity.getJhrexm() );
        rktHjslQrDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        rktHjslQrDTO.setJhrejhgx( entity.getJhrejhgx() );
        rktHjslQrDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        rktHjslQrDTO.setJhrezjhm( entity.getJhrezjhm() );
        rktHjslQrDTO.setJhrewwx( entity.getJhrewwx() );
        rktHjslQrDTO.setJhrewwm( entity.getJhrewwm() );
        rktHjslQrDTO.setJhrelxdh( entity.getJhrelxdh() );
        rktHjslQrDTO.setLcywlx( entity.getLcywlx() );
        rktHjslQrDTO.setLcslid( entity.getLcslid() );
        rktHjslQrDTO.setLcywbt( entity.getLcywbt() );
        rktHjslQrDTO.setLcrwjd( entity.getLcrwjd() );
        rktHjslQrDTO.setLczt( entity.getLczt() );
        rktHjslQrDTO.setBlzt( entity.getBlzt() );
        rktHjslQrDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        rktHjslQrDTO.setSqrxm( entity.getSqrxm() );
        rktHjslQrDTO.setSqrxb( entity.getSqrxb() );
        rktHjslQrDTO.setSqrlxdh( entity.getSqrlxdh() );
        rktHjslQrDTO.setSqrybdrgx( entity.getSqrybdrgx() );
        rktHjslQrDTO.setSqrq( entity.getSqrq() );
        rktHjslQrDTO.setSpdwgajgjgdm( entity.getSpdwgajgjgdm() );
        rktHjslQrDTO.setSpdwgajgmc( entity.getSpdwgajgmc() );
        rktHjslQrDTO.setSprxm( entity.getSprxm() );
        rktHjslQrDTO.setSpsj( entity.getSpsj() );
        rktHjslQrDTO.setSpjgdm( entity.getSpjgdm() );
        rktHjslQrDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        rktHjslQrDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        rktHjslQrDTO.setSlrxm( entity.getSlrxm() );
        rktHjslQrDTO.setSlsj( entity.getSlsj() );
        rktHjslQrDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        rktHjslQrDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        rktHjslQrDTO.setJcwh( entity.getJcwh() );
        rktHjslQrDTO.setBz( entity.getBz() );
        rktHjslQrDTO.setBlhjywid( entity.getBlhjywid() );
        rktHjslQrDTO.setHjywblsj( entity.getHjywblsj() );
        rktHjslQrDTO.setQtssxq( entity.getQtssxq() );
        rktHjslQrDTO.setQtzz( entity.getQtzz() );
        rktHjslQrDTO.setGjnzsbh( entity.getGjnzsbh() );
        rktHjslQrDTO.setJflhfz( entity.getJflhfz() );
        rktHjslQrDTO.setJftjrq( entity.getJftjrq() );
        rktHjslQrDTO.setJzzbh( entity.getJzzbh() );
        rktHjslQrDTO.setSfhgqwnyhk( entity.getSfhgqwnyhk() );
        rktHjslQrDTO.setJzzdjzd( entity.getJzzdjzd() );
        rktHjslQrDTO.setJzzjfz( entity.getJzzjfz() );
        rktHjslQrDTO.setCszmbh( entity.getCszmbh() );
        rktHjslQrDTO.setSfksxtqr( entity.getSfksxtqr() );
        rktHjslQrDTO.setJflhfjf1( entity.getJflhfjf1() );
        rktHjslQrDTO.setJflhfjf2( entity.getJflhfjf2() );
        rktHjslQrDTO.setJflhfjfhj( entity.getJflhfjfhj() );
        rktHjslQrDTO.setSftqjfpz( entity.getSftqjfpz() );

        return rktHjslQrDTO;
    }

    @Override
    public RktHjslQrDO convertToDO(RktHjslQrDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslQrDO rktHjslQrDO = new RktHjslQrDO();

        rktHjslQrDO.setId( dto.getId() );
        rktHjslQrDO.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslQrDO.setYwslh( dto.getYwslh() );
        rktHjslQrDO.setHmc( dto.getHmc() );
        rktHjslQrDO.setHjddzbm( dto.getHjddzbm() );
        rktHjslQrDO.setHjdssxq( dto.getHjdssxq() );
        rktHjslQrDO.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslQrDO.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslQrDO.setJlx( dto.getJlx() );
        rktHjslQrDO.setMlph( dto.getMlph() );
        rktHjslQrDO.setMlxz( dto.getMlxz() );
        rktHjslQrDO.setHhnbid( dto.getHhnbid() );
        rktHjslQrDO.setQrqhlx( dto.getQrqhlx() );
        rktHjslQrDO.setQrqhhnbid( dto.getQrqhhnbid() );
        rktHjslQrDO.setQrqhhid( dto.getQrqhhid() );
        rktHjslQrDO.setRynbid( dto.getRynbid() );
        rktHjslQrDO.setSfzqr( dto.getSfzqr() );
        rktHjslQrDO.setLcdyid( dto.getLcdyid() );
        rktHjslQrDO.setLcmc( dto.getLcmc() );
        rktHjslQrDO.setSpyj( dto.getSpyj() );
        rktHjslQrDO.setXh( dto.getXh() );
        rktHjslQrDO.setZjlb( dto.getZjlb() );
        rktHjslQrDO.setQfjg( dto.getQfjg() );
        rktHjslQrDO.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslQrDO.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslQrDO.setZqzbh( dto.getZqzbh() );
        rktHjslQrDO.setQyldyy( dto.getQyldyy() );
        rktHjslQrDO.setBdfw( dto.getBdfw() );
        rktHjslQrDO.setQyyyxflb( dto.getQyyyxflb() );
        rktHjslQrDO.setQyyybcsm( dto.getQyyybcsm() );
        rktHjslQrDO.setBtkrgmsfhm( dto.getBtkrgmsfhm() );
        rktHjslQrDO.setBtkrxm( dto.getBtkrxm() );
        rktHjslQrDO.setBtkrytkrgx( dto.getBtkrytkrgx() );
        rktHjslQrDO.setQyrysqrgx( dto.getQyrysqrgx() );
        rktHjslQrDO.setQrqcxsx( dto.getQrqcxsx() );
        rktHjslQrDO.setCxzhyy( dto.getCxzhyy() );
        rktHjslQrDO.setPdbzzyjszc( dto.getPdbzzyjszc() );
        rktHjslQrDO.setPdbzjndj( dto.getPdbzjndj() );
        rktHjslQrDO.setPdbzncjdzzyxbys( dto.getPdbzncjdzzyxbys() );
        rktHjslQrDO.setPdbzjjqx( dto.getPdbzjjqx() );
        rktHjslQrDO.setPdbzzczjyhjzwnys( dto.getPdbzzczjyhjzwnys() );
        rktHjslQrDO.setQrqhb( dto.getQrqhb() );
        rktHjslQrDO.setRyid( dto.getRyid() );
        rktHjslQrDO.setZqlx( dto.getZqlx() );
        rktHjslQrDO.setYhjddzbm( dto.getYhjddzbm() );
        rktHjslQrDO.setYhjdssxq( dto.getYhjdssxq() );
        rktHjslQrDO.setYhjdxxdz( dto.getYhjdxxdz() );
        rktHjslQrDO.setYhjszddjjgdm( dto.getYhjszddjjgdm() );
        rktHjslQrDO.setYhjszddjjgmc( dto.getYhjszddjjgmc() );
        rktHjslQrDO.setYhjdjwzrq( dto.getYhjdjwzrq() );
        rktHjslQrDO.setYhjdjcwh( dto.getYhjdjcwh() );
        rktHjslQrDO.setYhjdpcs( dto.getYhjdpcs() );
        rktHjslQrDO.setYhjdjlx( dto.getYhjdjlx() );
        rktHjslQrDO.setYhjdxzjd( dto.getYhjdxzjd() );
        rktHjslQrDO.setHh( dto.getHh() );
        rktHjslQrDO.setHhid( dto.getHhid() );
        rktHjslQrDO.setHlx( dto.getHlx() );
        rktHjslQrDO.setHb( dto.getHb() );
        rktHjslQrDO.setYhzgx( dto.getYhzgx() );
        rktHjslQrDO.setHkxz( dto.getHkxz() );
        rktHjslQrDO.setCxsx( dto.getCxsx() );
        rktHjslQrDO.setGmsfhm( dto.getGmsfhm() );
        rktHjslQrDO.setXm( dto.getXm() );
        rktHjslQrDO.setX( dto.getX() );
        rktHjslQrDO.setM( dto.getM() );
        rktHjslQrDO.setCym( dto.getCym() );
        rktHjslQrDO.setXmpy( dto.getXmpy() );
        rktHjslQrDO.setCympy( dto.getCympy() );
        rktHjslQrDO.setXb( dto.getXb() );
        rktHjslQrDO.setMz( dto.getMz() );
        rktHjslQrDO.setJggjdq( dto.getJggjdq() );
        rktHjslQrDO.setJgssxq( dto.getJgssxq() );
        rktHjslQrDO.setJgxz( dto.getJgxz() );
        rktHjslQrDO.setCsrq( dto.getCsrq() );
        rktHjslQrDO.setCssj( dto.getCssj() );
        rktHjslQrDO.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslQrDO.setCsdssxq( dto.getCsdssxq() );
        rktHjslQrDO.setCsdxz( dto.getCsdxz() );
        rktHjslQrDO.setWhcd( dto.getWhcd() );
        rktHjslQrDO.setHyzk( dto.getHyzk() );
        rktHjslQrDO.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslQrDO.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslQrDO.setZy( dto.getZy() );
        rktHjslQrDO.setZylb( dto.getZylb() );
        rktHjslQrDO.setZjxy( dto.getZjxy() );
        rktHjslQrDO.setSg( dto.getSg() );
        rktHjslQrDO.setXx( dto.getXx() );
        rktHjslQrDO.setByzk( dto.getByzk() );
        rktHjslQrDO.setXxjb( dto.getXxjb() );
        rktHjslQrDO.setLxdh( dto.getLxdh() );
        rktHjslQrDO.setFqxm( dto.getFqxm() );
        rktHjslQrDO.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslQrDO.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslQrDO.setFqzjhm( dto.getFqzjhm() );
        rktHjslQrDO.setFqwwx( dto.getFqwwx() );
        rktHjslQrDO.setFqwwm( dto.getFqwwm() );
        rktHjslQrDO.setMqxm( dto.getMqxm() );
        rktHjslQrDO.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslQrDO.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslQrDO.setMqzjhm( dto.getMqzjhm() );
        rktHjslQrDO.setMqwwx( dto.getMqwwx() );
        rktHjslQrDO.setMqwwm( dto.getMqwwm() );
        rktHjslQrDO.setPoxm( dto.getPoxm() );
        rktHjslQrDO.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslQrDO.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslQrDO.setPozjhm( dto.getPozjhm() );
        rktHjslQrDO.setPowwx( dto.getPowwx() );
        rktHjslQrDO.setPowwm( dto.getPowwm() );
        rktHjslQrDO.setJhryxm( dto.getJhryxm() );
        rktHjslQrDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslQrDO.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslQrDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslQrDO.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslQrDO.setJhrywwx( dto.getJhrywwx() );
        rktHjslQrDO.setJhrywwm( dto.getJhrywwm() );
        rktHjslQrDO.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslQrDO.setJhrexm( dto.getJhrexm() );
        rktHjslQrDO.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslQrDO.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslQrDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslQrDO.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslQrDO.setJhrewwx( dto.getJhrewwx() );
        rktHjslQrDO.setJhrewwm( dto.getJhrewwm() );
        rktHjslQrDO.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslQrDO.setLcywlx( dto.getLcywlx() );
        rktHjslQrDO.setLcslid( dto.getLcslid() );
        rktHjslQrDO.setLcywbt( dto.getLcywbt() );
        rktHjslQrDO.setLcrwjd( dto.getLcrwjd() );
        rktHjslQrDO.setLczt( dto.getLczt() );
        rktHjslQrDO.setBlzt( dto.getBlzt() );
        rktHjslQrDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslQrDO.setSqrxm( dto.getSqrxm() );
        rktHjslQrDO.setSqrxb( dto.getSqrxb() );
        rktHjslQrDO.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslQrDO.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslQrDO.setSqrq( dto.getSqrq() );
        rktHjslQrDO.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslQrDO.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslQrDO.setSprxm( dto.getSprxm() );
        rktHjslQrDO.setSpsj( dto.getSpsj() );
        rktHjslQrDO.setSpjgdm( dto.getSpjgdm() );
        rktHjslQrDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslQrDO.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslQrDO.setSlrxm( dto.getSlrxm() );
        rktHjslQrDO.setSlsj( dto.getSlsj() );
        rktHjslQrDO.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslQrDO.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslQrDO.setJcwh( dto.getJcwh() );
        rktHjslQrDO.setBz( dto.getBz() );
        rktHjslQrDO.setBlhjywid( dto.getBlhjywid() );
        rktHjslQrDO.setHjywblsj( dto.getHjywblsj() );
        rktHjslQrDO.setQtssxq( dto.getQtssxq() );
        rktHjslQrDO.setQtzz( dto.getQtzz() );
        rktHjslQrDO.setGjnzsbh( dto.getGjnzsbh() );
        rktHjslQrDO.setJflhfz( dto.getJflhfz() );
        rktHjslQrDO.setJftjrq( dto.getJftjrq() );
        rktHjslQrDO.setJzzbh( dto.getJzzbh() );
        rktHjslQrDO.setSfhgqwnyhk( dto.getSfhgqwnyhk() );
        rktHjslQrDO.setJzzdjzd( dto.getJzzdjzd() );
        rktHjslQrDO.setJzzjfz( dto.getJzzjfz() );
        rktHjslQrDO.setCszmbh( dto.getCszmbh() );
        rktHjslQrDO.setSfksxtqr( dto.getSfksxtqr() );
        rktHjslQrDO.setJflhfjf1( dto.getJflhfjf1() );
        rktHjslQrDO.setJflhfjf2( dto.getJflhfjf2() );
        rktHjslQrDO.setJflhfjfhj( dto.getJflhfjfhj() );
        rktHjslQrDO.setSftqjfpz( dto.getSftqjfpz() );

        return rktHjslQrDO;
    }

    @Override
    public RktHjslQrDTO convertToDTO(RktHjslQrPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslQrDTO rktHjslQrDTO = new RktHjslQrDTO();

        rktHjslQrDTO.setYwslh( req.getYwslh() );
        rktHjslQrDTO.setHmc( req.getHmc() );
        rktHjslQrDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslQrDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslQrDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslQrDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslQrDTO.setJlx( req.getJlx() );
        rktHjslQrDTO.setMlph( req.getMlph() );
        rktHjslQrDTO.setMlxz( req.getMlxz() );
        rktHjslQrDTO.setHhnbid( req.getHhnbid() );
        rktHjslQrDTO.setQrqhlx( req.getQrqhlx() );
        rktHjslQrDTO.setQrqhhnbid( req.getQrqhhnbid() );
        rktHjslQrDTO.setQrqhhid( req.getQrqhhid() );
        rktHjslQrDTO.setRynbid( req.getRynbid() );
        rktHjslQrDTO.setSfzqr( req.getSfzqr() );
        rktHjslQrDTO.setLcdyid( req.getLcdyid() );
        rktHjslQrDTO.setLcmc( req.getLcmc() );
        rktHjslQrDTO.setSpyj( req.getSpyj() );
        rktHjslQrDTO.setXh( req.getXh() );
        rktHjslQrDTO.setZjlb( req.getZjlb() );
        rktHjslQrDTO.setQfjg( req.getQfjg() );
        rktHjslQrDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslQrDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslQrDTO.setZqzbh( req.getZqzbh() );
        rktHjslQrDTO.setQyldyy( req.getQyldyy() );
        rktHjslQrDTO.setBdfw( req.getBdfw() );
        rktHjslQrDTO.setQyyyxflb( req.getQyyyxflb() );
        rktHjslQrDTO.setQyyybcsm( req.getQyyybcsm() );
        rktHjslQrDTO.setBtkrgmsfhm( req.getBtkrgmsfhm() );
        rktHjslQrDTO.setBtkrxm( req.getBtkrxm() );
        rktHjslQrDTO.setBtkrytkrgx( req.getBtkrytkrgx() );
        rktHjslQrDTO.setQyrysqrgx( req.getQyrysqrgx() );
        rktHjslQrDTO.setQrqcxsx( req.getQrqcxsx() );
        rktHjslQrDTO.setCxzhyy( req.getCxzhyy() );
        rktHjslQrDTO.setPdbzzyjszc( req.getPdbzzyjszc() );
        rktHjslQrDTO.setPdbzjndj( req.getPdbzjndj() );
        rktHjslQrDTO.setPdbzncjdzzyxbys( req.getPdbzncjdzzyxbys() );
        rktHjslQrDTO.setPdbzjjqx( req.getPdbzjjqx() );
        rktHjslQrDTO.setPdbzzczjyhjzwnys( req.getPdbzzczjyhjzwnys() );
        rktHjslQrDTO.setQrqhb( req.getQrqhb() );
        rktHjslQrDTO.setRyid( req.getRyid() );
        rktHjslQrDTO.setZqlx( req.getZqlx() );
        rktHjslQrDTO.setYhjddzbm( req.getYhjddzbm() );
        rktHjslQrDTO.setYhjdssxq( req.getYhjdssxq() );
        rktHjslQrDTO.setYhjdxxdz( req.getYhjdxxdz() );
        rktHjslQrDTO.setYhjszddjjgdm( req.getYhjszddjjgdm() );
        rktHjslQrDTO.setYhjszddjjgmc( req.getYhjszddjjgmc() );
        rktHjslQrDTO.setYhjdjwzrq( req.getYhjdjwzrq() );
        rktHjslQrDTO.setYhjdjcwh( req.getYhjdjcwh() );
        rktHjslQrDTO.setYhjdpcs( req.getYhjdpcs() );
        rktHjslQrDTO.setYhjdjlx( req.getYhjdjlx() );
        rktHjslQrDTO.setYhjdxzjd( req.getYhjdxzjd() );
        rktHjslQrDTO.setHh( req.getHh() );
        rktHjslQrDTO.setHhid( req.getHhid() );
        rktHjslQrDTO.setHlx( req.getHlx() );
        rktHjslQrDTO.setHb( req.getHb() );
        rktHjslQrDTO.setYhzgx( req.getYhzgx() );
        rktHjslQrDTO.setHkxz( req.getHkxz() );
        rktHjslQrDTO.setCxsx( req.getCxsx() );
        rktHjslQrDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslQrDTO.setXm( req.getXm() );
        rktHjslQrDTO.setX( req.getX() );
        rktHjslQrDTO.setM( req.getM() );
        rktHjslQrDTO.setCym( req.getCym() );
        rktHjslQrDTO.setXmpy( req.getXmpy() );
        rktHjslQrDTO.setCympy( req.getCympy() );
        rktHjslQrDTO.setXb( req.getXb() );
        rktHjslQrDTO.setMz( req.getMz() );
        rktHjslQrDTO.setJggjdq( req.getJggjdq() );
        rktHjslQrDTO.setJgssxq( req.getJgssxq() );
        rktHjslQrDTO.setJgxz( req.getJgxz() );
        rktHjslQrDTO.setCsrq( req.getCsrq() );
        rktHjslQrDTO.setCssj( req.getCssj() );
        rktHjslQrDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslQrDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslQrDTO.setCsdxz( req.getCsdxz() );
        rktHjslQrDTO.setWhcd( req.getWhcd() );
        rktHjslQrDTO.setHyzk( req.getHyzk() );
        rktHjslQrDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslQrDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslQrDTO.setZy( req.getZy() );
        rktHjslQrDTO.setZylb( req.getZylb() );
        rktHjslQrDTO.setZjxy( req.getZjxy() );
        rktHjslQrDTO.setSg( req.getSg() );
        rktHjslQrDTO.setXx( req.getXx() );
        rktHjslQrDTO.setByzk( req.getByzk() );
        rktHjslQrDTO.setXxjb( req.getXxjb() );
        rktHjslQrDTO.setLxdh( req.getLxdh() );
        rktHjslQrDTO.setFqxm( req.getFqxm() );
        rktHjslQrDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslQrDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslQrDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslQrDTO.setFqwwx( req.getFqwwx() );
        rktHjslQrDTO.setFqwwm( req.getFqwwm() );
        rktHjslQrDTO.setMqxm( req.getMqxm() );
        rktHjslQrDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslQrDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslQrDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslQrDTO.setMqwwx( req.getMqwwx() );
        rktHjslQrDTO.setMqwwm( req.getMqwwm() );
        rktHjslQrDTO.setPoxm( req.getPoxm() );
        rktHjslQrDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslQrDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslQrDTO.setPozjhm( req.getPozjhm() );
        rktHjslQrDTO.setPowwx( req.getPowwx() );
        rktHjslQrDTO.setPowwm( req.getPowwm() );
        rktHjslQrDTO.setJhryxm( req.getJhryxm() );
        rktHjslQrDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslQrDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslQrDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslQrDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslQrDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslQrDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslQrDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslQrDTO.setJhrexm( req.getJhrexm() );
        rktHjslQrDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslQrDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslQrDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslQrDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslQrDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslQrDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslQrDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslQrDTO.setLcywlx( req.getLcywlx() );
        rktHjslQrDTO.setLcslid( req.getLcslid() );
        rktHjslQrDTO.setLcywbt( req.getLcywbt() );
        rktHjslQrDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslQrDTO.setLczt( req.getLczt() );
        rktHjslQrDTO.setBlzt( req.getBlzt() );
        rktHjslQrDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslQrDTO.setSqrxm( req.getSqrxm() );
        rktHjslQrDTO.setSqrxb( req.getSqrxb() );
        rktHjslQrDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslQrDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslQrDTO.setSqrq( req.getSqrq() );
        rktHjslQrDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslQrDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslQrDTO.setSprxm( req.getSprxm() );
        rktHjslQrDTO.setSpsj( req.getSpsj() );
        rktHjslQrDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslQrDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslQrDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslQrDTO.setSlrxm( req.getSlrxm() );
        rktHjslQrDTO.setSlsj( req.getSlsj() );
        rktHjslQrDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslQrDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslQrDTO.setJcwh( req.getJcwh() );
        rktHjslQrDTO.setBz( req.getBz() );
        rktHjslQrDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslQrDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslQrDTO.setQtssxq( req.getQtssxq() );
        rktHjslQrDTO.setQtzz( req.getQtzz() );
        rktHjslQrDTO.setGjnzsbh( req.getGjnzsbh() );
        rktHjslQrDTO.setJflhfz( req.getJflhfz() );
        rktHjslQrDTO.setJftjrq( req.getJftjrq() );
        rktHjslQrDTO.setJzzbh( req.getJzzbh() );
        rktHjslQrDTO.setSfhgqwnyhk( req.getSfhgqwnyhk() );
        rktHjslQrDTO.setJzzdjzd( req.getJzzdjzd() );
        rktHjslQrDTO.setJzzjfz( req.getJzzjfz() );
        rktHjslQrDTO.setCszmbh( req.getCszmbh() );
        rktHjslQrDTO.setSfksxtqr( req.getSfksxtqr() );
        rktHjslQrDTO.setJflhfjf1( req.getJflhfjf1() );
        rktHjslQrDTO.setJflhfjf2( req.getJflhfjf2() );
        rktHjslQrDTO.setJflhfjfhj( req.getJflhfjfhj() );
        rktHjslQrDTO.setSftqjfpz( req.getSftqjfpz() );

        return rktHjslQrDTO;
    }

    @Override
    public RktHjslQrDTO convertToDTO(RktHjslQrCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslQrDTO rktHjslQrDTO = new RktHjslQrDTO();

        rktHjslQrDTO.setYwslh( req.getYwslh() );
        rktHjslQrDTO.setHmc( req.getHmc() );
        rktHjslQrDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslQrDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslQrDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslQrDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslQrDTO.setJlx( req.getJlx() );
        rktHjslQrDTO.setMlph( req.getMlph() );
        rktHjslQrDTO.setMlxz( req.getMlxz() );
        rktHjslQrDTO.setHhnbid( req.getHhnbid() );
        rktHjslQrDTO.setQrqhlx( req.getQrqhlx() );
        rktHjslQrDTO.setQrqhhnbid( req.getQrqhhnbid() );
        rktHjslQrDTO.setQrqhhid( req.getQrqhhid() );
        rktHjslQrDTO.setRynbid( req.getRynbid() );
        rktHjslQrDTO.setSfzqr( req.getSfzqr() );
        rktHjslQrDTO.setLcdyid( req.getLcdyid() );
        rktHjslQrDTO.setLcmc( req.getLcmc() );
        rktHjslQrDTO.setSpyj( req.getSpyj() );
        rktHjslQrDTO.setXh( req.getXh() );
        rktHjslQrDTO.setZjlb( req.getZjlb() );
        rktHjslQrDTO.setQfjg( req.getQfjg() );
        rktHjslQrDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslQrDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslQrDTO.setZqzbh( req.getZqzbh() );
        rktHjslQrDTO.setQyldyy( req.getQyldyy() );
        rktHjslQrDTO.setBdfw( req.getBdfw() );
        rktHjslQrDTO.setQyyyxflb( req.getQyyyxflb() );
        rktHjslQrDTO.setQyyybcsm( req.getQyyybcsm() );
        rktHjslQrDTO.setBtkrgmsfhm( req.getBtkrgmsfhm() );
        rktHjslQrDTO.setBtkrxm( req.getBtkrxm() );
        rktHjslQrDTO.setBtkrytkrgx( req.getBtkrytkrgx() );
        rktHjslQrDTO.setQyrysqrgx( req.getQyrysqrgx() );
        rktHjslQrDTO.setQrqcxsx( req.getQrqcxsx() );
        rktHjslQrDTO.setCxzhyy( req.getCxzhyy() );
        rktHjslQrDTO.setPdbzzyjszc( req.getPdbzzyjszc() );
        rktHjslQrDTO.setPdbzjndj( req.getPdbzjndj() );
        rktHjslQrDTO.setPdbzncjdzzyxbys( req.getPdbzncjdzzyxbys() );
        rktHjslQrDTO.setPdbzjjqx( req.getPdbzjjqx() );
        rktHjslQrDTO.setPdbzzczjyhjzwnys( req.getPdbzzczjyhjzwnys() );
        rktHjslQrDTO.setQrqhb( req.getQrqhb() );
        rktHjslQrDTO.setRyid( req.getRyid() );
        rktHjslQrDTO.setZqlx( req.getZqlx() );
        rktHjslQrDTO.setYhjddzbm( req.getYhjddzbm() );
        rktHjslQrDTO.setYhjdssxq( req.getYhjdssxq() );
        rktHjslQrDTO.setYhjdxxdz( req.getYhjdxxdz() );
        rktHjslQrDTO.setYhjszddjjgdm( req.getYhjszddjjgdm() );
        rktHjslQrDTO.setYhjszddjjgmc( req.getYhjszddjjgmc() );
        rktHjslQrDTO.setYhjdjwzrq( req.getYhjdjwzrq() );
        rktHjslQrDTO.setYhjdjcwh( req.getYhjdjcwh() );
        rktHjslQrDTO.setYhjdpcs( req.getYhjdpcs() );
        rktHjslQrDTO.setYhjdjlx( req.getYhjdjlx() );
        rktHjslQrDTO.setYhjdxzjd( req.getYhjdxzjd() );
        rktHjslQrDTO.setHh( req.getHh() );
        rktHjslQrDTO.setHhid( req.getHhid() );
        rktHjslQrDTO.setHlx( req.getHlx() );
        rktHjslQrDTO.setHb( req.getHb() );
        rktHjslQrDTO.setYhzgx( req.getYhzgx() );
        rktHjslQrDTO.setHkxz( req.getHkxz() );
        rktHjslQrDTO.setCxsx( req.getCxsx() );
        rktHjslQrDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslQrDTO.setXm( req.getXm() );
        rktHjslQrDTO.setX( req.getX() );
        rktHjslQrDTO.setM( req.getM() );
        rktHjslQrDTO.setCym( req.getCym() );
        rktHjslQrDTO.setXmpy( req.getXmpy() );
        rktHjslQrDTO.setCympy( req.getCympy() );
        rktHjslQrDTO.setXb( req.getXb() );
        rktHjslQrDTO.setMz( req.getMz() );
        rktHjslQrDTO.setJggjdq( req.getJggjdq() );
        rktHjslQrDTO.setJgssxq( req.getJgssxq() );
        rktHjslQrDTO.setJgxz( req.getJgxz() );
        rktHjslQrDTO.setCsrq( req.getCsrq() );
        rktHjslQrDTO.setCssj( req.getCssj() );
        rktHjslQrDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslQrDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslQrDTO.setCsdxz( req.getCsdxz() );
        rktHjslQrDTO.setWhcd( req.getWhcd() );
        rktHjslQrDTO.setHyzk( req.getHyzk() );
        rktHjslQrDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslQrDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslQrDTO.setZy( req.getZy() );
        rktHjslQrDTO.setZylb( req.getZylb() );
        rktHjslQrDTO.setZjxy( req.getZjxy() );
        rktHjslQrDTO.setSg( req.getSg() );
        rktHjslQrDTO.setXx( req.getXx() );
        rktHjslQrDTO.setByzk( req.getByzk() );
        rktHjslQrDTO.setXxjb( req.getXxjb() );
        rktHjslQrDTO.setLxdh( req.getLxdh() );
        rktHjslQrDTO.setFqxm( req.getFqxm() );
        rktHjslQrDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslQrDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslQrDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslQrDTO.setFqwwx( req.getFqwwx() );
        rktHjslQrDTO.setFqwwm( req.getFqwwm() );
        rktHjslQrDTO.setMqxm( req.getMqxm() );
        rktHjslQrDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslQrDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslQrDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslQrDTO.setMqwwx( req.getMqwwx() );
        rktHjslQrDTO.setMqwwm( req.getMqwwm() );
        rktHjslQrDTO.setPoxm( req.getPoxm() );
        rktHjslQrDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslQrDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslQrDTO.setPozjhm( req.getPozjhm() );
        rktHjslQrDTO.setPowwx( req.getPowwx() );
        rktHjslQrDTO.setPowwm( req.getPowwm() );
        rktHjslQrDTO.setJhryxm( req.getJhryxm() );
        rktHjslQrDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslQrDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslQrDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslQrDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslQrDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslQrDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslQrDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslQrDTO.setJhrexm( req.getJhrexm() );
        rktHjslQrDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslQrDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslQrDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslQrDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslQrDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslQrDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslQrDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslQrDTO.setLcywlx( req.getLcywlx() );
        rktHjslQrDTO.setLcslid( req.getLcslid() );
        rktHjslQrDTO.setLcywbt( req.getLcywbt() );
        rktHjslQrDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslQrDTO.setLczt( req.getLczt() );
        rktHjslQrDTO.setBlzt( req.getBlzt() );
        rktHjslQrDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslQrDTO.setSqrxm( req.getSqrxm() );
        rktHjslQrDTO.setSqrxb( req.getSqrxb() );
        rktHjslQrDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslQrDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslQrDTO.setSqrq( req.getSqrq() );
        rktHjslQrDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslQrDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslQrDTO.setSprxm( req.getSprxm() );
        rktHjslQrDTO.setSpsj( req.getSpsj() );
        rktHjslQrDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslQrDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslQrDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslQrDTO.setSlrxm( req.getSlrxm() );
        rktHjslQrDTO.setSlsj( req.getSlsj() );
        rktHjslQrDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslQrDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslQrDTO.setJcwh( req.getJcwh() );
        rktHjslQrDTO.setBz( req.getBz() );
        rktHjslQrDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslQrDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslQrDTO.setQtssxq( req.getQtssxq() );
        rktHjslQrDTO.setQtzz( req.getQtzz() );
        rktHjslQrDTO.setGjnzsbh( req.getGjnzsbh() );
        rktHjslQrDTO.setJflhfz( req.getJflhfz() );
        rktHjslQrDTO.setJftjrq( req.getJftjrq() );
        rktHjslQrDTO.setJzzbh( req.getJzzbh() );
        rktHjslQrDTO.setSfhgqwnyhk( req.getSfhgqwnyhk() );
        rktHjslQrDTO.setJzzdjzd( req.getJzzdjzd() );
        rktHjslQrDTO.setJzzjfz( req.getJzzjfz() );
        rktHjslQrDTO.setCszmbh( req.getCszmbh() );
        rktHjslQrDTO.setSfksxtqr( req.getSfksxtqr() );
        rktHjslQrDTO.setJflhfjf1( req.getJflhfjf1() );
        rktHjslQrDTO.setJflhfjf2( req.getJflhfjf2() );
        rktHjslQrDTO.setJflhfjfhj( req.getJflhfjfhj() );
        rktHjslQrDTO.setSftqjfpz( req.getSftqjfpz() );

        return rktHjslQrDTO;
    }

    @Override
    public RktHjslQrDTO convertToDTO(RktHjslQrUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslQrDTO rktHjslQrDTO = new RktHjslQrDTO();

        rktHjslQrDTO.setYwslnbbh( req.getYwslnbbh() );
        rktHjslQrDTO.setYwslh( req.getYwslh() );
        rktHjslQrDTO.setHmc( req.getHmc() );
        rktHjslQrDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslQrDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslQrDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslQrDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslQrDTO.setJlx( req.getJlx() );
        rktHjslQrDTO.setMlph( req.getMlph() );
        rktHjslQrDTO.setMlxz( req.getMlxz() );
        rktHjslQrDTO.setHhnbid( req.getHhnbid() );
        rktHjslQrDTO.setQrqhlx( req.getQrqhlx() );
        rktHjslQrDTO.setQrqhhnbid( req.getQrqhhnbid() );
        rktHjslQrDTO.setQrqhhid( req.getQrqhhid() );
        rktHjslQrDTO.setRynbid( req.getRynbid() );
        rktHjslQrDTO.setSfzqr( req.getSfzqr() );
        rktHjslQrDTO.setLcdyid( req.getLcdyid() );
        rktHjslQrDTO.setLcmc( req.getLcmc() );
        rktHjslQrDTO.setSpyj( req.getSpyj() );
        rktHjslQrDTO.setXh( req.getXh() );
        rktHjslQrDTO.setZjlb( req.getZjlb() );
        rktHjslQrDTO.setQfjg( req.getQfjg() );
        rktHjslQrDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslQrDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslQrDTO.setZqzbh( req.getZqzbh() );
        rktHjslQrDTO.setQyldyy( req.getQyldyy() );
        rktHjslQrDTO.setBdfw( req.getBdfw() );
        rktHjslQrDTO.setQyyyxflb( req.getQyyyxflb() );
        rktHjslQrDTO.setQyyybcsm( req.getQyyybcsm() );
        rktHjslQrDTO.setBtkrgmsfhm( req.getBtkrgmsfhm() );
        rktHjslQrDTO.setBtkrxm( req.getBtkrxm() );
        rktHjslQrDTO.setBtkrytkrgx( req.getBtkrytkrgx() );
        rktHjslQrDTO.setQyrysqrgx( req.getQyrysqrgx() );
        rktHjslQrDTO.setQrqcxsx( req.getQrqcxsx() );
        rktHjslQrDTO.setCxzhyy( req.getCxzhyy() );
        rktHjslQrDTO.setPdbzzyjszc( req.getPdbzzyjszc() );
        rktHjslQrDTO.setPdbzjndj( req.getPdbzjndj() );
        rktHjslQrDTO.setPdbzncjdzzyxbys( req.getPdbzncjdzzyxbys() );
        rktHjslQrDTO.setPdbzjjqx( req.getPdbzjjqx() );
        rktHjslQrDTO.setPdbzzczjyhjzwnys( req.getPdbzzczjyhjzwnys() );
        rktHjslQrDTO.setQrqhb( req.getQrqhb() );
        rktHjslQrDTO.setRyid( req.getRyid() );
        rktHjslQrDTO.setZqlx( req.getZqlx() );
        rktHjslQrDTO.setYhjddzbm( req.getYhjddzbm() );
        rktHjslQrDTO.setYhjdssxq( req.getYhjdssxq() );
        rktHjslQrDTO.setYhjdxxdz( req.getYhjdxxdz() );
        rktHjslQrDTO.setYhjszddjjgdm( req.getYhjszddjjgdm() );
        rktHjslQrDTO.setYhjszddjjgmc( req.getYhjszddjjgmc() );
        rktHjslQrDTO.setYhjdjwzrq( req.getYhjdjwzrq() );
        rktHjslQrDTO.setYhjdjcwh( req.getYhjdjcwh() );
        rktHjslQrDTO.setYhjdpcs( req.getYhjdpcs() );
        rktHjslQrDTO.setYhjdjlx( req.getYhjdjlx() );
        rktHjslQrDTO.setYhjdxzjd( req.getYhjdxzjd() );
        rktHjslQrDTO.setHh( req.getHh() );
        rktHjslQrDTO.setHhid( req.getHhid() );
        rktHjslQrDTO.setHlx( req.getHlx() );
        rktHjslQrDTO.setHb( req.getHb() );
        rktHjslQrDTO.setYhzgx( req.getYhzgx() );
        rktHjslQrDTO.setHkxz( req.getHkxz() );
        rktHjslQrDTO.setCxsx( req.getCxsx() );
        rktHjslQrDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslQrDTO.setXm( req.getXm() );
        rktHjslQrDTO.setX( req.getX() );
        rktHjslQrDTO.setM( req.getM() );
        rktHjslQrDTO.setCym( req.getCym() );
        rktHjslQrDTO.setXmpy( req.getXmpy() );
        rktHjslQrDTO.setCympy( req.getCympy() );
        rktHjslQrDTO.setXb( req.getXb() );
        rktHjslQrDTO.setMz( req.getMz() );
        rktHjslQrDTO.setJggjdq( req.getJggjdq() );
        rktHjslQrDTO.setJgssxq( req.getJgssxq() );
        rktHjslQrDTO.setJgxz( req.getJgxz() );
        rktHjslQrDTO.setCsrq( req.getCsrq() );
        rktHjslQrDTO.setCssj( req.getCssj() );
        rktHjslQrDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslQrDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslQrDTO.setCsdxz( req.getCsdxz() );
        rktHjslQrDTO.setWhcd( req.getWhcd() );
        rktHjslQrDTO.setHyzk( req.getHyzk() );
        rktHjslQrDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslQrDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslQrDTO.setZy( req.getZy() );
        rktHjslQrDTO.setZylb( req.getZylb() );
        rktHjslQrDTO.setZjxy( req.getZjxy() );
        rktHjslQrDTO.setSg( req.getSg() );
        rktHjslQrDTO.setXx( req.getXx() );
        rktHjslQrDTO.setByzk( req.getByzk() );
        rktHjslQrDTO.setXxjb( req.getXxjb() );
        rktHjslQrDTO.setLxdh( req.getLxdh() );
        rktHjslQrDTO.setFqxm( req.getFqxm() );
        rktHjslQrDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslQrDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslQrDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslQrDTO.setFqwwx( req.getFqwwx() );
        rktHjslQrDTO.setFqwwm( req.getFqwwm() );
        rktHjslQrDTO.setMqxm( req.getMqxm() );
        rktHjslQrDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslQrDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslQrDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslQrDTO.setMqwwx( req.getMqwwx() );
        rktHjslQrDTO.setMqwwm( req.getMqwwm() );
        rktHjslQrDTO.setPoxm( req.getPoxm() );
        rktHjslQrDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslQrDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslQrDTO.setPozjhm( req.getPozjhm() );
        rktHjslQrDTO.setPowwx( req.getPowwx() );
        rktHjslQrDTO.setPowwm( req.getPowwm() );
        rktHjslQrDTO.setJhryxm( req.getJhryxm() );
        rktHjslQrDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslQrDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslQrDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslQrDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslQrDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslQrDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslQrDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslQrDTO.setJhrexm( req.getJhrexm() );
        rktHjslQrDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslQrDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslQrDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslQrDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslQrDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslQrDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslQrDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslQrDTO.setLcywlx( req.getLcywlx() );
        rktHjslQrDTO.setLcslid( req.getLcslid() );
        rktHjslQrDTO.setLcywbt( req.getLcywbt() );
        rktHjslQrDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslQrDTO.setLczt( req.getLczt() );
        rktHjslQrDTO.setBlzt( req.getBlzt() );
        rktHjslQrDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslQrDTO.setSqrxm( req.getSqrxm() );
        rktHjslQrDTO.setSqrxb( req.getSqrxb() );
        rktHjslQrDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslQrDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslQrDTO.setSqrq( req.getSqrq() );
        rktHjslQrDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslQrDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslQrDTO.setSprxm( req.getSprxm() );
        rktHjslQrDTO.setSpsj( req.getSpsj() );
        rktHjslQrDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslQrDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslQrDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslQrDTO.setSlrxm( req.getSlrxm() );
        rktHjslQrDTO.setSlsj( req.getSlsj() );
        rktHjslQrDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslQrDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslQrDTO.setJcwh( req.getJcwh() );
        rktHjslQrDTO.setBz( req.getBz() );
        rktHjslQrDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslQrDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslQrDTO.setQtssxq( req.getQtssxq() );
        rktHjslQrDTO.setQtzz( req.getQtzz() );
        rktHjslQrDTO.setGjnzsbh( req.getGjnzsbh() );
        rktHjslQrDTO.setJflhfz( req.getJflhfz() );
        rktHjslQrDTO.setJftjrq( req.getJftjrq() );
        rktHjslQrDTO.setJzzbh( req.getJzzbh() );
        rktHjslQrDTO.setSfhgqwnyhk( req.getSfhgqwnyhk() );
        rktHjslQrDTO.setJzzdjzd( req.getJzzdjzd() );
        rktHjslQrDTO.setJzzjfz( req.getJzzjfz() );
        rktHjslQrDTO.setCszmbh( req.getCszmbh() );
        rktHjslQrDTO.setSfksxtqr( req.getSfksxtqr() );
        rktHjslQrDTO.setJflhfjf1( req.getJflhfjf1() );
        rktHjslQrDTO.setJflhfjf2( req.getJflhfjf2() );
        rktHjslQrDTO.setJflhfjfhj( req.getJflhfjfhj() );
        rktHjslQrDTO.setSftqjfpz( req.getSftqjfpz() );

        return rktHjslQrDTO;
    }

    @Override
    public RktHjslQrPageResp convertToPageResp(RktHjslQrDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslQrPageResp rktHjslQrPageResp = new RktHjslQrPageResp();

        rktHjslQrPageResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslQrPageResp.setYwslh( dto.getYwslh() );
        rktHjslQrPageResp.setHmc( dto.getHmc() );
        rktHjslQrPageResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslQrPageResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslQrPageResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslQrPageResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslQrPageResp.setJlx( dto.getJlx() );
        rktHjslQrPageResp.setMlph( dto.getMlph() );
        rktHjslQrPageResp.setMlxz( dto.getMlxz() );
        rktHjslQrPageResp.setHhnbid( dto.getHhnbid() );
        rktHjslQrPageResp.setQrqhlx( dto.getQrqhlx() );
        rktHjslQrPageResp.setQrqhhnbid( dto.getQrqhhnbid() );
        rktHjslQrPageResp.setQrqhhid( dto.getQrqhhid() );
        rktHjslQrPageResp.setRynbid( dto.getRynbid() );
        rktHjslQrPageResp.setSfzqr( dto.getSfzqr() );
        rktHjslQrPageResp.setLcdyid( dto.getLcdyid() );
        rktHjslQrPageResp.setLcmc( dto.getLcmc() );
        rktHjslQrPageResp.setSpyj( dto.getSpyj() );
        rktHjslQrPageResp.setXh( dto.getXh() );
        rktHjslQrPageResp.setZjlb( dto.getZjlb() );
        rktHjslQrPageResp.setQfjg( dto.getQfjg() );
        rktHjslQrPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslQrPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslQrPageResp.setZqzbh( dto.getZqzbh() );
        rktHjslQrPageResp.setQyldyy( dto.getQyldyy() );
        rktHjslQrPageResp.setBdfw( dto.getBdfw() );
        rktHjslQrPageResp.setQyyyxflb( dto.getQyyyxflb() );
        rktHjslQrPageResp.setQyyybcsm( dto.getQyyybcsm() );
        rktHjslQrPageResp.setBtkrgmsfhm( dto.getBtkrgmsfhm() );
        rktHjslQrPageResp.setBtkrxm( dto.getBtkrxm() );
        rktHjslQrPageResp.setBtkrytkrgx( dto.getBtkrytkrgx() );
        rktHjslQrPageResp.setQyrysqrgx( dto.getQyrysqrgx() );
        rktHjslQrPageResp.setQrqcxsx( dto.getQrqcxsx() );
        rktHjslQrPageResp.setCxzhyy( dto.getCxzhyy() );
        rktHjslQrPageResp.setPdbzzyjszc( dto.getPdbzzyjszc() );
        rktHjslQrPageResp.setPdbzjndj( dto.getPdbzjndj() );
        rktHjslQrPageResp.setPdbzncjdzzyxbys( dto.getPdbzncjdzzyxbys() );
        rktHjslQrPageResp.setPdbzjjqx( dto.getPdbzjjqx() );
        rktHjslQrPageResp.setPdbzzczjyhjzwnys( dto.getPdbzzczjyhjzwnys() );
        rktHjslQrPageResp.setQrqhb( dto.getQrqhb() );
        rktHjslQrPageResp.setRyid( dto.getRyid() );
        rktHjslQrPageResp.setZqlx( dto.getZqlx() );
        rktHjslQrPageResp.setYhjddzbm( dto.getYhjddzbm() );
        rktHjslQrPageResp.setYhjdssxq( dto.getYhjdssxq() );
        rktHjslQrPageResp.setYhjdxxdz( dto.getYhjdxxdz() );
        rktHjslQrPageResp.setYhjszddjjgdm( dto.getYhjszddjjgdm() );
        rktHjslQrPageResp.setYhjszddjjgmc( dto.getYhjszddjjgmc() );
        rktHjslQrPageResp.setYhjdjwzrq( dto.getYhjdjwzrq() );
        rktHjslQrPageResp.setYhjdjcwh( dto.getYhjdjcwh() );
        rktHjslQrPageResp.setYhjdpcs( dto.getYhjdpcs() );
        rktHjslQrPageResp.setYhjdjlx( dto.getYhjdjlx() );
        rktHjslQrPageResp.setYhjdxzjd( dto.getYhjdxzjd() );
        rktHjslQrPageResp.setHh( dto.getHh() );
        rktHjslQrPageResp.setHhid( dto.getHhid() );
        rktHjslQrPageResp.setHlx( dto.getHlx() );
        rktHjslQrPageResp.setHb( dto.getHb() );
        rktHjslQrPageResp.setYhzgx( dto.getYhzgx() );
        rktHjslQrPageResp.setHkxz( dto.getHkxz() );
        rktHjslQrPageResp.setCxsx( dto.getCxsx() );
        rktHjslQrPageResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslQrPageResp.setXm( dto.getXm() );
        rktHjslQrPageResp.setX( dto.getX() );
        rktHjslQrPageResp.setM( dto.getM() );
        rktHjslQrPageResp.setCym( dto.getCym() );
        rktHjslQrPageResp.setXmpy( dto.getXmpy() );
        rktHjslQrPageResp.setCympy( dto.getCympy() );
        rktHjslQrPageResp.setXb( dto.getXb() );
        rktHjslQrPageResp.setMz( dto.getMz() );
        rktHjslQrPageResp.setJggjdq( dto.getJggjdq() );
        rktHjslQrPageResp.setJgssxq( dto.getJgssxq() );
        rktHjslQrPageResp.setJgxz( dto.getJgxz() );
        rktHjslQrPageResp.setCsrq( dto.getCsrq() );
        rktHjslQrPageResp.setCssj( dto.getCssj() );
        rktHjslQrPageResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslQrPageResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslQrPageResp.setCsdxz( dto.getCsdxz() );
        rktHjslQrPageResp.setWhcd( dto.getWhcd() );
        rktHjslQrPageResp.setHyzk( dto.getHyzk() );
        rktHjslQrPageResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslQrPageResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslQrPageResp.setZy( dto.getZy() );
        rktHjslQrPageResp.setZylb( dto.getZylb() );
        rktHjslQrPageResp.setZjxy( dto.getZjxy() );
        rktHjslQrPageResp.setSg( dto.getSg() );
        rktHjslQrPageResp.setXx( dto.getXx() );
        rktHjslQrPageResp.setByzk( dto.getByzk() );
        rktHjslQrPageResp.setXxjb( dto.getXxjb() );
        rktHjslQrPageResp.setLxdh( dto.getLxdh() );
        rktHjslQrPageResp.setFqxm( dto.getFqxm() );
        rktHjslQrPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslQrPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslQrPageResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslQrPageResp.setFqwwx( dto.getFqwwx() );
        rktHjslQrPageResp.setFqwwm( dto.getFqwwm() );
        rktHjslQrPageResp.setMqxm( dto.getMqxm() );
        rktHjslQrPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslQrPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslQrPageResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslQrPageResp.setMqwwx( dto.getMqwwx() );
        rktHjslQrPageResp.setMqwwm( dto.getMqwwm() );
        rktHjslQrPageResp.setPoxm( dto.getPoxm() );
        rktHjslQrPageResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslQrPageResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslQrPageResp.setPozjhm( dto.getPozjhm() );
        rktHjslQrPageResp.setPowwx( dto.getPowwx() );
        rktHjslQrPageResp.setPowwm( dto.getPowwm() );
        rktHjslQrPageResp.setJhryxm( dto.getJhryxm() );
        rktHjslQrPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslQrPageResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslQrPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslQrPageResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslQrPageResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslQrPageResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslQrPageResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslQrPageResp.setJhrexm( dto.getJhrexm() );
        rktHjslQrPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslQrPageResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslQrPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslQrPageResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslQrPageResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslQrPageResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslQrPageResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslQrPageResp.setLcywlx( dto.getLcywlx() );
        rktHjslQrPageResp.setLcslid( dto.getLcslid() );
        rktHjslQrPageResp.setLcywbt( dto.getLcywbt() );
        rktHjslQrPageResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslQrPageResp.setLczt( dto.getLczt() );
        rktHjslQrPageResp.setBlzt( dto.getBlzt() );
        rktHjslQrPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslQrPageResp.setSqrxm( dto.getSqrxm() );
        rktHjslQrPageResp.setSqrxb( dto.getSqrxb() );
        rktHjslQrPageResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslQrPageResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslQrPageResp.setSqrq( dto.getSqrq() );
        rktHjslQrPageResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslQrPageResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslQrPageResp.setSprxm( dto.getSprxm() );
        rktHjslQrPageResp.setSpsj( dto.getSpsj() );
        rktHjslQrPageResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslQrPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslQrPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslQrPageResp.setSlrxm( dto.getSlrxm() );
        rktHjslQrPageResp.setSlsj( dto.getSlsj() );
        rktHjslQrPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslQrPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslQrPageResp.setJcwh( dto.getJcwh() );
        rktHjslQrPageResp.setBz( dto.getBz() );
        rktHjslQrPageResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslQrPageResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslQrPageResp.setQtssxq( dto.getQtssxq() );
        rktHjslQrPageResp.setQtzz( dto.getQtzz() );
        rktHjslQrPageResp.setGjnzsbh( dto.getGjnzsbh() );
        rktHjslQrPageResp.setJflhfz( dto.getJflhfz() );
        rktHjslQrPageResp.setJftjrq( dto.getJftjrq() );
        rktHjslQrPageResp.setJzzbh( dto.getJzzbh() );
        rktHjslQrPageResp.setSfhgqwnyhk( dto.getSfhgqwnyhk() );
        rktHjslQrPageResp.setJzzdjzd( dto.getJzzdjzd() );
        rktHjslQrPageResp.setJzzjfz( dto.getJzzjfz() );
        rktHjslQrPageResp.setCszmbh( dto.getCszmbh() );
        rktHjslQrPageResp.setSfksxtqr( dto.getSfksxtqr() );
        rktHjslQrPageResp.setJflhfjf1( dto.getJflhfjf1() );
        rktHjslQrPageResp.setJflhfjf2( dto.getJflhfjf2() );
        rktHjslQrPageResp.setJflhfjfhj( dto.getJflhfjfhj() );
        rktHjslQrPageResp.setSftqjfpz( dto.getSftqjfpz() );

        return rktHjslQrPageResp;
    }

    @Override
    public RktHjslQrViewResp convertToViewResp(RktHjslQrDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslQrViewResp rktHjslQrViewResp = new RktHjslQrViewResp();

        rktHjslQrViewResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslQrViewResp.setYwslh( dto.getYwslh() );
        rktHjslQrViewResp.setHmc( dto.getHmc() );
        rktHjslQrViewResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslQrViewResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslQrViewResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslQrViewResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslQrViewResp.setJlx( dto.getJlx() );
        rktHjslQrViewResp.setMlph( dto.getMlph() );
        rktHjslQrViewResp.setMlxz( dto.getMlxz() );
        rktHjslQrViewResp.setHhnbid( dto.getHhnbid() );
        rktHjslQrViewResp.setQrqhlx( dto.getQrqhlx() );
        rktHjslQrViewResp.setQrqhhnbid( dto.getQrqhhnbid() );
        rktHjslQrViewResp.setQrqhhid( dto.getQrqhhid() );
        rktHjslQrViewResp.setRynbid( dto.getRynbid() );
        rktHjslQrViewResp.setSfzqr( dto.getSfzqr() );
        rktHjslQrViewResp.setLcdyid( dto.getLcdyid() );
        rktHjslQrViewResp.setLcmc( dto.getLcmc() );
        rktHjslQrViewResp.setSpyj( dto.getSpyj() );
        rktHjslQrViewResp.setXh( dto.getXh() );
        rktHjslQrViewResp.setZjlb( dto.getZjlb() );
        rktHjslQrViewResp.setQfjg( dto.getQfjg() );
        rktHjslQrViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslQrViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslQrViewResp.setZqzbh( dto.getZqzbh() );
        rktHjslQrViewResp.setQyldyy( dto.getQyldyy() );
        rktHjslQrViewResp.setBdfw( dto.getBdfw() );
        rktHjslQrViewResp.setQyyyxflb( dto.getQyyyxflb() );
        rktHjslQrViewResp.setQyyybcsm( dto.getQyyybcsm() );
        rktHjslQrViewResp.setBtkrgmsfhm( dto.getBtkrgmsfhm() );
        rktHjslQrViewResp.setBtkrxm( dto.getBtkrxm() );
        rktHjslQrViewResp.setBtkrytkrgx( dto.getBtkrytkrgx() );
        rktHjslQrViewResp.setQyrysqrgx( dto.getQyrysqrgx() );
        rktHjslQrViewResp.setQrqcxsx( dto.getQrqcxsx() );
        rktHjslQrViewResp.setCxzhyy( dto.getCxzhyy() );
        rktHjslQrViewResp.setPdbzzyjszc( dto.getPdbzzyjszc() );
        rktHjslQrViewResp.setPdbzjndj( dto.getPdbzjndj() );
        rktHjslQrViewResp.setPdbzncjdzzyxbys( dto.getPdbzncjdzzyxbys() );
        rktHjslQrViewResp.setPdbzjjqx( dto.getPdbzjjqx() );
        rktHjslQrViewResp.setPdbzzczjyhjzwnys( dto.getPdbzzczjyhjzwnys() );
        rktHjslQrViewResp.setQrqhb( dto.getQrqhb() );
        rktHjslQrViewResp.setRyid( dto.getRyid() );
        rktHjslQrViewResp.setZqlx( dto.getZqlx() );
        rktHjslQrViewResp.setYhjddzbm( dto.getYhjddzbm() );
        rktHjslQrViewResp.setYhjdssxq( dto.getYhjdssxq() );
        rktHjslQrViewResp.setYhjdxxdz( dto.getYhjdxxdz() );
        rktHjslQrViewResp.setYhjszddjjgdm( dto.getYhjszddjjgdm() );
        rktHjslQrViewResp.setYhjszddjjgmc( dto.getYhjszddjjgmc() );
        rktHjslQrViewResp.setYhjdjwzrq( dto.getYhjdjwzrq() );
        rktHjslQrViewResp.setYhjdjcwh( dto.getYhjdjcwh() );
        rktHjslQrViewResp.setYhjdpcs( dto.getYhjdpcs() );
        rktHjslQrViewResp.setYhjdjlx( dto.getYhjdjlx() );
        rktHjslQrViewResp.setYhjdxzjd( dto.getYhjdxzjd() );
        rktHjslQrViewResp.setHh( dto.getHh() );
        rktHjslQrViewResp.setHhid( dto.getHhid() );
        rktHjslQrViewResp.setHlx( dto.getHlx() );
        rktHjslQrViewResp.setHb( dto.getHb() );
        rktHjslQrViewResp.setYhzgx( dto.getYhzgx() );
        rktHjslQrViewResp.setHkxz( dto.getHkxz() );
        rktHjslQrViewResp.setCxsx( dto.getCxsx() );
        rktHjslQrViewResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslQrViewResp.setXm( dto.getXm() );
        rktHjslQrViewResp.setX( dto.getX() );
        rktHjslQrViewResp.setM( dto.getM() );
        rktHjslQrViewResp.setCym( dto.getCym() );
        rktHjslQrViewResp.setXmpy( dto.getXmpy() );
        rktHjslQrViewResp.setCympy( dto.getCympy() );
        rktHjslQrViewResp.setXb( dto.getXb() );
        rktHjslQrViewResp.setMz( dto.getMz() );
        rktHjslQrViewResp.setJggjdq( dto.getJggjdq() );
        rktHjslQrViewResp.setJgssxq( dto.getJgssxq() );
        rktHjslQrViewResp.setJgxz( dto.getJgxz() );
        rktHjslQrViewResp.setCsrq( dto.getCsrq() );
        rktHjslQrViewResp.setCssj( dto.getCssj() );
        rktHjslQrViewResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslQrViewResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslQrViewResp.setCsdxz( dto.getCsdxz() );
        rktHjslQrViewResp.setWhcd( dto.getWhcd() );
        rktHjslQrViewResp.setHyzk( dto.getHyzk() );
        rktHjslQrViewResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslQrViewResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslQrViewResp.setZy( dto.getZy() );
        rktHjslQrViewResp.setZylb( dto.getZylb() );
        rktHjslQrViewResp.setZjxy( dto.getZjxy() );
        rktHjslQrViewResp.setSg( dto.getSg() );
        rktHjslQrViewResp.setXx( dto.getXx() );
        rktHjslQrViewResp.setByzk( dto.getByzk() );
        rktHjslQrViewResp.setXxjb( dto.getXxjb() );
        rktHjslQrViewResp.setLxdh( dto.getLxdh() );
        rktHjslQrViewResp.setFqxm( dto.getFqxm() );
        rktHjslQrViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslQrViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslQrViewResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslQrViewResp.setFqwwx( dto.getFqwwx() );
        rktHjslQrViewResp.setFqwwm( dto.getFqwwm() );
        rktHjslQrViewResp.setMqxm( dto.getMqxm() );
        rktHjslQrViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslQrViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslQrViewResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslQrViewResp.setMqwwx( dto.getMqwwx() );
        rktHjslQrViewResp.setMqwwm( dto.getMqwwm() );
        rktHjslQrViewResp.setPoxm( dto.getPoxm() );
        rktHjslQrViewResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslQrViewResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslQrViewResp.setPozjhm( dto.getPozjhm() );
        rktHjslQrViewResp.setPowwx( dto.getPowwx() );
        rktHjslQrViewResp.setPowwm( dto.getPowwm() );
        rktHjslQrViewResp.setJhryxm( dto.getJhryxm() );
        rktHjslQrViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslQrViewResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslQrViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslQrViewResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslQrViewResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslQrViewResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslQrViewResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslQrViewResp.setJhrexm( dto.getJhrexm() );
        rktHjslQrViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslQrViewResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslQrViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslQrViewResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslQrViewResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslQrViewResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslQrViewResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslQrViewResp.setLcywlx( dto.getLcywlx() );
        rktHjslQrViewResp.setLcslid( dto.getLcslid() );
        rktHjslQrViewResp.setLcywbt( dto.getLcywbt() );
        rktHjslQrViewResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslQrViewResp.setLczt( dto.getLczt() );
        rktHjslQrViewResp.setBlzt( dto.getBlzt() );
        rktHjslQrViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslQrViewResp.setSqrxm( dto.getSqrxm() );
        rktHjslQrViewResp.setSqrxb( dto.getSqrxb() );
        rktHjslQrViewResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslQrViewResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslQrViewResp.setSqrq( dto.getSqrq() );
        rktHjslQrViewResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslQrViewResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslQrViewResp.setSprxm( dto.getSprxm() );
        rktHjslQrViewResp.setSpsj( dto.getSpsj() );
        rktHjslQrViewResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslQrViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslQrViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslQrViewResp.setSlrxm( dto.getSlrxm() );
        rktHjslQrViewResp.setSlsj( dto.getSlsj() );
        rktHjslQrViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslQrViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslQrViewResp.setJcwh( dto.getJcwh() );
        rktHjslQrViewResp.setBz( dto.getBz() );
        rktHjslQrViewResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslQrViewResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslQrViewResp.setQtssxq( dto.getQtssxq() );
        rktHjslQrViewResp.setQtzz( dto.getQtzz() );
        rktHjslQrViewResp.setGjnzsbh( dto.getGjnzsbh() );
        rktHjslQrViewResp.setJflhfz( dto.getJflhfz() );
        rktHjslQrViewResp.setJftjrq( dto.getJftjrq() );
        rktHjslQrViewResp.setJzzbh( dto.getJzzbh() );
        rktHjslQrViewResp.setSfhgqwnyhk( dto.getSfhgqwnyhk() );
        rktHjslQrViewResp.setJzzdjzd( dto.getJzzdjzd() );
        rktHjslQrViewResp.setJzzjfz( dto.getJzzjfz() );
        rktHjslQrViewResp.setCszmbh( dto.getCszmbh() );
        rktHjslQrViewResp.setSfksxtqr( dto.getSfksxtqr() );
        rktHjslQrViewResp.setJflhfjf1( dto.getJflhfjf1() );
        rktHjslQrViewResp.setJflhfjf2( dto.getJflhfjf2() );
        rktHjslQrViewResp.setJflhfjfhj( dto.getJflhfjfhj() );
        rktHjslQrViewResp.setSftqjfpz( dto.getSftqjfpz() );

        return rktHjslQrViewResp;
    }

    @Override
    public RktHjslQrCreateResp convertToCreateResp(RktHjslQrDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslQrCreateResp rktHjslQrCreateResp = new RktHjslQrCreateResp();

        rktHjslQrCreateResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslQrCreateResp.setYwslh( dto.getYwslh() );
        rktHjslQrCreateResp.setHmc( dto.getHmc() );
        rktHjslQrCreateResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslQrCreateResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslQrCreateResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslQrCreateResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslQrCreateResp.setJlx( dto.getJlx() );
        rktHjslQrCreateResp.setMlph( dto.getMlph() );
        rktHjslQrCreateResp.setMlxz( dto.getMlxz() );
        rktHjslQrCreateResp.setHhnbid( dto.getHhnbid() );
        rktHjslQrCreateResp.setQrqhlx( dto.getQrqhlx() );
        rktHjslQrCreateResp.setQrqhhnbid( dto.getQrqhhnbid() );
        rktHjslQrCreateResp.setQrqhhid( dto.getQrqhhid() );
        rktHjslQrCreateResp.setRynbid( dto.getRynbid() );
        rktHjslQrCreateResp.setSfzqr( dto.getSfzqr() );
        rktHjslQrCreateResp.setLcdyid( dto.getLcdyid() );
        rktHjslQrCreateResp.setLcmc( dto.getLcmc() );
        rktHjslQrCreateResp.setSpyj( dto.getSpyj() );
        rktHjslQrCreateResp.setXh( dto.getXh() );
        rktHjslQrCreateResp.setZjlb( dto.getZjlb() );
        rktHjslQrCreateResp.setQfjg( dto.getQfjg() );
        rktHjslQrCreateResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslQrCreateResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslQrCreateResp.setZqzbh( dto.getZqzbh() );
        rktHjslQrCreateResp.setQyldyy( dto.getQyldyy() );
        rktHjslQrCreateResp.setBdfw( dto.getBdfw() );
        rktHjslQrCreateResp.setQyyyxflb( dto.getQyyyxflb() );
        rktHjslQrCreateResp.setQyyybcsm( dto.getQyyybcsm() );
        rktHjslQrCreateResp.setBtkrgmsfhm( dto.getBtkrgmsfhm() );
        rktHjslQrCreateResp.setBtkrxm( dto.getBtkrxm() );
        rktHjslQrCreateResp.setBtkrytkrgx( dto.getBtkrytkrgx() );
        rktHjslQrCreateResp.setQyrysqrgx( dto.getQyrysqrgx() );
        rktHjslQrCreateResp.setQrqcxsx( dto.getQrqcxsx() );
        rktHjslQrCreateResp.setCxzhyy( dto.getCxzhyy() );
        rktHjslQrCreateResp.setPdbzzyjszc( dto.getPdbzzyjszc() );
        rktHjslQrCreateResp.setPdbzjndj( dto.getPdbzjndj() );
        rktHjslQrCreateResp.setPdbzncjdzzyxbys( dto.getPdbzncjdzzyxbys() );
        rktHjslQrCreateResp.setPdbzjjqx( dto.getPdbzjjqx() );
        rktHjslQrCreateResp.setPdbzzczjyhjzwnys( dto.getPdbzzczjyhjzwnys() );
        rktHjslQrCreateResp.setQrqhb( dto.getQrqhb() );
        rktHjslQrCreateResp.setRyid( dto.getRyid() );
        rktHjslQrCreateResp.setZqlx( dto.getZqlx() );
        rktHjslQrCreateResp.setYhjddzbm( dto.getYhjddzbm() );
        rktHjslQrCreateResp.setYhjdssxq( dto.getYhjdssxq() );
        rktHjslQrCreateResp.setYhjdxxdz( dto.getYhjdxxdz() );
        rktHjslQrCreateResp.setYhjszddjjgdm( dto.getYhjszddjjgdm() );
        rktHjslQrCreateResp.setYhjszddjjgmc( dto.getYhjszddjjgmc() );
        rktHjslQrCreateResp.setYhjdjwzrq( dto.getYhjdjwzrq() );
        rktHjslQrCreateResp.setYhjdjcwh( dto.getYhjdjcwh() );
        rktHjslQrCreateResp.setYhjdpcs( dto.getYhjdpcs() );
        rktHjslQrCreateResp.setYhjdjlx( dto.getYhjdjlx() );
        rktHjslQrCreateResp.setYhjdxzjd( dto.getYhjdxzjd() );
        rktHjslQrCreateResp.setHh( dto.getHh() );
        rktHjslQrCreateResp.setHhid( dto.getHhid() );
        rktHjslQrCreateResp.setHlx( dto.getHlx() );
        rktHjslQrCreateResp.setHb( dto.getHb() );
        rktHjslQrCreateResp.setYhzgx( dto.getYhzgx() );
        rktHjslQrCreateResp.setHkxz( dto.getHkxz() );
        rktHjslQrCreateResp.setCxsx( dto.getCxsx() );
        rktHjslQrCreateResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslQrCreateResp.setXm( dto.getXm() );
        rktHjslQrCreateResp.setX( dto.getX() );
        rktHjslQrCreateResp.setM( dto.getM() );
        rktHjslQrCreateResp.setCym( dto.getCym() );
        rktHjslQrCreateResp.setXmpy( dto.getXmpy() );
        rktHjslQrCreateResp.setCympy( dto.getCympy() );
        rktHjslQrCreateResp.setXb( dto.getXb() );
        rktHjslQrCreateResp.setMz( dto.getMz() );
        rktHjslQrCreateResp.setJggjdq( dto.getJggjdq() );
        rktHjslQrCreateResp.setJgssxq( dto.getJgssxq() );
        rktHjslQrCreateResp.setJgxz( dto.getJgxz() );
        rktHjslQrCreateResp.setCsrq( dto.getCsrq() );
        rktHjslQrCreateResp.setCssj( dto.getCssj() );
        rktHjslQrCreateResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslQrCreateResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslQrCreateResp.setCsdxz( dto.getCsdxz() );
        rktHjslQrCreateResp.setWhcd( dto.getWhcd() );
        rktHjslQrCreateResp.setHyzk( dto.getHyzk() );
        rktHjslQrCreateResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslQrCreateResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslQrCreateResp.setZy( dto.getZy() );
        rktHjslQrCreateResp.setZylb( dto.getZylb() );
        rktHjslQrCreateResp.setZjxy( dto.getZjxy() );
        rktHjslQrCreateResp.setSg( dto.getSg() );
        rktHjslQrCreateResp.setXx( dto.getXx() );
        rktHjslQrCreateResp.setByzk( dto.getByzk() );
        rktHjslQrCreateResp.setXxjb( dto.getXxjb() );
        rktHjslQrCreateResp.setLxdh( dto.getLxdh() );
        rktHjslQrCreateResp.setFqxm( dto.getFqxm() );
        rktHjslQrCreateResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslQrCreateResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslQrCreateResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslQrCreateResp.setFqwwx( dto.getFqwwx() );
        rktHjslQrCreateResp.setFqwwm( dto.getFqwwm() );
        rktHjslQrCreateResp.setMqxm( dto.getMqxm() );
        rktHjslQrCreateResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslQrCreateResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslQrCreateResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslQrCreateResp.setMqwwx( dto.getMqwwx() );
        rktHjslQrCreateResp.setMqwwm( dto.getMqwwm() );
        rktHjslQrCreateResp.setPoxm( dto.getPoxm() );
        rktHjslQrCreateResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslQrCreateResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslQrCreateResp.setPozjhm( dto.getPozjhm() );
        rktHjslQrCreateResp.setPowwx( dto.getPowwx() );
        rktHjslQrCreateResp.setPowwm( dto.getPowwm() );
        rktHjslQrCreateResp.setJhryxm( dto.getJhryxm() );
        rktHjslQrCreateResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslQrCreateResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslQrCreateResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslQrCreateResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslQrCreateResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslQrCreateResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslQrCreateResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslQrCreateResp.setJhrexm( dto.getJhrexm() );
        rktHjslQrCreateResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslQrCreateResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslQrCreateResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslQrCreateResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslQrCreateResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslQrCreateResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslQrCreateResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslQrCreateResp.setLcywlx( dto.getLcywlx() );
        rktHjslQrCreateResp.setLcslid( dto.getLcslid() );
        rktHjslQrCreateResp.setLcywbt( dto.getLcywbt() );
        rktHjslQrCreateResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslQrCreateResp.setLczt( dto.getLczt() );
        rktHjslQrCreateResp.setBlzt( dto.getBlzt() );
        rktHjslQrCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslQrCreateResp.setSqrxm( dto.getSqrxm() );
        rktHjslQrCreateResp.setSqrxb( dto.getSqrxb() );
        rktHjslQrCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslQrCreateResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslQrCreateResp.setSqrq( dto.getSqrq() );
        rktHjslQrCreateResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslQrCreateResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslQrCreateResp.setSprxm( dto.getSprxm() );
        rktHjslQrCreateResp.setSpsj( dto.getSpsj() );
        rktHjslQrCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslQrCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslQrCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslQrCreateResp.setSlrxm( dto.getSlrxm() );
        rktHjslQrCreateResp.setSlsj( dto.getSlsj() );
        rktHjslQrCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslQrCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslQrCreateResp.setJcwh( dto.getJcwh() );
        rktHjslQrCreateResp.setBz( dto.getBz() );
        rktHjslQrCreateResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslQrCreateResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslQrCreateResp.setQtssxq( dto.getQtssxq() );
        rktHjslQrCreateResp.setQtzz( dto.getQtzz() );
        rktHjslQrCreateResp.setGjnzsbh( dto.getGjnzsbh() );
        rktHjslQrCreateResp.setJflhfz( dto.getJflhfz() );
        rktHjslQrCreateResp.setJftjrq( dto.getJftjrq() );
        rktHjslQrCreateResp.setJzzbh( dto.getJzzbh() );
        rktHjslQrCreateResp.setSfhgqwnyhk( dto.getSfhgqwnyhk() );
        rktHjslQrCreateResp.setJzzdjzd( dto.getJzzdjzd() );
        rktHjslQrCreateResp.setJzzjfz( dto.getJzzjfz() );
        rktHjslQrCreateResp.setCszmbh( dto.getCszmbh() );
        rktHjslQrCreateResp.setSfksxtqr( dto.getSfksxtqr() );
        rktHjslQrCreateResp.setJflhfjf1( dto.getJflhfjf1() );
        rktHjslQrCreateResp.setJflhfjf2( dto.getJflhfjf2() );
        rktHjslQrCreateResp.setJflhfjfhj( dto.getJflhfjfhj() );
        rktHjslQrCreateResp.setSftqjfpz( dto.getSftqjfpz() );

        return rktHjslQrCreateResp;
    }
}
