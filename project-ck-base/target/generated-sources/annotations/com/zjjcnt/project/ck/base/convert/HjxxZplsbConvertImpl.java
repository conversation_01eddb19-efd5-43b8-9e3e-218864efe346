package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxZplsbDTO;
import com.zjjcnt.project.ck.base.dto.resp.HjxxZplsbViewResp;
import com.zjjcnt.project.ck.base.entity.HjxxZplsbDO;
import java.util.Arrays;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:41+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjxxZplsbConvertImpl implements HjxxZplsbConvert {

    @Override
    public HjxxZplsbDTO convert(HjxxZplsbDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjxxZplsbDTO hjxxZplsbDTO = new HjxxZplsbDTO();

        hjxxZplsbDTO.setId( entity.getId() );
        hjxxZplsbDTO.setZplsid( entity.getZplsid() );
        hjxxZplsbDTO.setSlh( entity.getSlh() );
        hjxxZplsbDTO.setGmsfhm( entity.getGmsfhm() );
        hjxxZplsbDTO.setZpid( entity.getZpid() );
        hjxxZplsbDTO.setRyid( entity.getRyid() );
        hjxxZplsbDTO.setRynbid( entity.getRynbid() );
        byte[] zp = entity.getZp();
        if ( zp != null ) {
            hjxxZplsbDTO.setZp( Arrays.copyOf( zp, zp.length ) );
        }
        hjxxZplsbDTO.setBcsj( entity.getBcsj() );
        hjxxZplsbDTO.setCzrid( entity.getCzrid() );
        hjxxZplsbDTO.setJlbz( entity.getJlbz() );
        hjxxZplsbDTO.setBdrid( entity.getBdrid() );
        hjxxZplsbDTO.setBdrdwdm( entity.getBdrdwdm() );
        hjxxZplsbDTO.setBdsj( entity.getBdsj() );

        return hjxxZplsbDTO;
    }

    @Override
    public HjxxZplsbDO convertToDO(HjxxZplsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxZplsbDO hjxxZplsbDO = new HjxxZplsbDO();

        hjxxZplsbDO.setId( dto.getId() );
        hjxxZplsbDO.setZplsid( dto.getZplsid() );
        hjxxZplsbDO.setSlh( dto.getSlh() );
        hjxxZplsbDO.setGmsfhm( dto.getGmsfhm() );
        hjxxZplsbDO.setZpid( dto.getZpid() );
        hjxxZplsbDO.setRyid( dto.getRyid() );
        hjxxZplsbDO.setRynbid( dto.getRynbid() );
        byte[] zp = dto.getZp();
        if ( zp != null ) {
            hjxxZplsbDO.setZp( Arrays.copyOf( zp, zp.length ) );
        }
        hjxxZplsbDO.setBcsj( dto.getBcsj() );
        hjxxZplsbDO.setCzrid( dto.getCzrid() );
        hjxxZplsbDO.setJlbz( dto.getJlbz() );
        hjxxZplsbDO.setBdrid( dto.getBdrid() );
        hjxxZplsbDO.setBdrdwdm( dto.getBdrdwdm() );
        hjxxZplsbDO.setBdsj( dto.getBdsj() );

        return hjxxZplsbDO;
    }

    @Override
    public HjxxZplsbViewResp convertToViewResp(HjxxZplsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxZplsbViewResp hjxxZplsbViewResp = new HjxxZplsbViewResp();

        hjxxZplsbViewResp.setZplsid( dto.getZplsid() );
        hjxxZplsbViewResp.setSlh( dto.getSlh() );
        hjxxZplsbViewResp.setGmsfhm( dto.getGmsfhm() );
        hjxxZplsbViewResp.setZpid( dto.getZpid() );
        hjxxZplsbViewResp.setRyid( dto.getRyid() );
        hjxxZplsbViewResp.setRynbid( dto.getRynbid() );
        byte[] zp = dto.getZp();
        if ( zp != null ) {
            hjxxZplsbViewResp.setZp( Arrays.copyOf( zp, zp.length ) );
        }
        hjxxZplsbViewResp.setBcsj( dto.getBcsj() );
        hjxxZplsbViewResp.setCzrid( dto.getCzrid() );
        hjxxZplsbViewResp.setJlbz( dto.getJlbz() );
        hjxxZplsbViewResp.setBdrid( dto.getBdrid() );
        hjxxZplsbViewResp.setBdrdwdm( dto.getBdrdwdm() );
        hjxxZplsbViewResp.setBdsj( dto.getBdsj() );

        return hjxxZplsbViewResp;
    }
}
