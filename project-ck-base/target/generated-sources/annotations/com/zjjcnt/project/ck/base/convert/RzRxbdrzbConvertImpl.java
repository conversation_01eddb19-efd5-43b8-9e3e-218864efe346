package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RzRxbdrzbDTO;
import com.zjjcnt.project.ck.base.entity.RzRxbdrzbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RzRxbdrzbConvertImpl implements RzRxbdrzbConvert {

    @Override
    public RzRxbdrzbDTO convert(RzRxbdrzbDO entity) {
        if ( entity == null ) {
            return null;
        }

        RzRxbdrzbDTO rzRxbdrzbDTO = new RzRxbdrzbDTO();

        rzRxbdrzbDTO.setId( entity.getId() );
        rzRxbdrzbDTO.setRxbdrzid( entity.getRxbdrzid() );
        rzRxbdrzbDTO.setGmsfhm( entity.getGmsfhm() );
        rzRxbdrzbDTO.setJkmc( entity.getJkmc() );
        rzRxbdrzbDTO.setBdkssj( entity.getBdkssj() );
        rzRxbdrzbDTO.setBdhs( entity.getBdhs() );
        rzRxbdrzbDTO.setBdrid( entity.getBdrid() );
        rzRxbdrzbDTO.setBdrxm( entity.getBdrxm() );
        rzRxbdrzbDTO.setBdrdw( entity.getBdrdw() );
        rzRxbdrzbDTO.setBdrip( entity.getBdrip() );
        rzRxbdrzbDTO.setCzsj( entity.getCzsj() );

        return rzRxbdrzbDTO;
    }

    @Override
    public RzRxbdrzbDO convertToDO(RzRxbdrzbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzRxbdrzbDO rzRxbdrzbDO = new RzRxbdrzbDO();

        rzRxbdrzbDO.setId( dto.getId() );
        rzRxbdrzbDO.setRxbdrzid( dto.getRxbdrzid() );
        rzRxbdrzbDO.setGmsfhm( dto.getGmsfhm() );
        rzRxbdrzbDO.setJkmc( dto.getJkmc() );
        rzRxbdrzbDO.setBdkssj( dto.getBdkssj() );
        rzRxbdrzbDO.setBdhs( dto.getBdhs() );
        rzRxbdrzbDO.setBdrid( dto.getBdrid() );
        rzRxbdrzbDO.setBdrxm( dto.getBdrxm() );
        rzRxbdrzbDO.setBdrdw( dto.getBdrdw() );
        rzRxbdrzbDO.setBdrip( dto.getBdrip() );
        rzRxbdrzbDO.setCzsj( dto.getCzsj() );

        return rzRxbdrzbDO;
    }
}
