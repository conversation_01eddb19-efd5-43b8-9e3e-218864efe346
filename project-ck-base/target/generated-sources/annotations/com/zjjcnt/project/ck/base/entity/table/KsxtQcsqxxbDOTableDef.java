package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class KsxtQcsqxxbDOTableDef extends TableDef {

    /**
     * 跨省协同迁出申请信息表DO

 <AUTHOR>
 @date 2025-08-05 13:59:15
 @see com.zjjcnt.project.ck.base.dto.KsxtQcsqxxbDTO
     */
    public static final KsxtQcsqxxbDOTableDef KSXT_QCSQXXB_DO = new KsxtQcsqxxbDOTableDef();

    /**
     * 备注
     */
    public final QueryColumn BZ = new QueryColumn(this, "bz");

    /**
     * 姓名
     */
    public final QueryColumn XM = new QueryColumn(this, "xm");

    /**
     * 出生日期
     */
    public final QueryColumn CSRQ = new QueryColumn(this, "csrq");

    /**
     * 入库时间
     */
    public final QueryColumn RKSJ = new QueryColumn(this, "rksj");

    /**
     * 受理时间
     */
    public final QueryColumn SLSJ = new QueryColumn(this, "slsj");

    /**
     * 性别
     */
    public final QueryColumn XBDM = new QueryColumn(this, "xbdm");

    /**
     * 受理人姓名
     */
    public final QueryColumn SLRXM = new QueryColumn(this, "slrxm");

    /**
     * 申请人姓名
     */
    public final QueryColumn SQRXM = new QueryColumn(this, "sqrxm");

    /**
     * 准迁证编号
     */
    public final QueryColumn ZQZBH = new QueryColumn(this, "zqzbh");

    /**
     * 公民身份号码
     */
    public final QueryColumn GMSFHM = new QueryColumn(this, "gmsfhm");

    /**
     * 户籍迁出标志
     */
    public final QueryColumn HJQCBZ = new QueryColumn(this, "hjqcbz");

    /**
     * 跨省协同ID
     */
    public final QueryColumn KSXTID = new QueryColumn(this, "ksxtid");

    /**
     * 区域范围代码
     */
    public final QueryColumn QYFWDM = new QueryColumn(this, "qyfwdm");

    /**
     * 归档配置版本号
     */
    public final QueryColumn GDPZBBH = new QueryColumn(this, "gdpzbbh");

    /**
     * 受理地联系电话
     */
    public final QueryColumn SLDLXDH = new QueryColumn(this, "sldlxdh");

    /**
     * 审批业务受理号
     */
    public final QueryColumn SPYWSLH = new QueryColumn(this, "spywslh");

    /**
     * 申请人联系电话
     */
    public final QueryColumn SQRLXDH = new QueryColumn(this, "sqrlxdh");

    /**
     * 准迁证_签发日期
     */
    public final QueryColumn ZQZQFRQ = new QueryColumn(this, "zqzqfrq");

    /**
     * 迁移（流动）原因
     */
    public final QueryColumn QYLDYYDM = new QueryColumn(this, "qyldyydm");

    /**
     * 审批业务受理时间
     */
    public final QueryColumn SPYWSLSJ = new QueryColumn(this, "spywslsj");

    /**
     * 迁出地省市县（区）
     */
    public final QueryColumn QCDSSXQDM = new QueryColumn(this, "qcdssxqdm");

    /**
     * 迁入地省市县（区）
     */
    public final QueryColumn QRDSSXQDM = new QueryColumn(this, "qrdssxqdm");

    /**
     * 受理地公安机关名称
     */
    public final QueryColumn SLDGAJGMC = new QueryColumn(this, "sldgajgmc");

    /**
     * 申请人公民身份号码
     */
    public final QueryColumn SQRGMSFHM = new QueryColumn(this, "sqrgmsfhm");

    /**
     * 电子准迁_证照标识
     */
    public final QueryColumn DZZQDZZZBZ = new QueryColumn(this, "dzzqdzzzbz");

    /**
     * 跨省协同迁出申请ID
     */
    public final QueryColumn KSXTQCSQID = new QueryColumn(this, "ksxtqcsqid");

    /**
     * 迁出地区划内详细地址
     */
    public final QueryColumn QCDQHNXXDZ = new QueryColumn(this, "qcdqhnxxdz");

    /**
     * 迁入地区划内详细地址
     */
    public final QueryColumn QRDQHNXXDZ = new QueryColumn(this, "qrdqhnxxdz");

    /**
     * 治安管理业务类别代码
     */
    public final QueryColumn ZAGLYWLBDM = new QueryColumn(this, "zaglywlbdm");

    /**
     * 治安管理业务协同编号
     */
    public final QueryColumn ZAGLYWXTBH = new QueryColumn(this, "zaglywxtbh");

    /**
     * 准迁证_有效期截止日期
     */
    public final QueryColumn ZQZYXQJZRQ = new QueryColumn(this, "zqzyxqjzrq");

    /**
     * 迁出地数据归属单位代码
     */
    public final QueryColumn QCDSJGSDWDM = new QueryColumn(this, "qcdsjgsdwdm");

    /**
     * 迁出地数据归属单位名称
     */
    public final QueryColumn QCDSJGSDWMC = new QueryColumn(this, "qcdsjgsdwmc");

    /**
     * 受理地公安机关机构代码
     */
    public final QueryColumn SLDGAJGJGDM = new QueryColumn(this, "sldgajgjgdm");

    /**
     * 受理地数据归属单位代码
     */
    public final QueryColumn SLDSJGSDWDM = new QueryColumn(this, "sldsjgsdwdm");

    /**
     * 受理地数据归属单位名称
     */
    public final QueryColumn SLDSJGSDWMC = new QueryColumn(this, "sldsjgsdwmc");

    /**
     * 申请人住址省市县（区）
     */
    public final QueryColumn SQRZZSSXQDM = new QueryColumn(this, "sqrzzssxqdm");

    /**
     * 发送单位数据归属单位代码
     */
    public final QueryColumn FSDWSJGSDWDM = new QueryColumn(this, "fsdwsjgsdwdm");

    /**
     * 发送单位数据归属单位名称
     */
    public final QueryColumn FSDWSJGSDWMC = new QueryColumn(this, "fsdwsjgsdwmc");

    /**
     * 接收单位数据归属单位代码
     */
    public final QueryColumn JSDWSJGSDWDM = new QueryColumn(this, "jsdwsjgsdwdm");

    /**
     * 接收单位数据归属单位名称
     */
    public final QueryColumn JSDWSJGSDWMC = new QueryColumn(this, "jsdwsjgsdwmc");

    /**
     * 申请人住址区划内详细地址
     */
    public final QueryColumn SQRZZQHNXXDZ = new QueryColumn(this, "sqrzzqhnxxdz");

    /**
     * 与申请人关系_家庭关系
     */
    public final QueryColumn YSQRGXJTGXDM = new QueryColumn(this, "ysqrgxjtgxdm");

    /**
     * 治安管理政务服务事项编码
     */
    public final QueryColumn ZAGLZWFWSXBM = new QueryColumn(this, "zaglzwfwsxbm");

    /**
     * 准迁证_签发机关公安机关名称
     */
    public final QueryColumn ZQZQFJGGAJGMC = new QueryColumn(this, "zqzqfjggajgmc");

    /**
     * 电子准迁_治安管理电子证照编号
     */
    public final QueryColumn DZZQZAGLDZZZBH = new QueryColumn(this, "dzzqzagldzzzbh");

    /**
     * 迁出地户口登记机关公安机关名称
     */
    public final QueryColumn QCDHKDJJGGAJGMC = new QueryColumn(this, "qcdhkdjjggajgmc");

    /**
     * 迁入地户口登记机关公安机关名称
     */
    public final QueryColumn QRDHKDJJGGAJGMC = new QueryColumn(this, "qrdhkdjjggajgmc");

    /**
     * 申请人户口登记机关公安机关名称
     */
    public final QueryColumn SQRHKDJJGGAJGMC = new QueryColumn(this, "sqrhkdjjggajgmc");

    /**
     * 准迁证_签发机关公安机关机构代码
     */
    public final QueryColumn ZQZQFJGGAJGJGDM = new QueryColumn(this, "zqzqfjggajgjgdm");

    /**
     * 迁出地户口登记机关公安机关机构代码
     */
    public final QueryColumn QCDHKDJJGGAJGJGDM = new QueryColumn(this, "qcdhkdjjggajgjgdm");

    /**
     * 迁入地户口登记机关公安机关机构代码
     */
    public final QueryColumn QRDHKDJJGGAJGJGDM = new QueryColumn(this, "qrdhkdjjggajgjgdm");

    /**
     * 申请人户口登记机关公安机关机构代码
     */
    public final QueryColumn SQRHKDJJGGAJGJGDM = new QueryColumn(this, "sqrhkdjjggajgjgdm");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{BZ, XM, CSRQ, RKSJ, SLSJ, XBDM, SLRXM, SQRXM, ZQZBH, GMSFHM, HJQCBZ, KSXTID, QYFWDM, GDPZBBH, SLDLXDH, SPYWSLH, SQRLXDH, ZQZQFRQ, QYLDYYDM, SPYWSLSJ, QCDSSXQDM, QRDSSXQDM, SLDGAJGMC, SQRGMSFHM, DZZQDZZZBZ, KSXTQCSQID, QCDQHNXXDZ, QRDQHNXXDZ, ZAGLYWLBDM, ZAGLYWXTBH, ZQZYXQJZRQ, QCDSJGSDWDM, QCDSJGSDWMC, SLDGAJGJGDM, SLDSJGSDWDM, SLDSJGSDWMC, SQRZZSSXQDM, FSDWSJGSDWDM, FSDWSJGSDWMC, JSDWSJGSDWDM, JSDWSJGSDWMC, SQRZZQHNXXDZ, YSQRGXJTGXDM, ZAGLZWFWSXBM, ZQZQFJGGAJGMC, DZZQZAGLDZZZBH, QCDHKDJJGGAJGMC, QRDHKDJJGGAJGMC, SQRHKDJJGGAJGMC, ZQZQFJGGAJGJGDM, QCDHKDJJGGAJGJGDM, QRDHKDJJGGAJGJGDM, SQRHKDJJGGAJGJGDM};

    public KsxtQcsqxxbDOTableDef() {
        super("", "ksxt_qcsqxxb");
    }

    private KsxtQcsqxxbDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public KsxtQcsqxxbDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new KsxtQcsqxxbDOTableDef("", "ksxt_qcsqxxb", alias));
    }

}
