package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsywCssbxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsywCssbxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsywCssbxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsywCssbxxbViewResp;
import com.zjjcnt.project.ck.base.entity.KsywCssbxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:46+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsywCssbxxbConvertImpl implements KsywCssbxxbConvert {

    @Override
    public KsywCssbxxbDTO convert(KsywCssbxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsywCssbxxbDTO ksywCssbxxbDTO = new KsywCssbxxbDTO();

        ksywCssbxxbDTO.setId( entity.getId() );
        ksywCssbxxbDTO.setKscsywid( entity.getKscsywid() );
        ksywCssbxxbDTO.setKsywid( entity.getKsywid() );
        ksywCssbxxbDTO.setZaglywxtbh( entity.getZaglywxtbh() );
        ksywCssbxxbDTO.setGmsfhm( entity.getGmsfhm() );
        ksywCssbxxbDTO.setXm( entity.getXm() );
        ksywCssbxxbDTO.setX( entity.getX() );
        ksywCssbxxbDTO.setM( entity.getM() );
        ksywCssbxxbDTO.setXbdm( entity.getXbdm() );
        ksywCssbxxbDTO.setMzdm( entity.getMzdm() );
        ksywCssbxxbDTO.setCsrq( entity.getCsrq() );
        ksywCssbxxbDTO.setCssj( entity.getCssj() );
        ksywCssbxxbDTO.setCsdgjhdqdm( entity.getCsdgjhdqdm() );
        ksywCssbxxbDTO.setCsdssxqdm( entity.getCsdssxqdm() );
        ksywCssbxxbDTO.setCsdqhnxxdz( entity.getCsdqhnxxdz() );
        ksywCssbxxbDTO.setJggjhdqdm( entity.getJggjhdqdm() );
        ksywCssbxxbDTO.setJgssxqdm( entity.getJgssxqdm() );
        ksywCssbxxbDTO.setJgqhnxxdz( entity.getJgqhnxxdz() );
        ksywCssbxxbDTO.setXxdm( entity.getXxdm() );
        ksywCssbxxbDTO.setRkxxjbdm( entity.getRkxxjbdm() );
        ksywCssbxxbDTO.setCsdjlbdm( entity.getCsdjlbdm() );
        ksywCssbxxbDTO.setCszmbh( entity.getCszmbh() );
        ksywCssbxxbDTO.setYhzgxdm( entity.getYhzgxdm() );
        ksywCssbxxbDTO.setHzxm( entity.getHzxm() );
        ksywCssbxxbDTO.setHzgmsfhm( entity.getHzgmsfhm() );
        ksywCssbxxbDTO.setHjdzssxqdm( entity.getHjdzssxqdm() );
        ksywCssbxxbDTO.setHjdzqhnxxdz( entity.getHjdzqhnxxdz() );
        ksywCssbxxbDTO.setHjdzsjgsdwdm( entity.getHjdzsjgsdwdm() );
        ksywCssbxxbDTO.setHjdzsjgsdwmc( entity.getHjdzsjgsdwmc() );
        ksywCssbxxbDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        ksywCssbxxbDTO.setFqxm( entity.getFqxm() );
        ksywCssbxxbDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        ksywCssbxxbDTO.setFqzjhm( entity.getFqzjhm() );
        ksywCssbxxbDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        ksywCssbxxbDTO.setMqxm( entity.getMqxm() );
        ksywCssbxxbDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        ksywCssbxxbDTO.setMqzjhm( entity.getMqzjhm() );
        ksywCssbxxbDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        ksywCssbxxbDTO.setJhryxm( entity.getJhryxm() );
        ksywCssbxxbDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        ksywCssbxxbDTO.setJhryzjhm( entity.getJhryzjhm() );
        ksywCssbxxbDTO.setJhrywwx( entity.getJhrywwx() );
        ksywCssbxxbDTO.setJhrywwm( entity.getJhrywwm() );
        ksywCssbxxbDTO.setJhryjhgxdm( entity.getJhryjhgxdm() );
        ksywCssbxxbDTO.setJhrylxdh( entity.getJhrylxdh() );
        ksywCssbxxbDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        ksywCssbxxbDTO.setJhrexm( entity.getJhrexm() );
        ksywCssbxxbDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        ksywCssbxxbDTO.setJhrezjhm( entity.getJhrezjhm() );
        ksywCssbxxbDTO.setJhrewwx( entity.getJhrewwx() );
        ksywCssbxxbDTO.setJhrewwm( entity.getJhrewwm() );
        ksywCssbxxbDTO.setJhrejhgxdm( entity.getJhrejhgxdm() );
        ksywCssbxxbDTO.setJhrelxdh( entity.getJhrelxdh() );
        ksywCssbxxbDTO.setXzzssxqdm( entity.getXzzssxqdm() );
        ksywCssbxxbDTO.setXzzqhnxxdz( entity.getXzzqhnxxdz() );
        ksywCssbxxbDTO.setSbrgmsfhm( entity.getSbrgmsfhm() );
        ksywCssbxxbDTO.setSbrxm( entity.getSbrxm() );
        ksywCssbxxbDTO.setSbrlxdh( entity.getSbrlxdh() );
        ksywCssbxxbDTO.setSbrycsrgxjtgxdm( entity.getSbrycsrgxjtgxdm() );
        ksywCssbxxbDTO.setSjrxm( entity.getSjrxm() );
        ksywCssbxxbDTO.setSjrgmsfhm( entity.getSjrgmsfhm() );
        ksywCssbxxbDTO.setSjrlxdh( entity.getSjrlxdh() );
        ksywCssbxxbDTO.setSjdzssxqdm( entity.getSjdzssxqdm() );
        ksywCssbxxbDTO.setSjdzqhnxxdz( entity.getSjdzqhnxxdz() );
        ksywCssbxxbDTO.setSldgajgjgdm( entity.getSldgajgjgdm() );
        ksywCssbxxbDTO.setSldgajgmc( entity.getSldgajgmc() );
        ksywCssbxxbDTO.setSldlxdh( entity.getSldlxdh() );
        ksywCssbxxbDTO.setSlrid( entity.getSlrid() );
        ksywCssbxxbDTO.setSlrxm( entity.getSlrxm() );
        ksywCssbxxbDTO.setSlsj( entity.getSlsj() );
        ksywCssbxxbDTO.setSldwsjgsdwdm( entity.getSldwsjgsdwdm() );
        ksywCssbxxbDTO.setSldwsjgsdwmc( entity.getSldwsjgsdwmc() );
        ksywCssbxxbDTO.setRksj( entity.getRksj() );

        return ksywCssbxxbDTO;
    }

    @Override
    public KsywCssbxxbDO convertToDO(KsywCssbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywCssbxxbDO ksywCssbxxbDO = new KsywCssbxxbDO();

        ksywCssbxxbDO.setId( dto.getId() );
        ksywCssbxxbDO.setKscsywid( dto.getKscsywid() );
        ksywCssbxxbDO.setKsywid( dto.getKsywid() );
        ksywCssbxxbDO.setZaglywxtbh( dto.getZaglywxtbh() );
        ksywCssbxxbDO.setGmsfhm( dto.getGmsfhm() );
        ksywCssbxxbDO.setXm( dto.getXm() );
        ksywCssbxxbDO.setX( dto.getX() );
        ksywCssbxxbDO.setM( dto.getM() );
        ksywCssbxxbDO.setXbdm( dto.getXbdm() );
        ksywCssbxxbDO.setMzdm( dto.getMzdm() );
        ksywCssbxxbDO.setCsrq( dto.getCsrq() );
        ksywCssbxxbDO.setCssj( dto.getCssj() );
        ksywCssbxxbDO.setCsdgjhdqdm( dto.getCsdgjhdqdm() );
        ksywCssbxxbDO.setCsdssxqdm( dto.getCsdssxqdm() );
        ksywCssbxxbDO.setCsdqhnxxdz( dto.getCsdqhnxxdz() );
        ksywCssbxxbDO.setJggjhdqdm( dto.getJggjhdqdm() );
        ksywCssbxxbDO.setJgssxqdm( dto.getJgssxqdm() );
        ksywCssbxxbDO.setJgqhnxxdz( dto.getJgqhnxxdz() );
        ksywCssbxxbDO.setXxdm( dto.getXxdm() );
        ksywCssbxxbDO.setRkxxjbdm( dto.getRkxxjbdm() );
        ksywCssbxxbDO.setCsdjlbdm( dto.getCsdjlbdm() );
        ksywCssbxxbDO.setCszmbh( dto.getCszmbh() );
        ksywCssbxxbDO.setYhzgxdm( dto.getYhzgxdm() );
        ksywCssbxxbDO.setHzxm( dto.getHzxm() );
        ksywCssbxxbDO.setHzgmsfhm( dto.getHzgmsfhm() );
        ksywCssbxxbDO.setHjdzssxqdm( dto.getHjdzssxqdm() );
        ksywCssbxxbDO.setHjdzqhnxxdz( dto.getHjdzqhnxxdz() );
        ksywCssbxxbDO.setHjdzsjgsdwdm( dto.getHjdzsjgsdwdm() );
        ksywCssbxxbDO.setHjdzsjgsdwmc( dto.getHjdzsjgsdwmc() );
        ksywCssbxxbDO.setFqgmsfhm( dto.getFqgmsfhm() );
        ksywCssbxxbDO.setFqxm( dto.getFqxm() );
        ksywCssbxxbDO.setFqcyzjdm( dto.getFqcyzjdm() );
        ksywCssbxxbDO.setFqzjhm( dto.getFqzjhm() );
        ksywCssbxxbDO.setMqgmsfhm( dto.getMqgmsfhm() );
        ksywCssbxxbDO.setMqxm( dto.getMqxm() );
        ksywCssbxxbDO.setMqcyzjdm( dto.getMqcyzjdm() );
        ksywCssbxxbDO.setMqzjhm( dto.getMqzjhm() );
        ksywCssbxxbDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        ksywCssbxxbDO.setJhryxm( dto.getJhryxm() );
        ksywCssbxxbDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        ksywCssbxxbDO.setJhryzjhm( dto.getJhryzjhm() );
        ksywCssbxxbDO.setJhrywwx( dto.getJhrywwx() );
        ksywCssbxxbDO.setJhrywwm( dto.getJhrywwm() );
        ksywCssbxxbDO.setJhryjhgxdm( dto.getJhryjhgxdm() );
        ksywCssbxxbDO.setJhrylxdh( dto.getJhrylxdh() );
        ksywCssbxxbDO.setJhregmsfhm( dto.getJhregmsfhm() );
        ksywCssbxxbDO.setJhrexm( dto.getJhrexm() );
        ksywCssbxxbDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        ksywCssbxxbDO.setJhrezjhm( dto.getJhrezjhm() );
        ksywCssbxxbDO.setJhrewwx( dto.getJhrewwx() );
        ksywCssbxxbDO.setJhrewwm( dto.getJhrewwm() );
        ksywCssbxxbDO.setJhrejhgxdm( dto.getJhrejhgxdm() );
        ksywCssbxxbDO.setJhrelxdh( dto.getJhrelxdh() );
        ksywCssbxxbDO.setXzzssxqdm( dto.getXzzssxqdm() );
        ksywCssbxxbDO.setXzzqhnxxdz( dto.getXzzqhnxxdz() );
        ksywCssbxxbDO.setSbrgmsfhm( dto.getSbrgmsfhm() );
        ksywCssbxxbDO.setSbrxm( dto.getSbrxm() );
        ksywCssbxxbDO.setSbrlxdh( dto.getSbrlxdh() );
        ksywCssbxxbDO.setSbrycsrgxjtgxdm( dto.getSbrycsrgxjtgxdm() );
        ksywCssbxxbDO.setSjrxm( dto.getSjrxm() );
        ksywCssbxxbDO.setSjrgmsfhm( dto.getSjrgmsfhm() );
        ksywCssbxxbDO.setSjrlxdh( dto.getSjrlxdh() );
        ksywCssbxxbDO.setSjdzssxqdm( dto.getSjdzssxqdm() );
        ksywCssbxxbDO.setSjdzqhnxxdz( dto.getSjdzqhnxxdz() );
        ksywCssbxxbDO.setSldgajgjgdm( dto.getSldgajgjgdm() );
        ksywCssbxxbDO.setSldgajgmc( dto.getSldgajgmc() );
        ksywCssbxxbDO.setSldlxdh( dto.getSldlxdh() );
        ksywCssbxxbDO.setSlrid( dto.getSlrid() );
        ksywCssbxxbDO.setSlrxm( dto.getSlrxm() );
        ksywCssbxxbDO.setSlsj( dto.getSlsj() );
        ksywCssbxxbDO.setSldwsjgsdwdm( dto.getSldwsjgsdwdm() );
        ksywCssbxxbDO.setSldwsjgsdwmc( dto.getSldwsjgsdwmc() );
        ksywCssbxxbDO.setRksj( dto.getRksj() );

        return ksywCssbxxbDO;
    }

    @Override
    public KsywCssbxxbDTO convertToDTO(KsywCssbxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsywCssbxxbDTO ksywCssbxxbDTO = new KsywCssbxxbDTO();

        ksywCssbxxbDTO.setKsywid( req.getKsywid() );
        ksywCssbxxbDTO.setZaglywxtbh( req.getZaglywxtbh() );
        ksywCssbxxbDTO.setGmsfhm( req.getGmsfhm() );
        ksywCssbxxbDTO.setXm( req.getXm() );
        ksywCssbxxbDTO.setX( req.getX() );
        ksywCssbxxbDTO.setM( req.getM() );
        ksywCssbxxbDTO.setXbdm( req.getXbdm() );
        ksywCssbxxbDTO.setMzdm( req.getMzdm() );
        ksywCssbxxbDTO.setCsrq( req.getCsrq() );
        ksywCssbxxbDTO.setCssj( req.getCssj() );
        ksywCssbxxbDTO.setCsdgjhdqdm( req.getCsdgjhdqdm() );
        ksywCssbxxbDTO.setCsdssxqdm( req.getCsdssxqdm() );
        ksywCssbxxbDTO.setCsdqhnxxdz( req.getCsdqhnxxdz() );
        ksywCssbxxbDTO.setJggjhdqdm( req.getJggjhdqdm() );
        ksywCssbxxbDTO.setJgssxqdm( req.getJgssxqdm() );
        ksywCssbxxbDTO.setJgqhnxxdz( req.getJgqhnxxdz() );
        ksywCssbxxbDTO.setXxdm( req.getXxdm() );
        ksywCssbxxbDTO.setRkxxjbdm( req.getRkxxjbdm() );
        ksywCssbxxbDTO.setCsdjlbdm( req.getCsdjlbdm() );
        ksywCssbxxbDTO.setCszmbh( req.getCszmbh() );
        ksywCssbxxbDTO.setYhzgxdm( req.getYhzgxdm() );
        ksywCssbxxbDTO.setHzxm( req.getHzxm() );
        ksywCssbxxbDTO.setHzgmsfhm( req.getHzgmsfhm() );
        ksywCssbxxbDTO.setHjdzssxqdm( req.getHjdzssxqdm() );
        ksywCssbxxbDTO.setHjdzqhnxxdz( req.getHjdzqhnxxdz() );
        ksywCssbxxbDTO.setHjdzsjgsdwdm( req.getHjdzsjgsdwdm() );
        ksywCssbxxbDTO.setHjdzsjgsdwmc( req.getHjdzsjgsdwmc() );
        ksywCssbxxbDTO.setFqgmsfhm( req.getFqgmsfhm() );
        ksywCssbxxbDTO.setFqxm( req.getFqxm() );
        ksywCssbxxbDTO.setFqcyzjdm( req.getFqcyzjdm() );
        ksywCssbxxbDTO.setFqzjhm( req.getFqzjhm() );
        ksywCssbxxbDTO.setMqgmsfhm( req.getMqgmsfhm() );
        ksywCssbxxbDTO.setMqxm( req.getMqxm() );
        ksywCssbxxbDTO.setMqcyzjdm( req.getMqcyzjdm() );
        ksywCssbxxbDTO.setMqzjhm( req.getMqzjhm() );
        ksywCssbxxbDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        ksywCssbxxbDTO.setJhryxm( req.getJhryxm() );
        ksywCssbxxbDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        ksywCssbxxbDTO.setJhryzjhm( req.getJhryzjhm() );
        ksywCssbxxbDTO.setJhrywwx( req.getJhrywwx() );
        ksywCssbxxbDTO.setJhrywwm( req.getJhrywwm() );
        ksywCssbxxbDTO.setJhryjhgxdm( req.getJhryjhgxdm() );
        ksywCssbxxbDTO.setJhrylxdh( req.getJhrylxdh() );
        ksywCssbxxbDTO.setJhregmsfhm( req.getJhregmsfhm() );
        ksywCssbxxbDTO.setJhrexm( req.getJhrexm() );
        ksywCssbxxbDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        ksywCssbxxbDTO.setJhrezjhm( req.getJhrezjhm() );
        ksywCssbxxbDTO.setJhrewwx( req.getJhrewwx() );
        ksywCssbxxbDTO.setJhrewwm( req.getJhrewwm() );
        ksywCssbxxbDTO.setJhrejhgxdm( req.getJhrejhgxdm() );
        ksywCssbxxbDTO.setJhrelxdh( req.getJhrelxdh() );
        ksywCssbxxbDTO.setXzzssxqdm( req.getXzzssxqdm() );
        ksywCssbxxbDTO.setXzzqhnxxdz( req.getXzzqhnxxdz() );
        ksywCssbxxbDTO.setSbrgmsfhm( req.getSbrgmsfhm() );
        ksywCssbxxbDTO.setSbrxm( req.getSbrxm() );
        ksywCssbxxbDTO.setSbrlxdh( req.getSbrlxdh() );
        ksywCssbxxbDTO.setSbrycsrgxjtgxdm( req.getSbrycsrgxjtgxdm() );
        ksywCssbxxbDTO.setSjrxm( req.getSjrxm() );
        ksywCssbxxbDTO.setSjrgmsfhm( req.getSjrgmsfhm() );
        ksywCssbxxbDTO.setSjrlxdh( req.getSjrlxdh() );
        ksywCssbxxbDTO.setSjdzssxqdm( req.getSjdzssxqdm() );
        ksywCssbxxbDTO.setSjdzqhnxxdz( req.getSjdzqhnxxdz() );
        ksywCssbxxbDTO.setSldgajgjgdm( req.getSldgajgjgdm() );
        ksywCssbxxbDTO.setSldgajgmc( req.getSldgajgmc() );
        ksywCssbxxbDTO.setSldlxdh( req.getSldlxdh() );
        ksywCssbxxbDTO.setSlrid( req.getSlrid() );
        ksywCssbxxbDTO.setSlrxm( req.getSlrxm() );
        ksywCssbxxbDTO.setSlsj( req.getSlsj() );
        ksywCssbxxbDTO.setSldwsjgsdwdm( req.getSldwsjgsdwdm() );
        ksywCssbxxbDTO.setSldwsjgsdwmc( req.getSldwsjgsdwmc() );
        ksywCssbxxbDTO.setRksj( req.getRksj() );

        return ksywCssbxxbDTO;
    }

    @Override
    public KsywCssbxxbPageResp convertToPageResp(KsywCssbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywCssbxxbPageResp ksywCssbxxbPageResp = new KsywCssbxxbPageResp();

        ksywCssbxxbPageResp.setKscsywid( dto.getKscsywid() );
        ksywCssbxxbPageResp.setKsywid( dto.getKsywid() );
        ksywCssbxxbPageResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksywCssbxxbPageResp.setGmsfhm( dto.getGmsfhm() );
        ksywCssbxxbPageResp.setXm( dto.getXm() );
        ksywCssbxxbPageResp.setX( dto.getX() );
        ksywCssbxxbPageResp.setM( dto.getM() );
        ksywCssbxxbPageResp.setXbdm( dto.getXbdm() );
        ksywCssbxxbPageResp.setMzdm( dto.getMzdm() );
        ksywCssbxxbPageResp.setCsrq( dto.getCsrq() );
        ksywCssbxxbPageResp.setCssj( dto.getCssj() );
        ksywCssbxxbPageResp.setCsdgjhdqdm( dto.getCsdgjhdqdm() );
        ksywCssbxxbPageResp.setCsdssxqdm( dto.getCsdssxqdm() );
        ksywCssbxxbPageResp.setCsdqhnxxdz( dto.getCsdqhnxxdz() );
        ksywCssbxxbPageResp.setJggjhdqdm( dto.getJggjhdqdm() );
        ksywCssbxxbPageResp.setJgssxqdm( dto.getJgssxqdm() );
        ksywCssbxxbPageResp.setJgqhnxxdz( dto.getJgqhnxxdz() );
        ksywCssbxxbPageResp.setXxdm( dto.getXxdm() );
        ksywCssbxxbPageResp.setRkxxjbdm( dto.getRkxxjbdm() );
        ksywCssbxxbPageResp.setCsdjlbdm( dto.getCsdjlbdm() );
        ksywCssbxxbPageResp.setCszmbh( dto.getCszmbh() );
        ksywCssbxxbPageResp.setYhzgxdm( dto.getYhzgxdm() );
        ksywCssbxxbPageResp.setHzxm( dto.getHzxm() );
        ksywCssbxxbPageResp.setHzgmsfhm( dto.getHzgmsfhm() );
        ksywCssbxxbPageResp.setHjdzssxqdm( dto.getHjdzssxqdm() );
        ksywCssbxxbPageResp.setHjdzqhnxxdz( dto.getHjdzqhnxxdz() );
        ksywCssbxxbPageResp.setHjdzsjgsdwdm( dto.getHjdzsjgsdwdm() );
        ksywCssbxxbPageResp.setHjdzsjgsdwmc( dto.getHjdzsjgsdwmc() );
        ksywCssbxxbPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        ksywCssbxxbPageResp.setFqxm( dto.getFqxm() );
        ksywCssbxxbPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        ksywCssbxxbPageResp.setFqzjhm( dto.getFqzjhm() );
        ksywCssbxxbPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        ksywCssbxxbPageResp.setMqxm( dto.getMqxm() );
        ksywCssbxxbPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        ksywCssbxxbPageResp.setMqzjhm( dto.getMqzjhm() );
        ksywCssbxxbPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        ksywCssbxxbPageResp.setJhryxm( dto.getJhryxm() );
        ksywCssbxxbPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        ksywCssbxxbPageResp.setJhryzjhm( dto.getJhryzjhm() );
        ksywCssbxxbPageResp.setJhrywwx( dto.getJhrywwx() );
        ksywCssbxxbPageResp.setJhrywwm( dto.getJhrywwm() );
        ksywCssbxxbPageResp.setJhryjhgxdm( dto.getJhryjhgxdm() );
        ksywCssbxxbPageResp.setJhrylxdh( dto.getJhrylxdh() );
        ksywCssbxxbPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        ksywCssbxxbPageResp.setJhrexm( dto.getJhrexm() );
        ksywCssbxxbPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        ksywCssbxxbPageResp.setJhrezjhm( dto.getJhrezjhm() );
        ksywCssbxxbPageResp.setJhrewwx( dto.getJhrewwx() );
        ksywCssbxxbPageResp.setJhrewwm( dto.getJhrewwm() );
        ksywCssbxxbPageResp.setJhrejhgxdm( dto.getJhrejhgxdm() );
        ksywCssbxxbPageResp.setJhrelxdh( dto.getJhrelxdh() );
        ksywCssbxxbPageResp.setXzzssxqdm( dto.getXzzssxqdm() );
        ksywCssbxxbPageResp.setXzzqhnxxdz( dto.getXzzqhnxxdz() );
        ksywCssbxxbPageResp.setSbrgmsfhm( dto.getSbrgmsfhm() );
        ksywCssbxxbPageResp.setSbrxm( dto.getSbrxm() );
        ksywCssbxxbPageResp.setSbrlxdh( dto.getSbrlxdh() );
        ksywCssbxxbPageResp.setSbrycsrgxjtgxdm( dto.getSbrycsrgxjtgxdm() );
        ksywCssbxxbPageResp.setSjrxm( dto.getSjrxm() );
        ksywCssbxxbPageResp.setSjrgmsfhm( dto.getSjrgmsfhm() );
        ksywCssbxxbPageResp.setSjrlxdh( dto.getSjrlxdh() );
        ksywCssbxxbPageResp.setSjdzssxqdm( dto.getSjdzssxqdm() );
        ksywCssbxxbPageResp.setSjdzqhnxxdz( dto.getSjdzqhnxxdz() );
        ksywCssbxxbPageResp.setSldgajgjgdm( dto.getSldgajgjgdm() );
        ksywCssbxxbPageResp.setSldgajgmc( dto.getSldgajgmc() );
        ksywCssbxxbPageResp.setSldlxdh( dto.getSldlxdh() );
        ksywCssbxxbPageResp.setSlrid( dto.getSlrid() );
        ksywCssbxxbPageResp.setSlrxm( dto.getSlrxm() );
        ksywCssbxxbPageResp.setSlsj( dto.getSlsj() );
        ksywCssbxxbPageResp.setSldwsjgsdwdm( dto.getSldwsjgsdwdm() );
        ksywCssbxxbPageResp.setSldwsjgsdwmc( dto.getSldwsjgsdwmc() );
        ksywCssbxxbPageResp.setRksj( dto.getRksj() );

        return ksywCssbxxbPageResp;
    }

    @Override
    public KsywCssbxxbViewResp convertToViewResp(KsywCssbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywCssbxxbViewResp ksywCssbxxbViewResp = new KsywCssbxxbViewResp();

        ksywCssbxxbViewResp.setKscsywid( dto.getKscsywid() );
        ksywCssbxxbViewResp.setKsywid( dto.getKsywid() );
        ksywCssbxxbViewResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksywCssbxxbViewResp.setGmsfhm( dto.getGmsfhm() );
        ksywCssbxxbViewResp.setXm( dto.getXm() );
        ksywCssbxxbViewResp.setX( dto.getX() );
        ksywCssbxxbViewResp.setM( dto.getM() );
        ksywCssbxxbViewResp.setXbdm( dto.getXbdm() );
        ksywCssbxxbViewResp.setMzdm( dto.getMzdm() );
        ksywCssbxxbViewResp.setCsrq( dto.getCsrq() );
        ksywCssbxxbViewResp.setCssj( dto.getCssj() );
        ksywCssbxxbViewResp.setCsdgjhdqdm( dto.getCsdgjhdqdm() );
        ksywCssbxxbViewResp.setCsdssxqdm( dto.getCsdssxqdm() );
        ksywCssbxxbViewResp.setCsdqhnxxdz( dto.getCsdqhnxxdz() );
        ksywCssbxxbViewResp.setJggjhdqdm( dto.getJggjhdqdm() );
        ksywCssbxxbViewResp.setJgssxqdm( dto.getJgssxqdm() );
        ksywCssbxxbViewResp.setJgqhnxxdz( dto.getJgqhnxxdz() );
        ksywCssbxxbViewResp.setXxdm( dto.getXxdm() );
        ksywCssbxxbViewResp.setRkxxjbdm( dto.getRkxxjbdm() );
        ksywCssbxxbViewResp.setCsdjlbdm( dto.getCsdjlbdm() );
        ksywCssbxxbViewResp.setCszmbh( dto.getCszmbh() );
        ksywCssbxxbViewResp.setYhzgxdm( dto.getYhzgxdm() );
        ksywCssbxxbViewResp.setHzxm( dto.getHzxm() );
        ksywCssbxxbViewResp.setHzgmsfhm( dto.getHzgmsfhm() );
        ksywCssbxxbViewResp.setHjdzssxqdm( dto.getHjdzssxqdm() );
        ksywCssbxxbViewResp.setHjdzqhnxxdz( dto.getHjdzqhnxxdz() );
        ksywCssbxxbViewResp.setHjdzsjgsdwdm( dto.getHjdzsjgsdwdm() );
        ksywCssbxxbViewResp.setHjdzsjgsdwmc( dto.getHjdzsjgsdwmc() );
        ksywCssbxxbViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        ksywCssbxxbViewResp.setFqxm( dto.getFqxm() );
        ksywCssbxxbViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        ksywCssbxxbViewResp.setFqzjhm( dto.getFqzjhm() );
        ksywCssbxxbViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        ksywCssbxxbViewResp.setMqxm( dto.getMqxm() );
        ksywCssbxxbViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        ksywCssbxxbViewResp.setMqzjhm( dto.getMqzjhm() );
        ksywCssbxxbViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        ksywCssbxxbViewResp.setJhryxm( dto.getJhryxm() );
        ksywCssbxxbViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        ksywCssbxxbViewResp.setJhryzjhm( dto.getJhryzjhm() );
        ksywCssbxxbViewResp.setJhrywwx( dto.getJhrywwx() );
        ksywCssbxxbViewResp.setJhrywwm( dto.getJhrywwm() );
        ksywCssbxxbViewResp.setJhryjhgxdm( dto.getJhryjhgxdm() );
        ksywCssbxxbViewResp.setJhrylxdh( dto.getJhrylxdh() );
        ksywCssbxxbViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        ksywCssbxxbViewResp.setJhrexm( dto.getJhrexm() );
        ksywCssbxxbViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        ksywCssbxxbViewResp.setJhrezjhm( dto.getJhrezjhm() );
        ksywCssbxxbViewResp.setJhrewwx( dto.getJhrewwx() );
        ksywCssbxxbViewResp.setJhrewwm( dto.getJhrewwm() );
        ksywCssbxxbViewResp.setJhrejhgxdm( dto.getJhrejhgxdm() );
        ksywCssbxxbViewResp.setJhrelxdh( dto.getJhrelxdh() );
        ksywCssbxxbViewResp.setXzzssxqdm( dto.getXzzssxqdm() );
        ksywCssbxxbViewResp.setXzzqhnxxdz( dto.getXzzqhnxxdz() );
        ksywCssbxxbViewResp.setSbrgmsfhm( dto.getSbrgmsfhm() );
        ksywCssbxxbViewResp.setSbrxm( dto.getSbrxm() );
        ksywCssbxxbViewResp.setSbrlxdh( dto.getSbrlxdh() );
        ksywCssbxxbViewResp.setSbrycsrgxjtgxdm( dto.getSbrycsrgxjtgxdm() );
        ksywCssbxxbViewResp.setSjrxm( dto.getSjrxm() );
        ksywCssbxxbViewResp.setSjrgmsfhm( dto.getSjrgmsfhm() );
        ksywCssbxxbViewResp.setSjrlxdh( dto.getSjrlxdh() );
        ksywCssbxxbViewResp.setSjdzssxqdm( dto.getSjdzssxqdm() );
        ksywCssbxxbViewResp.setSjdzqhnxxdz( dto.getSjdzqhnxxdz() );
        ksywCssbxxbViewResp.setSldgajgjgdm( dto.getSldgajgjgdm() );
        ksywCssbxxbViewResp.setSldgajgmc( dto.getSldgajgmc() );
        ksywCssbxxbViewResp.setSldlxdh( dto.getSldlxdh() );
        ksywCssbxxbViewResp.setSlrid( dto.getSlrid() );
        ksywCssbxxbViewResp.setSlrxm( dto.getSlrxm() );
        ksywCssbxxbViewResp.setSlsj( dto.getSlsj() );
        ksywCssbxxbViewResp.setSldwsjgsdwdm( dto.getSldwsjgsdwdm() );
        ksywCssbxxbViewResp.setSldwsjgsdwmc( dto.getSldwsjgsdwmc() );
        ksywCssbxxbViewResp.setRksj( dto.getRksj() );

        return ksywCssbxxbViewResp;
    }
}
