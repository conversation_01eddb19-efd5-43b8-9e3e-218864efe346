package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtCzzsdwdybDTO;
import com.zjjcnt.project.ck.base.entity.ZjtCzzsdwdybDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:41+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtCzzsdwdybConvertImpl implements ZjtCzzsdwdybConvert {

    @Override
    public ZjtCzzsdwdybDTO convert(ZjtCzzsdwdybDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtCzzsdwdybDTO zjtCzzsdwdybDTO = new ZjtCzzsdwdybDTO();

        zjtCzzsdwdybDTO.setId( entity.getId() );
        zjtCzzsdwdybDTO.setDyid( entity.getDyid() );
        zjtCzzsdwdybDTO.setSldfjsjgsdwdm( entity.getSldfjsjgsdwdm() );
        zjtCzzsdwdybDTO.setSldfjsjgsdwmc( entity.getSldfjsjgsdwmc() );
        zjtCzzsdwdybDTO.setZsdwdm( entity.getZsdwdm() );
        zjtCzzsdwdybDTO.setZsdwmc( entity.getZsdwmc() );
        zjtCzzsdwdybDTO.setXzqhdm( entity.getXzqhdm() );
        zjtCzzsdwdybDTO.setSfywlx( entity.getSfywlx() );
        zjtCzzsdwdybDTO.setZsxmbm( entity.getZsxmbm() );
        zjtCzzsdwdybDTO.setZsxmmc( entity.getZsxmmc() );
        zjtCzzsdwdybDTO.setSfdj( entity.getSfdj() );
        zjtCzzsdwdybDTO.setSfsl( entity.getSfsl() );
        zjtCzzsdwdybDTO.setSfje( entity.getSfje() );
        zjtCzzsdwdybDTO.setBz( entity.getBz() );

        return zjtCzzsdwdybDTO;
    }

    @Override
    public ZjtCzzsdwdybDO convertToDO(ZjtCzzsdwdybDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtCzzsdwdybDO zjtCzzsdwdybDO = new ZjtCzzsdwdybDO();

        zjtCzzsdwdybDO.setId( dto.getId() );
        zjtCzzsdwdybDO.setDyid( dto.getDyid() );
        zjtCzzsdwdybDO.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtCzzsdwdybDO.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtCzzsdwdybDO.setZsdwdm( dto.getZsdwdm() );
        zjtCzzsdwdybDO.setZsdwmc( dto.getZsdwmc() );
        zjtCzzsdwdybDO.setXzqhdm( dto.getXzqhdm() );
        zjtCzzsdwdybDO.setSfywlx( dto.getSfywlx() );
        zjtCzzsdwdybDO.setZsxmbm( dto.getZsxmbm() );
        zjtCzzsdwdybDO.setZsxmmc( dto.getZsxmmc() );
        zjtCzzsdwdybDO.setSfdj( dto.getSfdj() );
        zjtCzzsdwdybDO.setSfsl( dto.getSfsl() );
        zjtCzzsdwdybDO.setSfje( dto.getSfje() );
        zjtCzzsdwdybDO.setBz( dto.getBz() );

        return zjtCzzsdwdybDO;
    }
}
