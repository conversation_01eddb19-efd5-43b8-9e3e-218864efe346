package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RzbSfmdyrzbDTO;
import com.zjjcnt.project.ck.base.entity.RzbSfmdyrzbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:45+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RzbSfmdyrzbConvertImpl implements RzbSfmdyrzbConvert {

    @Override
    public RzbSfmdyrzbDTO convert(RzbSfmdyrzbDO entity) {
        if ( entity == null ) {
            return null;
        }

        RzbSfmdyrzbDTO rzbSfmdyrzbDTO = new RzbSfmdyrzbDTO();

        rzbSfmdyrzbDTO.setId( entity.getId() );
        rzbSfmdyrzbDTO.setRzid( entity.getRzid() );
        rzbSfmdyrzbDTO.setCzsj( entity.getCzsj() );
        rzbSfmdyrzbDTO.setCzrxm( entity.getCzrxm() );
        rzbSfmdyrzbDTO.setCzrip( entity.getCzrip() );
        rzbSfmdyrzbDTO.setRetcode( entity.getRetcode() );
        rzbSfmdyrzbDTO.setMsg( entity.getMsg() );
        rzbSfmdyrzbDTO.setGmsfhm( entity.getGmsfhm() );
        rzbSfmdyrzbDTO.setSfmlb( entity.getSfmlb() );

        return rzbSfmdyrzbDTO;
    }

    @Override
    public RzbSfmdyrzbDO convertToDO(RzbSfmdyrzbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzbSfmdyrzbDO rzbSfmdyrzbDO = new RzbSfmdyrzbDO();

        rzbSfmdyrzbDO.setId( dto.getId() );
        rzbSfmdyrzbDO.setRzid( dto.getRzid() );
        rzbSfmdyrzbDO.setCzsj( dto.getCzsj() );
        rzbSfmdyrzbDO.setCzrxm( dto.getCzrxm() );
        rzbSfmdyrzbDO.setCzrip( dto.getCzrip() );
        rzbSfmdyrzbDO.setRetcode( dto.getRetcode() );
        rzbSfmdyrzbDO.setMsg( dto.getMsg() );
        rzbSfmdyrzbDO.setGmsfhm( dto.getGmsfhm() );
        rzbSfmdyrzbDO.setSfmlb( dto.getSfmlb() );

        return rzbSfmdyrzbDO;
    }
}
