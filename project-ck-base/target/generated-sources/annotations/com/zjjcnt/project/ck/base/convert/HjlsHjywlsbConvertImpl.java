package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.domain.Sbjbxx;
import com.zjjcnt.project.ck.base.dto.HjlsHjywlsbDTO;
import com.zjjcnt.project.ck.base.entity.HjlsHjywlsbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:45+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjlsHjywlsbConvertImpl implements HjlsHjywlsbConvert {

    @Override
    public HjlsHjywlsbDTO convert(HjlsHjywlsbDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjlsHjywlsbDTO hjlsHjywlsbDTO = new HjlsHjywlsbDTO();

        hjlsHjywlsbDTO.setId( entity.getId() );
        hjlsHjywlsbDTO.setHjywid( entity.getHjywid() );
        hjlsHjywlsbDTO.setYwbz( entity.getYwbz() );
        hjlsHjywlsbDTO.setYwlx( entity.getYwlx() );
        hjlsHjywlsbDTO.setCzsm( entity.getCzsm() );
        hjlsHjywlsbDTO.setSbsj( entity.getSbsj() );
        hjlsHjywlsbDTO.setSbryxm( entity.getSbryxm() );
        hjlsHjywlsbDTO.setSbrgmsfhm( entity.getSbrgmsfhm() );
        hjlsHjywlsbDTO.setSlsj( entity.getSlsj() );
        hjlsHjywlsbDTO.setSldw( entity.getSldw() );
        hjlsHjywlsbDTO.setSlrid( entity.getSlrid() );
        hjlsHjywlsbDTO.setCzip( entity.getCzip() );

        return hjlsHjywlsbDTO;
    }

    @Override
    public HjlsHjywlsbDO convertToDO(HjlsHjywlsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjlsHjywlsbDO hjlsHjywlsbDO = new HjlsHjywlsbDO();

        hjlsHjywlsbDO.setId( dto.getId() );
        hjlsHjywlsbDO.setHjywid( dto.getHjywid() );
        hjlsHjywlsbDO.setYwbz( dto.getYwbz() );
        hjlsHjywlsbDO.setYwlx( dto.getYwlx() );
        hjlsHjywlsbDO.setCzsm( dto.getCzsm() );
        hjlsHjywlsbDO.setSbsj( dto.getSbsj() );
        hjlsHjywlsbDO.setSbryxm( dto.getSbryxm() );
        hjlsHjywlsbDO.setSbrgmsfhm( dto.getSbrgmsfhm() );
        hjlsHjywlsbDO.setSlsj( dto.getSlsj() );
        hjlsHjywlsbDO.setSldw( dto.getSldw() );
        hjlsHjywlsbDO.setSlrid( dto.getSlrid() );
        hjlsHjywlsbDO.setCzip( dto.getCzip() );

        return hjlsHjywlsbDO;
    }

    @Override
    public HjlsHjywlsbDTO convertToDTO(Sbjbxx vosbxx) {
        if ( vosbxx == null ) {
            return null;
        }

        HjlsHjywlsbDTO hjlsHjywlsbDTO = new HjlsHjywlsbDTO();

        hjlsHjywlsbDTO.setSbsj( vosbxx.getSbsj() );
        hjlsHjywlsbDTO.setSbryxm( vosbxx.getSbryxm() );
        hjlsHjywlsbDTO.setSbrgmsfhm( vosbxx.getSbrgmsfhm() );

        return hjlsHjywlsbDTO;
    }
}
