package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.GaythtYthSlclDTO;
import com.zjjcnt.project.ck.base.dto.GaythtYthSlclsjDTO;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclCreateReq;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclPageReq;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclItemResp;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclPageResp;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclViewResp;
import com.zjjcnt.project.ck.base.entity.GaythtYthSlclDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:45+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class GaythtYthSlclConvertImpl implements GaythtYthSlclConvert {

    @Override
    public GaythtYthSlclDTO convert(GaythtYthSlclDO entity) {
        if ( entity == null ) {
            return null;
        }

        GaythtYthSlclDTO gaythtYthSlclDTO = new GaythtYthSlclDTO();

        gaythtYthSlclDTO.setId( entity.getId() );
        gaythtYthSlclDTO.setUnid( entity.getUnid() );
        gaythtYthSlclDTO.setProjid( entity.getProjid() );
        gaythtYthSlclDTO.setAttrname( entity.getAttrname() );
        gaythtYthSlclDTO.setSortid( entity.getSortid() );
        gaythtYthSlclDTO.setTaketype( entity.getTaketype() );
        gaythtYthSlclDTO.setIstake( entity.getIstake() );
        gaythtYthSlclDTO.setTaketime( entity.getTaketime() );
        gaythtYthSlclDTO.setMemo( entity.getMemo() );
        gaythtYthSlclDTO.setBelongsystem( entity.getBelongsystem() );
        gaythtYthSlclDTO.setAreacode( entity.getAreacode() );
        gaythtYthSlclDTO.setDataversion( entity.getDataversion() );
        gaythtYthSlclDTO.setSyncStatus( entity.getSyncStatus() );
        gaythtYthSlclDTO.setCreateTime( entity.getCreateTime() );
        gaythtYthSlclDTO.setAttrid( entity.getAttrid() );
        gaythtYthSlclDTO.setCjr( entity.getCjr() );
        gaythtYthSlclDTO.setCjrip( entity.getCjrip() );
        gaythtYthSlclDTO.setCjsj( entity.getCjsj() );
        gaythtYthSlclDTO.setXgr( entity.getXgr() );
        gaythtYthSlclDTO.setXgrip( entity.getXgrip() );
        gaythtYthSlclDTO.setXgsj( entity.getXgsj() );
        gaythtYthSlclDTO.setYxbz( entity.getYxbz() );
        gaythtYthSlclDTO.setExtend( entity.getExtend() );
        gaythtYthSlclDTO.setExtend2( entity.getExtend2() );
        gaythtYthSlclDTO.setExtend3( entity.getExtend3() );
        gaythtYthSlclDTO.setExtend4( entity.getExtend4() );
        gaythtYthSlclDTO.setExtend5( entity.getExtend5() );

        return gaythtYthSlclDTO;
    }

    @Override
    public GaythtYthSlclDO convertToDO(GaythtYthSlclDTO dto) {
        if ( dto == null ) {
            return null;
        }

        GaythtYthSlclDO gaythtYthSlclDO = new GaythtYthSlclDO();

        gaythtYthSlclDO.setId( dto.getId() );
        gaythtYthSlclDO.setUnid( dto.getUnid() );
        gaythtYthSlclDO.setProjid( dto.getProjid() );
        gaythtYthSlclDO.setAttrname( dto.getAttrname() );
        gaythtYthSlclDO.setSortid( dto.getSortid() );
        gaythtYthSlclDO.setTaketype( dto.getTaketype() );
        gaythtYthSlclDO.setIstake( dto.getIstake() );
        gaythtYthSlclDO.setTaketime( dto.getTaketime() );
        gaythtYthSlclDO.setMemo( dto.getMemo() );
        gaythtYthSlclDO.setBelongsystem( dto.getBelongsystem() );
        gaythtYthSlclDO.setAreacode( dto.getAreacode() );
        gaythtYthSlclDO.setDataversion( dto.getDataversion() );
        gaythtYthSlclDO.setSyncStatus( dto.getSyncStatus() );
        gaythtYthSlclDO.setCreateTime( dto.getCreateTime() );
        gaythtYthSlclDO.setAttrid( dto.getAttrid() );
        gaythtYthSlclDO.setCjr( dto.getCjr() );
        gaythtYthSlclDO.setCjrip( dto.getCjrip() );
        gaythtYthSlclDO.setCjsj( dto.getCjsj() );
        gaythtYthSlclDO.setXgr( dto.getXgr() );
        gaythtYthSlclDO.setXgrip( dto.getXgrip() );
        gaythtYthSlclDO.setXgsj( dto.getXgsj() );
        gaythtYthSlclDO.setYxbz( dto.getYxbz() );
        gaythtYthSlclDO.setExtend( dto.getExtend() );
        gaythtYthSlclDO.setExtend2( dto.getExtend2() );
        gaythtYthSlclDO.setExtend3( dto.getExtend3() );
        gaythtYthSlclDO.setExtend4( dto.getExtend4() );
        gaythtYthSlclDO.setExtend5( dto.getExtend5() );

        return gaythtYthSlclDO;
    }

    @Override
    public GaythtYthSlclDTO convertToDTO(GaythtYthSlclPageReq req) {
        if ( req == null ) {
            return null;
        }

        GaythtYthSlclDTO gaythtYthSlclDTO = new GaythtYthSlclDTO();

        gaythtYthSlclDTO.setProjid( req.getProjid() );
        gaythtYthSlclDTO.setAttrname( req.getAttrname() );
        gaythtYthSlclDTO.setSortid( req.getSortid() );
        gaythtYthSlclDTO.setTaketype( req.getTaketype() );
        gaythtYthSlclDTO.setIstake( req.getIstake() );
        gaythtYthSlclDTO.setTaketime( req.getTaketime() );
        gaythtYthSlclDTO.setMemo( req.getMemo() );
        gaythtYthSlclDTO.setBelongsystem( req.getBelongsystem() );
        gaythtYthSlclDTO.setAreacode( req.getAreacode() );
        gaythtYthSlclDTO.setDataversion( req.getDataversion() );
        gaythtYthSlclDTO.setSyncStatus( req.getSyncStatus() );
        gaythtYthSlclDTO.setCreateTime( req.getCreateTime() );
        gaythtYthSlclDTO.setAttrid( req.getAttrid() );
        gaythtYthSlclDTO.setCjr( req.getCjr() );
        gaythtYthSlclDTO.setCjrip( req.getCjrip() );
        gaythtYthSlclDTO.setCjsj( req.getCjsj() );
        gaythtYthSlclDTO.setXgr( req.getXgr() );
        gaythtYthSlclDTO.setXgrip( req.getXgrip() );
        gaythtYthSlclDTO.setXgsj( req.getXgsj() );
        gaythtYthSlclDTO.setYxbz( req.getYxbz() );
        gaythtYthSlclDTO.setExtend( req.getExtend() );
        gaythtYthSlclDTO.setExtend2( req.getExtend2() );
        gaythtYthSlclDTO.setExtend3( req.getExtend3() );
        gaythtYthSlclDTO.setExtend4( req.getExtend4() );
        gaythtYthSlclDTO.setExtend5( req.getExtend5() );
        gaythtYthSlclDTO.setSortidOrder( req.getSortidOrder() );

        return gaythtYthSlclDTO;
    }

    @Override
    public GaythtYthSlclDTO convertToDTO(GaythtYthSlclCreateReq req) {
        if ( req == null ) {
            return null;
        }

        GaythtYthSlclDTO gaythtYthSlclDTO = new GaythtYthSlclDTO();

        gaythtYthSlclDTO.setProjid( req.getProjid() );
        gaythtYthSlclDTO.setAttrname( req.getAttrname() );
        gaythtYthSlclDTO.setSortid( req.getSortid() );
        gaythtYthSlclDTO.setTaketype( req.getTaketype() );
        gaythtYthSlclDTO.setIstake( req.getIstake() );
        gaythtYthSlclDTO.setTaketime( req.getTaketime() );
        gaythtYthSlclDTO.setMemo( req.getMemo() );
        gaythtYthSlclDTO.setBelongsystem( req.getBelongsystem() );
        gaythtYthSlclDTO.setAreacode( req.getAreacode() );
        gaythtYthSlclDTO.setDataversion( req.getDataversion() );
        gaythtYthSlclDTO.setSyncStatus( req.getSyncStatus() );
        gaythtYthSlclDTO.setCreateTime( req.getCreateTime() );
        gaythtYthSlclDTO.setAttrid( req.getAttrid() );
        gaythtYthSlclDTO.setCjr( req.getCjr() );
        gaythtYthSlclDTO.setCjrip( req.getCjrip() );
        gaythtYthSlclDTO.setCjsj( req.getCjsj() );
        gaythtYthSlclDTO.setXgr( req.getXgr() );
        gaythtYthSlclDTO.setXgrip( req.getXgrip() );
        gaythtYthSlclDTO.setXgsj( req.getXgsj() );
        gaythtYthSlclDTO.setYxbz( req.getYxbz() );
        gaythtYthSlclDTO.setExtend( req.getExtend() );
        gaythtYthSlclDTO.setExtend2( req.getExtend2() );
        gaythtYthSlclDTO.setExtend3( req.getExtend3() );
        gaythtYthSlclDTO.setExtend4( req.getExtend4() );
        gaythtYthSlclDTO.setExtend5( req.getExtend5() );

        return gaythtYthSlclDTO;
    }

    @Override
    public GaythtYthSlclDTO convertToDTO(GaythtYthSlclUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        GaythtYthSlclDTO gaythtYthSlclDTO = new GaythtYthSlclDTO();

        gaythtYthSlclDTO.setUnid( req.getUnid() );
        gaythtYthSlclDTO.setProjid( req.getProjid() );
        gaythtYthSlclDTO.setAttrname( req.getAttrname() );
        gaythtYthSlclDTO.setSortid( req.getSortid() );
        gaythtYthSlclDTO.setTaketype( req.getTaketype() );
        gaythtYthSlclDTO.setIstake( req.getIstake() );
        gaythtYthSlclDTO.setTaketime( req.getTaketime() );
        gaythtYthSlclDTO.setMemo( req.getMemo() );
        gaythtYthSlclDTO.setBelongsystem( req.getBelongsystem() );
        gaythtYthSlclDTO.setAreacode( req.getAreacode() );
        gaythtYthSlclDTO.setDataversion( req.getDataversion() );
        gaythtYthSlclDTO.setSyncStatus( req.getSyncStatus() );
        gaythtYthSlclDTO.setCreateTime( req.getCreateTime() );
        gaythtYthSlclDTO.setAttrid( req.getAttrid() );
        gaythtYthSlclDTO.setExtend( req.getExtend() );
        gaythtYthSlclDTO.setExtend2( req.getExtend2() );
        gaythtYthSlclDTO.setExtend3( req.getExtend3() );
        gaythtYthSlclDTO.setExtend4( req.getExtend4() );
        gaythtYthSlclDTO.setExtend5( req.getExtend5() );

        return gaythtYthSlclDTO;
    }

    @Override
    public GaythtYthSlclPageResp convertToPageResp(GaythtYthSlclDTO dto) {
        if ( dto == null ) {
            return null;
        }

        GaythtYthSlclPageResp gaythtYthSlclPageResp = new GaythtYthSlclPageResp();

        gaythtYthSlclPageResp.setUnid( dto.getUnid() );
        gaythtYthSlclPageResp.setProjid( dto.getProjid() );
        gaythtYthSlclPageResp.setAttrname( dto.getAttrname() );
        gaythtYthSlclPageResp.setSortid( dto.getSortid() );
        gaythtYthSlclPageResp.setTaketype( dto.getTaketype() );
        gaythtYthSlclPageResp.setIstake( dto.getIstake() );
        gaythtYthSlclPageResp.setTaketime( dto.getTaketime() );
        gaythtYthSlclPageResp.setMemo( dto.getMemo() );
        gaythtYthSlclPageResp.setBelongsystem( dto.getBelongsystem() );
        gaythtYthSlclPageResp.setAreacode( dto.getAreacode() );
        gaythtYthSlclPageResp.setDataversion( dto.getDataversion() );
        gaythtYthSlclPageResp.setSyncStatus( dto.getSyncStatus() );
        gaythtYthSlclPageResp.setCreateTime( dto.getCreateTime() );
        gaythtYthSlclPageResp.setAttrid( dto.getAttrid() );
        gaythtYthSlclPageResp.setExtend( dto.getExtend() );
        gaythtYthSlclPageResp.setExtend2( dto.getExtend2() );
        gaythtYthSlclPageResp.setExtend3( dto.getExtend3() );
        gaythtYthSlclPageResp.setExtend4( dto.getExtend4() );
        gaythtYthSlclPageResp.setExtend5( dto.getExtend5() );

        return gaythtYthSlclPageResp;
    }

    @Override
    public GaythtYthSlclViewResp convertToViewResp(GaythtYthSlclDTO dto) {
        if ( dto == null ) {
            return null;
        }

        GaythtYthSlclViewResp gaythtYthSlclViewResp = new GaythtYthSlclViewResp();

        gaythtYthSlclViewResp.setUnid( dto.getUnid() );
        gaythtYthSlclViewResp.setProjid( dto.getProjid() );
        gaythtYthSlclViewResp.setAttrname( dto.getAttrname() );
        gaythtYthSlclViewResp.setSortid( dto.getSortid() );
        gaythtYthSlclViewResp.setTaketype( dto.getTaketype() );
        gaythtYthSlclViewResp.setIstake( dto.getIstake() );
        gaythtYthSlclViewResp.setTaketime( dto.getTaketime() );
        gaythtYthSlclViewResp.setMemo( dto.getMemo() );
        gaythtYthSlclViewResp.setBelongsystem( dto.getBelongsystem() );
        gaythtYthSlclViewResp.setAreacode( dto.getAreacode() );
        gaythtYthSlclViewResp.setDataversion( dto.getDataversion() );
        gaythtYthSlclViewResp.setSyncStatus( dto.getSyncStatus() );
        gaythtYthSlclViewResp.setCreateTime( dto.getCreateTime() );
        gaythtYthSlclViewResp.setAttrid( dto.getAttrid() );
        gaythtYthSlclViewResp.setExtend( dto.getExtend() );
        gaythtYthSlclViewResp.setExtend2( dto.getExtend2() );
        gaythtYthSlclViewResp.setExtend3( dto.getExtend3() );
        gaythtYthSlclViewResp.setExtend4( dto.getExtend4() );
        gaythtYthSlclViewResp.setExtend5( dto.getExtend5() );

        return gaythtYthSlclViewResp;
    }

    @Override
    public GaythtYthSlclCreateResp convertToCreateResp(GaythtYthSlclDTO dto) {
        if ( dto == null ) {
            return null;
        }

        GaythtYthSlclCreateResp gaythtYthSlclCreateResp = new GaythtYthSlclCreateResp();

        gaythtYthSlclCreateResp.setUnid( dto.getUnid() );
        gaythtYthSlclCreateResp.setProjid( dto.getProjid() );
        gaythtYthSlclCreateResp.setAttrname( dto.getAttrname() );
        gaythtYthSlclCreateResp.setSortid( dto.getSortid() );
        gaythtYthSlclCreateResp.setTaketype( dto.getTaketype() );
        gaythtYthSlclCreateResp.setIstake( dto.getIstake() );
        gaythtYthSlclCreateResp.setTaketime( dto.getTaketime() );
        gaythtYthSlclCreateResp.setMemo( dto.getMemo() );
        gaythtYthSlclCreateResp.setBelongsystem( dto.getBelongsystem() );
        gaythtYthSlclCreateResp.setAreacode( dto.getAreacode() );
        gaythtYthSlclCreateResp.setDataversion( dto.getDataversion() );
        gaythtYthSlclCreateResp.setSyncStatus( dto.getSyncStatus() );
        gaythtYthSlclCreateResp.setCreateTime( dto.getCreateTime() );
        gaythtYthSlclCreateResp.setAttrid( dto.getAttrid() );
        gaythtYthSlclCreateResp.setExtend( dto.getExtend() );
        gaythtYthSlclCreateResp.setExtend2( dto.getExtend2() );
        gaythtYthSlclCreateResp.setExtend3( dto.getExtend3() );
        gaythtYthSlclCreateResp.setExtend4( dto.getExtend4() );
        gaythtYthSlclCreateResp.setExtend5( dto.getExtend5() );

        return gaythtYthSlclCreateResp;
    }

    @Override
    public List<GaythtYthSlclItemResp> convertToItemResp(List<GaythtYthSlclDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<GaythtYthSlclItemResp> list1 = new ArrayList<GaythtYthSlclItemResp>( list.size() );
        for ( GaythtYthSlclDTO gaythtYthSlclDTO : list ) {
            list1.add( gaythtYthSlclDTOToGaythtYthSlclItemResp( gaythtYthSlclDTO ) );
        }

        return list1;
    }

    protected GaythtYthSlclItemResp gaythtYthSlclDTOToGaythtYthSlclItemResp(GaythtYthSlclDTO gaythtYthSlclDTO) {
        if ( gaythtYthSlclDTO == null ) {
            return null;
        }

        GaythtYthSlclItemResp gaythtYthSlclItemResp = new GaythtYthSlclItemResp();

        gaythtYthSlclItemResp.setUnid( gaythtYthSlclDTO.getUnid() );
        gaythtYthSlclItemResp.setProjid( gaythtYthSlclDTO.getProjid() );
        gaythtYthSlclItemResp.setAttrname( gaythtYthSlclDTO.getAttrname() );
        gaythtYthSlclItemResp.setSortid( gaythtYthSlclDTO.getSortid() );
        gaythtYthSlclItemResp.setTaketype( gaythtYthSlclDTO.getTaketype() );
        gaythtYthSlclItemResp.setIstake( gaythtYthSlclDTO.getIstake() );
        gaythtYthSlclItemResp.setTaketime( gaythtYthSlclDTO.getTaketime() );
        gaythtYthSlclItemResp.setMemo( gaythtYthSlclDTO.getMemo() );
        gaythtYthSlclItemResp.setBelongsystem( gaythtYthSlclDTO.getBelongsystem() );
        gaythtYthSlclItemResp.setAreacode( gaythtYthSlclDTO.getAreacode() );
        gaythtYthSlclItemResp.setDataversion( gaythtYthSlclDTO.getDataversion() );
        gaythtYthSlclItemResp.setSyncStatus( gaythtYthSlclDTO.getSyncStatus() );
        gaythtYthSlclItemResp.setCreateTime( gaythtYthSlclDTO.getCreateTime() );
        gaythtYthSlclItemResp.setAttrid( gaythtYthSlclDTO.getAttrid() );
        gaythtYthSlclItemResp.setExtend( gaythtYthSlclDTO.getExtend() );
        gaythtYthSlclItemResp.setExtend2( gaythtYthSlclDTO.getExtend2() );
        gaythtYthSlclItemResp.setExtend3( gaythtYthSlclDTO.getExtend3() );
        gaythtYthSlclItemResp.setExtend4( gaythtYthSlclDTO.getExtend4() );
        gaythtYthSlclItemResp.setExtend5( gaythtYthSlclDTO.getExtend5() );
        List<GaythtYthSlclsjDTO> list = gaythtYthSlclDTO.getGaythtYthSlclsjList();
        if ( list != null ) {
            gaythtYthSlclItemResp.setGaythtYthSlclsjList( new ArrayList<GaythtYthSlclsjDTO>( list ) );
        }
        gaythtYthSlclItemResp.setSlclsjsl( gaythtYthSlclDTO.getSlclsjsl() );

        return gaythtYthSlclItemResp;
    }
}
