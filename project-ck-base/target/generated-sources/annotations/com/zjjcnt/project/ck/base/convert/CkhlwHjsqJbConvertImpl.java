package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.CkhlwHjsqJbDTO;
import com.zjjcnt.project.ck.base.entity.CkhlwHjsqJbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:46+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class CkhlwHjsqJbConvertImpl implements CkhlwHjsqJbConvert {

    @Override
    public CkhlwHjsqJbDTO convert(CkhlwHjsqJbDO entity) {
        if ( entity == null ) {
            return null;
        }

        CkhlwHjsqJbDTO ckhlwHjsqJbDTO = new CkhlwHjsqJbDTO();

        ckhlwHjsqJbDTO.setId( entity.getId() );
        ckhlwHjsqJbDTO.setHlwsqid( entity.getHlwsqid() );
        ckhlwHjsqJbDTO.setSqlx( entity.getSqlx() );
        ckhlwHjsqJbDTO.setYwbm( entity.getYwbm() );
        ckhlwHjsqJbDTO.setSjly( entity.getSjly() );
        ckhlwHjsqJbDTO.setProjectid( entity.getProjectid() );
        ckhlwHjsqJbDTO.setPcs( entity.getPcs() );
        ckhlwHjsqJbDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        ckhlwHjsqJbDTO.setSqrxm( entity.getSqrxm() );
        ckhlwHjsqJbDTO.setSqrlxdh( entity.getSqrlxdh() );
        ckhlwHjsqJbDTO.setSqrdz( entity.getSqrdz() );
        ckhlwHjsqJbDTO.setBlfs( entity.getBlfs() );
        ckhlwHjsqJbDTO.setSjrxm( entity.getSjrxm() );
        ckhlwHjsqJbDTO.setSjrlxdh( entity.getSjrlxdh() );
        ckhlwHjsqJbDTO.setSjssxq( entity.getSjssxq() );
        ckhlwHjsqJbDTO.setSjdz( entity.getSjdz() );
        ckhlwHjsqJbDTO.setJjlxrxm( entity.getJjlxrxm() );
        ckhlwHjsqJbDTO.setJjlxrgmsfhm( entity.getJjlxrgmsfhm() );
        ckhlwHjsqJbDTO.setJjlxrlxdh( entity.getJjlxrlxdh() );
        ckhlwHjsqJbDTO.setSqrsl( entity.getSqrsl() );
        ckhlwHjsqJbDTO.setSqzt( entity.getSqzt() );
        ckhlwHjsqJbDTO.setShbz( entity.getShbz() );
        ckhlwHjsqJbDTO.setShjg( entity.getShjg() );
        ckhlwHjsqJbDTO.setShrid( entity.getShrid() );
        ckhlwHjsqJbDTO.setShrxm( entity.getShrxm() );
        ckhlwHjsqJbDTO.setShrip( entity.getShrip() );
        ckhlwHjsqJbDTO.setShsj( entity.getShsj() );
        ckhlwHjsqJbDTO.setYxbz( entity.getYxbz() );
        ckhlwHjsqJbDTO.setCjr( entity.getCjr() );
        ckhlwHjsqJbDTO.setCjrip( entity.getCjrip() );
        ckhlwHjsqJbDTO.setCjsj( entity.getCjsj() );
        ckhlwHjsqJbDTO.setXgr( entity.getXgr() );
        ckhlwHjsqJbDTO.setXgrip( entity.getXgrip() );
        ckhlwHjsqJbDTO.setXgsj( entity.getXgsj() );
        ckhlwHjsqJbDTO.setYwtable( entity.getYwtable() );
        ckhlwHjsqJbDTO.setBustype( entity.getBustype() );
        ckhlwHjsqJbDTO.setRelbusid( entity.getRelbusid() );
        ckhlwHjsqJbDTO.setApplyfrom( entity.getApplyfrom() );
        ckhlwHjsqJbDTO.setQrjg( entity.getQrjg() );
        ckhlwHjsqJbDTO.setQrms( entity.getQrms() );
        ckhlwHjsqJbDTO.setSpywslh( entity.getSpywslh() );
        ckhlwHjsqJbDTO.setUsername( entity.getUsername() );
        ckhlwHjsqJbDTO.setYyblsj( entity.getYyblsj() );
        ckhlwHjsqJbDTO.setHallid( entity.getHallid() );
        ckhlwHjsqJbDTO.setHallname( entity.getHallname() );
        ckhlwHjsqJbDTO.setJkdh( entity.getJkdh() );
        ckhlwHjsqJbDTO.setTbcxbz( entity.getTbcxbz() );
        ckhlwHjsqJbDTO.setHalluserid( entity.getHalluserid() );
        ckhlwHjsqJbDTO.setHallusername( entity.getHallusername() );
        ckhlwHjsqJbDTO.setSqsj( entity.getSqsj() );
        ckhlwHjsqJbDTO.setSlsj( entity.getSlsj() );
        ckhlwHjsqJbDTO.setTbcxkssj( entity.getTbcxkssj() );
        ckhlwHjsqJbDTO.setTbcxjssj( entity.getTbcxjssj() );
        ckhlwHjsqJbDTO.setBjsj( entity.getBjsj() );

        return ckhlwHjsqJbDTO;
    }

    @Override
    public CkhlwHjsqJbDO convertToDO(CkhlwHjsqJbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CkhlwHjsqJbDO ckhlwHjsqJbDO = new CkhlwHjsqJbDO();

        ckhlwHjsqJbDO.setId( dto.getId() );
        ckhlwHjsqJbDO.setHlwsqid( dto.getHlwsqid() );
        ckhlwHjsqJbDO.setSqlx( dto.getSqlx() );
        ckhlwHjsqJbDO.setYwbm( dto.getYwbm() );
        ckhlwHjsqJbDO.setSjly( dto.getSjly() );
        ckhlwHjsqJbDO.setProjectid( dto.getProjectid() );
        ckhlwHjsqJbDO.setPcs( dto.getPcs() );
        ckhlwHjsqJbDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        ckhlwHjsqJbDO.setSqrxm( dto.getSqrxm() );
        ckhlwHjsqJbDO.setSqrlxdh( dto.getSqrlxdh() );
        ckhlwHjsqJbDO.setSqrdz( dto.getSqrdz() );
        ckhlwHjsqJbDO.setBlfs( dto.getBlfs() );
        ckhlwHjsqJbDO.setSjrxm( dto.getSjrxm() );
        ckhlwHjsqJbDO.setSjrlxdh( dto.getSjrlxdh() );
        ckhlwHjsqJbDO.setSjssxq( dto.getSjssxq() );
        ckhlwHjsqJbDO.setSjdz( dto.getSjdz() );
        ckhlwHjsqJbDO.setJjlxrxm( dto.getJjlxrxm() );
        ckhlwHjsqJbDO.setJjlxrgmsfhm( dto.getJjlxrgmsfhm() );
        ckhlwHjsqJbDO.setJjlxrlxdh( dto.getJjlxrlxdh() );
        ckhlwHjsqJbDO.setSqrsl( dto.getSqrsl() );
        ckhlwHjsqJbDO.setSqzt( dto.getSqzt() );
        ckhlwHjsqJbDO.setShbz( dto.getShbz() );
        ckhlwHjsqJbDO.setShjg( dto.getShjg() );
        ckhlwHjsqJbDO.setShrid( dto.getShrid() );
        ckhlwHjsqJbDO.setShrxm( dto.getShrxm() );
        ckhlwHjsqJbDO.setShrip( dto.getShrip() );
        ckhlwHjsqJbDO.setShsj( dto.getShsj() );
        ckhlwHjsqJbDO.setYxbz( dto.getYxbz() );
        ckhlwHjsqJbDO.setCjr( dto.getCjr() );
        ckhlwHjsqJbDO.setCjrip( dto.getCjrip() );
        ckhlwHjsqJbDO.setCjsj( dto.getCjsj() );
        ckhlwHjsqJbDO.setXgr( dto.getXgr() );
        ckhlwHjsqJbDO.setXgrip( dto.getXgrip() );
        ckhlwHjsqJbDO.setXgsj( dto.getXgsj() );
        ckhlwHjsqJbDO.setYwtable( dto.getYwtable() );
        ckhlwHjsqJbDO.setBustype( dto.getBustype() );
        ckhlwHjsqJbDO.setRelbusid( dto.getRelbusid() );
        ckhlwHjsqJbDO.setApplyfrom( dto.getApplyfrom() );
        ckhlwHjsqJbDO.setQrjg( dto.getQrjg() );
        ckhlwHjsqJbDO.setQrms( dto.getQrms() );
        ckhlwHjsqJbDO.setSpywslh( dto.getSpywslh() );
        ckhlwHjsqJbDO.setUsername( dto.getUsername() );
        ckhlwHjsqJbDO.setYyblsj( dto.getYyblsj() );
        ckhlwHjsqJbDO.setHallid( dto.getHallid() );
        ckhlwHjsqJbDO.setHallname( dto.getHallname() );
        ckhlwHjsqJbDO.setJkdh( dto.getJkdh() );
        ckhlwHjsqJbDO.setTbcxbz( dto.getTbcxbz() );
        ckhlwHjsqJbDO.setHalluserid( dto.getHalluserid() );
        ckhlwHjsqJbDO.setHallusername( dto.getHallusername() );
        ckhlwHjsqJbDO.setSqsj( dto.getSqsj() );
        ckhlwHjsqJbDO.setSlsj( dto.getSlsj() );
        ckhlwHjsqJbDO.setTbcxkssj( dto.getTbcxkssj() );
        ckhlwHjsqJbDO.setTbcxjssj( dto.getTbcxjssj() );
        ckhlwHjsqJbDO.setBjsj( dto.getBjsj() );

        return ckhlwHjsqJbDO;
    }
}
