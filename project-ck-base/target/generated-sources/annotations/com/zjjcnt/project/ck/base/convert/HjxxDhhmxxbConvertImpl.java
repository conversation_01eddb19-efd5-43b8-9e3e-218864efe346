package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxDhhmxxbDTO;
import com.zjjcnt.project.ck.base.dto.HjxxDhhmxxbErrorDTO;
import com.zjjcnt.project.ck.base.entity.HjxxDhhmxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:41+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjxxDhhmxxbConvertImpl implements HjxxDhhmxxbConvert {

    @Override
    public HjxxDhhmxxbDTO convert(HjxxDhhmxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjxxDhhmxxbDTO hjxxDhhmxxbDTO = new HjxxDhhmxxbDTO();

        if ( entity.getId() != null ) {
            hjxxDhhmxxbDTO.setId( entity.getId().longValue() );
        }
        if ( entity.getDhhmid() != null ) {
            hjxxDhhmxxbDTO.setDhhmid( entity.getDhhmid().longValue() );
        }
        hjxxDhhmxxbDTO.setRynbid( entity.getRynbid() );
        hjxxDhhmxxbDTO.setRyid( entity.getRyid() );
        hjxxDhhmxxbDTO.setGmsfhm( entity.getGmsfhm() );
        hjxxDhhmxxbDTO.setXm( entity.getXm() );
        hjxxDhhmxxbDTO.setDhhm( entity.getDhhm() );
        hjxxDhhmxxbDTO.setRksj( entity.getRksj() );
        hjxxDhhmxxbDTO.setSjly( entity.getSjly() );
        hjxxDhhmxxbDTO.setYxbz( entity.getYxbz() );

        return hjxxDhhmxxbDTO;
    }

    @Override
    public HjxxDhhmxxbDO convertToDO(HjxxDhhmxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxDhhmxxbDO hjxxDhhmxxbDO = new HjxxDhhmxxbDO();

        if ( dto.getId() != null ) {
            hjxxDhhmxxbDO.setId( dto.getId().doubleValue() );
        }
        if ( dto.getDhhmid() != null ) {
            hjxxDhhmxxbDO.setDhhmid( dto.getDhhmid().doubleValue() );
        }
        hjxxDhhmxxbDO.setRynbid( dto.getRynbid() );
        hjxxDhhmxxbDO.setRyid( dto.getRyid() );
        hjxxDhhmxxbDO.setGmsfhm( dto.getGmsfhm() );
        hjxxDhhmxxbDO.setXm( dto.getXm() );
        hjxxDhhmxxbDO.setDhhm( dto.getDhhm() );
        hjxxDhhmxxbDO.setRksj( dto.getRksj() );
        hjxxDhhmxxbDO.setSjly( dto.getSjly() );
        hjxxDhhmxxbDO.setYxbz( dto.getYxbz() );

        return hjxxDhhmxxbDO;
    }

    @Override
    public HjxxDhhmxxbErrorDTO convertToErrorDTO(HjxxDhhmxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxDhhmxxbErrorDTO hjxxDhhmxxbErrorDTO = new HjxxDhhmxxbErrorDTO();

        hjxxDhhmxxbErrorDTO.setRynbid( dto.getRynbid() );
        hjxxDhhmxxbErrorDTO.setRyid( dto.getRyid() );
        hjxxDhhmxxbErrorDTO.setGmsfhm( dto.getGmsfhm() );
        hjxxDhhmxxbErrorDTO.setXm( dto.getXm() );
        hjxxDhhmxxbErrorDTO.setDhhm( dto.getDhhm() );
        hjxxDhhmxxbErrorDTO.setRksj( dto.getRksj() );
        hjxxDhhmxxbErrorDTO.setSjly( dto.getSjly() );
        hjxxDhhmxxbErrorDTO.setYxbz( dto.getYxbz() );
        hjxxDhhmxxbErrorDTO.setRksjStart( dto.getRksjStart() );
        hjxxDhhmxxbErrorDTO.setRksjEnd( dto.getRksjEnd() );

        return hjxxDhhmxxbErrorDTO;
    }

    @Override
    public HjxxDhhmxxbDTO copy(HjxxDhhmxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxDhhmxxbDTO hjxxDhhmxxbDTO = new HjxxDhhmxxbDTO();

        hjxxDhhmxxbDTO.setRynbid( dto.getRynbid() );
        hjxxDhhmxxbDTO.setRyid( dto.getRyid() );
        hjxxDhhmxxbDTO.setGmsfhm( dto.getGmsfhm() );
        hjxxDhhmxxbDTO.setXm( dto.getXm() );
        hjxxDhhmxxbDTO.setDhhm( dto.getDhhm() );
        hjxxDhhmxxbDTO.setRksj( dto.getRksj() );
        hjxxDhhmxxbDTO.setSjly( dto.getSjly() );
        hjxxDhhmxxbDTO.setYxbz( dto.getYxbz() );
        hjxxDhhmxxbDTO.setRksjStart( dto.getRksjStart() );
        hjxxDhhmxxbDTO.setRksjEnd( dto.getRksjEnd() );

        return hjxxDhhmxxbDTO;
    }
}
