package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class RktHjslTwzylhDOTableDef extends TableDef {

    /**
     * 退伍转业落户申请审批信息DO

 <AUTHOR>
 @date 2025-07-22 14:33:27
 @see com.zjjcnt.project.ck.base.dto.RktHjslTwzylhDTO
     */
    public static final RktHjslTwzylhDOTableDef RKT_HJSL_TWZYLH_DO = new RktHjslTwzylhDOTableDef();

    /**
     * 名
     */
    public final QueryColumn M = new QueryColumn(this, "m");

    /**
     * 姓
     */
    public final QueryColumn X = new QueryColumn(this, "x");

    /**
     * 备注
     */
    public final QueryColumn BZ = new QueryColumn(this, "bz");

    /**
     * 户别
     */
    public final QueryColumn HB = new QueryColumn(this, "hb");

    /**
     * 户号
     */
    public final QueryColumn HH = new QueryColumn(this, "hh");

    /**
     * 民族
     */
    public final QueryColumn MZ = new QueryColumn(this, "mz");

    /**
     * 身高
     */
    public final QueryColumn SG = new QueryColumn(this, "sg");

    /**
     * 性别
     */
    public final QueryColumn XB = new QueryColumn(this, "xb");

    public final QueryColumn XH = new QueryColumn(this, "xh");

    /**
     * 姓名
     */
    public final QueryColumn XM = new QueryColumn(this, "xm");

    /**
     * 血型
     */
    public final QueryColumn XX = new QueryColumn(this, "xx");

    /**
     * 职业
     */
    public final QueryColumn ZY = new QueryColumn(this, "zy");

    /**
     * 曾用名
     */
    public final QueryColumn CYM = new QueryColumn(this, "cym");

    /**
     * 户类型
     */
    public final QueryColumn HLX = new QueryColumn(this, "hlx");

    public final QueryColumn HMC = new QueryColumn(this, "hmc");

    /**
     * 街路巷
     */
    public final QueryColumn JLX = new QueryColumn(this, "jlx");

    /**
     * 变动范围
     */
    public final QueryColumn BDFW = new QueryColumn(this, "bdfw");

    public final QueryColumn BLZT = new QueryColumn(this, "blzt");

    /**
     * 兵役状况
     */
    public final QueryColumn BYZK = new QueryColumn(this, "byzk");

    /**
     * 出生日期
     */
    public final QueryColumn CSRQ = new QueryColumn(this, "csrq");

    /**
     * 出生时间
     */
    public final QueryColumn CSSJ = new QueryColumn(this, "cssj");

    /**
     * 城乡属性
     */
    public final QueryColumn CXSX = new QueryColumn(this, "cxsx");

    /**
     * 父亲姓名
     */
    public final QueryColumn FQXM = new QueryColumn(this, "fqxm");

    /**
     * 户号ID
     */
    public final QueryColumn HHID = new QueryColumn(this, "hhid");

    public final QueryColumn HKXZ = new QueryColumn(this, "hkxz");

    /**
     * 婚姻状况
     */
    public final QueryColumn HYZK = new QueryColumn(this, "hyzk");

    /**
     * 居（村）委会
     */
    public final QueryColumn JCWH = new QueryColumn(this, "jcwh");

    /**
     * 籍贯详址
     */
    public final QueryColumn JGXZ = new QueryColumn(this, "jgxz");

    public final QueryColumn LCMC = new QueryColumn(this, "lcmc");

    public final QueryColumn LCZT = new QueryColumn(this, "lczt");

    public final QueryColumn LXDH = new QueryColumn(this, "lxdh");

    /**
     * 门（楼）牌号
     */
    public final QueryColumn MLPH = new QueryColumn(this, "mlph");

    /**
     * 门（楼）详址
     */
    public final QueryColumn MLXZ = new QueryColumn(this, "mlxz");

    /**
     * 母亲姓名
     */
    public final QueryColumn MQXM = new QueryColumn(this, "mqxm");

    /**
     * 配偶姓名
     */
    public final QueryColumn POXM = new QueryColumn(this, "poxm");

    /**
     * 签发机关
     */
    public final QueryColumn QFJG = new QueryColumn(this, "qfjg");

    /**
     * 其他住址
     */
    public final QueryColumn QTZZ = new QueryColumn(this, "qtzz");

    /**
     * 人员ID
     */
    public final QueryColumn RYID = new QueryColumn(this, "ryid");

    /**
     * 受理时间
     */
    public final QueryColumn SLSJ = new QueryColumn(this, "slsj");

    public final QueryColumn SPSJ = new QueryColumn(this, "spsj");

    public final QueryColumn SPYJ = new QueryColumn(this, "spyj");

    public final QueryColumn SQRQ = new QueryColumn(this, "sqrq");

    /**
     * 文化程度
     */
    public final QueryColumn WHCD = new QueryColumn(this, "whcd");

    /**
     * 姓名拼音
     */
    public final QueryColumn XMPY = new QueryColumn(this, "xmpy");

    /**
     * 信息级别
     */
    public final QueryColumn XXJB = new QueryColumn(this, "xxjb");

    /**
     * 证件类别
     */
    public final QueryColumn ZJLB = new QueryColumn(this, "zjlb");

    /**
     * 宗教信仰
     */
    public final QueryColumn ZJXY = new QueryColumn(this, "zjxy");

    /**
     * 准迁类型
     */
    public final QueryColumn ZQLX = new QueryColumn(this, "zqlx");

    /**
     * 职业类别
     */
    public final QueryColumn ZYLB = new QueryColumn(this, "zylb");

    /**
     * 出生地详址
     */
    public final QueryColumn CSDXZ = new QueryColumn(this, "csdxz");

    /**
     * 曾用名拼音
     */
    public final QueryColumn CYMPY = new QueryColumn(this, "cympy");

    /**
     * 父亲外文名
     */
    public final QueryColumn FQWWM = new QueryColumn(this, "fqwwm");

    /**
     * 父亲外文姓
     */
    public final QueryColumn FQWWX = new QueryColumn(this, "fqwwx");

    /**
     * 母亲外文名
     */
    public final QueryColumn MQWWM = new QueryColumn(this, "mqwwm");

    /**
     * 母亲外文姓
     */
    public final QueryColumn MQWWX = new QueryColumn(this, "mqwwx");

    /**
     * 配偶外文名
     */
    public final QueryColumn POWWM = new QueryColumn(this, "powwm");

    /**
     * 配偶外文姓
     */
    public final QueryColumn POWWX = new QueryColumn(this, "powwx");

    /**
     * 迁入前户别
     */
    public final QueryColumn QRQHB = new QueryColumn(this, "qrqhb");

    /**
     * 是否主迁人
     */
    public final QueryColumn SFZQR = new QueryColumn(this, "sfzqr");

    /**
     * 受理人姓名
     */
    public final QueryColumn SLRXM = new QueryColumn(this, "slrxm");

    public final QueryColumn SPRXM = new QueryColumn(this, "sprxm");

    public final QueryColumn SQRXB = new QueryColumn(this, "sqrxb");

    /**
     * 申请人_姓名
     */
    public final QueryColumn SQRXM = new QueryColumn(this, "sqrxm");

    /**
     * 与户主关系
     */
    public final QueryColumn YHZGX = new QueryColumn(this, "yhzgx");

    public final QueryColumn YWSLH = new QueryColumn(this, "ywslh");

    /**
     * 准迁证编号
     */
    public final QueryColumn ZQZBH = new QueryColumn(this, "zqzbh");

    /**
     * 城乡转换原因
     */
    public final QueryColumn CXZHYY = new QueryColumn(this, "cxzhyy");

    /**
     * 父亲证件号码
     */
    public final QueryColumn FQZJHM = new QueryColumn(this, "fqzjhm");

    /**
     * 公民身份号码
     */
    public final QueryColumn GMSFHM = new QueryColumn(this, "gmsfhm");

    /**
     * 户号内部ID
     */
    public final QueryColumn HHNBID = new QueryColumn(this, "hhnbid");

    /**
     * 籍贯国家（地区）
     */
    public final QueryColumn JGGJDQ = new QueryColumn(this, "jggjdq");

    /**
     * 籍贯省市县（区）
     */
    public final QueryColumn JGSSXQ = new QueryColumn(this, "jgssxq");

    /**
     * 监护人二姓名
     */
    public final QueryColumn JHREXM = new QueryColumn(this, "jhrexm");

    /**
     * 监护人一姓名
     */
    public final QueryColumn JHRYXM = new QueryColumn(this, "jhryxm");

    /**
     * 流程定义ID，格式为 流程KEY：版本号：部署号
     */
    public final QueryColumn LCDYID = new QueryColumn(this, "lcdyid");

    public final QueryColumn LCRWJD = new QueryColumn(this, "lcrwjd");

    public final QueryColumn LCSLID = new QueryColumn(this, "lcslid");

    /**
     * 关于**等几人落户的申请报告
     */
    public final QueryColumn LCYWBT = new QueryColumn(this, "lcywbt");

    /**
     * 金铖流程业务类型，扩展自D_ZAGLYWFL治安管理业务分类与代码
            0100	实有人口管理	
            0101	　户籍管理	
            0102	　居民身份证管理
     */
    public final QueryColumn LCYWLX = new QueryColumn(this, "lcywlx");

    /**
     * 母亲证件号码
     */
    public final QueryColumn MQZJHM = new QueryColumn(this, "mqzjhm");

    /**
     * 配偶证件号码
     */
    public final QueryColumn POZJHM = new QueryColumn(this, "pozjhm");

    /**
     * 迁入前户类型
     */
    public final QueryColumn QRQHLX = new QueryColumn(this, "qrqhlx");

    /**
     * 其他省市县（区）
     */
    public final QueryColumn QTSSXQ = new QueryColumn(this, "qtssxq");

    /**
     * 迁移（流动）原因
     */
    public final QueryColumn QYLDYY = new QueryColumn(this, "qyldyy");

    /**
     * 人员内部ID
     */
    public final QueryColumn RYNBID = new QueryColumn(this, "rynbid");

    public final QueryColumn SPJGDM = new QueryColumn(this, "spjgdm");

    /**
     * 随迁变动范围
     */
    public final QueryColumn SQBDFW = new QueryColumn(this, "sqbdfw");

    /**
     * 出生地国家（地区）
     */
    public final QueryColumn CSDGJDQ = new QueryColumn(this, "csdgjdq");

    /**
     * 出生地省市县（区）
     */
    public final QueryColumn CSDSSXQ = new QueryColumn(this, "csdssxq");

    /**
     * 户籍地地址编码
     */
    public final QueryColumn HJDDZBM = new QueryColumn(this, "hjddzbm");

    /**
     * 户籍地省市县区
     */
    public final QueryColumn HJDSSXQ = new QueryColumn(this, "hjdssxq");

    /**
     * 户籍地详细地址
     */
    public final QueryColumn HJDXXDZ = new QueryColumn(this, "hjdxxdz");

    /**
     * 监护人二外文名
     */
    public final QueryColumn JHREWWM = new QueryColumn(this, "jhrewwm");

    /**
     * 监护人二外文姓
     */
    public final QueryColumn JHREWWX = new QueryColumn(this, "jhrewwx");

    /**
     * 监护人一外文名
     */
    public final QueryColumn JHRYWWM = new QueryColumn(this, "jhrywwm");

    /**
     * 监护人一外文姓
     */
    public final QueryColumn JHRYWWX = new QueryColumn(this, "jhrywwx");

    /**
     * 迁入前城乡属性
     */
    public final QueryColumn QRQCXSX = new QueryColumn(this, "qrqcxsx");

    /**
     * 迁入前户号编号
     */
    public final QueryColumn QRQHHID = new QueryColumn(this, "qrqhhid");

    public final QueryColumn SQRLXDH = new QueryColumn(this, "sqrlxdh");

    /**
     * 申请人住址详址
     */
    public final QueryColumn SQRZZXZ = new QueryColumn(this, "sqrzzxz");

    /**
     * 原户籍地街路巷
     */
    public final QueryColumn YHJDJLX = new QueryColumn(this, "yhjdjlx");

    /**
     * 原户籍地派出所
     */
    public final QueryColumn YHJDPCS = new QueryColumn(this, "yhjdpcs");

    /**
     * 办理户籍业务id
     */
    public final QueryColumn BLHJYWID = new QueryColumn(this, "blhjywid");

    /**
     * 从业状况单位代码
     */
    public final QueryColumn CYZKDWBM = new QueryColumn(this, "cyzkdwbm");

    /**
     * 从业状况单位名称
     */
    public final QueryColumn CYZKDWMC = new QueryColumn(this, "cyzkdwmc");

    /**
     * 父亲证件类别
     */
    public final QueryColumn FQCYZJDM = new QueryColumn(this, "fqcyzjdm");

    /**
     * 父亲公民身份号码
     */
    public final QueryColumn FQGMSFHM = new QueryColumn(this, "fqgmsfhm");

    /**
     * 户籍业务办理时间
     */
    public final QueryColumn HJYWBLSJ = new QueryColumn(this, "hjywblsj");

    /**
     * 监护人二监护关系
     */
    public final QueryColumn JHREJHGX = new QueryColumn(this, "jhrejhgx");

    /**
     * 监护人二联系电话
     */
    public final QueryColumn JHRELXDH = new QueryColumn(this, "jhrelxdh");

    /**
     * 监护人二证件号码
     */
    public final QueryColumn JHREZJHM = new QueryColumn(this, "jhrezjhm");

    /**
     * 监护人一监护关系
     */
    public final QueryColumn JHRYJHGX = new QueryColumn(this, "jhryjhgx");

    /**
     * 监护人一联系电话
     */
    public final QueryColumn JHRYLXDH = new QueryColumn(this, "jhrylxdh");

    /**
     * 监护人一证件号码
     */
    public final QueryColumn JHRYZJHM = new QueryColumn(this, "jhryzjhm");

    /**
     * 母亲证件类别
     */
    public final QueryColumn MQCYZJDM = new QueryColumn(this, "mqcyzjdm");

    /**
     * 母亲公民身份号码
     */
    public final QueryColumn MQGMSFHM = new QueryColumn(this, "mqgmsfhm");

    /**
     * 举家迁徙的农业转移人口落户城镇_判断标识
     */
    public final QueryColumn PDBZJJQX = new QueryColumn(this, "pdbzjjqx");

    /**
     * 具有技能等级的落户城镇_判断标识
     */
    public final QueryColumn PDBZJNDJ = new QueryColumn(this, "pdbzjndj");

    /**
     * 配偶证件类别
     */
    public final QueryColumn POCYZJDM = new QueryColumn(this, "pocyzjdm");

    /**
     * 配偶公民身份号码
     */
    public final QueryColumn POGMSFHM = new QueryColumn(this, "pogmsfhm");

    /**
     * 迁移原因补充说明
     */
    public final QueryColumn QYYYBCSM = new QueryColumn(this, "qyyybcsm");

    /**
     * 迁移原因细分类别
     */
    public final QueryColumn QYYYXFLB = new QueryColumn(this, "qyyyxflb");

    /**
     * 数据归属单位代码
     */
    public final QueryColumn SJGSDWDM = new QueryColumn(this, "sjgsdwdm");

    /**
     * 数据归属单位名称
     */
    public final QueryColumn SJGSDWMC = new QueryColumn(this, "sjgsdwmc");

    public final QueryColumn TWZYLHLB = new QueryColumn(this, "twzylhlb");

    /**
     * 户籍地地址编码
     */
    public final QueryColumn YHJDDZBM = new QueryColumn(this, "yhjddzbm");

    /**
     * 原户籍地居村委会
     */
    public final QueryColumn YHJDJCWH = new QueryColumn(this, "yhjdjcwh");

    /**
     * 户籍地省市县区
     */
    public final QueryColumn YHJDSSXQ = new QueryColumn(this, "yhjdssxq");

    /**
     * 户籍地详细地址
     */
    public final QueryColumn YHJDXXDZ = new QueryColumn(this, "yhjdxxdz");

    /**
     * 原户籍地乡镇街道
     */
    public final QueryColumn YHJDXZJD = new QueryColumn(this, "yhjdxzjd");

    public final QueryColumn YWSLNBBH = new QueryColumn(this, "ywslnbbh");

    /**
     * 有效期限截止日期
     */
    public final QueryColumn YXQXJZRQ = new QueryColumn(this, "yxqxjzrq");

    /**
     * 有效期限起始日期
     */
    public final QueryColumn YXQXQSRQ = new QueryColumn(this, "yxqxqsrq");

    /**
     * 户籍地人户一致标识
     */
    public final QueryColumn HJDRHYZBS = new QueryColumn(this, "hjdrhyzbs");

    /**
     * 迁入前户号内部编号
     */
    public final QueryColumn QRQHHNBID = new QueryColumn(this, "qrqhhnbid");

    /**
     * 迁移人与申请人关系
     */
    public final QueryColumn QYRYSQRGX = new QueryColumn(this, "qyrysqrgx");

    /**
     * 申请人_公民身份号码
     */
    public final QueryColumn SQRGMSFHM = new QueryColumn(this, "sqrgmsfhm");

    /**
     * 申请人户口登记机关
     */
    public final QueryColumn SQRHKDJJG = new QueryColumn(this, "sqrhkdjjg");

    public final QueryColumn SQRYBDRGX = new QueryColumn(this, "sqrybdrgx");

    /**
     * 申请人住址省市县（区）
     */
    public final QueryColumn SQRZZSSXQ = new QueryColumn(this, "sqrzzssxq");

    /**
     * 原户籍地警务责任区
     */
    public final QueryColumn YHJDJWZRQ = new QueryColumn(this, "yhjdjwzrq");

    /**
     * 监护人二证件类别
     */
    public final QueryColumn JHRECYZJDM = new QueryColumn(this, "jhrecyzjdm");

    /**
     * 监护人二公民身份号码
     */
    public final QueryColumn JHREGMSFHM = new QueryColumn(this, "jhregmsfhm");

    /**
     * 监护人一证件类别
     */
    public final QueryColumn JHRYCYZJDM = new QueryColumn(this, "jhrycyzjdm");

    /**
     * 监护人一公民身份号码
     */
    public final QueryColumn JHRYGMSFHM = new QueryColumn(this, "jhrygmsfhm");

    /**
     * 具有专业技术职称的落户城镇_判断标识
     */
    public final QueryColumn PDBZZYJSZC = new QueryColumn(this, "pdbzzyjszc");

    /**
     * 受理单位_公安机关名称
     */
    public final QueryColumn SLDWGAJGMC = new QueryColumn(this, "sldwgajgmc");

    public final QueryColumn SPDWGAJGMC = new QueryColumn(this, "spdwgajgmc");

    public final QueryColumn TWZYLHLBDM = new QueryColumn(this, "twzylhlbdm");

    /**
     * 受理单位_公安机关机构代码
     */
    public final QueryColumn SLDWGAJGJGDM = new QueryColumn(this, "sldwgajgjgdm");

    public final QueryColumn SPDWGAJGJGDM = new QueryColumn(this, "spdwgajgjgdm");

    /**
     * 原户籍所在地登记机关代码
     */
    public final QueryColumn YHJSZDDJJGDM = new QueryColumn(this, "yhjszddjjgdm");

    /**
     * 原户籍所在地登记机关名称
     */
    public final QueryColumn YHJSZDDJJGMC = new QueryColumn(this, "yhjszddjjgmc");

    public final QueryColumn PDBZNCJSBTCXY = new QueryColumn(this, "pdbzncjsbtcxy");

    public final QueryColumn CJFBYSZDSSXQDM = new QueryColumn(this, "cjfbyszdssxqdm");

    public final QueryColumn CJQHKSZDSSXQDM = new QueryColumn(this, "cjqhkszdssxqdm");

    public final QueryColumn CJFBYSZDQHNXXDZ = new QueryColumn(this, "cjfbyszdqhnxxdz");

    public final QueryColumn CJQHKSZDQHNXXDZ = new QueryColumn(this, "cjqhkszdqhnxxdz");

    /**
     * 农村籍大中专院校毕业生落户城镇_判断标识
     */
    public final QueryColumn PDBZNCJDZZYXBYS = new QueryColumn(this, "pdbzncjdzzyxbys");

    /**
     * 在城镇就业和居住五年以上的农业转移人口落户城镇_判断标识
     */
    public final QueryColumn PDBZZCZJYHJZWNYS = new QueryColumn(this, "pdbzzczjyhjzwnys");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{M, X, BZ, HB, HH, MZ, SG, XB, XH, XM, XX, ZY, CYM, HLX, HMC, JLX, BDFW, BLZT, BYZK, CSRQ, CSSJ, CXSX, FQXM, HHID, HKXZ, HYZK, JCWH, JGXZ, LCMC, LCZT, LXDH, MLPH, MLXZ, MQXM, POXM, QFJG, QTZZ, RYID, SLSJ, SPSJ, SPYJ, SQRQ, WHCD, XMPY, XXJB, ZJLB, ZJXY, ZQLX, ZYLB, CSDXZ, CYMPY, FQWWM, FQWWX, MQWWM, MQWWX, POWWM, POWWX, QRQHB, SFZQR, SLRXM, SPRXM, SQRXB, SQRXM, YHZGX, YWSLH, ZQZBH, CXZHYY, FQZJHM, GMSFHM, HHNBID, JGGJDQ, JGSSXQ, JHREXM, JHRYXM, LCDYID, LCRWJD, LCSLID, LCYWBT, LCYWLX, MQZJHM, POZJHM, QRQHLX, QTSSXQ, QYLDYY, RYNBID, SPJGDM, SQBDFW, CSDGJDQ, CSDSSXQ, HJDDZBM, HJDSSXQ, HJDXXDZ, JHREWWM, JHREWWX, JHRYWWM, JHRYWWX, QRQCXSX, QRQHHID, SQRLXDH, SQRZZXZ, YHJDJLX, YHJDPCS, BLHJYWID, CYZKDWBM, CYZKDWMC, FQCYZJDM, FQGMSFHM, HJYWBLSJ, JHREJHGX, JHRELXDH, JHREZJHM, JHRYJHGX, JHRYLXDH, JHRYZJHM, MQCYZJDM, MQGMSFHM, PDBZJJQX, PDBZJNDJ, POCYZJDM, POGMSFHM, QYYYBCSM, QYYYXFLB, SJGSDWDM, SJGSDWMC, TWZYLHLB, YHJDDZBM, YHJDJCWH, YHJDSSXQ, YHJDXXDZ, YHJDXZJD, YWSLNBBH, YXQXJZRQ, YXQXQSRQ, HJDRHYZBS, QRQHHNBID, QYRYSQRGX, SQRGMSFHM, SQRHKDJJG, SQRYBDRGX, SQRZZSSXQ, YHJDJWZRQ, JHRECYZJDM, JHREGMSFHM, JHRYCYZJDM, JHRYGMSFHM, PDBZZYJSZC, SLDWGAJGMC, SPDWGAJGMC, TWZYLHLBDM, SLDWGAJGJGDM, SPDWGAJGJGDM, YHJSZDDJJGDM, YHJSZDDJJGMC, PDBZNCJSBTCXY, CJFBYSZDSSXQDM, CJQHKSZDSSXQDM, CJFBYSZDQHNXXDZ, CJQHKSZDQHNXXDZ, PDBZNCJDZZYXBYS, PDBZZCZJYHJZWNYS};

    public RktHjslTwzylhDOTableDef() {
        super("", "rkt_hjsl_twzylh");
    }

    private RktHjslTwzylhDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public RktHjslTwzylhDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new RktHjslTwzylhDOTableDef("", "rkt_hjsl_twzylh", alias));
    }

}
