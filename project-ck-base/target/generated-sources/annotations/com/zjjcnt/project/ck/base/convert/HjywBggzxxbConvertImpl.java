package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.base.dto.HjywBggzxxbDTO;
import com.zjjcnt.project.ck.base.entity.HjywBggzxxbDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:41+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjywBggzxxbConvertImpl implements HjywBggzxxbConvert {

    @Override
    public HjywBggzxxbDTO convert(HjywBggzxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjywBggzxxbDTO hjywBggzxxbDTO = new HjywBggzxxbDTO();

        hjywBggzxxbDTO.setId( entity.getId() );
        hjywBggzxxbDTO.setBggzid( entity.getBggzid() );
        hjywBggzxxbDTO.setRynbid( entity.getRynbid() );
        hjywBggzxxbDTO.setBggzxm( entity.getBggzxm() );
        hjywBggzxxbDTO.setBggzlb( entity.getBggzlb() );
        hjywBggzxxbDTO.setBggzrq( entity.getBggzrq() );
        hjywBggzxxbDTO.setBggzqnr( entity.getBggzqnr() );
        hjywBggzxxbDTO.setBggzhnr( entity.getBggzhnr() );
        hjywBggzxxbDTO.setDybz( entity.getDybz() );
        hjywBggzxxbDTO.setHjywid( entity.getHjywid() );
        hjywBggzxxbDTO.setTbbz( entity.getTbbz() );
        hjywBggzxxbDTO.setBwbh( entity.getBwbh() );
        hjywBggzxxbDTO.setSbryxm( entity.getSbryxm() );
        hjywBggzxxbDTO.setSbrgmsfhm( entity.getSbrgmsfhm() );
        hjywBggzxxbDTO.setSlsj( entity.getSlsj() );
        hjywBggzxxbDTO.setSldw( entity.getSldw() );
        hjywBggzxxbDTO.setSlrid( entity.getSlrid() );
        hjywBggzxxbDTO.setRyid( entity.getRyid() );
        hjywBggzxxbDTO.setSbsj( entity.getSbsj() );
        hjywBggzxxbDTO.setBgqxm( entity.getBgqxm() );
        hjywBggzxxbDTO.setBgqgmsfhm( entity.getBgqgmsfhm() );
        hjywBggzxxbDTO.setGmsfhm( entity.getGmsfhm() );
        hjywBggzxxbDTO.setXm( entity.getXm() );
        hjywBggzxxbDTO.setXb( entity.getXb() );
        hjywBggzxxbDTO.setMz( entity.getMz() );
        hjywBggzxxbDTO.setCsrq( entity.getCsrq() );
        hjywBggzxxbDTO.setCsdssxq( entity.getCsdssxq() );
        hjywBggzxxbDTO.setMlpnbid( entity.getMlpnbid() );
        hjywBggzxxbDTO.setSsxq( entity.getSsxq() );
        hjywBggzxxbDTO.setJlx( entity.getJlx() );
        hjywBggzxxbDTO.setMlph( entity.getMlph() );
        hjywBggzxxbDTO.setMlxz( entity.getMlxz() );
        hjywBggzxxbDTO.setPcs( entity.getPcs() );
        hjywBggzxxbDTO.setZrq( entity.getZrq() );
        hjywBggzxxbDTO.setXzjd( entity.getXzjd() );
        hjywBggzxxbDTO.setJcwh( entity.getJcwh() );
        hjywBggzxxbDTO.setPxh( entity.getPxh() );
        hjywBggzxxbDTO.setCssj( entity.getCssj() );
        hjywBggzxxbDTO.setHhnbid( entity.getHhnbid() );
        hjywBggzxxbDTO.setRyzt( entity.getRyzt() );
        hjywBggzxxbDTO.setBggzqdm( entity.getBggzqdm() );
        hjywBggzxxbDTO.setBggzhdm( entity.getBggzhdm() );
        hjywBggzxxbDTO.setZaglxxlbdm( entity.getZaglxxlbdm() );
        hjywBggzxxbDTO.setXxlbmc( entity.getXxlbmc() );
        hjywBggzxxbDTO.setBggzsjxbsf( entity.getBggzsjxbsf() );
        hjywBggzxxbDTO.setBggzsjxmc( entity.getBggzsjxmc() );
        hjywBggzxxbDTO.setSbrybdrgx( entity.getSbrybdrgx() );
        hjywBggzxxbDTO.setSldwmc( entity.getSldwmc() );
        hjywBggzxxbDTO.setSlrxm( entity.getSlrxm() );
        hjywBggzxxbDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        hjywBggzxxbDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        hjywBggzxxbDTO.setHjddzbm( entity.getHjddzbm() );
        hjywBggzxxbDTO.setHjdssxq( entity.getHjdssxq() );
        hjywBggzxxbDTO.setHjdxxdz( entity.getHjdxxdz() );
        hjywBggzxxbDTO.setSbrlxdh( entity.getSbrlxdh() );
        hjywBggzxxbDTO.setSpywslh( entity.getSpywslh() );
        hjywBggzxxbDTO.setSpxxlylb( entity.getSpxxlylb() );
        hjywBggzxxbDTO.setSpsbdwdm( entity.getSpsbdwdm() );
        hjywBggzxxbDTO.setSpsbsj( entity.getSpsbsj() );

        return hjywBggzxxbDTO;
    }

    @Override
    public HjywBggzxxbDO convertToDO(HjywBggzxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjywBggzxxbDO hjywBggzxxbDO = new HjywBggzxxbDO();

        hjywBggzxxbDO.setId( dto.getId() );
        hjywBggzxxbDO.setBggzid( dto.getBggzid() );
        hjywBggzxxbDO.setRynbid( dto.getRynbid() );
        hjywBggzxxbDO.setBggzxm( dto.getBggzxm() );
        hjywBggzxxbDO.setBggzlb( dto.getBggzlb() );
        hjywBggzxxbDO.setBggzrq( dto.getBggzrq() );
        hjywBggzxxbDO.setBggzqnr( dto.getBggzqnr() );
        hjywBggzxxbDO.setBggzhnr( dto.getBggzhnr() );
        hjywBggzxxbDO.setDybz( dto.getDybz() );
        hjywBggzxxbDO.setHjywid( dto.getHjywid() );
        hjywBggzxxbDO.setTbbz( dto.getTbbz() );
        hjywBggzxxbDO.setBwbh( dto.getBwbh() );
        hjywBggzxxbDO.setSbryxm( dto.getSbryxm() );
        hjywBggzxxbDO.setSbrgmsfhm( dto.getSbrgmsfhm() );
        hjywBggzxxbDO.setSlsj( dto.getSlsj() );
        hjywBggzxxbDO.setSldw( dto.getSldw() );
        hjywBggzxxbDO.setSlrid( dto.getSlrid() );
        hjywBggzxxbDO.setRyid( dto.getRyid() );
        hjywBggzxxbDO.setSbsj( dto.getSbsj() );
        hjywBggzxxbDO.setBgqxm( dto.getBgqxm() );
        hjywBggzxxbDO.setBgqgmsfhm( dto.getBgqgmsfhm() );
        hjywBggzxxbDO.setGmsfhm( dto.getGmsfhm() );
        hjywBggzxxbDO.setXm( dto.getXm() );
        hjywBggzxxbDO.setXb( dto.getXb() );
        hjywBggzxxbDO.setMz( dto.getMz() );
        hjywBggzxxbDO.setCsrq( dto.getCsrq() );
        hjywBggzxxbDO.setCsdssxq( dto.getCsdssxq() );
        hjywBggzxxbDO.setMlpnbid( dto.getMlpnbid() );
        hjywBggzxxbDO.setSsxq( dto.getSsxq() );
        hjywBggzxxbDO.setJlx( dto.getJlx() );
        hjywBggzxxbDO.setMlph( dto.getMlph() );
        hjywBggzxxbDO.setMlxz( dto.getMlxz() );
        hjywBggzxxbDO.setPcs( dto.getPcs() );
        hjywBggzxxbDO.setZrq( dto.getZrq() );
        hjywBggzxxbDO.setXzjd( dto.getXzjd() );
        hjywBggzxxbDO.setJcwh( dto.getJcwh() );
        hjywBggzxxbDO.setPxh( dto.getPxh() );
        hjywBggzxxbDO.setCssj( dto.getCssj() );
        hjywBggzxxbDO.setHhnbid( dto.getHhnbid() );
        hjywBggzxxbDO.setRyzt( dto.getRyzt() );
        hjywBggzxxbDO.setBggzqdm( dto.getBggzqdm() );
        hjywBggzxxbDO.setBggzhdm( dto.getBggzhdm() );
        hjywBggzxxbDO.setZaglxxlbdm( dto.getZaglxxlbdm() );
        hjywBggzxxbDO.setXxlbmc( dto.getXxlbmc() );
        hjywBggzxxbDO.setBggzsjxbsf( dto.getBggzsjxbsf() );
        hjywBggzxxbDO.setBggzsjxmc( dto.getBggzsjxmc() );
        hjywBggzxxbDO.setSbrybdrgx( dto.getSbrybdrgx() );
        hjywBggzxxbDO.setSldwmc( dto.getSldwmc() );
        hjywBggzxxbDO.setSlrxm( dto.getSlrxm() );
        hjywBggzxxbDO.setSjgsdwdm( dto.getSjgsdwdm() );
        hjywBggzxxbDO.setSjgsdwmc( dto.getSjgsdwmc() );
        hjywBggzxxbDO.setHjddzbm( dto.getHjddzbm() );
        hjywBggzxxbDO.setHjdssxq( dto.getHjdssxq() );
        hjywBggzxxbDO.setHjdxxdz( dto.getHjdxxdz() );
        hjywBggzxxbDO.setSbrlxdh( dto.getSbrlxdh() );
        hjywBggzxxbDO.setSpywslh( dto.getSpywslh() );
        hjywBggzxxbDO.setSpxxlylb( dto.getSpxxlylb() );
        hjywBggzxxbDO.setSpsbdwdm( dto.getSpsbdwdm() );
        hjywBggzxxbDO.setSpsbsj( dto.getSpsbsj() );

        return hjywBggzxxbDO;
    }

    @Override
    public List<HjywBggzxxbDO> convertToDO(List<HjywBggzxxbDTO> dto) {
        if ( dto == null ) {
            return null;
        }

        List<HjywBggzxxbDO> list = new ArrayList<HjywBggzxxbDO>( dto.size() );
        for ( HjywBggzxxbDTO hjywBggzxxbDTO : dto ) {
            list.add( convertToDO( hjywBggzxxbDTO ) );
        }

        return list;
    }

    @Override
    public HjywBggzxxbDTO convertToDTO(HjxxCzrkjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjywBggzxxbDTO hjywBggzxxbDTO = new HjywBggzxxbDTO();

        hjywBggzxxbDTO.setRynbid( dto.getRynbid() );
        hjywBggzxxbDTO.setRyid( dto.getRyid() );
        hjywBggzxxbDTO.setGmsfhm( dto.getGmsfhm() );
        hjywBggzxxbDTO.setXm( dto.getXm() );
        hjywBggzxxbDTO.setXb( dto.getXb() );
        hjywBggzxxbDTO.setMz( dto.getMz() );
        hjywBggzxxbDTO.setCsrq( dto.getCsrq() );
        hjywBggzxxbDTO.setCsdssxq( dto.getCsdssxq() );
        hjywBggzxxbDTO.setMlpnbid( dto.getMlpnbid() );
        hjywBggzxxbDTO.setSsxq( dto.getSsxq() );
        hjywBggzxxbDTO.setJlx( dto.getJlx() );
        hjywBggzxxbDTO.setMlph( dto.getMlph() );
        hjywBggzxxbDTO.setMlxz( dto.getMlxz() );
        hjywBggzxxbDTO.setPcs( dto.getPcs() );
        hjywBggzxxbDTO.setZrq( dto.getZrq() );
        hjywBggzxxbDTO.setXzjd( dto.getXzjd() );
        hjywBggzxxbDTO.setJcwh( dto.getJcwh() );
        hjywBggzxxbDTO.setPxh( dto.getPxh() );
        hjywBggzxxbDTO.setCssj( dto.getCssj() );
        hjywBggzxxbDTO.setHhnbid( dto.getHhnbid() );
        hjywBggzxxbDTO.setRyzt( dto.getRyzt() );
        hjywBggzxxbDTO.setSjgsdwdm( dto.getSjgsdwdm() );
        hjywBggzxxbDTO.setSjgsdwmc( dto.getSjgsdwmc() );
        hjywBggzxxbDTO.setHjddzbm( dto.getHjddzbm() );
        hjywBggzxxbDTO.setHjdssxq( dto.getHjdssxq() );
        hjywBggzxxbDTO.setHjdxxdz( dto.getHjdxxdz() );
        hjywBggzxxbDTO.setCsrqStart( dto.getCsrqStart() );
        hjywBggzxxbDTO.setCsrqEnd( dto.getCsrqEnd() );

        return hjywBggzxxbDTO;
    }
}
