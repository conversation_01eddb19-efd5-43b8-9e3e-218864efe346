package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtLssfzSlxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtLssfzSlxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtLssfzSlxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtLssfzSlxxbViewResp;
import com.zjjcnt.project.ck.base.entity.ZjtLssfzSlxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:42+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtLssfzSlxxbConvertImpl implements ZjtLssfzSlxxbConvert {

    @Override
    public ZjtLssfzSlxxbDTO convert(ZjtLssfzSlxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtLssfzSlxxbDTO zjtLssfzSlxxbDTO = new ZjtLssfzSlxxbDTO();

        zjtLssfzSlxxbDTO.setId( entity.getId() );
        zjtLssfzSlxxbDTO.setLsslid( entity.getLsslid() );
        zjtLssfzSlxxbDTO.setRynbid( entity.getRynbid() );
        zjtLssfzSlxxbDTO.setZpid( entity.getZpid() );
        zjtLssfzSlxxbDTO.setRyid( entity.getRyid() );
        zjtLssfzSlxxbDTO.setQfjg( entity.getQfjg() );
        zjtLssfzSlxxbDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        zjtLssfzSlxxbDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        zjtLssfzSlxxbDTO.setLsjmsfzkh( entity.getLsjmsfzkh() );
        zjtLssfzSlxxbDTO.setZz( entity.getZz() );
        zjtLssfzSlxxbDTO.setXm( entity.getXm() );
        zjtLssfzSlxxbDTO.setGmsfhm( entity.getGmsfhm() );
        zjtLssfzSlxxbDTO.setNbsfzid( entity.getNbsfzid() );
        zjtLssfzSlxxbDTO.setXb( entity.getXb() );
        zjtLssfzSlxxbDTO.setMz( entity.getMz() );
        zjtLssfzSlxxbDTO.setCsrq( entity.getCsrq() );
        zjtLssfzSlxxbDTO.setCsdssxq( entity.getCsdssxq() );
        zjtLssfzSlxxbDTO.setLxdh( entity.getLxdh() );
        zjtLssfzSlxxbDTO.setMlpnbid( entity.getMlpnbid() );
        zjtLssfzSlxxbDTO.setSsxq( entity.getSsxq() );
        zjtLssfzSlxxbDTO.setJlx( entity.getJlx() );
        zjtLssfzSlxxbDTO.setMlph( entity.getMlph() );
        zjtLssfzSlxxbDTO.setMlxz( entity.getMlxz() );
        zjtLssfzSlxxbDTO.setPcs( entity.getPcs() );
        zjtLssfzSlxxbDTO.setZrq( entity.getZrq() );
        zjtLssfzSlxxbDTO.setXzjd( entity.getXzjd() );
        zjtLssfzSlxxbDTO.setJcwh( entity.getJcwh() );
        zjtLssfzSlxxbDTO.setPxh( entity.getPxh() );
        zjtLssfzSlxxbDTO.setCzyid( entity.getCzyid() );
        zjtLssfzSlxxbDTO.setCzyxm( entity.getCzyxm() );
        zjtLssfzSlxxbDTO.setCzsj( entity.getCzsj() );
        zjtLssfzSlxxbDTO.setCzyip( entity.getCzyip() );
        zjtLssfzSlxxbDTO.setDybz( entity.getDybz() );
        zjtLssfzSlxxbDTO.setDyrid( entity.getDyrid() );
        zjtLssfzSlxxbDTO.setDyrxm( entity.getDyrxm() );
        zjtLssfzSlxxbDTO.setDysj( entity.getDysj() );
        zjtLssfzSlxxbDTO.setDyrip( entity.getDyrip() );
        zjtLssfzSlxxbDTO.setShjg( entity.getShjg() );
        zjtLssfzSlxxbDTO.setShrid( entity.getShrid() );
        zjtLssfzSlxxbDTO.setShrip( entity.getShrip() );
        zjtLssfzSlxxbDTO.setShrxm( entity.getShrxm() );
        zjtLssfzSlxxbDTO.setShsj( entity.getShsj() );
        zjtLssfzSlxxbDTO.setSldsjgsdwdmgab( entity.getSldsjgsdwdmgab() );
        zjtLssfzSlxxbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        zjtLssfzSlxxbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        zjtLssfzSlxxbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        zjtLssfzSlxxbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        zjtLssfzSlxxbDTO.setZzlx( entity.getZzlx() );
        zjtLssfzSlxxbDTO.setNbslid( entity.getNbslid() );
        zjtLssfzSlxxbDTO.setJmsfzslh( entity.getJmsfzslh() );
        zjtLssfzSlxxbDTO.setSldfjsjgsdwdm( entity.getSldfjsjgsdwdm() );
        zjtLssfzSlxxbDTO.setSldfjsjgsdwmc( entity.getSldfjsjgsdwmc() );
        zjtLssfzSlxxbDTO.setHlwsqid( entity.getHlwsqid() );
        zjtLssfzSlxxbDTO.setUsername( entity.getUsername() );
        zjtLssfzSlxxbDTO.setPjjg( entity.getPjjg() );
        zjtLssfzSlxxbDTO.setPjpljc( entity.getPjpljc() );
        zjtLssfzSlxxbDTO.setPjsj( entity.getPjsj() );
        zjtLssfzSlxxbDTO.setLqfs( entity.getLqfs() );
        zjtLssfzSlxxbDTO.setSjrxm( entity.getSjrxm() );
        zjtLssfzSlxxbDTO.setSjrlxdh( entity.getSjrlxdh() );
        zjtLssfzSlxxbDTO.setSjryb( entity.getSjryb() );
        zjtLssfzSlxxbDTO.setSjrssxq( entity.getSjrssxq() );
        zjtLssfzSlxxbDTO.setSjrxz( entity.getSjrxz() );
        zjtLssfzSlxxbDTO.setSjrtxdz( entity.getSjrtxdz() );
        zjtLssfzSlxxbDTO.setBz( entity.getBz() );
        zjtLssfzSlxxbDTO.setFjshhs( entity.getFjshhs() );
        zjtLssfzSlxxbDTO.setCjfs( entity.getCjfs() );
        zjtLssfzSlxxbDTO.setFxjsfzt( entity.getFxjsfzt() );
        zjtLssfzSlxxbDTO.setFxjsfsj( entity.getFxjsfsj() );
        zjtLssfzSlxxbDTO.setFwdx( entity.getFwdx() );
        zjtLssfzSlxxbDTO.setSflx( entity.getSflx() );
        zjtLssfzSlxxbDTO.setSfje( entity.getSfje() );

        return zjtLssfzSlxxbDTO;
    }

    @Override
    public ZjtLssfzSlxxbDO convertToDO(ZjtLssfzSlxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtLssfzSlxxbDO zjtLssfzSlxxbDO = new ZjtLssfzSlxxbDO();

        zjtLssfzSlxxbDO.setId( dto.getId() );
        zjtLssfzSlxxbDO.setLsslid( dto.getLsslid() );
        zjtLssfzSlxxbDO.setRynbid( dto.getRynbid() );
        zjtLssfzSlxxbDO.setZpid( dto.getZpid() );
        zjtLssfzSlxxbDO.setRyid( dto.getRyid() );
        zjtLssfzSlxxbDO.setQfjg( dto.getQfjg() );
        zjtLssfzSlxxbDO.setYxqxqsrq( dto.getYxqxqsrq() );
        zjtLssfzSlxxbDO.setYxqxjzrq( dto.getYxqxjzrq() );
        zjtLssfzSlxxbDO.setLsjmsfzkh( dto.getLsjmsfzkh() );
        zjtLssfzSlxxbDO.setZz( dto.getZz() );
        zjtLssfzSlxxbDO.setXm( dto.getXm() );
        zjtLssfzSlxxbDO.setGmsfhm( dto.getGmsfhm() );
        zjtLssfzSlxxbDO.setNbsfzid( dto.getNbsfzid() );
        zjtLssfzSlxxbDO.setXb( dto.getXb() );
        zjtLssfzSlxxbDO.setMz( dto.getMz() );
        zjtLssfzSlxxbDO.setCsrq( dto.getCsrq() );
        zjtLssfzSlxxbDO.setCsdssxq( dto.getCsdssxq() );
        zjtLssfzSlxxbDO.setLxdh( dto.getLxdh() );
        zjtLssfzSlxxbDO.setMlpnbid( dto.getMlpnbid() );
        zjtLssfzSlxxbDO.setSsxq( dto.getSsxq() );
        zjtLssfzSlxxbDO.setJlx( dto.getJlx() );
        zjtLssfzSlxxbDO.setMlph( dto.getMlph() );
        zjtLssfzSlxxbDO.setMlxz( dto.getMlxz() );
        zjtLssfzSlxxbDO.setPcs( dto.getPcs() );
        zjtLssfzSlxxbDO.setZrq( dto.getZrq() );
        zjtLssfzSlxxbDO.setXzjd( dto.getXzjd() );
        zjtLssfzSlxxbDO.setJcwh( dto.getJcwh() );
        zjtLssfzSlxxbDO.setPxh( dto.getPxh() );
        zjtLssfzSlxxbDO.setCzyid( dto.getCzyid() );
        zjtLssfzSlxxbDO.setCzyxm( dto.getCzyxm() );
        zjtLssfzSlxxbDO.setCzsj( dto.getCzsj() );
        zjtLssfzSlxxbDO.setCzyip( dto.getCzyip() );
        zjtLssfzSlxxbDO.setDybz( dto.getDybz() );
        zjtLssfzSlxxbDO.setDyrid( dto.getDyrid() );
        zjtLssfzSlxxbDO.setDyrxm( dto.getDyrxm() );
        zjtLssfzSlxxbDO.setDysj( dto.getDysj() );
        zjtLssfzSlxxbDO.setDyrip( dto.getDyrip() );
        zjtLssfzSlxxbDO.setShjg( dto.getShjg() );
        zjtLssfzSlxxbDO.setShrid( dto.getShrid() );
        zjtLssfzSlxxbDO.setShrip( dto.getShrip() );
        zjtLssfzSlxxbDO.setShrxm( dto.getShrxm() );
        zjtLssfzSlxxbDO.setShsj( dto.getShsj() );
        zjtLssfzSlxxbDO.setSldsjgsdwdmgab( dto.getSldsjgsdwdmgab() );
        zjtLssfzSlxxbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtLssfzSlxxbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtLssfzSlxxbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtLssfzSlxxbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtLssfzSlxxbDO.setZzlx( dto.getZzlx() );
        zjtLssfzSlxxbDO.setNbslid( dto.getNbslid() );
        zjtLssfzSlxxbDO.setJmsfzslh( dto.getJmsfzslh() );
        zjtLssfzSlxxbDO.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtLssfzSlxxbDO.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtLssfzSlxxbDO.setHlwsqid( dto.getHlwsqid() );
        zjtLssfzSlxxbDO.setUsername( dto.getUsername() );
        zjtLssfzSlxxbDO.setPjjg( dto.getPjjg() );
        zjtLssfzSlxxbDO.setPjpljc( dto.getPjpljc() );
        zjtLssfzSlxxbDO.setPjsj( dto.getPjsj() );
        zjtLssfzSlxxbDO.setLqfs( dto.getLqfs() );
        zjtLssfzSlxxbDO.setSjrxm( dto.getSjrxm() );
        zjtLssfzSlxxbDO.setSjrlxdh( dto.getSjrlxdh() );
        zjtLssfzSlxxbDO.setSjryb( dto.getSjryb() );
        zjtLssfzSlxxbDO.setSjrssxq( dto.getSjrssxq() );
        zjtLssfzSlxxbDO.setSjrxz( dto.getSjrxz() );
        zjtLssfzSlxxbDO.setSjrtxdz( dto.getSjrtxdz() );
        zjtLssfzSlxxbDO.setBz( dto.getBz() );
        zjtLssfzSlxxbDO.setFjshhs( dto.getFjshhs() );
        zjtLssfzSlxxbDO.setCjfs( dto.getCjfs() );
        zjtLssfzSlxxbDO.setFxjsfzt( dto.getFxjsfzt() );
        zjtLssfzSlxxbDO.setFxjsfsj( dto.getFxjsfsj() );
        zjtLssfzSlxxbDO.setFwdx( dto.getFwdx() );
        zjtLssfzSlxxbDO.setSflx( dto.getSflx() );
        zjtLssfzSlxxbDO.setSfje( dto.getSfje() );

        return zjtLssfzSlxxbDO;
    }

    @Override
    public ZjtLssfzSlxxbDTO convertToDTO(ZjtLssfzSlxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        ZjtLssfzSlxxbDTO zjtLssfzSlxxbDTO = new ZjtLssfzSlxxbDTO();

        zjtLssfzSlxxbDTO.setXm( req.getXm() );
        zjtLssfzSlxxbDTO.setGmsfhm( req.getGmsfhm() );
        zjtLssfzSlxxbDTO.setHjdsjgsdwdm( req.getHjdsjgsdwdm() );
        zjtLssfzSlxxbDTO.setSldsjgsdwdm( req.getSldsjgsdwdm() );
        zjtLssfzSlxxbDTO.setCzsjStart( req.getCzsjStart() );
        zjtLssfzSlxxbDTO.setCzsjEnd( req.getCzsjEnd() );

        return zjtLssfzSlxxbDTO;
    }

    @Override
    public ZjtLssfzSlxxbPageResp convertToPageResp(ZjtLssfzSlxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtLssfzSlxxbPageResp zjtLssfzSlxxbPageResp = new ZjtLssfzSlxxbPageResp();

        zjtLssfzSlxxbPageResp.setLsslid( dto.getLsslid() );
        zjtLssfzSlxxbPageResp.setRynbid( dto.getRynbid() );
        zjtLssfzSlxxbPageResp.setZpid( dto.getZpid() );
        zjtLssfzSlxxbPageResp.setRyid( dto.getRyid() );
        zjtLssfzSlxxbPageResp.setQfjg( dto.getQfjg() );
        zjtLssfzSlxxbPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        zjtLssfzSlxxbPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        zjtLssfzSlxxbPageResp.setLsjmsfzkh( dto.getLsjmsfzkh() );
        zjtLssfzSlxxbPageResp.setZz( dto.getZz() );
        zjtLssfzSlxxbPageResp.setXm( dto.getXm() );
        zjtLssfzSlxxbPageResp.setGmsfhm( dto.getGmsfhm() );
        zjtLssfzSlxxbPageResp.setNbsfzid( dto.getNbsfzid() );
        zjtLssfzSlxxbPageResp.setXb( dto.getXb() );
        zjtLssfzSlxxbPageResp.setMz( dto.getMz() );
        zjtLssfzSlxxbPageResp.setCsrq( dto.getCsrq() );
        zjtLssfzSlxxbPageResp.setCsdssxq( dto.getCsdssxq() );
        zjtLssfzSlxxbPageResp.setLxdh( dto.getLxdh() );
        zjtLssfzSlxxbPageResp.setMlpnbid( dto.getMlpnbid() );
        zjtLssfzSlxxbPageResp.setSsxq( dto.getSsxq() );
        zjtLssfzSlxxbPageResp.setJlx( dto.getJlx() );
        zjtLssfzSlxxbPageResp.setMlph( dto.getMlph() );
        zjtLssfzSlxxbPageResp.setMlxz( dto.getMlxz() );
        zjtLssfzSlxxbPageResp.setPcs( dto.getPcs() );
        zjtLssfzSlxxbPageResp.setZrq( dto.getZrq() );
        zjtLssfzSlxxbPageResp.setXzjd( dto.getXzjd() );
        zjtLssfzSlxxbPageResp.setJcwh( dto.getJcwh() );
        zjtLssfzSlxxbPageResp.setPxh( dto.getPxh() );
        zjtLssfzSlxxbPageResp.setCzyid( dto.getCzyid() );
        zjtLssfzSlxxbPageResp.setCzyxm( dto.getCzyxm() );
        zjtLssfzSlxxbPageResp.setCzsj( dto.getCzsj() );
        zjtLssfzSlxxbPageResp.setCzyip( dto.getCzyip() );
        zjtLssfzSlxxbPageResp.setDybz( dto.getDybz() );
        zjtLssfzSlxxbPageResp.setDyrid( dto.getDyrid() );
        zjtLssfzSlxxbPageResp.setDyrxm( dto.getDyrxm() );
        zjtLssfzSlxxbPageResp.setDysj( dto.getDysj() );
        zjtLssfzSlxxbPageResp.setDyrip( dto.getDyrip() );
        zjtLssfzSlxxbPageResp.setShjg( dto.getShjg() );
        zjtLssfzSlxxbPageResp.setShrid( dto.getShrid() );
        zjtLssfzSlxxbPageResp.setShrip( dto.getShrip() );
        zjtLssfzSlxxbPageResp.setShrxm( dto.getShrxm() );
        zjtLssfzSlxxbPageResp.setShsj( dto.getShsj() );
        zjtLssfzSlxxbPageResp.setSldsjgsdwdmgab( dto.getSldsjgsdwdmgab() );
        zjtLssfzSlxxbPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtLssfzSlxxbPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtLssfzSlxxbPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtLssfzSlxxbPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtLssfzSlxxbPageResp.setZzlx( dto.getZzlx() );
        zjtLssfzSlxxbPageResp.setNbslid( dto.getNbslid() );
        zjtLssfzSlxxbPageResp.setJmsfzslh( dto.getJmsfzslh() );
        zjtLssfzSlxxbPageResp.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtLssfzSlxxbPageResp.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtLssfzSlxxbPageResp.setHlwsqid( dto.getHlwsqid() );
        zjtLssfzSlxxbPageResp.setUsername( dto.getUsername() );
        zjtLssfzSlxxbPageResp.setPjjg( dto.getPjjg() );
        zjtLssfzSlxxbPageResp.setPjpljc( dto.getPjpljc() );
        zjtLssfzSlxxbPageResp.setPjsj( dto.getPjsj() );
        zjtLssfzSlxxbPageResp.setLqfs( dto.getLqfs() );
        zjtLssfzSlxxbPageResp.setSjrxm( dto.getSjrxm() );
        zjtLssfzSlxxbPageResp.setSjrlxdh( dto.getSjrlxdh() );
        zjtLssfzSlxxbPageResp.setSjryb( dto.getSjryb() );
        zjtLssfzSlxxbPageResp.setSjrssxq( dto.getSjrssxq() );
        zjtLssfzSlxxbPageResp.setSjrxz( dto.getSjrxz() );
        zjtLssfzSlxxbPageResp.setSjrtxdz( dto.getSjrtxdz() );
        zjtLssfzSlxxbPageResp.setBz( dto.getBz() );
        zjtLssfzSlxxbPageResp.setFjshhs( dto.getFjshhs() );
        zjtLssfzSlxxbPageResp.setCjfs( dto.getCjfs() );
        zjtLssfzSlxxbPageResp.setFxjsfzt( dto.getFxjsfzt() );
        zjtLssfzSlxxbPageResp.setFxjsfsj( dto.getFxjsfsj() );
        zjtLssfzSlxxbPageResp.setFwdx( dto.getFwdx() );
        zjtLssfzSlxxbPageResp.setSflx( dto.getSflx() );
        zjtLssfzSlxxbPageResp.setSfje( dto.getSfje() );

        return zjtLssfzSlxxbPageResp;
    }

    @Override
    public ZjtLssfzSlxxbViewResp convertToViewResp(ZjtLssfzSlxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtLssfzSlxxbViewResp zjtLssfzSlxxbViewResp = new ZjtLssfzSlxxbViewResp();

        zjtLssfzSlxxbViewResp.setLsslid( dto.getLsslid() );
        zjtLssfzSlxxbViewResp.setRynbid( dto.getRynbid() );
        zjtLssfzSlxxbViewResp.setZpid( dto.getZpid() );
        zjtLssfzSlxxbViewResp.setRyid( dto.getRyid() );
        zjtLssfzSlxxbViewResp.setQfjg( dto.getQfjg() );
        zjtLssfzSlxxbViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        zjtLssfzSlxxbViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        zjtLssfzSlxxbViewResp.setLsjmsfzkh( dto.getLsjmsfzkh() );
        zjtLssfzSlxxbViewResp.setZz( dto.getZz() );
        zjtLssfzSlxxbViewResp.setXm( dto.getXm() );
        zjtLssfzSlxxbViewResp.setGmsfhm( dto.getGmsfhm() );
        zjtLssfzSlxxbViewResp.setNbsfzid( dto.getNbsfzid() );
        zjtLssfzSlxxbViewResp.setXb( dto.getXb() );
        zjtLssfzSlxxbViewResp.setMz( dto.getMz() );
        zjtLssfzSlxxbViewResp.setCsrq( dto.getCsrq() );
        zjtLssfzSlxxbViewResp.setCsdssxq( dto.getCsdssxq() );
        zjtLssfzSlxxbViewResp.setLxdh( dto.getLxdh() );
        zjtLssfzSlxxbViewResp.setMlpnbid( dto.getMlpnbid() );
        zjtLssfzSlxxbViewResp.setSsxq( dto.getSsxq() );
        zjtLssfzSlxxbViewResp.setJlx( dto.getJlx() );
        zjtLssfzSlxxbViewResp.setMlph( dto.getMlph() );
        zjtLssfzSlxxbViewResp.setMlxz( dto.getMlxz() );
        zjtLssfzSlxxbViewResp.setPcs( dto.getPcs() );
        zjtLssfzSlxxbViewResp.setZrq( dto.getZrq() );
        zjtLssfzSlxxbViewResp.setXzjd( dto.getXzjd() );
        zjtLssfzSlxxbViewResp.setJcwh( dto.getJcwh() );
        zjtLssfzSlxxbViewResp.setPxh( dto.getPxh() );
        zjtLssfzSlxxbViewResp.setCzyid( dto.getCzyid() );
        zjtLssfzSlxxbViewResp.setCzyxm( dto.getCzyxm() );
        zjtLssfzSlxxbViewResp.setCzsj( dto.getCzsj() );
        zjtLssfzSlxxbViewResp.setCzyip( dto.getCzyip() );
        zjtLssfzSlxxbViewResp.setDybz( dto.getDybz() );
        zjtLssfzSlxxbViewResp.setDyrid( dto.getDyrid() );
        zjtLssfzSlxxbViewResp.setDyrxm( dto.getDyrxm() );
        zjtLssfzSlxxbViewResp.setDysj( dto.getDysj() );
        zjtLssfzSlxxbViewResp.setDyrip( dto.getDyrip() );
        zjtLssfzSlxxbViewResp.setShjg( dto.getShjg() );
        zjtLssfzSlxxbViewResp.setShrid( dto.getShrid() );
        zjtLssfzSlxxbViewResp.setShrip( dto.getShrip() );
        zjtLssfzSlxxbViewResp.setShrxm( dto.getShrxm() );
        zjtLssfzSlxxbViewResp.setShsj( dto.getShsj() );
        zjtLssfzSlxxbViewResp.setSldsjgsdwdmgab( dto.getSldsjgsdwdmgab() );
        zjtLssfzSlxxbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtLssfzSlxxbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtLssfzSlxxbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtLssfzSlxxbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtLssfzSlxxbViewResp.setZzlx( dto.getZzlx() );
        zjtLssfzSlxxbViewResp.setNbslid( dto.getNbslid() );
        zjtLssfzSlxxbViewResp.setJmsfzslh( dto.getJmsfzslh() );
        zjtLssfzSlxxbViewResp.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtLssfzSlxxbViewResp.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtLssfzSlxxbViewResp.setHlwsqid( dto.getHlwsqid() );
        zjtLssfzSlxxbViewResp.setUsername( dto.getUsername() );
        zjtLssfzSlxxbViewResp.setPjjg( dto.getPjjg() );
        zjtLssfzSlxxbViewResp.setPjpljc( dto.getPjpljc() );
        zjtLssfzSlxxbViewResp.setPjsj( dto.getPjsj() );
        zjtLssfzSlxxbViewResp.setLqfs( dto.getLqfs() );
        zjtLssfzSlxxbViewResp.setSjrxm( dto.getSjrxm() );
        zjtLssfzSlxxbViewResp.setSjrlxdh( dto.getSjrlxdh() );
        zjtLssfzSlxxbViewResp.setSjryb( dto.getSjryb() );
        zjtLssfzSlxxbViewResp.setSjrssxq( dto.getSjrssxq() );
        zjtLssfzSlxxbViewResp.setSjrxz( dto.getSjrxz() );
        zjtLssfzSlxxbViewResp.setSjrtxdz( dto.getSjrtxdz() );
        zjtLssfzSlxxbViewResp.setBz( dto.getBz() );
        zjtLssfzSlxxbViewResp.setFjshhs( dto.getFjshhs() );
        zjtLssfzSlxxbViewResp.setCjfs( dto.getCjfs() );
        zjtLssfzSlxxbViewResp.setFxjsfzt( dto.getFxjsfzt() );
        zjtLssfzSlxxbViewResp.setFxjsfsj( dto.getFxjsfsj() );
        zjtLssfzSlxxbViewResp.setFwdx( dto.getFwdx() );
        zjtLssfzSlxxbViewResp.setSflx( dto.getSflx() );
        zjtLssfzSlxxbViewResp.setSfje( dto.getSfje() );

        return zjtLssfzSlxxbViewResp;
    }
}
