package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktHjslZxhkDTO;
import com.zjjcnt.project.ck.base.dto.req.RktHjslZxhkCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslZxhkPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslZxhkUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslZxhkCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslZxhkPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslZxhkViewResp;
import com.zjjcnt.project.ck.base.entity.RktHjslZxhkDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:41+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktHjslZxhkConvertImpl implements RktHjslZxhkConvert {

    @Override
    public RktHjslZxhkDTO convert(RktHjslZxhkDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktHjslZxhkDTO rktHjslZxhkDTO = new RktHjslZxhkDTO();

        rktHjslZxhkDTO.setId( entity.getId() );
        rktHjslZxhkDTO.setYwslnbbh( entity.getYwslnbbh() );
        rktHjslZxhkDTO.setYwslh( entity.getYwslh() );
        rktHjslZxhkDTO.setHh( entity.getHh() );
        rktHjslZxhkDTO.setHhid( entity.getHhid() );
        rktHjslZxhkDTO.setRynbid( entity.getRynbid() );
        rktHjslZxhkDTO.setHlx( entity.getHlx() );
        rktHjslZxhkDTO.setHmc( entity.getHmc() );
        rktHjslZxhkDTO.setHjddzbm( entity.getHjddzbm() );
        rktHjslZxhkDTO.setHjdssxq( entity.getHjdssxq() );
        rktHjslZxhkDTO.setHjdxxdz( entity.getHjdxxdz() );
        rktHjslZxhkDTO.setHjdrhyzbs( entity.getHjdrhyzbs() );
        rktHjslZxhkDTO.setJlx( entity.getJlx() );
        rktHjslZxhkDTO.setMlph( entity.getMlph() );
        rktHjslZxhkDTO.setMlxz( entity.getMlxz() );
        rktHjslZxhkDTO.setLcdyid( entity.getLcdyid() );
        rktHjslZxhkDTO.setLcmc( entity.getLcmc() );
        rktHjslZxhkDTO.setSpyj( entity.getSpyj() );
        rktHjslZxhkDTO.setXh( entity.getXh() );
        rktHjslZxhkDTO.setHhnbid( entity.getHhnbid() );
        rktHjslZxhkDTO.setRyid( entity.getRyid() );
        rktHjslZxhkDTO.setHkxz( entity.getHkxz() );
        rktHjslZxhkDTO.setHb( entity.getHb() );
        rktHjslZxhkDTO.setYhzgx( entity.getYhzgx() );
        rktHjslZxhkDTO.setCxsx( entity.getCxsx() );
        rktHjslZxhkDTO.setGmsfhm( entity.getGmsfhm() );
        rktHjslZxhkDTO.setXm( entity.getXm() );
        rktHjslZxhkDTO.setX( entity.getX() );
        rktHjslZxhkDTO.setM( entity.getM() );
        rktHjslZxhkDTO.setCym( entity.getCym() );
        rktHjslZxhkDTO.setXmpy( entity.getXmpy() );
        rktHjslZxhkDTO.setCympy( entity.getCympy() );
        rktHjslZxhkDTO.setXb( entity.getXb() );
        rktHjslZxhkDTO.setMz( entity.getMz() );
        rktHjslZxhkDTO.setJggjdq( entity.getJggjdq() );
        rktHjslZxhkDTO.setJgssxq( entity.getJgssxq() );
        rktHjslZxhkDTO.setJgxz( entity.getJgxz() );
        rktHjslZxhkDTO.setCsrq( entity.getCsrq() );
        rktHjslZxhkDTO.setCssj( entity.getCssj() );
        rktHjslZxhkDTO.setCsdgjdq( entity.getCsdgjdq() );
        rktHjslZxhkDTO.setCsdssxq( entity.getCsdssxq() );
        rktHjslZxhkDTO.setCsdxz( entity.getCsdxz() );
        rktHjslZxhkDTO.setWhcd( entity.getWhcd() );
        rktHjslZxhkDTO.setHyzk( entity.getHyzk() );
        rktHjslZxhkDTO.setCyzkdwbm( entity.getCyzkdwbm() );
        rktHjslZxhkDTO.setCyzkdwmc( entity.getCyzkdwmc() );
        rktHjslZxhkDTO.setZy( entity.getZy() );
        rktHjslZxhkDTO.setZylb( entity.getZylb() );
        rktHjslZxhkDTO.setZjxy( entity.getZjxy() );
        rktHjslZxhkDTO.setSg( entity.getSg() );
        rktHjslZxhkDTO.setXx( entity.getXx() );
        rktHjslZxhkDTO.setByzk( entity.getByzk() );
        rktHjslZxhkDTO.setXxjb( entity.getXxjb() );
        rktHjslZxhkDTO.setLxdh( entity.getLxdh() );
        rktHjslZxhkDTO.setFqxm( entity.getFqxm() );
        rktHjslZxhkDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        rktHjslZxhkDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        rktHjslZxhkDTO.setFqzjhm( entity.getFqzjhm() );
        rktHjslZxhkDTO.setFqwwx( entity.getFqwwx() );
        rktHjslZxhkDTO.setFqwwm( entity.getFqwwm() );
        rktHjslZxhkDTO.setMqxm( entity.getMqxm() );
        rktHjslZxhkDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        rktHjslZxhkDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        rktHjslZxhkDTO.setMqzjhm( entity.getMqzjhm() );
        rktHjslZxhkDTO.setMqwwx( entity.getMqwwx() );
        rktHjslZxhkDTO.setMqwwm( entity.getMqwwm() );
        rktHjslZxhkDTO.setPoxm( entity.getPoxm() );
        rktHjslZxhkDTO.setPogmsfhm( entity.getPogmsfhm() );
        rktHjslZxhkDTO.setPocyzjdm( entity.getPocyzjdm() );
        rktHjslZxhkDTO.setPozjhm( entity.getPozjhm() );
        rktHjslZxhkDTO.setPowwx( entity.getPowwx() );
        rktHjslZxhkDTO.setPowwm( entity.getPowwm() );
        rktHjslZxhkDTO.setJhryxm( entity.getJhryxm() );
        rktHjslZxhkDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        rktHjslZxhkDTO.setJhryjhgx( entity.getJhryjhgx() );
        rktHjslZxhkDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        rktHjslZxhkDTO.setJhryzjhm( entity.getJhryzjhm() );
        rktHjslZxhkDTO.setJhrywwx( entity.getJhrywwx() );
        rktHjslZxhkDTO.setJhrywwm( entity.getJhrywwm() );
        rktHjslZxhkDTO.setJhrylxdh( entity.getJhrylxdh() );
        rktHjslZxhkDTO.setJhrexm( entity.getJhrexm() );
        rktHjslZxhkDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        rktHjslZxhkDTO.setJhrejhgx( entity.getJhrejhgx() );
        rktHjslZxhkDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        rktHjslZxhkDTO.setJhrezjhm( entity.getJhrezjhm() );
        rktHjslZxhkDTO.setJhrewwx( entity.getJhrewwx() );
        rktHjslZxhkDTO.setJhrewwm( entity.getJhrewwm() );
        rktHjslZxhkDTO.setJhrelxdh( entity.getJhrelxdh() );
        rktHjslZxhkDTO.setZjlb( entity.getZjlb() );
        rktHjslZxhkDTO.setQfjg( entity.getQfjg() );
        rktHjslZxhkDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        rktHjslZxhkDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        rktHjslZxhkDTO.setZxlb( entity.getZxlb() );
        rktHjslZxhkDTO.setQcrq( entity.getQcrq() );
        rktHjslZxhkDTO.setQwdgjdq( entity.getQwdgjdq() );
        rktHjslZxhkDTO.setQwdssxq( entity.getQwdssxq() );
        rktHjslZxhkDTO.setQwdxz( entity.getQwdxz() );
        rktHjslZxhkDTO.setBdfw( entity.getBdfw() );
        rktHjslZxhkDTO.setQyldyy( entity.getQyldyy() );
        rktHjslZxhkDTO.setZxsm( entity.getZxsm() );
        rktHjslZxhkDTO.setZqzbh( entity.getZqzbh() );
        rktHjslZxhkDTO.setSwzmbh( entity.getSwzmbh() );
        rktHjslZxhkDTO.setLcywlx( entity.getLcywlx() );
        rktHjslZxhkDTO.setLcslid( entity.getLcslid() );
        rktHjslZxhkDTO.setLcywbt( entity.getLcywbt() );
        rktHjslZxhkDTO.setLcrwjd( entity.getLcrwjd() );
        rktHjslZxhkDTO.setLczt( entity.getLczt() );
        rktHjslZxhkDTO.setBlzt( entity.getBlzt() );
        rktHjslZxhkDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        rktHjslZxhkDTO.setSqrxm( entity.getSqrxm() );
        rktHjslZxhkDTO.setSqrxb( entity.getSqrxb() );
        rktHjslZxhkDTO.setSqrlxdh( entity.getSqrlxdh() );
        rktHjslZxhkDTO.setSqrybdrgx( entity.getSqrybdrgx() );
        rktHjslZxhkDTO.setSqrzzssxq( entity.getSqrzzssxq() );
        rktHjslZxhkDTO.setSqrzzxz( entity.getSqrzzxz() );
        rktHjslZxhkDTO.setSqrhkdjjg( entity.getSqrhkdjjg() );
        rktHjslZxhkDTO.setSqrq( entity.getSqrq() );
        rktHjslZxhkDTO.setSpdwgajgjgdm( entity.getSpdwgajgjgdm() );
        rktHjslZxhkDTO.setSpdwgajgmc( entity.getSpdwgajgmc() );
        rktHjslZxhkDTO.setSprxm( entity.getSprxm() );
        rktHjslZxhkDTO.setSpsj( entity.getSpsj() );
        rktHjslZxhkDTO.setSpjgdm( entity.getSpjgdm() );
        rktHjslZxhkDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        rktHjslZxhkDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        rktHjslZxhkDTO.setSlrxm( entity.getSlrxm() );
        rktHjslZxhkDTO.setSlsj( entity.getSlsj() );
        rktHjslZxhkDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        rktHjslZxhkDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        rktHjslZxhkDTO.setJcwh( entity.getJcwh() );
        rktHjslZxhkDTO.setBz( entity.getBz() );
        rktHjslZxhkDTO.setBlhjywid( entity.getBlhjywid() );
        rktHjslZxhkDTO.setHjywblsj( entity.getHjywblsj() );
        rktHjslZxhkDTO.setQtssxq( entity.getQtssxq() );
        rktHjslZxhkDTO.setQtzz( entity.getQtzz() );

        return rktHjslZxhkDTO;
    }

    @Override
    public RktHjslZxhkDO convertToDO(RktHjslZxhkDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslZxhkDO rktHjslZxhkDO = new RktHjslZxhkDO();

        rktHjslZxhkDO.setId( dto.getId() );
        rktHjslZxhkDO.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslZxhkDO.setYwslh( dto.getYwslh() );
        rktHjslZxhkDO.setHh( dto.getHh() );
        rktHjslZxhkDO.setHhid( dto.getHhid() );
        rktHjslZxhkDO.setRynbid( dto.getRynbid() );
        rktHjslZxhkDO.setHlx( dto.getHlx() );
        rktHjslZxhkDO.setHmc( dto.getHmc() );
        rktHjslZxhkDO.setHjddzbm( dto.getHjddzbm() );
        rktHjslZxhkDO.setHjdssxq( dto.getHjdssxq() );
        rktHjslZxhkDO.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslZxhkDO.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslZxhkDO.setJlx( dto.getJlx() );
        rktHjslZxhkDO.setMlph( dto.getMlph() );
        rktHjslZxhkDO.setMlxz( dto.getMlxz() );
        rktHjslZxhkDO.setLcdyid( dto.getLcdyid() );
        rktHjslZxhkDO.setLcmc( dto.getLcmc() );
        rktHjslZxhkDO.setSpyj( dto.getSpyj() );
        rktHjslZxhkDO.setXh( dto.getXh() );
        rktHjslZxhkDO.setHhnbid( dto.getHhnbid() );
        rktHjslZxhkDO.setRyid( dto.getRyid() );
        rktHjslZxhkDO.setHkxz( dto.getHkxz() );
        rktHjslZxhkDO.setHb( dto.getHb() );
        rktHjslZxhkDO.setYhzgx( dto.getYhzgx() );
        rktHjslZxhkDO.setCxsx( dto.getCxsx() );
        rktHjslZxhkDO.setGmsfhm( dto.getGmsfhm() );
        rktHjslZxhkDO.setXm( dto.getXm() );
        rktHjslZxhkDO.setX( dto.getX() );
        rktHjslZxhkDO.setM( dto.getM() );
        rktHjslZxhkDO.setCym( dto.getCym() );
        rktHjslZxhkDO.setXmpy( dto.getXmpy() );
        rktHjslZxhkDO.setCympy( dto.getCympy() );
        rktHjslZxhkDO.setXb( dto.getXb() );
        rktHjslZxhkDO.setMz( dto.getMz() );
        rktHjslZxhkDO.setJggjdq( dto.getJggjdq() );
        rktHjslZxhkDO.setJgssxq( dto.getJgssxq() );
        rktHjslZxhkDO.setJgxz( dto.getJgxz() );
        rktHjslZxhkDO.setCsrq( dto.getCsrq() );
        rktHjslZxhkDO.setCssj( dto.getCssj() );
        rktHjslZxhkDO.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslZxhkDO.setCsdssxq( dto.getCsdssxq() );
        rktHjslZxhkDO.setCsdxz( dto.getCsdxz() );
        rktHjslZxhkDO.setWhcd( dto.getWhcd() );
        rktHjslZxhkDO.setHyzk( dto.getHyzk() );
        rktHjslZxhkDO.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslZxhkDO.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslZxhkDO.setZy( dto.getZy() );
        rktHjslZxhkDO.setZylb( dto.getZylb() );
        rktHjslZxhkDO.setZjxy( dto.getZjxy() );
        rktHjslZxhkDO.setSg( dto.getSg() );
        rktHjslZxhkDO.setXx( dto.getXx() );
        rktHjslZxhkDO.setByzk( dto.getByzk() );
        rktHjslZxhkDO.setXxjb( dto.getXxjb() );
        rktHjslZxhkDO.setLxdh( dto.getLxdh() );
        rktHjslZxhkDO.setFqxm( dto.getFqxm() );
        rktHjslZxhkDO.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslZxhkDO.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslZxhkDO.setFqzjhm( dto.getFqzjhm() );
        rktHjslZxhkDO.setFqwwx( dto.getFqwwx() );
        rktHjslZxhkDO.setFqwwm( dto.getFqwwm() );
        rktHjslZxhkDO.setMqxm( dto.getMqxm() );
        rktHjslZxhkDO.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslZxhkDO.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslZxhkDO.setMqzjhm( dto.getMqzjhm() );
        rktHjslZxhkDO.setMqwwx( dto.getMqwwx() );
        rktHjslZxhkDO.setMqwwm( dto.getMqwwm() );
        rktHjslZxhkDO.setPoxm( dto.getPoxm() );
        rktHjslZxhkDO.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslZxhkDO.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslZxhkDO.setPozjhm( dto.getPozjhm() );
        rktHjslZxhkDO.setPowwx( dto.getPowwx() );
        rktHjslZxhkDO.setPowwm( dto.getPowwm() );
        rktHjslZxhkDO.setJhryxm( dto.getJhryxm() );
        rktHjslZxhkDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslZxhkDO.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslZxhkDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslZxhkDO.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslZxhkDO.setJhrywwx( dto.getJhrywwx() );
        rktHjslZxhkDO.setJhrywwm( dto.getJhrywwm() );
        rktHjslZxhkDO.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslZxhkDO.setJhrexm( dto.getJhrexm() );
        rktHjslZxhkDO.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslZxhkDO.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslZxhkDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslZxhkDO.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslZxhkDO.setJhrewwx( dto.getJhrewwx() );
        rktHjslZxhkDO.setJhrewwm( dto.getJhrewwm() );
        rktHjslZxhkDO.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslZxhkDO.setZjlb( dto.getZjlb() );
        rktHjslZxhkDO.setQfjg( dto.getQfjg() );
        rktHjslZxhkDO.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslZxhkDO.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslZxhkDO.setZxlb( dto.getZxlb() );
        rktHjslZxhkDO.setQcrq( dto.getQcrq() );
        rktHjslZxhkDO.setQwdgjdq( dto.getQwdgjdq() );
        rktHjslZxhkDO.setQwdssxq( dto.getQwdssxq() );
        rktHjslZxhkDO.setQwdxz( dto.getQwdxz() );
        rktHjslZxhkDO.setBdfw( dto.getBdfw() );
        rktHjslZxhkDO.setQyldyy( dto.getQyldyy() );
        rktHjslZxhkDO.setZxsm( dto.getZxsm() );
        rktHjslZxhkDO.setZqzbh( dto.getZqzbh() );
        rktHjslZxhkDO.setSwzmbh( dto.getSwzmbh() );
        rktHjslZxhkDO.setLcywlx( dto.getLcywlx() );
        rktHjslZxhkDO.setLcslid( dto.getLcslid() );
        rktHjslZxhkDO.setLcywbt( dto.getLcywbt() );
        rktHjslZxhkDO.setLcrwjd( dto.getLcrwjd() );
        rktHjslZxhkDO.setLczt( dto.getLczt() );
        rktHjslZxhkDO.setBlzt( dto.getBlzt() );
        rktHjslZxhkDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslZxhkDO.setSqrxm( dto.getSqrxm() );
        rktHjslZxhkDO.setSqrxb( dto.getSqrxb() );
        rktHjslZxhkDO.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslZxhkDO.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslZxhkDO.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslZxhkDO.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslZxhkDO.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslZxhkDO.setSqrq( dto.getSqrq() );
        rktHjslZxhkDO.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslZxhkDO.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslZxhkDO.setSprxm( dto.getSprxm() );
        rktHjslZxhkDO.setSpsj( dto.getSpsj() );
        rktHjslZxhkDO.setSpjgdm( dto.getSpjgdm() );
        rktHjslZxhkDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslZxhkDO.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslZxhkDO.setSlrxm( dto.getSlrxm() );
        rktHjslZxhkDO.setSlsj( dto.getSlsj() );
        rktHjslZxhkDO.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslZxhkDO.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslZxhkDO.setJcwh( dto.getJcwh() );
        rktHjslZxhkDO.setBz( dto.getBz() );
        rktHjslZxhkDO.setBlhjywid( dto.getBlhjywid() );
        rktHjslZxhkDO.setHjywblsj( dto.getHjywblsj() );
        rktHjslZxhkDO.setQtssxq( dto.getQtssxq() );
        rktHjslZxhkDO.setQtzz( dto.getQtzz() );

        return rktHjslZxhkDO;
    }

    @Override
    public RktHjslZxhkDTO convertToDTO(RktHjslZxhkPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslZxhkDTO rktHjslZxhkDTO = new RktHjslZxhkDTO();

        rktHjslZxhkDTO.setYwslh( req.getYwslh() );
        rktHjslZxhkDTO.setHh( req.getHh() );
        rktHjslZxhkDTO.setHhid( req.getHhid() );
        rktHjslZxhkDTO.setRynbid( req.getRynbid() );
        rktHjslZxhkDTO.setHlx( req.getHlx() );
        rktHjslZxhkDTO.setHmc( req.getHmc() );
        rktHjslZxhkDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslZxhkDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslZxhkDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslZxhkDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslZxhkDTO.setJlx( req.getJlx() );
        rktHjslZxhkDTO.setMlph( req.getMlph() );
        rktHjslZxhkDTO.setMlxz( req.getMlxz() );
        rktHjslZxhkDTO.setLcdyid( req.getLcdyid() );
        rktHjslZxhkDTO.setLcmc( req.getLcmc() );
        rktHjslZxhkDTO.setSpyj( req.getSpyj() );
        rktHjslZxhkDTO.setXh( req.getXh() );
        rktHjslZxhkDTO.setHhnbid( req.getHhnbid() );
        rktHjslZxhkDTO.setRyid( req.getRyid() );
        rktHjslZxhkDTO.setHkxz( req.getHkxz() );
        rktHjslZxhkDTO.setHb( req.getHb() );
        rktHjslZxhkDTO.setYhzgx( req.getYhzgx() );
        rktHjslZxhkDTO.setCxsx( req.getCxsx() );
        rktHjslZxhkDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslZxhkDTO.setXm( req.getXm() );
        rktHjslZxhkDTO.setX( req.getX() );
        rktHjslZxhkDTO.setM( req.getM() );
        rktHjslZxhkDTO.setCym( req.getCym() );
        rktHjslZxhkDTO.setXmpy( req.getXmpy() );
        rktHjslZxhkDTO.setCympy( req.getCympy() );
        rktHjslZxhkDTO.setXb( req.getXb() );
        rktHjslZxhkDTO.setMz( req.getMz() );
        rktHjslZxhkDTO.setJggjdq( req.getJggjdq() );
        rktHjslZxhkDTO.setJgssxq( req.getJgssxq() );
        rktHjslZxhkDTO.setJgxz( req.getJgxz() );
        rktHjslZxhkDTO.setCsrq( req.getCsrq() );
        rktHjslZxhkDTO.setCssj( req.getCssj() );
        rktHjslZxhkDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslZxhkDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslZxhkDTO.setCsdxz( req.getCsdxz() );
        rktHjslZxhkDTO.setWhcd( req.getWhcd() );
        rktHjslZxhkDTO.setHyzk( req.getHyzk() );
        rktHjslZxhkDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslZxhkDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslZxhkDTO.setZy( req.getZy() );
        rktHjslZxhkDTO.setZylb( req.getZylb() );
        rktHjslZxhkDTO.setZjxy( req.getZjxy() );
        rktHjslZxhkDTO.setSg( req.getSg() );
        rktHjslZxhkDTO.setXx( req.getXx() );
        rktHjslZxhkDTO.setByzk( req.getByzk() );
        rktHjslZxhkDTO.setXxjb( req.getXxjb() );
        rktHjslZxhkDTO.setLxdh( req.getLxdh() );
        rktHjslZxhkDTO.setFqxm( req.getFqxm() );
        rktHjslZxhkDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslZxhkDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslZxhkDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslZxhkDTO.setFqwwx( req.getFqwwx() );
        rktHjslZxhkDTO.setFqwwm( req.getFqwwm() );
        rktHjslZxhkDTO.setMqxm( req.getMqxm() );
        rktHjslZxhkDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslZxhkDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslZxhkDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslZxhkDTO.setMqwwx( req.getMqwwx() );
        rktHjslZxhkDTO.setMqwwm( req.getMqwwm() );
        rktHjslZxhkDTO.setPoxm( req.getPoxm() );
        rktHjslZxhkDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslZxhkDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslZxhkDTO.setPozjhm( req.getPozjhm() );
        rktHjslZxhkDTO.setPowwx( req.getPowwx() );
        rktHjslZxhkDTO.setPowwm( req.getPowwm() );
        rktHjslZxhkDTO.setJhryxm( req.getJhryxm() );
        rktHjslZxhkDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslZxhkDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslZxhkDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslZxhkDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslZxhkDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslZxhkDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslZxhkDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslZxhkDTO.setJhrexm( req.getJhrexm() );
        rktHjslZxhkDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslZxhkDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslZxhkDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslZxhkDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslZxhkDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslZxhkDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslZxhkDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslZxhkDTO.setZjlb( req.getZjlb() );
        rktHjslZxhkDTO.setQfjg( req.getQfjg() );
        rktHjslZxhkDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslZxhkDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslZxhkDTO.setZxlb( req.getZxlb() );
        rktHjslZxhkDTO.setQcrq( req.getQcrq() );
        rktHjslZxhkDTO.setQwdgjdq( req.getQwdgjdq() );
        rktHjslZxhkDTO.setQwdssxq( req.getQwdssxq() );
        rktHjslZxhkDTO.setQwdxz( req.getQwdxz() );
        rktHjslZxhkDTO.setBdfw( req.getBdfw() );
        rktHjslZxhkDTO.setQyldyy( req.getQyldyy() );
        rktHjslZxhkDTO.setZxsm( req.getZxsm() );
        rktHjslZxhkDTO.setZqzbh( req.getZqzbh() );
        rktHjslZxhkDTO.setSwzmbh( req.getSwzmbh() );
        rktHjslZxhkDTO.setLcywlx( req.getLcywlx() );
        rktHjslZxhkDTO.setLcslid( req.getLcslid() );
        rktHjslZxhkDTO.setLcywbt( req.getLcywbt() );
        rktHjslZxhkDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslZxhkDTO.setLczt( req.getLczt() );
        rktHjslZxhkDTO.setBlzt( req.getBlzt() );
        rktHjslZxhkDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslZxhkDTO.setSqrxm( req.getSqrxm() );
        rktHjslZxhkDTO.setSqrxb( req.getSqrxb() );
        rktHjslZxhkDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslZxhkDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslZxhkDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslZxhkDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslZxhkDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslZxhkDTO.setSqrq( req.getSqrq() );
        rktHjslZxhkDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslZxhkDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslZxhkDTO.setSprxm( req.getSprxm() );
        rktHjslZxhkDTO.setSpsj( req.getSpsj() );
        rktHjslZxhkDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslZxhkDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslZxhkDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslZxhkDTO.setSlrxm( req.getSlrxm() );
        rktHjslZxhkDTO.setSlsj( req.getSlsj() );
        rktHjslZxhkDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslZxhkDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslZxhkDTO.setJcwh( req.getJcwh() );
        rktHjslZxhkDTO.setBz( req.getBz() );
        rktHjslZxhkDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslZxhkDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslZxhkDTO.setQtssxq( req.getQtssxq() );
        rktHjslZxhkDTO.setQtzz( req.getQtzz() );

        return rktHjslZxhkDTO;
    }

    @Override
    public RktHjslZxhkDTO convertToDTO(RktHjslZxhkCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslZxhkDTO rktHjslZxhkDTO = new RktHjslZxhkDTO();

        rktHjslZxhkDTO.setYwslh( req.getYwslh() );
        rktHjslZxhkDTO.setHh( req.getHh() );
        rktHjslZxhkDTO.setHhid( req.getHhid() );
        rktHjslZxhkDTO.setRynbid( req.getRynbid() );
        rktHjslZxhkDTO.setHlx( req.getHlx() );
        rktHjslZxhkDTO.setHmc( req.getHmc() );
        rktHjslZxhkDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslZxhkDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslZxhkDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslZxhkDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslZxhkDTO.setJlx( req.getJlx() );
        rktHjslZxhkDTO.setMlph( req.getMlph() );
        rktHjslZxhkDTO.setMlxz( req.getMlxz() );
        rktHjslZxhkDTO.setLcdyid( req.getLcdyid() );
        rktHjslZxhkDTO.setLcmc( req.getLcmc() );
        rktHjslZxhkDTO.setSpyj( req.getSpyj() );
        rktHjslZxhkDTO.setXh( req.getXh() );
        rktHjslZxhkDTO.setHhnbid( req.getHhnbid() );
        rktHjslZxhkDTO.setRyid( req.getRyid() );
        rktHjslZxhkDTO.setHkxz( req.getHkxz() );
        rktHjslZxhkDTO.setHb( req.getHb() );
        rktHjslZxhkDTO.setYhzgx( req.getYhzgx() );
        rktHjslZxhkDTO.setCxsx( req.getCxsx() );
        rktHjslZxhkDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslZxhkDTO.setXm( req.getXm() );
        rktHjslZxhkDTO.setX( req.getX() );
        rktHjslZxhkDTO.setM( req.getM() );
        rktHjslZxhkDTO.setCym( req.getCym() );
        rktHjslZxhkDTO.setXmpy( req.getXmpy() );
        rktHjslZxhkDTO.setCympy( req.getCympy() );
        rktHjslZxhkDTO.setXb( req.getXb() );
        rktHjslZxhkDTO.setMz( req.getMz() );
        rktHjslZxhkDTO.setJggjdq( req.getJggjdq() );
        rktHjslZxhkDTO.setJgssxq( req.getJgssxq() );
        rktHjslZxhkDTO.setJgxz( req.getJgxz() );
        rktHjslZxhkDTO.setCsrq( req.getCsrq() );
        rktHjslZxhkDTO.setCssj( req.getCssj() );
        rktHjslZxhkDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslZxhkDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslZxhkDTO.setCsdxz( req.getCsdxz() );
        rktHjslZxhkDTO.setWhcd( req.getWhcd() );
        rktHjslZxhkDTO.setHyzk( req.getHyzk() );
        rktHjslZxhkDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslZxhkDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslZxhkDTO.setZy( req.getZy() );
        rktHjslZxhkDTO.setZylb( req.getZylb() );
        rktHjslZxhkDTO.setZjxy( req.getZjxy() );
        rktHjslZxhkDTO.setSg( req.getSg() );
        rktHjslZxhkDTO.setXx( req.getXx() );
        rktHjslZxhkDTO.setByzk( req.getByzk() );
        rktHjslZxhkDTO.setXxjb( req.getXxjb() );
        rktHjslZxhkDTO.setLxdh( req.getLxdh() );
        rktHjslZxhkDTO.setFqxm( req.getFqxm() );
        rktHjslZxhkDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslZxhkDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslZxhkDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslZxhkDTO.setFqwwx( req.getFqwwx() );
        rktHjslZxhkDTO.setFqwwm( req.getFqwwm() );
        rktHjslZxhkDTO.setMqxm( req.getMqxm() );
        rktHjslZxhkDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslZxhkDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslZxhkDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslZxhkDTO.setMqwwx( req.getMqwwx() );
        rktHjslZxhkDTO.setMqwwm( req.getMqwwm() );
        rktHjslZxhkDTO.setPoxm( req.getPoxm() );
        rktHjslZxhkDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslZxhkDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslZxhkDTO.setPozjhm( req.getPozjhm() );
        rktHjslZxhkDTO.setPowwx( req.getPowwx() );
        rktHjslZxhkDTO.setPowwm( req.getPowwm() );
        rktHjslZxhkDTO.setJhryxm( req.getJhryxm() );
        rktHjslZxhkDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslZxhkDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslZxhkDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslZxhkDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslZxhkDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslZxhkDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslZxhkDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslZxhkDTO.setJhrexm( req.getJhrexm() );
        rktHjslZxhkDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslZxhkDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslZxhkDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslZxhkDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslZxhkDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslZxhkDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslZxhkDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslZxhkDTO.setZjlb( req.getZjlb() );
        rktHjslZxhkDTO.setQfjg( req.getQfjg() );
        rktHjslZxhkDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslZxhkDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslZxhkDTO.setZxlb( req.getZxlb() );
        rktHjslZxhkDTO.setQcrq( req.getQcrq() );
        rktHjslZxhkDTO.setQwdgjdq( req.getQwdgjdq() );
        rktHjslZxhkDTO.setQwdssxq( req.getQwdssxq() );
        rktHjslZxhkDTO.setQwdxz( req.getQwdxz() );
        rktHjslZxhkDTO.setBdfw( req.getBdfw() );
        rktHjslZxhkDTO.setQyldyy( req.getQyldyy() );
        rktHjslZxhkDTO.setZxsm( req.getZxsm() );
        rktHjslZxhkDTO.setZqzbh( req.getZqzbh() );
        rktHjslZxhkDTO.setSwzmbh( req.getSwzmbh() );
        rktHjslZxhkDTO.setLcywlx( req.getLcywlx() );
        rktHjslZxhkDTO.setLcslid( req.getLcslid() );
        rktHjslZxhkDTO.setLcywbt( req.getLcywbt() );
        rktHjslZxhkDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslZxhkDTO.setLczt( req.getLczt() );
        rktHjslZxhkDTO.setBlzt( req.getBlzt() );
        rktHjslZxhkDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslZxhkDTO.setSqrxm( req.getSqrxm() );
        rktHjslZxhkDTO.setSqrxb( req.getSqrxb() );
        rktHjslZxhkDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslZxhkDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslZxhkDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslZxhkDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslZxhkDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslZxhkDTO.setSqrq( req.getSqrq() );
        rktHjslZxhkDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslZxhkDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslZxhkDTO.setSprxm( req.getSprxm() );
        rktHjslZxhkDTO.setSpsj( req.getSpsj() );
        rktHjslZxhkDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslZxhkDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslZxhkDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslZxhkDTO.setSlrxm( req.getSlrxm() );
        rktHjslZxhkDTO.setSlsj( req.getSlsj() );
        rktHjslZxhkDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslZxhkDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslZxhkDTO.setJcwh( req.getJcwh() );
        rktHjslZxhkDTO.setBz( req.getBz() );
        rktHjslZxhkDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslZxhkDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslZxhkDTO.setQtssxq( req.getQtssxq() );
        rktHjslZxhkDTO.setQtzz( req.getQtzz() );

        return rktHjslZxhkDTO;
    }

    @Override
    public RktHjslZxhkDTO convertToDTO(RktHjslZxhkUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslZxhkDTO rktHjslZxhkDTO = new RktHjslZxhkDTO();

        rktHjslZxhkDTO.setYwslnbbh( req.getYwslnbbh() );
        rktHjslZxhkDTO.setYwslh( req.getYwslh() );
        rktHjslZxhkDTO.setHh( req.getHh() );
        rktHjslZxhkDTO.setHhid( req.getHhid() );
        rktHjslZxhkDTO.setRynbid( req.getRynbid() );
        rktHjslZxhkDTO.setHlx( req.getHlx() );
        rktHjslZxhkDTO.setHmc( req.getHmc() );
        rktHjslZxhkDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslZxhkDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslZxhkDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslZxhkDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslZxhkDTO.setJlx( req.getJlx() );
        rktHjslZxhkDTO.setMlph( req.getMlph() );
        rktHjslZxhkDTO.setMlxz( req.getMlxz() );
        rktHjslZxhkDTO.setLcdyid( req.getLcdyid() );
        rktHjslZxhkDTO.setLcmc( req.getLcmc() );
        rktHjslZxhkDTO.setSpyj( req.getSpyj() );
        rktHjslZxhkDTO.setXh( req.getXh() );
        rktHjslZxhkDTO.setHhnbid( req.getHhnbid() );
        rktHjslZxhkDTO.setRyid( req.getRyid() );
        rktHjslZxhkDTO.setHkxz( req.getHkxz() );
        rktHjslZxhkDTO.setHb( req.getHb() );
        rktHjslZxhkDTO.setYhzgx( req.getYhzgx() );
        rktHjslZxhkDTO.setCxsx( req.getCxsx() );
        rktHjslZxhkDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslZxhkDTO.setXm( req.getXm() );
        rktHjslZxhkDTO.setX( req.getX() );
        rktHjslZxhkDTO.setM( req.getM() );
        rktHjslZxhkDTO.setCym( req.getCym() );
        rktHjslZxhkDTO.setXmpy( req.getXmpy() );
        rktHjslZxhkDTO.setCympy( req.getCympy() );
        rktHjslZxhkDTO.setXb( req.getXb() );
        rktHjslZxhkDTO.setMz( req.getMz() );
        rktHjslZxhkDTO.setJggjdq( req.getJggjdq() );
        rktHjslZxhkDTO.setJgssxq( req.getJgssxq() );
        rktHjslZxhkDTO.setJgxz( req.getJgxz() );
        rktHjslZxhkDTO.setCsrq( req.getCsrq() );
        rktHjslZxhkDTO.setCssj( req.getCssj() );
        rktHjslZxhkDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslZxhkDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslZxhkDTO.setCsdxz( req.getCsdxz() );
        rktHjslZxhkDTO.setWhcd( req.getWhcd() );
        rktHjslZxhkDTO.setHyzk( req.getHyzk() );
        rktHjslZxhkDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslZxhkDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslZxhkDTO.setZy( req.getZy() );
        rktHjslZxhkDTO.setZylb( req.getZylb() );
        rktHjslZxhkDTO.setZjxy( req.getZjxy() );
        rktHjslZxhkDTO.setSg( req.getSg() );
        rktHjslZxhkDTO.setXx( req.getXx() );
        rktHjslZxhkDTO.setByzk( req.getByzk() );
        rktHjslZxhkDTO.setXxjb( req.getXxjb() );
        rktHjslZxhkDTO.setLxdh( req.getLxdh() );
        rktHjslZxhkDTO.setFqxm( req.getFqxm() );
        rktHjslZxhkDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslZxhkDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslZxhkDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslZxhkDTO.setFqwwx( req.getFqwwx() );
        rktHjslZxhkDTO.setFqwwm( req.getFqwwm() );
        rktHjslZxhkDTO.setMqxm( req.getMqxm() );
        rktHjslZxhkDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslZxhkDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslZxhkDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslZxhkDTO.setMqwwx( req.getMqwwx() );
        rktHjslZxhkDTO.setMqwwm( req.getMqwwm() );
        rktHjslZxhkDTO.setPoxm( req.getPoxm() );
        rktHjslZxhkDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslZxhkDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslZxhkDTO.setPozjhm( req.getPozjhm() );
        rktHjslZxhkDTO.setPowwx( req.getPowwx() );
        rktHjslZxhkDTO.setPowwm( req.getPowwm() );
        rktHjslZxhkDTO.setJhryxm( req.getJhryxm() );
        rktHjslZxhkDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslZxhkDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslZxhkDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslZxhkDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslZxhkDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslZxhkDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslZxhkDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslZxhkDTO.setJhrexm( req.getJhrexm() );
        rktHjslZxhkDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslZxhkDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslZxhkDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslZxhkDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslZxhkDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslZxhkDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslZxhkDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslZxhkDTO.setZjlb( req.getZjlb() );
        rktHjslZxhkDTO.setQfjg( req.getQfjg() );
        rktHjslZxhkDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslZxhkDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslZxhkDTO.setZxlb( req.getZxlb() );
        rktHjslZxhkDTO.setQcrq( req.getQcrq() );
        rktHjslZxhkDTO.setQwdgjdq( req.getQwdgjdq() );
        rktHjslZxhkDTO.setQwdssxq( req.getQwdssxq() );
        rktHjslZxhkDTO.setQwdxz( req.getQwdxz() );
        rktHjslZxhkDTO.setBdfw( req.getBdfw() );
        rktHjslZxhkDTO.setQyldyy( req.getQyldyy() );
        rktHjslZxhkDTO.setZxsm( req.getZxsm() );
        rktHjslZxhkDTO.setZqzbh( req.getZqzbh() );
        rktHjslZxhkDTO.setSwzmbh( req.getSwzmbh() );
        rktHjslZxhkDTO.setLcywlx( req.getLcywlx() );
        rktHjslZxhkDTO.setLcslid( req.getLcslid() );
        rktHjslZxhkDTO.setLcywbt( req.getLcywbt() );
        rktHjslZxhkDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslZxhkDTO.setLczt( req.getLczt() );
        rktHjslZxhkDTO.setBlzt( req.getBlzt() );
        rktHjslZxhkDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslZxhkDTO.setSqrxm( req.getSqrxm() );
        rktHjslZxhkDTO.setSqrxb( req.getSqrxb() );
        rktHjslZxhkDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslZxhkDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslZxhkDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslZxhkDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslZxhkDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslZxhkDTO.setSqrq( req.getSqrq() );
        rktHjslZxhkDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslZxhkDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslZxhkDTO.setSprxm( req.getSprxm() );
        rktHjslZxhkDTO.setSpsj( req.getSpsj() );
        rktHjslZxhkDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslZxhkDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslZxhkDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslZxhkDTO.setSlrxm( req.getSlrxm() );
        rktHjslZxhkDTO.setSlsj( req.getSlsj() );
        rktHjslZxhkDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslZxhkDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslZxhkDTO.setJcwh( req.getJcwh() );
        rktHjslZxhkDTO.setBz( req.getBz() );
        rktHjslZxhkDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslZxhkDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslZxhkDTO.setQtssxq( req.getQtssxq() );
        rktHjslZxhkDTO.setQtzz( req.getQtzz() );

        return rktHjslZxhkDTO;
    }

    @Override
    public RktHjslZxhkPageResp convertToPageResp(RktHjslZxhkDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslZxhkPageResp rktHjslZxhkPageResp = new RktHjslZxhkPageResp();

        rktHjslZxhkPageResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslZxhkPageResp.setYwslh( dto.getYwslh() );
        rktHjslZxhkPageResp.setHh( dto.getHh() );
        rktHjslZxhkPageResp.setHhid( dto.getHhid() );
        rktHjslZxhkPageResp.setRynbid( dto.getRynbid() );
        rktHjslZxhkPageResp.setHlx( dto.getHlx() );
        rktHjslZxhkPageResp.setHmc( dto.getHmc() );
        rktHjslZxhkPageResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslZxhkPageResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslZxhkPageResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslZxhkPageResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslZxhkPageResp.setJlx( dto.getJlx() );
        rktHjslZxhkPageResp.setMlph( dto.getMlph() );
        rktHjslZxhkPageResp.setMlxz( dto.getMlxz() );
        rktHjslZxhkPageResp.setLcdyid( dto.getLcdyid() );
        rktHjslZxhkPageResp.setLcmc( dto.getLcmc() );
        rktHjslZxhkPageResp.setSpyj( dto.getSpyj() );
        rktHjslZxhkPageResp.setXh( dto.getXh() );
        rktHjslZxhkPageResp.setHhnbid( dto.getHhnbid() );
        rktHjslZxhkPageResp.setRyid( dto.getRyid() );
        rktHjslZxhkPageResp.setHkxz( dto.getHkxz() );
        rktHjslZxhkPageResp.setHb( dto.getHb() );
        rktHjslZxhkPageResp.setYhzgx( dto.getYhzgx() );
        rktHjslZxhkPageResp.setCxsx( dto.getCxsx() );
        rktHjslZxhkPageResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslZxhkPageResp.setXm( dto.getXm() );
        rktHjslZxhkPageResp.setX( dto.getX() );
        rktHjslZxhkPageResp.setM( dto.getM() );
        rktHjslZxhkPageResp.setCym( dto.getCym() );
        rktHjslZxhkPageResp.setXmpy( dto.getXmpy() );
        rktHjslZxhkPageResp.setCympy( dto.getCympy() );
        rktHjslZxhkPageResp.setXb( dto.getXb() );
        rktHjslZxhkPageResp.setMz( dto.getMz() );
        rktHjslZxhkPageResp.setJggjdq( dto.getJggjdq() );
        rktHjslZxhkPageResp.setJgssxq( dto.getJgssxq() );
        rktHjslZxhkPageResp.setJgxz( dto.getJgxz() );
        rktHjslZxhkPageResp.setCsrq( dto.getCsrq() );
        rktHjslZxhkPageResp.setCssj( dto.getCssj() );
        rktHjslZxhkPageResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslZxhkPageResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslZxhkPageResp.setCsdxz( dto.getCsdxz() );
        rktHjslZxhkPageResp.setWhcd( dto.getWhcd() );
        rktHjslZxhkPageResp.setHyzk( dto.getHyzk() );
        rktHjslZxhkPageResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslZxhkPageResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslZxhkPageResp.setZy( dto.getZy() );
        rktHjslZxhkPageResp.setZylb( dto.getZylb() );
        rktHjslZxhkPageResp.setZjxy( dto.getZjxy() );
        rktHjslZxhkPageResp.setSg( dto.getSg() );
        rktHjslZxhkPageResp.setXx( dto.getXx() );
        rktHjslZxhkPageResp.setByzk( dto.getByzk() );
        rktHjslZxhkPageResp.setXxjb( dto.getXxjb() );
        rktHjslZxhkPageResp.setLxdh( dto.getLxdh() );
        rktHjslZxhkPageResp.setFqxm( dto.getFqxm() );
        rktHjslZxhkPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslZxhkPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslZxhkPageResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslZxhkPageResp.setFqwwx( dto.getFqwwx() );
        rktHjslZxhkPageResp.setFqwwm( dto.getFqwwm() );
        rktHjslZxhkPageResp.setMqxm( dto.getMqxm() );
        rktHjslZxhkPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslZxhkPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslZxhkPageResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslZxhkPageResp.setMqwwx( dto.getMqwwx() );
        rktHjslZxhkPageResp.setMqwwm( dto.getMqwwm() );
        rktHjslZxhkPageResp.setPoxm( dto.getPoxm() );
        rktHjslZxhkPageResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslZxhkPageResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslZxhkPageResp.setPozjhm( dto.getPozjhm() );
        rktHjslZxhkPageResp.setPowwx( dto.getPowwx() );
        rktHjslZxhkPageResp.setPowwm( dto.getPowwm() );
        rktHjslZxhkPageResp.setJhryxm( dto.getJhryxm() );
        rktHjslZxhkPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslZxhkPageResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslZxhkPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslZxhkPageResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslZxhkPageResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslZxhkPageResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslZxhkPageResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslZxhkPageResp.setJhrexm( dto.getJhrexm() );
        rktHjslZxhkPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslZxhkPageResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslZxhkPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslZxhkPageResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslZxhkPageResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslZxhkPageResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslZxhkPageResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslZxhkPageResp.setZjlb( dto.getZjlb() );
        rktHjslZxhkPageResp.setQfjg( dto.getQfjg() );
        rktHjslZxhkPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslZxhkPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslZxhkPageResp.setZxlb( dto.getZxlb() );
        rktHjslZxhkPageResp.setQcrq( dto.getQcrq() );
        rktHjslZxhkPageResp.setQwdgjdq( dto.getQwdgjdq() );
        rktHjslZxhkPageResp.setQwdssxq( dto.getQwdssxq() );
        rktHjslZxhkPageResp.setQwdxz( dto.getQwdxz() );
        rktHjslZxhkPageResp.setBdfw( dto.getBdfw() );
        rktHjslZxhkPageResp.setQyldyy( dto.getQyldyy() );
        rktHjslZxhkPageResp.setZxsm( dto.getZxsm() );
        rktHjslZxhkPageResp.setZqzbh( dto.getZqzbh() );
        rktHjslZxhkPageResp.setSwzmbh( dto.getSwzmbh() );
        rktHjslZxhkPageResp.setLcywlx( dto.getLcywlx() );
        rktHjslZxhkPageResp.setLcslid( dto.getLcslid() );
        rktHjslZxhkPageResp.setLcywbt( dto.getLcywbt() );
        rktHjslZxhkPageResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslZxhkPageResp.setLczt( dto.getLczt() );
        rktHjslZxhkPageResp.setBlzt( dto.getBlzt() );
        rktHjslZxhkPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslZxhkPageResp.setSqrxm( dto.getSqrxm() );
        rktHjslZxhkPageResp.setSqrxb( dto.getSqrxb() );
        rktHjslZxhkPageResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslZxhkPageResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslZxhkPageResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslZxhkPageResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslZxhkPageResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslZxhkPageResp.setSqrq( dto.getSqrq() );
        rktHjslZxhkPageResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslZxhkPageResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslZxhkPageResp.setSprxm( dto.getSprxm() );
        rktHjslZxhkPageResp.setSpsj( dto.getSpsj() );
        rktHjslZxhkPageResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslZxhkPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslZxhkPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslZxhkPageResp.setSlrxm( dto.getSlrxm() );
        rktHjslZxhkPageResp.setSlsj( dto.getSlsj() );
        rktHjslZxhkPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslZxhkPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslZxhkPageResp.setJcwh( dto.getJcwh() );
        rktHjslZxhkPageResp.setBz( dto.getBz() );
        rktHjslZxhkPageResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslZxhkPageResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslZxhkPageResp.setQtssxq( dto.getQtssxq() );
        rktHjslZxhkPageResp.setQtzz( dto.getQtzz() );

        return rktHjslZxhkPageResp;
    }

    @Override
    public RktHjslZxhkViewResp convertToViewResp(RktHjslZxhkDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslZxhkViewResp rktHjslZxhkViewResp = new RktHjslZxhkViewResp();

        rktHjslZxhkViewResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslZxhkViewResp.setYwslh( dto.getYwslh() );
        rktHjslZxhkViewResp.setHh( dto.getHh() );
        rktHjslZxhkViewResp.setHhid( dto.getHhid() );
        rktHjslZxhkViewResp.setRynbid( dto.getRynbid() );
        rktHjslZxhkViewResp.setHlx( dto.getHlx() );
        rktHjslZxhkViewResp.setHmc( dto.getHmc() );
        rktHjslZxhkViewResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslZxhkViewResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslZxhkViewResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslZxhkViewResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslZxhkViewResp.setJlx( dto.getJlx() );
        rktHjslZxhkViewResp.setMlph( dto.getMlph() );
        rktHjslZxhkViewResp.setMlxz( dto.getMlxz() );
        rktHjslZxhkViewResp.setLcdyid( dto.getLcdyid() );
        rktHjslZxhkViewResp.setLcmc( dto.getLcmc() );
        rktHjslZxhkViewResp.setSpyj( dto.getSpyj() );
        rktHjslZxhkViewResp.setXh( dto.getXh() );
        rktHjslZxhkViewResp.setHhnbid( dto.getHhnbid() );
        rktHjslZxhkViewResp.setRyid( dto.getRyid() );
        rktHjslZxhkViewResp.setHkxz( dto.getHkxz() );
        rktHjslZxhkViewResp.setHb( dto.getHb() );
        rktHjslZxhkViewResp.setYhzgx( dto.getYhzgx() );
        rktHjslZxhkViewResp.setCxsx( dto.getCxsx() );
        rktHjslZxhkViewResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslZxhkViewResp.setXm( dto.getXm() );
        rktHjslZxhkViewResp.setX( dto.getX() );
        rktHjslZxhkViewResp.setM( dto.getM() );
        rktHjslZxhkViewResp.setCym( dto.getCym() );
        rktHjslZxhkViewResp.setXmpy( dto.getXmpy() );
        rktHjslZxhkViewResp.setCympy( dto.getCympy() );
        rktHjslZxhkViewResp.setXb( dto.getXb() );
        rktHjslZxhkViewResp.setMz( dto.getMz() );
        rktHjslZxhkViewResp.setJggjdq( dto.getJggjdq() );
        rktHjslZxhkViewResp.setJgssxq( dto.getJgssxq() );
        rktHjslZxhkViewResp.setJgxz( dto.getJgxz() );
        rktHjslZxhkViewResp.setCsrq( dto.getCsrq() );
        rktHjslZxhkViewResp.setCssj( dto.getCssj() );
        rktHjslZxhkViewResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslZxhkViewResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslZxhkViewResp.setCsdxz( dto.getCsdxz() );
        rktHjslZxhkViewResp.setWhcd( dto.getWhcd() );
        rktHjslZxhkViewResp.setHyzk( dto.getHyzk() );
        rktHjslZxhkViewResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslZxhkViewResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslZxhkViewResp.setZy( dto.getZy() );
        rktHjslZxhkViewResp.setZylb( dto.getZylb() );
        rktHjslZxhkViewResp.setZjxy( dto.getZjxy() );
        rktHjslZxhkViewResp.setSg( dto.getSg() );
        rktHjslZxhkViewResp.setXx( dto.getXx() );
        rktHjslZxhkViewResp.setByzk( dto.getByzk() );
        rktHjslZxhkViewResp.setXxjb( dto.getXxjb() );
        rktHjslZxhkViewResp.setLxdh( dto.getLxdh() );
        rktHjslZxhkViewResp.setFqxm( dto.getFqxm() );
        rktHjslZxhkViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslZxhkViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslZxhkViewResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslZxhkViewResp.setFqwwx( dto.getFqwwx() );
        rktHjslZxhkViewResp.setFqwwm( dto.getFqwwm() );
        rktHjslZxhkViewResp.setMqxm( dto.getMqxm() );
        rktHjslZxhkViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslZxhkViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslZxhkViewResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslZxhkViewResp.setMqwwx( dto.getMqwwx() );
        rktHjslZxhkViewResp.setMqwwm( dto.getMqwwm() );
        rktHjslZxhkViewResp.setPoxm( dto.getPoxm() );
        rktHjslZxhkViewResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslZxhkViewResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslZxhkViewResp.setPozjhm( dto.getPozjhm() );
        rktHjslZxhkViewResp.setPowwx( dto.getPowwx() );
        rktHjslZxhkViewResp.setPowwm( dto.getPowwm() );
        rktHjslZxhkViewResp.setJhryxm( dto.getJhryxm() );
        rktHjslZxhkViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslZxhkViewResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslZxhkViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslZxhkViewResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslZxhkViewResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslZxhkViewResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslZxhkViewResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslZxhkViewResp.setJhrexm( dto.getJhrexm() );
        rktHjslZxhkViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslZxhkViewResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslZxhkViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslZxhkViewResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslZxhkViewResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslZxhkViewResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslZxhkViewResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslZxhkViewResp.setZjlb( dto.getZjlb() );
        rktHjslZxhkViewResp.setQfjg( dto.getQfjg() );
        rktHjslZxhkViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslZxhkViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslZxhkViewResp.setZxlb( dto.getZxlb() );
        rktHjslZxhkViewResp.setQcrq( dto.getQcrq() );
        rktHjslZxhkViewResp.setQwdgjdq( dto.getQwdgjdq() );
        rktHjslZxhkViewResp.setQwdssxq( dto.getQwdssxq() );
        rktHjslZxhkViewResp.setQwdxz( dto.getQwdxz() );
        rktHjslZxhkViewResp.setBdfw( dto.getBdfw() );
        rktHjslZxhkViewResp.setQyldyy( dto.getQyldyy() );
        rktHjslZxhkViewResp.setZxsm( dto.getZxsm() );
        rktHjslZxhkViewResp.setZqzbh( dto.getZqzbh() );
        rktHjslZxhkViewResp.setSwzmbh( dto.getSwzmbh() );
        rktHjslZxhkViewResp.setLcywlx( dto.getLcywlx() );
        rktHjslZxhkViewResp.setLcslid( dto.getLcslid() );
        rktHjslZxhkViewResp.setLcywbt( dto.getLcywbt() );
        rktHjslZxhkViewResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslZxhkViewResp.setLczt( dto.getLczt() );
        rktHjslZxhkViewResp.setBlzt( dto.getBlzt() );
        rktHjslZxhkViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslZxhkViewResp.setSqrxm( dto.getSqrxm() );
        rktHjslZxhkViewResp.setSqrxb( dto.getSqrxb() );
        rktHjslZxhkViewResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslZxhkViewResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslZxhkViewResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslZxhkViewResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslZxhkViewResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslZxhkViewResp.setSqrq( dto.getSqrq() );
        rktHjslZxhkViewResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslZxhkViewResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslZxhkViewResp.setSprxm( dto.getSprxm() );
        rktHjslZxhkViewResp.setSpsj( dto.getSpsj() );
        rktHjslZxhkViewResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslZxhkViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslZxhkViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslZxhkViewResp.setSlrxm( dto.getSlrxm() );
        rktHjslZxhkViewResp.setSlsj( dto.getSlsj() );
        rktHjslZxhkViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslZxhkViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslZxhkViewResp.setJcwh( dto.getJcwh() );
        rktHjslZxhkViewResp.setBz( dto.getBz() );
        rktHjslZxhkViewResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslZxhkViewResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslZxhkViewResp.setQtssxq( dto.getQtssxq() );
        rktHjslZxhkViewResp.setQtzz( dto.getQtzz() );

        return rktHjslZxhkViewResp;
    }

    @Override
    public RktHjslZxhkCreateResp convertToCreateResp(RktHjslZxhkDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslZxhkCreateResp rktHjslZxhkCreateResp = new RktHjslZxhkCreateResp();

        rktHjslZxhkCreateResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslZxhkCreateResp.setYwslh( dto.getYwslh() );
        rktHjslZxhkCreateResp.setHh( dto.getHh() );
        rktHjslZxhkCreateResp.setHhid( dto.getHhid() );
        rktHjslZxhkCreateResp.setRynbid( dto.getRynbid() );
        rktHjslZxhkCreateResp.setHlx( dto.getHlx() );
        rktHjslZxhkCreateResp.setHmc( dto.getHmc() );
        rktHjslZxhkCreateResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslZxhkCreateResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslZxhkCreateResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslZxhkCreateResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslZxhkCreateResp.setJlx( dto.getJlx() );
        rktHjslZxhkCreateResp.setMlph( dto.getMlph() );
        rktHjslZxhkCreateResp.setMlxz( dto.getMlxz() );
        rktHjslZxhkCreateResp.setLcdyid( dto.getLcdyid() );
        rktHjslZxhkCreateResp.setLcmc( dto.getLcmc() );
        rktHjslZxhkCreateResp.setSpyj( dto.getSpyj() );
        rktHjslZxhkCreateResp.setXh( dto.getXh() );
        rktHjslZxhkCreateResp.setHhnbid( dto.getHhnbid() );
        rktHjslZxhkCreateResp.setRyid( dto.getRyid() );
        rktHjslZxhkCreateResp.setHkxz( dto.getHkxz() );
        rktHjslZxhkCreateResp.setHb( dto.getHb() );
        rktHjslZxhkCreateResp.setYhzgx( dto.getYhzgx() );
        rktHjslZxhkCreateResp.setCxsx( dto.getCxsx() );
        rktHjslZxhkCreateResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslZxhkCreateResp.setXm( dto.getXm() );
        rktHjslZxhkCreateResp.setX( dto.getX() );
        rktHjslZxhkCreateResp.setM( dto.getM() );
        rktHjslZxhkCreateResp.setCym( dto.getCym() );
        rktHjslZxhkCreateResp.setXmpy( dto.getXmpy() );
        rktHjslZxhkCreateResp.setCympy( dto.getCympy() );
        rktHjslZxhkCreateResp.setXb( dto.getXb() );
        rktHjslZxhkCreateResp.setMz( dto.getMz() );
        rktHjslZxhkCreateResp.setJggjdq( dto.getJggjdq() );
        rktHjslZxhkCreateResp.setJgssxq( dto.getJgssxq() );
        rktHjslZxhkCreateResp.setJgxz( dto.getJgxz() );
        rktHjslZxhkCreateResp.setCsrq( dto.getCsrq() );
        rktHjslZxhkCreateResp.setCssj( dto.getCssj() );
        rktHjslZxhkCreateResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslZxhkCreateResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslZxhkCreateResp.setCsdxz( dto.getCsdxz() );
        rktHjslZxhkCreateResp.setWhcd( dto.getWhcd() );
        rktHjslZxhkCreateResp.setHyzk( dto.getHyzk() );
        rktHjslZxhkCreateResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslZxhkCreateResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslZxhkCreateResp.setZy( dto.getZy() );
        rktHjslZxhkCreateResp.setZylb( dto.getZylb() );
        rktHjslZxhkCreateResp.setZjxy( dto.getZjxy() );
        rktHjslZxhkCreateResp.setSg( dto.getSg() );
        rktHjslZxhkCreateResp.setXx( dto.getXx() );
        rktHjslZxhkCreateResp.setByzk( dto.getByzk() );
        rktHjslZxhkCreateResp.setXxjb( dto.getXxjb() );
        rktHjslZxhkCreateResp.setLxdh( dto.getLxdh() );
        rktHjslZxhkCreateResp.setFqxm( dto.getFqxm() );
        rktHjslZxhkCreateResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslZxhkCreateResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslZxhkCreateResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslZxhkCreateResp.setFqwwx( dto.getFqwwx() );
        rktHjslZxhkCreateResp.setFqwwm( dto.getFqwwm() );
        rktHjslZxhkCreateResp.setMqxm( dto.getMqxm() );
        rktHjslZxhkCreateResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslZxhkCreateResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslZxhkCreateResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslZxhkCreateResp.setMqwwx( dto.getMqwwx() );
        rktHjslZxhkCreateResp.setMqwwm( dto.getMqwwm() );
        rktHjslZxhkCreateResp.setPoxm( dto.getPoxm() );
        rktHjslZxhkCreateResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslZxhkCreateResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslZxhkCreateResp.setPozjhm( dto.getPozjhm() );
        rktHjslZxhkCreateResp.setPowwx( dto.getPowwx() );
        rktHjslZxhkCreateResp.setPowwm( dto.getPowwm() );
        rktHjslZxhkCreateResp.setJhryxm( dto.getJhryxm() );
        rktHjslZxhkCreateResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslZxhkCreateResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslZxhkCreateResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslZxhkCreateResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslZxhkCreateResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslZxhkCreateResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslZxhkCreateResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslZxhkCreateResp.setJhrexm( dto.getJhrexm() );
        rktHjslZxhkCreateResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslZxhkCreateResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslZxhkCreateResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslZxhkCreateResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslZxhkCreateResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslZxhkCreateResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslZxhkCreateResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslZxhkCreateResp.setZjlb( dto.getZjlb() );
        rktHjslZxhkCreateResp.setQfjg( dto.getQfjg() );
        rktHjslZxhkCreateResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslZxhkCreateResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslZxhkCreateResp.setZxlb( dto.getZxlb() );
        rktHjslZxhkCreateResp.setQcrq( dto.getQcrq() );
        rktHjslZxhkCreateResp.setQwdgjdq( dto.getQwdgjdq() );
        rktHjslZxhkCreateResp.setQwdssxq( dto.getQwdssxq() );
        rktHjslZxhkCreateResp.setQwdxz( dto.getQwdxz() );
        rktHjslZxhkCreateResp.setBdfw( dto.getBdfw() );
        rktHjslZxhkCreateResp.setQyldyy( dto.getQyldyy() );
        rktHjslZxhkCreateResp.setZxsm( dto.getZxsm() );
        rktHjslZxhkCreateResp.setZqzbh( dto.getZqzbh() );
        rktHjslZxhkCreateResp.setSwzmbh( dto.getSwzmbh() );
        rktHjslZxhkCreateResp.setLcywlx( dto.getLcywlx() );
        rktHjslZxhkCreateResp.setLcslid( dto.getLcslid() );
        rktHjslZxhkCreateResp.setLcywbt( dto.getLcywbt() );
        rktHjslZxhkCreateResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslZxhkCreateResp.setLczt( dto.getLczt() );
        rktHjslZxhkCreateResp.setBlzt( dto.getBlzt() );
        rktHjslZxhkCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslZxhkCreateResp.setSqrxm( dto.getSqrxm() );
        rktHjslZxhkCreateResp.setSqrxb( dto.getSqrxb() );
        rktHjslZxhkCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslZxhkCreateResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslZxhkCreateResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslZxhkCreateResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslZxhkCreateResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslZxhkCreateResp.setSqrq( dto.getSqrq() );
        rktHjslZxhkCreateResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslZxhkCreateResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslZxhkCreateResp.setSprxm( dto.getSprxm() );
        rktHjslZxhkCreateResp.setSpsj( dto.getSpsj() );
        rktHjslZxhkCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslZxhkCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslZxhkCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslZxhkCreateResp.setSlrxm( dto.getSlrxm() );
        rktHjslZxhkCreateResp.setSlsj( dto.getSlsj() );
        rktHjslZxhkCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslZxhkCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslZxhkCreateResp.setJcwh( dto.getJcwh() );
        rktHjslZxhkCreateResp.setBz( dto.getBz() );
        rktHjslZxhkCreateResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslZxhkCreateResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslZxhkCreateResp.setQtssxq( dto.getQtssxq() );
        rktHjslZxhkCreateResp.setQtzz( dto.getQtzz() );

        return rktHjslZxhkCreateResp;
    }
}
