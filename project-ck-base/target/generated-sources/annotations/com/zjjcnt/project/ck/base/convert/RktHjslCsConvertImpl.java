package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktHjslCsDTO;
import com.zjjcnt.project.ck.base.dto.req.RktHjslCsCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslCsPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslCsUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslCsCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslCsPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslCsViewResp;
import com.zjjcnt.project.ck.base.entity.RktHjslCsDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:42+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktHjslCsConvertImpl implements RktHjslCsConvert {

    @Override
    public RktHjslCsDTO convert(RktHjslCsDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktHjslCsDTO rktHjslCsDTO = new RktHjslCsDTO();

        rktHjslCsDTO.setId( entity.getId() );
        rktHjslCsDTO.setYwslnbbh( entity.getYwslnbbh() );
        rktHjslCsDTO.setYwslh( entity.getYwslh() );
        rktHjslCsDTO.setHmc( entity.getHmc() );
        rktHjslCsDTO.setJlx( entity.getJlx() );
        rktHjslCsDTO.setMlph( entity.getMlph() );
        rktHjslCsDTO.setMlxz( entity.getMlxz() );
        rktHjslCsDTO.setLcdyid( entity.getLcdyid() );
        rktHjslCsDTO.setLcmc( entity.getLcmc() );
        rktHjslCsDTO.setSpyj( entity.getSpyj() );
        rktHjslCsDTO.setXh( entity.getXh() );
        rktHjslCsDTO.setZjlb( entity.getZjlb() );
        rktHjslCsDTO.setQfjg( entity.getQfjg() );
        rktHjslCsDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        rktHjslCsDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        rktHjslCsDTO.setCsdjlbdm( entity.getCsdjlbdm() );
        rktHjslCsDTO.setCsdjlb( entity.getCsdjlb() );
        rktHjslCsDTO.setHb( entity.getHb() );
        rktHjslCsDTO.setCxsx( entity.getCxsx() );
        rktHjslCsDTO.setGmsfhm( entity.getGmsfhm() );
        rktHjslCsDTO.setXm( entity.getXm() );
        rktHjslCsDTO.setX( entity.getX() );
        rktHjslCsDTO.setM( entity.getM() );
        rktHjslCsDTO.setCym( entity.getCym() );
        rktHjslCsDTO.setXmpy( entity.getXmpy() );
        rktHjslCsDTO.setCympy( entity.getCympy() );
        rktHjslCsDTO.setXb( entity.getXb() );
        rktHjslCsDTO.setMz( entity.getMz() );
        rktHjslCsDTO.setCsrq( entity.getCsrq() );
        rktHjslCsDTO.setCssj( entity.getCssj() );
        rktHjslCsDTO.setCsdgjdq( entity.getCsdgjdq() );
        rktHjslCsDTO.setCsdssxq( entity.getCsdssxq() );
        rktHjslCsDTO.setCsdxz( entity.getCsdxz() );
        rktHjslCsDTO.setCszmbh( entity.getCszmbh() );
        rktHjslCsDTO.setCszqfrq( entity.getCszqfrq() );
        rktHjslCsDTO.setJggjdq( entity.getJggjdq() );
        rktHjslCsDTO.setJgssxq( entity.getJgssxq() );
        rktHjslCsDTO.setJgxz( entity.getJgxz() );
        rktHjslCsDTO.setHh( entity.getHh() );
        rktHjslCsDTO.setHhnbid( entity.getHhnbid() );
        rktHjslCsDTO.setHhid( entity.getHhid() );
        rktHjslCsDTO.setHlx( entity.getHlx() );
        rktHjslCsDTO.setYhzgx( entity.getYhzgx() );
        rktHjslCsDTO.setHjddzbm( entity.getHjddzbm() );
        rktHjslCsDTO.setHjdssxq( entity.getHjdssxq() );
        rktHjslCsDTO.setHjdxxdz( entity.getHjdxxdz() );
        rktHjslCsDTO.setHjdrhyzbs( entity.getHjdrhyzbs() );
        rktHjslCsDTO.setFqmz( entity.getFqmz() );
        rktHjslCsDTO.setFqjggjdq( entity.getFqjggjdq() );
        rktHjslCsDTO.setFqjgssxq( entity.getFqjgssxq() );
        rktHjslCsDTO.setFqzt( entity.getFqzt() );
        rktHjslCsDTO.setFqdhhm( entity.getFqdhhm() );
        rktHjslCsDTO.setMqmz( entity.getMqmz() );
        rktHjslCsDTO.setMqjggjdq( entity.getMqjggjdq() );
        rktHjslCsDTO.setMqjgssxq( entity.getMqjgssxq() );
        rktHjslCsDTO.setMqzt( entity.getMqzt() );
        rktHjslCsDTO.setMqdhhm( entity.getMqdhhm() );
        rktHjslCsDTO.setHkxz( entity.getHkxz() );
        rktHjslCsDTO.setWhcd( entity.getWhcd() );
        rktHjslCsDTO.setHyzk( entity.getHyzk() );
        rktHjslCsDTO.setCyzkdwbm( entity.getCyzkdwbm() );
        rktHjslCsDTO.setCyzkdwmc( entity.getCyzkdwmc() );
        rktHjslCsDTO.setZy( entity.getZy() );
        rktHjslCsDTO.setZylb( entity.getZylb() );
        rktHjslCsDTO.setZjxy( entity.getZjxy() );
        rktHjslCsDTO.setSg( entity.getSg() );
        rktHjslCsDTO.setXx( entity.getXx() );
        rktHjslCsDTO.setByzk( entity.getByzk() );
        rktHjslCsDTO.setLxdh( entity.getLxdh() );
        rktHjslCsDTO.setFqxm( entity.getFqxm() );
        rktHjslCsDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        rktHjslCsDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        rktHjslCsDTO.setFqzjhm( entity.getFqzjhm() );
        rktHjslCsDTO.setFqwwx( entity.getFqwwx() );
        rktHjslCsDTO.setFqwwm( entity.getFqwwm() );
        rktHjslCsDTO.setMqxm( entity.getMqxm() );
        rktHjslCsDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        rktHjslCsDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        rktHjslCsDTO.setMqzjhm( entity.getMqzjhm() );
        rktHjslCsDTO.setMqwwx( entity.getMqwwx() );
        rktHjslCsDTO.setMqwwm( entity.getMqwwm() );
        rktHjslCsDTO.setPoxm( entity.getPoxm() );
        rktHjslCsDTO.setPogmsfhm( entity.getPogmsfhm() );
        rktHjslCsDTO.setPocyzjdm( entity.getPocyzjdm() );
        rktHjslCsDTO.setPozjhm( entity.getPozjhm() );
        rktHjslCsDTO.setPowwx( entity.getPowwx() );
        rktHjslCsDTO.setPowwm( entity.getPowwm() );
        rktHjslCsDTO.setJhryxm( entity.getJhryxm() );
        rktHjslCsDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        rktHjslCsDTO.setJhryjhgx( entity.getJhryjhgx() );
        rktHjslCsDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        rktHjslCsDTO.setJhryzjhm( entity.getJhryzjhm() );
        rktHjslCsDTO.setJhrywwx( entity.getJhrywwx() );
        rktHjslCsDTO.setJhrywwm( entity.getJhrywwm() );
        rktHjslCsDTO.setJhrylxdh( entity.getJhrylxdh() );
        rktHjslCsDTO.setJhrexm( entity.getJhrexm() );
        rktHjslCsDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        rktHjslCsDTO.setJhrejhgx( entity.getJhrejhgx() );
        rktHjslCsDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        rktHjslCsDTO.setJhrezjhm( entity.getJhrezjhm() );
        rktHjslCsDTO.setJhrewwx( entity.getJhrewwx() );
        rktHjslCsDTO.setJhrewwm( entity.getJhrewwm() );
        rktHjslCsDTO.setJhrelxdh( entity.getJhrelxdh() );
        rktHjslCsDTO.setRylb( entity.getRylb() );
        rktHjslCsDTO.setXxjb( entity.getXxjb() );
        rktHjslCsDTO.setLcywlx( entity.getLcywlx() );
        rktHjslCsDTO.setLcslid( entity.getLcslid() );
        rktHjslCsDTO.setLcywbt( entity.getLcywbt() );
        rktHjslCsDTO.setLcrwjd( entity.getLcrwjd() );
        rktHjslCsDTO.setLczt( entity.getLczt() );
        rktHjslCsDTO.setBlzt( entity.getBlzt() );
        rktHjslCsDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        rktHjslCsDTO.setSqrxm( entity.getSqrxm() );
        rktHjslCsDTO.setSqrxb( entity.getSqrxb() );
        rktHjslCsDTO.setSqrlxdh( entity.getSqrlxdh() );
        rktHjslCsDTO.setSqrybdrgx( entity.getSqrybdrgx() );
        rktHjslCsDTO.setSqrzzssxq( entity.getSqrzzssxq() );
        rktHjslCsDTO.setSqrzzxz( entity.getSqrzzxz() );
        rktHjslCsDTO.setSqrhkdjjg( entity.getSqrhkdjjg() );
        rktHjslCsDTO.setSqrq( entity.getSqrq() );
        rktHjslCsDTO.setSpdwgajgjgdm( entity.getSpdwgajgjgdm() );
        rktHjslCsDTO.setSpdwgajgmc( entity.getSpdwgajgmc() );
        rktHjslCsDTO.setSprxm( entity.getSprxm() );
        rktHjslCsDTO.setSpsj( entity.getSpsj() );
        rktHjslCsDTO.setSpjgdm( entity.getSpjgdm() );
        rktHjslCsDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        rktHjslCsDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        rktHjslCsDTO.setSlrxm( entity.getSlrxm() );
        rktHjslCsDTO.setSlsj( entity.getSlsj() );
        rktHjslCsDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        rktHjslCsDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        rktHjslCsDTO.setJcwh( entity.getJcwh() );
        rktHjslCsDTO.setBz( entity.getBz() );
        rktHjslCsDTO.setBlhjywid( entity.getBlhjywid() );
        rktHjslCsDTO.setHjywblsj( entity.getHjywblsj() );
        rktHjslCsDTO.setQtssxq( entity.getQtssxq() );
        rktHjslCsDTO.setQtzz( entity.getQtzz() );
        rktHjslCsDTO.setPdbzsflh( entity.getPdbzsflh() );
        rktHjslCsDTO.setPdbzcszmhmd( entity.getPdbzcszmhmd() );
        rktHjslCsDTO.setBdjg( entity.getBdjg() );
        rktHjslCsDTO.setBdms( entity.getBdms() );
        rktHjslCsDTO.setBdsj( entity.getBdsj() );
        rktHjslCsDTO.setBdrid( entity.getBdrid() );
        rktHjslCsDTO.setBdrxm( entity.getBdrxm() );
        rktHjslCsDTO.setBdrip( entity.getBdrip() );
        rktHjslCsDTO.setKscsywid( entity.getKscsywid() );

        return rktHjslCsDTO;
    }

    @Override
    public RktHjslCsDO convertToDO(RktHjslCsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslCsDO rktHjslCsDO = new RktHjslCsDO();

        rktHjslCsDO.setId( dto.getId() );
        rktHjslCsDO.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslCsDO.setYwslh( dto.getYwslh() );
        rktHjslCsDO.setHmc( dto.getHmc() );
        rktHjslCsDO.setJlx( dto.getJlx() );
        rktHjslCsDO.setMlph( dto.getMlph() );
        rktHjslCsDO.setMlxz( dto.getMlxz() );
        rktHjslCsDO.setLcdyid( dto.getLcdyid() );
        rktHjslCsDO.setLcmc( dto.getLcmc() );
        rktHjslCsDO.setSpyj( dto.getSpyj() );
        rktHjslCsDO.setXh( dto.getXh() );
        rktHjslCsDO.setZjlb( dto.getZjlb() );
        rktHjslCsDO.setQfjg( dto.getQfjg() );
        rktHjslCsDO.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslCsDO.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslCsDO.setCsdjlbdm( dto.getCsdjlbdm() );
        rktHjslCsDO.setCsdjlb( dto.getCsdjlb() );
        rktHjslCsDO.setHb( dto.getHb() );
        rktHjslCsDO.setCxsx( dto.getCxsx() );
        rktHjslCsDO.setGmsfhm( dto.getGmsfhm() );
        rktHjslCsDO.setXm( dto.getXm() );
        rktHjslCsDO.setX( dto.getX() );
        rktHjslCsDO.setM( dto.getM() );
        rktHjslCsDO.setCym( dto.getCym() );
        rktHjslCsDO.setXmpy( dto.getXmpy() );
        rktHjslCsDO.setCympy( dto.getCympy() );
        rktHjslCsDO.setXb( dto.getXb() );
        rktHjslCsDO.setMz( dto.getMz() );
        rktHjslCsDO.setCsrq( dto.getCsrq() );
        rktHjslCsDO.setCssj( dto.getCssj() );
        rktHjslCsDO.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslCsDO.setCsdssxq( dto.getCsdssxq() );
        rktHjslCsDO.setCsdxz( dto.getCsdxz() );
        rktHjslCsDO.setCszmbh( dto.getCszmbh() );
        rktHjslCsDO.setCszqfrq( dto.getCszqfrq() );
        rktHjslCsDO.setJggjdq( dto.getJggjdq() );
        rktHjslCsDO.setJgssxq( dto.getJgssxq() );
        rktHjslCsDO.setJgxz( dto.getJgxz() );
        rktHjslCsDO.setHh( dto.getHh() );
        rktHjslCsDO.setHhnbid( dto.getHhnbid() );
        rktHjslCsDO.setHhid( dto.getHhid() );
        rktHjslCsDO.setHlx( dto.getHlx() );
        rktHjslCsDO.setYhzgx( dto.getYhzgx() );
        rktHjslCsDO.setHjddzbm( dto.getHjddzbm() );
        rktHjslCsDO.setHjdssxq( dto.getHjdssxq() );
        rktHjslCsDO.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslCsDO.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslCsDO.setFqmz( dto.getFqmz() );
        rktHjslCsDO.setFqjggjdq( dto.getFqjggjdq() );
        rktHjslCsDO.setFqjgssxq( dto.getFqjgssxq() );
        rktHjslCsDO.setFqzt( dto.getFqzt() );
        rktHjslCsDO.setFqdhhm( dto.getFqdhhm() );
        rktHjslCsDO.setMqmz( dto.getMqmz() );
        rktHjslCsDO.setMqjggjdq( dto.getMqjggjdq() );
        rktHjslCsDO.setMqjgssxq( dto.getMqjgssxq() );
        rktHjslCsDO.setMqzt( dto.getMqzt() );
        rktHjslCsDO.setMqdhhm( dto.getMqdhhm() );
        rktHjslCsDO.setHkxz( dto.getHkxz() );
        rktHjslCsDO.setWhcd( dto.getWhcd() );
        rktHjslCsDO.setHyzk( dto.getHyzk() );
        rktHjslCsDO.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslCsDO.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslCsDO.setZy( dto.getZy() );
        rktHjslCsDO.setZylb( dto.getZylb() );
        rktHjslCsDO.setZjxy( dto.getZjxy() );
        rktHjslCsDO.setSg( dto.getSg() );
        rktHjslCsDO.setXx( dto.getXx() );
        rktHjslCsDO.setByzk( dto.getByzk() );
        rktHjslCsDO.setLxdh( dto.getLxdh() );
        rktHjslCsDO.setFqxm( dto.getFqxm() );
        rktHjslCsDO.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslCsDO.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslCsDO.setFqzjhm( dto.getFqzjhm() );
        rktHjslCsDO.setFqwwx( dto.getFqwwx() );
        rktHjslCsDO.setFqwwm( dto.getFqwwm() );
        rktHjslCsDO.setMqxm( dto.getMqxm() );
        rktHjslCsDO.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslCsDO.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslCsDO.setMqzjhm( dto.getMqzjhm() );
        rktHjslCsDO.setMqwwx( dto.getMqwwx() );
        rktHjslCsDO.setMqwwm( dto.getMqwwm() );
        rktHjslCsDO.setPoxm( dto.getPoxm() );
        rktHjslCsDO.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslCsDO.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslCsDO.setPozjhm( dto.getPozjhm() );
        rktHjslCsDO.setPowwx( dto.getPowwx() );
        rktHjslCsDO.setPowwm( dto.getPowwm() );
        rktHjslCsDO.setJhryxm( dto.getJhryxm() );
        rktHjslCsDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslCsDO.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslCsDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslCsDO.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslCsDO.setJhrywwx( dto.getJhrywwx() );
        rktHjslCsDO.setJhrywwm( dto.getJhrywwm() );
        rktHjslCsDO.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslCsDO.setJhrexm( dto.getJhrexm() );
        rktHjslCsDO.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslCsDO.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslCsDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslCsDO.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslCsDO.setJhrewwx( dto.getJhrewwx() );
        rktHjslCsDO.setJhrewwm( dto.getJhrewwm() );
        rktHjslCsDO.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslCsDO.setRylb( dto.getRylb() );
        rktHjslCsDO.setXxjb( dto.getXxjb() );
        rktHjslCsDO.setLcywlx( dto.getLcywlx() );
        rktHjslCsDO.setLcslid( dto.getLcslid() );
        rktHjslCsDO.setLcywbt( dto.getLcywbt() );
        rktHjslCsDO.setLcrwjd( dto.getLcrwjd() );
        rktHjslCsDO.setLczt( dto.getLczt() );
        rktHjslCsDO.setBlzt( dto.getBlzt() );
        rktHjslCsDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslCsDO.setSqrxm( dto.getSqrxm() );
        rktHjslCsDO.setSqrxb( dto.getSqrxb() );
        rktHjslCsDO.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslCsDO.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslCsDO.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslCsDO.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslCsDO.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslCsDO.setSqrq( dto.getSqrq() );
        rktHjslCsDO.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslCsDO.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslCsDO.setSprxm( dto.getSprxm() );
        rktHjslCsDO.setSpsj( dto.getSpsj() );
        rktHjslCsDO.setSpjgdm( dto.getSpjgdm() );
        rktHjslCsDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslCsDO.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslCsDO.setSlrxm( dto.getSlrxm() );
        rktHjslCsDO.setSlsj( dto.getSlsj() );
        rktHjslCsDO.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslCsDO.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslCsDO.setJcwh( dto.getJcwh() );
        rktHjslCsDO.setBz( dto.getBz() );
        rktHjslCsDO.setBlhjywid( dto.getBlhjywid() );
        rktHjslCsDO.setHjywblsj( dto.getHjywblsj() );
        rktHjslCsDO.setQtssxq( dto.getQtssxq() );
        rktHjslCsDO.setQtzz( dto.getQtzz() );
        rktHjslCsDO.setPdbzsflh( dto.getPdbzsflh() );
        rktHjslCsDO.setPdbzcszmhmd( dto.getPdbzcszmhmd() );
        rktHjslCsDO.setBdjg( dto.getBdjg() );
        rktHjslCsDO.setBdms( dto.getBdms() );
        rktHjslCsDO.setBdsj( dto.getBdsj() );
        rktHjslCsDO.setBdrid( dto.getBdrid() );
        rktHjslCsDO.setBdrxm( dto.getBdrxm() );
        rktHjslCsDO.setBdrip( dto.getBdrip() );
        rktHjslCsDO.setKscsywid( dto.getKscsywid() );

        return rktHjslCsDO;
    }

    @Override
    public RktHjslCsDTO convertToDTO(RktHjslCsPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslCsDTO rktHjslCsDTO = new RktHjslCsDTO();

        rktHjslCsDTO.setYwslh( req.getYwslh() );
        rktHjslCsDTO.setHmc( req.getHmc() );
        rktHjslCsDTO.setJlx( req.getJlx() );
        rktHjslCsDTO.setMlph( req.getMlph() );
        rktHjslCsDTO.setMlxz( req.getMlxz() );
        rktHjslCsDTO.setLcdyid( req.getLcdyid() );
        rktHjslCsDTO.setLcmc( req.getLcmc() );
        rktHjslCsDTO.setSpyj( req.getSpyj() );
        rktHjslCsDTO.setXh( req.getXh() );
        rktHjslCsDTO.setZjlb( req.getZjlb() );
        rktHjslCsDTO.setQfjg( req.getQfjg() );
        rktHjslCsDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslCsDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslCsDTO.setCsdjlbdm( req.getCsdjlbdm() );
        rktHjslCsDTO.setCsdjlb( req.getCsdjlb() );
        rktHjslCsDTO.setHb( req.getHb() );
        rktHjslCsDTO.setCxsx( req.getCxsx() );
        rktHjslCsDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslCsDTO.setXm( req.getXm() );
        rktHjslCsDTO.setX( req.getX() );
        rktHjslCsDTO.setM( req.getM() );
        rktHjslCsDTO.setCym( req.getCym() );
        rktHjslCsDTO.setXmpy( req.getXmpy() );
        rktHjslCsDTO.setCympy( req.getCympy() );
        rktHjslCsDTO.setXb( req.getXb() );
        rktHjslCsDTO.setMz( req.getMz() );
        rktHjslCsDTO.setCsrq( req.getCsrq() );
        rktHjslCsDTO.setCssj( req.getCssj() );
        rktHjslCsDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslCsDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslCsDTO.setCsdxz( req.getCsdxz() );
        rktHjslCsDTO.setCszmbh( req.getCszmbh() );
        rktHjslCsDTO.setCszqfrq( req.getCszqfrq() );
        rktHjslCsDTO.setJggjdq( req.getJggjdq() );
        rktHjslCsDTO.setJgssxq( req.getJgssxq() );
        rktHjslCsDTO.setJgxz( req.getJgxz() );
        rktHjslCsDTO.setHh( req.getHh() );
        rktHjslCsDTO.setHhnbid( req.getHhnbid() );
        rktHjslCsDTO.setHhid( req.getHhid() );
        rktHjslCsDTO.setHlx( req.getHlx() );
        rktHjslCsDTO.setYhzgx( req.getYhzgx() );
        rktHjslCsDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslCsDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslCsDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslCsDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslCsDTO.setFqmz( req.getFqmz() );
        rktHjslCsDTO.setFqjggjdq( req.getFqjggjdq() );
        rktHjslCsDTO.setFqjgssxq( req.getFqjgssxq() );
        rktHjslCsDTO.setFqzt( req.getFqzt() );
        rktHjslCsDTO.setFqdhhm( req.getFqdhhm() );
        rktHjslCsDTO.setMqmz( req.getMqmz() );
        rktHjslCsDTO.setMqjggjdq( req.getMqjggjdq() );
        rktHjslCsDTO.setMqjgssxq( req.getMqjgssxq() );
        rktHjslCsDTO.setMqzt( req.getMqzt() );
        rktHjslCsDTO.setMqdhhm( req.getMqdhhm() );
        rktHjslCsDTO.setHkxz( req.getHkxz() );
        rktHjslCsDTO.setWhcd( req.getWhcd() );
        rktHjslCsDTO.setHyzk( req.getHyzk() );
        rktHjslCsDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslCsDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslCsDTO.setZy( req.getZy() );
        rktHjslCsDTO.setZylb( req.getZylb() );
        rktHjslCsDTO.setZjxy( req.getZjxy() );
        rktHjslCsDTO.setSg( req.getSg() );
        rktHjslCsDTO.setXx( req.getXx() );
        rktHjslCsDTO.setByzk( req.getByzk() );
        rktHjslCsDTO.setLxdh( req.getLxdh() );
        rktHjslCsDTO.setFqxm( req.getFqxm() );
        rktHjslCsDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslCsDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslCsDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslCsDTO.setFqwwx( req.getFqwwx() );
        rktHjslCsDTO.setFqwwm( req.getFqwwm() );
        rktHjslCsDTO.setMqxm( req.getMqxm() );
        rktHjslCsDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslCsDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslCsDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslCsDTO.setMqwwx( req.getMqwwx() );
        rktHjslCsDTO.setMqwwm( req.getMqwwm() );
        rktHjslCsDTO.setPoxm( req.getPoxm() );
        rktHjslCsDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslCsDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslCsDTO.setPozjhm( req.getPozjhm() );
        rktHjslCsDTO.setPowwx( req.getPowwx() );
        rktHjslCsDTO.setPowwm( req.getPowwm() );
        rktHjslCsDTO.setJhryxm( req.getJhryxm() );
        rktHjslCsDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslCsDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslCsDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslCsDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslCsDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslCsDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslCsDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslCsDTO.setJhrexm( req.getJhrexm() );
        rktHjslCsDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslCsDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslCsDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslCsDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslCsDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslCsDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslCsDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslCsDTO.setRylb( req.getRylb() );
        rktHjslCsDTO.setXxjb( req.getXxjb() );
        rktHjslCsDTO.setLcywlx( req.getLcywlx() );
        rktHjslCsDTO.setLcslid( req.getLcslid() );
        rktHjslCsDTO.setLcywbt( req.getLcywbt() );
        rktHjslCsDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslCsDTO.setLczt( req.getLczt() );
        rktHjslCsDTO.setBlzt( req.getBlzt() );
        rktHjslCsDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslCsDTO.setSqrxm( req.getSqrxm() );
        rktHjslCsDTO.setSqrxb( req.getSqrxb() );
        rktHjslCsDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslCsDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslCsDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslCsDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslCsDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslCsDTO.setSqrq( req.getSqrq() );
        rktHjslCsDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslCsDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslCsDTO.setSprxm( req.getSprxm() );
        rktHjslCsDTO.setSpsj( req.getSpsj() );
        rktHjslCsDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslCsDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslCsDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslCsDTO.setSlrxm( req.getSlrxm() );
        rktHjslCsDTO.setSlsj( req.getSlsj() );
        rktHjslCsDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslCsDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslCsDTO.setJcwh( req.getJcwh() );
        rktHjslCsDTO.setBz( req.getBz() );
        rktHjslCsDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslCsDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslCsDTO.setQtssxq( req.getQtssxq() );
        rktHjslCsDTO.setQtzz( req.getQtzz() );
        rktHjslCsDTO.setPdbzsflh( req.getPdbzsflh() );
        rktHjslCsDTO.setPdbzcszmhmd( req.getPdbzcszmhmd() );
        rktHjslCsDTO.setBdjg( req.getBdjg() );
        rktHjslCsDTO.setBdms( req.getBdms() );
        rktHjslCsDTO.setBdsj( req.getBdsj() );
        rktHjslCsDTO.setBdrid( req.getBdrid() );
        rktHjslCsDTO.setBdrxm( req.getBdrxm() );
        rktHjslCsDTO.setBdrip( req.getBdrip() );
        rktHjslCsDTO.setKscsywid( req.getKscsywid() );

        return rktHjslCsDTO;
    }

    @Override
    public RktHjslCsDTO convertToDTO(RktHjslCsCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslCsDTO rktHjslCsDTO = new RktHjslCsDTO();

        rktHjslCsDTO.setYwslh( req.getYwslh() );
        rktHjslCsDTO.setHmc( req.getHmc() );
        rktHjslCsDTO.setJlx( req.getJlx() );
        rktHjslCsDTO.setMlph( req.getMlph() );
        rktHjslCsDTO.setMlxz( req.getMlxz() );
        rktHjslCsDTO.setLcdyid( req.getLcdyid() );
        rktHjslCsDTO.setLcmc( req.getLcmc() );
        rktHjslCsDTO.setSpyj( req.getSpyj() );
        rktHjslCsDTO.setXh( req.getXh() );
        rktHjslCsDTO.setZjlb( req.getZjlb() );
        rktHjslCsDTO.setQfjg( req.getQfjg() );
        rktHjslCsDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslCsDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslCsDTO.setCsdjlbdm( req.getCsdjlbdm() );
        rktHjslCsDTO.setCsdjlb( req.getCsdjlb() );
        rktHjslCsDTO.setHb( req.getHb() );
        rktHjslCsDTO.setCxsx( req.getCxsx() );
        rktHjslCsDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslCsDTO.setXm( req.getXm() );
        rktHjslCsDTO.setX( req.getX() );
        rktHjslCsDTO.setM( req.getM() );
        rktHjslCsDTO.setCym( req.getCym() );
        rktHjslCsDTO.setXmpy( req.getXmpy() );
        rktHjslCsDTO.setCympy( req.getCympy() );
        rktHjslCsDTO.setXb( req.getXb() );
        rktHjslCsDTO.setMz( req.getMz() );
        rktHjslCsDTO.setCsrq( req.getCsrq() );
        rktHjslCsDTO.setCssj( req.getCssj() );
        rktHjslCsDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslCsDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslCsDTO.setCsdxz( req.getCsdxz() );
        rktHjslCsDTO.setCszmbh( req.getCszmbh() );
        rktHjslCsDTO.setCszqfrq( req.getCszqfrq() );
        rktHjslCsDTO.setJggjdq( req.getJggjdq() );
        rktHjslCsDTO.setJgssxq( req.getJgssxq() );
        rktHjslCsDTO.setJgxz( req.getJgxz() );
        rktHjslCsDTO.setHh( req.getHh() );
        rktHjslCsDTO.setHhnbid( req.getHhnbid() );
        rktHjslCsDTO.setHhid( req.getHhid() );
        rktHjslCsDTO.setHlx( req.getHlx() );
        rktHjslCsDTO.setYhzgx( req.getYhzgx() );
        rktHjslCsDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslCsDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslCsDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslCsDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslCsDTO.setFqmz( req.getFqmz() );
        rktHjslCsDTO.setFqjggjdq( req.getFqjggjdq() );
        rktHjslCsDTO.setFqjgssxq( req.getFqjgssxq() );
        rktHjslCsDTO.setFqzt( req.getFqzt() );
        rktHjslCsDTO.setFqdhhm( req.getFqdhhm() );
        rktHjslCsDTO.setMqmz( req.getMqmz() );
        rktHjslCsDTO.setMqjggjdq( req.getMqjggjdq() );
        rktHjslCsDTO.setMqjgssxq( req.getMqjgssxq() );
        rktHjslCsDTO.setMqzt( req.getMqzt() );
        rktHjslCsDTO.setMqdhhm( req.getMqdhhm() );
        rktHjslCsDTO.setHkxz( req.getHkxz() );
        rktHjslCsDTO.setWhcd( req.getWhcd() );
        rktHjslCsDTO.setHyzk( req.getHyzk() );
        rktHjslCsDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslCsDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslCsDTO.setZy( req.getZy() );
        rktHjslCsDTO.setZylb( req.getZylb() );
        rktHjslCsDTO.setZjxy( req.getZjxy() );
        rktHjslCsDTO.setSg( req.getSg() );
        rktHjslCsDTO.setXx( req.getXx() );
        rktHjslCsDTO.setByzk( req.getByzk() );
        rktHjslCsDTO.setLxdh( req.getLxdh() );
        rktHjslCsDTO.setFqxm( req.getFqxm() );
        rktHjslCsDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslCsDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslCsDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslCsDTO.setFqwwx( req.getFqwwx() );
        rktHjslCsDTO.setFqwwm( req.getFqwwm() );
        rktHjslCsDTO.setMqxm( req.getMqxm() );
        rktHjslCsDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslCsDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslCsDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslCsDTO.setMqwwx( req.getMqwwx() );
        rktHjslCsDTO.setMqwwm( req.getMqwwm() );
        rktHjslCsDTO.setPoxm( req.getPoxm() );
        rktHjslCsDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslCsDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslCsDTO.setPozjhm( req.getPozjhm() );
        rktHjslCsDTO.setPowwx( req.getPowwx() );
        rktHjslCsDTO.setPowwm( req.getPowwm() );
        rktHjslCsDTO.setJhryxm( req.getJhryxm() );
        rktHjslCsDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslCsDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslCsDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslCsDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslCsDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslCsDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslCsDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslCsDTO.setJhrexm( req.getJhrexm() );
        rktHjslCsDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslCsDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslCsDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslCsDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslCsDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslCsDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslCsDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslCsDTO.setRylb( req.getRylb() );
        rktHjslCsDTO.setXxjb( req.getXxjb() );
        rktHjslCsDTO.setLcywlx( req.getLcywlx() );
        rktHjslCsDTO.setLcslid( req.getLcslid() );
        rktHjslCsDTO.setLcywbt( req.getLcywbt() );
        rktHjslCsDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslCsDTO.setLczt( req.getLczt() );
        rktHjslCsDTO.setBlzt( req.getBlzt() );
        rktHjslCsDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslCsDTO.setSqrxm( req.getSqrxm() );
        rktHjslCsDTO.setSqrxb( req.getSqrxb() );
        rktHjslCsDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslCsDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslCsDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslCsDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslCsDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslCsDTO.setSqrq( req.getSqrq() );
        rktHjslCsDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslCsDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslCsDTO.setSprxm( req.getSprxm() );
        rktHjslCsDTO.setSpsj( req.getSpsj() );
        rktHjslCsDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslCsDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslCsDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslCsDTO.setSlrxm( req.getSlrxm() );
        rktHjslCsDTO.setSlsj( req.getSlsj() );
        rktHjslCsDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslCsDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslCsDTO.setJcwh( req.getJcwh() );
        rktHjslCsDTO.setBz( req.getBz() );
        rktHjslCsDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslCsDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslCsDTO.setQtssxq( req.getQtssxq() );
        rktHjslCsDTO.setQtzz( req.getQtzz() );
        rktHjslCsDTO.setPdbzsflh( req.getPdbzsflh() );
        rktHjslCsDTO.setPdbzcszmhmd( req.getPdbzcszmhmd() );
        rktHjslCsDTO.setBdjg( req.getBdjg() );
        rktHjslCsDTO.setBdms( req.getBdms() );
        rktHjslCsDTO.setBdsj( req.getBdsj() );
        rktHjslCsDTO.setBdrid( req.getBdrid() );
        rktHjslCsDTO.setBdrxm( req.getBdrxm() );
        rktHjslCsDTO.setBdrip( req.getBdrip() );
        rktHjslCsDTO.setKscsywid( req.getKscsywid() );

        return rktHjslCsDTO;
    }

    @Override
    public RktHjslCsDTO convertToDTO(RktHjslCsUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslCsDTO rktHjslCsDTO = new RktHjslCsDTO();

        rktHjslCsDTO.setYwslnbbh( req.getYwslnbbh() );
        rktHjslCsDTO.setYwslh( req.getYwslh() );
        rktHjslCsDTO.setHmc( req.getHmc() );
        rktHjslCsDTO.setJlx( req.getJlx() );
        rktHjslCsDTO.setMlph( req.getMlph() );
        rktHjslCsDTO.setMlxz( req.getMlxz() );
        rktHjslCsDTO.setLcdyid( req.getLcdyid() );
        rktHjslCsDTO.setLcmc( req.getLcmc() );
        rktHjslCsDTO.setSpyj( req.getSpyj() );
        rktHjslCsDTO.setXh( req.getXh() );
        rktHjslCsDTO.setZjlb( req.getZjlb() );
        rktHjslCsDTO.setQfjg( req.getQfjg() );
        rktHjslCsDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslCsDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslCsDTO.setCsdjlbdm( req.getCsdjlbdm() );
        rktHjslCsDTO.setCsdjlb( req.getCsdjlb() );
        rktHjslCsDTO.setHb( req.getHb() );
        rktHjslCsDTO.setCxsx( req.getCxsx() );
        rktHjslCsDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslCsDTO.setXm( req.getXm() );
        rktHjslCsDTO.setX( req.getX() );
        rktHjslCsDTO.setM( req.getM() );
        rktHjslCsDTO.setCym( req.getCym() );
        rktHjslCsDTO.setXmpy( req.getXmpy() );
        rktHjslCsDTO.setCympy( req.getCympy() );
        rktHjslCsDTO.setXb( req.getXb() );
        rktHjslCsDTO.setMz( req.getMz() );
        rktHjslCsDTO.setCsrq( req.getCsrq() );
        rktHjslCsDTO.setCssj( req.getCssj() );
        rktHjslCsDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslCsDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslCsDTO.setCsdxz( req.getCsdxz() );
        rktHjslCsDTO.setCszmbh( req.getCszmbh() );
        rktHjslCsDTO.setCszqfrq( req.getCszqfrq() );
        rktHjslCsDTO.setJggjdq( req.getJggjdq() );
        rktHjslCsDTO.setJgssxq( req.getJgssxq() );
        rktHjslCsDTO.setJgxz( req.getJgxz() );
        rktHjslCsDTO.setHh( req.getHh() );
        rktHjslCsDTO.setHhnbid( req.getHhnbid() );
        rktHjslCsDTO.setHhid( req.getHhid() );
        rktHjslCsDTO.setHlx( req.getHlx() );
        rktHjslCsDTO.setYhzgx( req.getYhzgx() );
        rktHjslCsDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslCsDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslCsDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslCsDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslCsDTO.setFqmz( req.getFqmz() );
        rktHjslCsDTO.setFqjggjdq( req.getFqjggjdq() );
        rktHjslCsDTO.setFqjgssxq( req.getFqjgssxq() );
        rktHjslCsDTO.setFqzt( req.getFqzt() );
        rktHjslCsDTO.setFqdhhm( req.getFqdhhm() );
        rktHjslCsDTO.setMqmz( req.getMqmz() );
        rktHjslCsDTO.setMqjggjdq( req.getMqjggjdq() );
        rktHjslCsDTO.setMqjgssxq( req.getMqjgssxq() );
        rktHjslCsDTO.setMqzt( req.getMqzt() );
        rktHjslCsDTO.setMqdhhm( req.getMqdhhm() );
        rktHjslCsDTO.setHkxz( req.getHkxz() );
        rktHjslCsDTO.setWhcd( req.getWhcd() );
        rktHjslCsDTO.setHyzk( req.getHyzk() );
        rktHjslCsDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslCsDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslCsDTO.setZy( req.getZy() );
        rktHjslCsDTO.setZylb( req.getZylb() );
        rktHjslCsDTO.setZjxy( req.getZjxy() );
        rktHjslCsDTO.setSg( req.getSg() );
        rktHjslCsDTO.setXx( req.getXx() );
        rktHjslCsDTO.setByzk( req.getByzk() );
        rktHjslCsDTO.setLxdh( req.getLxdh() );
        rktHjslCsDTO.setFqxm( req.getFqxm() );
        rktHjslCsDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslCsDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslCsDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslCsDTO.setFqwwx( req.getFqwwx() );
        rktHjslCsDTO.setFqwwm( req.getFqwwm() );
        rktHjslCsDTO.setMqxm( req.getMqxm() );
        rktHjslCsDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslCsDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslCsDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslCsDTO.setMqwwx( req.getMqwwx() );
        rktHjslCsDTO.setMqwwm( req.getMqwwm() );
        rktHjslCsDTO.setPoxm( req.getPoxm() );
        rktHjslCsDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslCsDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslCsDTO.setPozjhm( req.getPozjhm() );
        rktHjslCsDTO.setPowwx( req.getPowwx() );
        rktHjslCsDTO.setPowwm( req.getPowwm() );
        rktHjslCsDTO.setJhryxm( req.getJhryxm() );
        rktHjslCsDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslCsDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslCsDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslCsDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslCsDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslCsDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslCsDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslCsDTO.setJhrexm( req.getJhrexm() );
        rktHjslCsDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslCsDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslCsDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslCsDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslCsDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslCsDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslCsDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslCsDTO.setRylb( req.getRylb() );
        rktHjslCsDTO.setXxjb( req.getXxjb() );
        rktHjslCsDTO.setLcywlx( req.getLcywlx() );
        rktHjslCsDTO.setLcslid( req.getLcslid() );
        rktHjslCsDTO.setLcywbt( req.getLcywbt() );
        rktHjslCsDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslCsDTO.setLczt( req.getLczt() );
        rktHjslCsDTO.setBlzt( req.getBlzt() );
        rktHjslCsDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslCsDTO.setSqrxm( req.getSqrxm() );
        rktHjslCsDTO.setSqrxb( req.getSqrxb() );
        rktHjslCsDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslCsDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslCsDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslCsDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslCsDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslCsDTO.setSqrq( req.getSqrq() );
        rktHjslCsDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslCsDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslCsDTO.setSprxm( req.getSprxm() );
        rktHjslCsDTO.setSpsj( req.getSpsj() );
        rktHjslCsDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslCsDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslCsDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslCsDTO.setSlrxm( req.getSlrxm() );
        rktHjslCsDTO.setSlsj( req.getSlsj() );
        rktHjslCsDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslCsDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslCsDTO.setJcwh( req.getJcwh() );
        rktHjslCsDTO.setBz( req.getBz() );
        rktHjslCsDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslCsDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslCsDTO.setQtssxq( req.getQtssxq() );
        rktHjslCsDTO.setQtzz( req.getQtzz() );
        rktHjslCsDTO.setPdbzsflh( req.getPdbzsflh() );
        rktHjslCsDTO.setPdbzcszmhmd( req.getPdbzcszmhmd() );
        rktHjslCsDTO.setBdjg( req.getBdjg() );
        rktHjslCsDTO.setBdms( req.getBdms() );
        rktHjslCsDTO.setBdsj( req.getBdsj() );
        rktHjslCsDTO.setBdrid( req.getBdrid() );
        rktHjslCsDTO.setBdrxm( req.getBdrxm() );
        rktHjslCsDTO.setBdrip( req.getBdrip() );
        rktHjslCsDTO.setKscsywid( req.getKscsywid() );

        return rktHjslCsDTO;
    }

    @Override
    public RktHjslCsPageResp convertToPageResp(RktHjslCsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslCsPageResp rktHjslCsPageResp = new RktHjslCsPageResp();

        rktHjslCsPageResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslCsPageResp.setYwslh( dto.getYwslh() );
        rktHjslCsPageResp.setHmc( dto.getHmc() );
        rktHjslCsPageResp.setJlx( dto.getJlx() );
        rktHjslCsPageResp.setMlph( dto.getMlph() );
        rktHjslCsPageResp.setMlxz( dto.getMlxz() );
        rktHjslCsPageResp.setLcdyid( dto.getLcdyid() );
        rktHjslCsPageResp.setLcmc( dto.getLcmc() );
        rktHjslCsPageResp.setSpyj( dto.getSpyj() );
        rktHjslCsPageResp.setXh( dto.getXh() );
        rktHjslCsPageResp.setZjlb( dto.getZjlb() );
        rktHjslCsPageResp.setQfjg( dto.getQfjg() );
        rktHjslCsPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslCsPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslCsPageResp.setCsdjlbdm( dto.getCsdjlbdm() );
        rktHjslCsPageResp.setCsdjlb( dto.getCsdjlb() );
        rktHjslCsPageResp.setHb( dto.getHb() );
        rktHjslCsPageResp.setCxsx( dto.getCxsx() );
        rktHjslCsPageResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslCsPageResp.setXm( dto.getXm() );
        rktHjslCsPageResp.setX( dto.getX() );
        rktHjslCsPageResp.setM( dto.getM() );
        rktHjslCsPageResp.setCym( dto.getCym() );
        rktHjslCsPageResp.setXmpy( dto.getXmpy() );
        rktHjslCsPageResp.setCympy( dto.getCympy() );
        rktHjslCsPageResp.setXb( dto.getXb() );
        rktHjslCsPageResp.setMz( dto.getMz() );
        rktHjslCsPageResp.setCsrq( dto.getCsrq() );
        rktHjslCsPageResp.setCssj( dto.getCssj() );
        rktHjslCsPageResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslCsPageResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslCsPageResp.setCsdxz( dto.getCsdxz() );
        rktHjslCsPageResp.setCszmbh( dto.getCszmbh() );
        rktHjslCsPageResp.setCszqfrq( dto.getCszqfrq() );
        rktHjslCsPageResp.setJggjdq( dto.getJggjdq() );
        rktHjslCsPageResp.setJgssxq( dto.getJgssxq() );
        rktHjslCsPageResp.setJgxz( dto.getJgxz() );
        rktHjslCsPageResp.setHh( dto.getHh() );
        rktHjslCsPageResp.setHhnbid( dto.getHhnbid() );
        rktHjslCsPageResp.setHhid( dto.getHhid() );
        rktHjslCsPageResp.setHlx( dto.getHlx() );
        rktHjslCsPageResp.setYhzgx( dto.getYhzgx() );
        rktHjslCsPageResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslCsPageResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslCsPageResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslCsPageResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslCsPageResp.setFqmz( dto.getFqmz() );
        rktHjslCsPageResp.setFqjggjdq( dto.getFqjggjdq() );
        rktHjslCsPageResp.setFqjgssxq( dto.getFqjgssxq() );
        rktHjslCsPageResp.setFqzt( dto.getFqzt() );
        rktHjslCsPageResp.setFqdhhm( dto.getFqdhhm() );
        rktHjslCsPageResp.setMqmz( dto.getMqmz() );
        rktHjslCsPageResp.setMqjggjdq( dto.getMqjggjdq() );
        rktHjslCsPageResp.setMqjgssxq( dto.getMqjgssxq() );
        rktHjslCsPageResp.setMqzt( dto.getMqzt() );
        rktHjslCsPageResp.setMqdhhm( dto.getMqdhhm() );
        rktHjslCsPageResp.setHkxz( dto.getHkxz() );
        rktHjslCsPageResp.setWhcd( dto.getWhcd() );
        rktHjslCsPageResp.setHyzk( dto.getHyzk() );
        rktHjslCsPageResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslCsPageResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslCsPageResp.setZy( dto.getZy() );
        rktHjslCsPageResp.setZylb( dto.getZylb() );
        rktHjslCsPageResp.setZjxy( dto.getZjxy() );
        rktHjslCsPageResp.setSg( dto.getSg() );
        rktHjslCsPageResp.setXx( dto.getXx() );
        rktHjslCsPageResp.setByzk( dto.getByzk() );
        rktHjslCsPageResp.setLxdh( dto.getLxdh() );
        rktHjslCsPageResp.setFqxm( dto.getFqxm() );
        rktHjslCsPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslCsPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslCsPageResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslCsPageResp.setFqwwx( dto.getFqwwx() );
        rktHjslCsPageResp.setFqwwm( dto.getFqwwm() );
        rktHjslCsPageResp.setMqxm( dto.getMqxm() );
        rktHjslCsPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslCsPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslCsPageResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslCsPageResp.setMqwwx( dto.getMqwwx() );
        rktHjslCsPageResp.setMqwwm( dto.getMqwwm() );
        rktHjslCsPageResp.setPoxm( dto.getPoxm() );
        rktHjslCsPageResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslCsPageResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslCsPageResp.setPozjhm( dto.getPozjhm() );
        rktHjslCsPageResp.setPowwx( dto.getPowwx() );
        rktHjslCsPageResp.setPowwm( dto.getPowwm() );
        rktHjslCsPageResp.setJhryxm( dto.getJhryxm() );
        rktHjslCsPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslCsPageResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslCsPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslCsPageResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslCsPageResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslCsPageResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslCsPageResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslCsPageResp.setJhrexm( dto.getJhrexm() );
        rktHjslCsPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslCsPageResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslCsPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslCsPageResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslCsPageResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslCsPageResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslCsPageResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslCsPageResp.setRylb( dto.getRylb() );
        rktHjslCsPageResp.setXxjb( dto.getXxjb() );
        rktHjslCsPageResp.setLcywlx( dto.getLcywlx() );
        rktHjslCsPageResp.setLcslid( dto.getLcslid() );
        rktHjslCsPageResp.setLcywbt( dto.getLcywbt() );
        rktHjslCsPageResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslCsPageResp.setLczt( dto.getLczt() );
        rktHjslCsPageResp.setBlzt( dto.getBlzt() );
        rktHjslCsPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslCsPageResp.setSqrxm( dto.getSqrxm() );
        rktHjslCsPageResp.setSqrxb( dto.getSqrxb() );
        rktHjslCsPageResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslCsPageResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslCsPageResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslCsPageResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslCsPageResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslCsPageResp.setSqrq( dto.getSqrq() );
        rktHjslCsPageResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslCsPageResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslCsPageResp.setSprxm( dto.getSprxm() );
        rktHjslCsPageResp.setSpsj( dto.getSpsj() );
        rktHjslCsPageResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslCsPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslCsPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslCsPageResp.setSlrxm( dto.getSlrxm() );
        rktHjslCsPageResp.setSlsj( dto.getSlsj() );
        rktHjslCsPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslCsPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslCsPageResp.setJcwh( dto.getJcwh() );
        rktHjslCsPageResp.setBz( dto.getBz() );
        rktHjslCsPageResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslCsPageResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslCsPageResp.setQtssxq( dto.getQtssxq() );
        rktHjslCsPageResp.setQtzz( dto.getQtzz() );
        rktHjslCsPageResp.setPdbzsflh( dto.getPdbzsflh() );
        rktHjslCsPageResp.setPdbzcszmhmd( dto.getPdbzcszmhmd() );
        rktHjslCsPageResp.setBdjg( dto.getBdjg() );
        rktHjslCsPageResp.setBdms( dto.getBdms() );
        rktHjslCsPageResp.setBdsj( dto.getBdsj() );
        rktHjslCsPageResp.setBdrid( dto.getBdrid() );
        rktHjslCsPageResp.setBdrxm( dto.getBdrxm() );
        rktHjslCsPageResp.setBdrip( dto.getBdrip() );
        rktHjslCsPageResp.setKscsywid( dto.getKscsywid() );

        return rktHjslCsPageResp;
    }

    @Override
    public RktHjslCsViewResp convertToViewResp(RktHjslCsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslCsViewResp rktHjslCsViewResp = new RktHjslCsViewResp();

        rktHjslCsViewResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslCsViewResp.setYwslh( dto.getYwslh() );
        rktHjslCsViewResp.setHmc( dto.getHmc() );
        rktHjslCsViewResp.setJlx( dto.getJlx() );
        rktHjslCsViewResp.setMlph( dto.getMlph() );
        rktHjslCsViewResp.setMlxz( dto.getMlxz() );
        rktHjslCsViewResp.setLcdyid( dto.getLcdyid() );
        rktHjslCsViewResp.setLcmc( dto.getLcmc() );
        rktHjslCsViewResp.setSpyj( dto.getSpyj() );
        rktHjslCsViewResp.setXh( dto.getXh() );
        rktHjslCsViewResp.setZjlb( dto.getZjlb() );
        rktHjslCsViewResp.setQfjg( dto.getQfjg() );
        rktHjslCsViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslCsViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslCsViewResp.setCsdjlbdm( dto.getCsdjlbdm() );
        rktHjslCsViewResp.setCsdjlb( dto.getCsdjlb() );
        rktHjslCsViewResp.setHb( dto.getHb() );
        rktHjslCsViewResp.setCxsx( dto.getCxsx() );
        rktHjslCsViewResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslCsViewResp.setXm( dto.getXm() );
        rktHjslCsViewResp.setX( dto.getX() );
        rktHjslCsViewResp.setM( dto.getM() );
        rktHjslCsViewResp.setCym( dto.getCym() );
        rktHjslCsViewResp.setXmpy( dto.getXmpy() );
        rktHjslCsViewResp.setCympy( dto.getCympy() );
        rktHjslCsViewResp.setXb( dto.getXb() );
        rktHjslCsViewResp.setMz( dto.getMz() );
        rktHjslCsViewResp.setCsrq( dto.getCsrq() );
        rktHjslCsViewResp.setCssj( dto.getCssj() );
        rktHjslCsViewResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslCsViewResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslCsViewResp.setCsdxz( dto.getCsdxz() );
        rktHjslCsViewResp.setCszmbh( dto.getCszmbh() );
        rktHjslCsViewResp.setCszqfrq( dto.getCszqfrq() );
        rktHjslCsViewResp.setJggjdq( dto.getJggjdq() );
        rktHjslCsViewResp.setJgssxq( dto.getJgssxq() );
        rktHjslCsViewResp.setJgxz( dto.getJgxz() );
        rktHjslCsViewResp.setHh( dto.getHh() );
        rktHjslCsViewResp.setHhnbid( dto.getHhnbid() );
        rktHjslCsViewResp.setHhid( dto.getHhid() );
        rktHjslCsViewResp.setHlx( dto.getHlx() );
        rktHjslCsViewResp.setYhzgx( dto.getYhzgx() );
        rktHjslCsViewResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslCsViewResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslCsViewResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslCsViewResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslCsViewResp.setFqmz( dto.getFqmz() );
        rktHjslCsViewResp.setFqjggjdq( dto.getFqjggjdq() );
        rktHjslCsViewResp.setFqjgssxq( dto.getFqjgssxq() );
        rktHjslCsViewResp.setFqzt( dto.getFqzt() );
        rktHjslCsViewResp.setFqdhhm( dto.getFqdhhm() );
        rktHjslCsViewResp.setMqmz( dto.getMqmz() );
        rktHjslCsViewResp.setMqjggjdq( dto.getMqjggjdq() );
        rktHjslCsViewResp.setMqjgssxq( dto.getMqjgssxq() );
        rktHjslCsViewResp.setMqzt( dto.getMqzt() );
        rktHjslCsViewResp.setMqdhhm( dto.getMqdhhm() );
        rktHjslCsViewResp.setHkxz( dto.getHkxz() );
        rktHjslCsViewResp.setWhcd( dto.getWhcd() );
        rktHjslCsViewResp.setHyzk( dto.getHyzk() );
        rktHjslCsViewResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslCsViewResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslCsViewResp.setZy( dto.getZy() );
        rktHjslCsViewResp.setZylb( dto.getZylb() );
        rktHjslCsViewResp.setZjxy( dto.getZjxy() );
        rktHjslCsViewResp.setSg( dto.getSg() );
        rktHjslCsViewResp.setXx( dto.getXx() );
        rktHjslCsViewResp.setByzk( dto.getByzk() );
        rktHjslCsViewResp.setLxdh( dto.getLxdh() );
        rktHjslCsViewResp.setFqxm( dto.getFqxm() );
        rktHjslCsViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslCsViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslCsViewResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslCsViewResp.setFqwwx( dto.getFqwwx() );
        rktHjslCsViewResp.setFqwwm( dto.getFqwwm() );
        rktHjslCsViewResp.setMqxm( dto.getMqxm() );
        rktHjslCsViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslCsViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslCsViewResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslCsViewResp.setMqwwx( dto.getMqwwx() );
        rktHjslCsViewResp.setMqwwm( dto.getMqwwm() );
        rktHjslCsViewResp.setPoxm( dto.getPoxm() );
        rktHjslCsViewResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslCsViewResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslCsViewResp.setPozjhm( dto.getPozjhm() );
        rktHjslCsViewResp.setPowwx( dto.getPowwx() );
        rktHjslCsViewResp.setPowwm( dto.getPowwm() );
        rktHjslCsViewResp.setJhryxm( dto.getJhryxm() );
        rktHjslCsViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslCsViewResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslCsViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslCsViewResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslCsViewResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslCsViewResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslCsViewResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslCsViewResp.setJhrexm( dto.getJhrexm() );
        rktHjslCsViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslCsViewResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslCsViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslCsViewResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslCsViewResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslCsViewResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslCsViewResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslCsViewResp.setRylb( dto.getRylb() );
        rktHjslCsViewResp.setXxjb( dto.getXxjb() );
        rktHjslCsViewResp.setLcywlx( dto.getLcywlx() );
        rktHjslCsViewResp.setLcslid( dto.getLcslid() );
        rktHjslCsViewResp.setLcywbt( dto.getLcywbt() );
        rktHjslCsViewResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslCsViewResp.setLczt( dto.getLczt() );
        rktHjslCsViewResp.setBlzt( dto.getBlzt() );
        rktHjslCsViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslCsViewResp.setSqrxm( dto.getSqrxm() );
        rktHjslCsViewResp.setSqrxb( dto.getSqrxb() );
        rktHjslCsViewResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslCsViewResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslCsViewResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslCsViewResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslCsViewResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslCsViewResp.setSqrq( dto.getSqrq() );
        rktHjslCsViewResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslCsViewResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslCsViewResp.setSprxm( dto.getSprxm() );
        rktHjslCsViewResp.setSpsj( dto.getSpsj() );
        rktHjslCsViewResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslCsViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslCsViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslCsViewResp.setSlrxm( dto.getSlrxm() );
        rktHjslCsViewResp.setSlsj( dto.getSlsj() );
        rktHjslCsViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslCsViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslCsViewResp.setJcwh( dto.getJcwh() );
        rktHjslCsViewResp.setBz( dto.getBz() );
        rktHjslCsViewResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslCsViewResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslCsViewResp.setQtssxq( dto.getQtssxq() );
        rktHjslCsViewResp.setQtzz( dto.getQtzz() );
        rktHjslCsViewResp.setPdbzsflh( dto.getPdbzsflh() );
        rktHjslCsViewResp.setPdbzcszmhmd( dto.getPdbzcszmhmd() );
        rktHjslCsViewResp.setBdjg( dto.getBdjg() );
        rktHjslCsViewResp.setBdms( dto.getBdms() );
        rktHjslCsViewResp.setBdsj( dto.getBdsj() );
        rktHjslCsViewResp.setBdrid( dto.getBdrid() );
        rktHjslCsViewResp.setBdrxm( dto.getBdrxm() );
        rktHjslCsViewResp.setBdrip( dto.getBdrip() );
        rktHjslCsViewResp.setKscsywid( dto.getKscsywid() );

        return rktHjslCsViewResp;
    }

    @Override
    public RktHjslCsCreateResp convertToCreateResp(RktHjslCsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslCsCreateResp rktHjslCsCreateResp = new RktHjslCsCreateResp();

        rktHjslCsCreateResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslCsCreateResp.setYwslh( dto.getYwslh() );
        rktHjslCsCreateResp.setHmc( dto.getHmc() );
        rktHjslCsCreateResp.setJlx( dto.getJlx() );
        rktHjslCsCreateResp.setMlph( dto.getMlph() );
        rktHjslCsCreateResp.setMlxz( dto.getMlxz() );
        rktHjslCsCreateResp.setLcdyid( dto.getLcdyid() );
        rktHjslCsCreateResp.setLcmc( dto.getLcmc() );
        rktHjslCsCreateResp.setSpyj( dto.getSpyj() );
        rktHjslCsCreateResp.setXh( dto.getXh() );
        rktHjslCsCreateResp.setZjlb( dto.getZjlb() );
        rktHjslCsCreateResp.setQfjg( dto.getQfjg() );
        rktHjslCsCreateResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslCsCreateResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslCsCreateResp.setCsdjlbdm( dto.getCsdjlbdm() );
        rktHjslCsCreateResp.setCsdjlb( dto.getCsdjlb() );
        rktHjslCsCreateResp.setHb( dto.getHb() );
        rktHjslCsCreateResp.setCxsx( dto.getCxsx() );
        rktHjslCsCreateResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslCsCreateResp.setXm( dto.getXm() );
        rktHjslCsCreateResp.setX( dto.getX() );
        rktHjslCsCreateResp.setM( dto.getM() );
        rktHjslCsCreateResp.setCym( dto.getCym() );
        rktHjslCsCreateResp.setXmpy( dto.getXmpy() );
        rktHjslCsCreateResp.setCympy( dto.getCympy() );
        rktHjslCsCreateResp.setXb( dto.getXb() );
        rktHjslCsCreateResp.setMz( dto.getMz() );
        rktHjslCsCreateResp.setCsrq( dto.getCsrq() );
        rktHjslCsCreateResp.setCssj( dto.getCssj() );
        rktHjslCsCreateResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslCsCreateResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslCsCreateResp.setCsdxz( dto.getCsdxz() );
        rktHjslCsCreateResp.setCszmbh( dto.getCszmbh() );
        rktHjslCsCreateResp.setCszqfrq( dto.getCszqfrq() );
        rktHjslCsCreateResp.setJggjdq( dto.getJggjdq() );
        rktHjslCsCreateResp.setJgssxq( dto.getJgssxq() );
        rktHjslCsCreateResp.setJgxz( dto.getJgxz() );
        rktHjslCsCreateResp.setHh( dto.getHh() );
        rktHjslCsCreateResp.setHhnbid( dto.getHhnbid() );
        rktHjslCsCreateResp.setHhid( dto.getHhid() );
        rktHjslCsCreateResp.setHlx( dto.getHlx() );
        rktHjslCsCreateResp.setYhzgx( dto.getYhzgx() );
        rktHjslCsCreateResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslCsCreateResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslCsCreateResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslCsCreateResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslCsCreateResp.setFqmz( dto.getFqmz() );
        rktHjslCsCreateResp.setFqjggjdq( dto.getFqjggjdq() );
        rktHjslCsCreateResp.setFqjgssxq( dto.getFqjgssxq() );
        rktHjslCsCreateResp.setFqzt( dto.getFqzt() );
        rktHjslCsCreateResp.setFqdhhm( dto.getFqdhhm() );
        rktHjslCsCreateResp.setMqmz( dto.getMqmz() );
        rktHjslCsCreateResp.setMqjggjdq( dto.getMqjggjdq() );
        rktHjslCsCreateResp.setMqjgssxq( dto.getMqjgssxq() );
        rktHjslCsCreateResp.setMqzt( dto.getMqzt() );
        rktHjslCsCreateResp.setMqdhhm( dto.getMqdhhm() );
        rktHjslCsCreateResp.setHkxz( dto.getHkxz() );
        rktHjslCsCreateResp.setWhcd( dto.getWhcd() );
        rktHjslCsCreateResp.setHyzk( dto.getHyzk() );
        rktHjslCsCreateResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslCsCreateResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslCsCreateResp.setZy( dto.getZy() );
        rktHjslCsCreateResp.setZylb( dto.getZylb() );
        rktHjslCsCreateResp.setZjxy( dto.getZjxy() );
        rktHjslCsCreateResp.setSg( dto.getSg() );
        rktHjslCsCreateResp.setXx( dto.getXx() );
        rktHjslCsCreateResp.setByzk( dto.getByzk() );
        rktHjslCsCreateResp.setLxdh( dto.getLxdh() );
        rktHjslCsCreateResp.setFqxm( dto.getFqxm() );
        rktHjslCsCreateResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslCsCreateResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslCsCreateResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslCsCreateResp.setFqwwx( dto.getFqwwx() );
        rktHjslCsCreateResp.setFqwwm( dto.getFqwwm() );
        rktHjslCsCreateResp.setMqxm( dto.getMqxm() );
        rktHjslCsCreateResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslCsCreateResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslCsCreateResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslCsCreateResp.setMqwwx( dto.getMqwwx() );
        rktHjslCsCreateResp.setMqwwm( dto.getMqwwm() );
        rktHjslCsCreateResp.setPoxm( dto.getPoxm() );
        rktHjslCsCreateResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslCsCreateResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslCsCreateResp.setPozjhm( dto.getPozjhm() );
        rktHjslCsCreateResp.setPowwx( dto.getPowwx() );
        rktHjslCsCreateResp.setPowwm( dto.getPowwm() );
        rktHjslCsCreateResp.setJhryxm( dto.getJhryxm() );
        rktHjslCsCreateResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslCsCreateResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslCsCreateResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslCsCreateResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslCsCreateResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslCsCreateResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslCsCreateResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslCsCreateResp.setJhrexm( dto.getJhrexm() );
        rktHjslCsCreateResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslCsCreateResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslCsCreateResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslCsCreateResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslCsCreateResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslCsCreateResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslCsCreateResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslCsCreateResp.setRylb( dto.getRylb() );
        rktHjslCsCreateResp.setXxjb( dto.getXxjb() );
        rktHjslCsCreateResp.setLcywlx( dto.getLcywlx() );
        rktHjslCsCreateResp.setLcslid( dto.getLcslid() );
        rktHjslCsCreateResp.setLcywbt( dto.getLcywbt() );
        rktHjslCsCreateResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslCsCreateResp.setLczt( dto.getLczt() );
        rktHjslCsCreateResp.setBlzt( dto.getBlzt() );
        rktHjslCsCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslCsCreateResp.setSqrxm( dto.getSqrxm() );
        rktHjslCsCreateResp.setSqrxb( dto.getSqrxb() );
        rktHjslCsCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslCsCreateResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslCsCreateResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslCsCreateResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslCsCreateResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslCsCreateResp.setSqrq( dto.getSqrq() );
        rktHjslCsCreateResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslCsCreateResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslCsCreateResp.setSprxm( dto.getSprxm() );
        rktHjslCsCreateResp.setSpsj( dto.getSpsj() );
        rktHjslCsCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslCsCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslCsCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslCsCreateResp.setSlrxm( dto.getSlrxm() );
        rktHjslCsCreateResp.setSlsj( dto.getSlsj() );
        rktHjslCsCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslCsCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslCsCreateResp.setJcwh( dto.getJcwh() );
        rktHjslCsCreateResp.setBz( dto.getBz() );
        rktHjslCsCreateResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslCsCreateResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslCsCreateResp.setQtssxq( dto.getQtssxq() );
        rktHjslCsCreateResp.setQtzz( dto.getQtzz() );
        rktHjslCsCreateResp.setPdbzsflh( dto.getPdbzsflh() );
        rktHjslCsCreateResp.setPdbzcszmhmd( dto.getPdbzcszmhmd() );
        rktHjslCsCreateResp.setBdjg( dto.getBdjg() );
        rktHjslCsCreateResp.setBdms( dto.getBdms() );
        rktHjslCsCreateResp.setBdsj( dto.getBdsj() );
        rktHjslCsCreateResp.setBdrid( dto.getBdrid() );
        rktHjslCsCreateResp.setBdrxm( dto.getBdrxm() );
        rktHjslCsCreateResp.setBdrip( dto.getBdrip() );
        rktHjslCsCreateResp.setKscsywid( dto.getKscsywid() );

        return rktHjslCsCreateResp;
    }
}
