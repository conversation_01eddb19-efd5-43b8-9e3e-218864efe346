package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ActJcYwslclszDTO;
import com.zjjcnt.project.ck.base.dto.resp.ActJcYwslclszPageResp;
import com.zjjcnt.project.ck.base.entity.ActJcYwslclszDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:46+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ActJcYwslclszConvertImpl implements ActJcYwslclszConvert {

    @Override
    public ActJcYwslclszDTO convert(ActJcYwslclszDO entity) {
        if ( entity == null ) {
            return null;
        }

        ActJcYwslclszDTO actJcYwslclszDTO = new ActJcYwslclszDTO();

        actJcYwslclszDTO.setId( entity.getId() );
        actJcYwslclszDTO.setNbbh( entity.getNbbh() );
        actJcYwslclszDTO.setLcywlx( entity.getLcywlx() );
        actJcYwslclszDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        actJcYwslclszDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        actJcYwslclszDTO.setSjgsdwddm( entity.getSjgsdwddm() );
        actJcYwslclszDTO.setXh( entity.getXh() );
        actJcYwslclszDTO.setCllxdl( entity.getCllxdl() );
        actJcYwslclszDTO.setCllxzl( entity.getCllxzl() );
        actJcYwslclszDTO.setCllxxl( entity.getCllxxl() );
        actJcYwslclszDTO.setCllxdm( entity.getCllxdm() );
        actJcYwslclszDTO.setCllxmc( entity.getCllxmc() );
        actJcYwslclszDTO.setQysj( entity.getQysj() );
        actJcYwslclszDTO.setJssj( entity.getJssj() );
        actJcYwslclszDTO.setYxbz( entity.getYxbz() );
        actJcYwslclszDTO.setQstgclbz( entity.getQstgclbz() );
        actJcYwslclszDTO.setBdsj( entity.getBdsj() );

        return actJcYwslclszDTO;
    }

    @Override
    public ActJcYwslclszDO convertToDO(ActJcYwslclszDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ActJcYwslclszDO actJcYwslclszDO = new ActJcYwslclszDO();

        actJcYwslclszDO.setId( dto.getId() );
        actJcYwslclszDO.setNbbh( dto.getNbbh() );
        actJcYwslclszDO.setLcywlx( dto.getLcywlx() );
        actJcYwslclszDO.setSjgsdwdm( dto.getSjgsdwdm() );
        actJcYwslclszDO.setSjgsdwmc( dto.getSjgsdwmc() );
        actJcYwslclszDO.setSjgsdwddm( dto.getSjgsdwddm() );
        actJcYwslclszDO.setXh( dto.getXh() );
        actJcYwslclszDO.setCllxdl( dto.getCllxdl() );
        actJcYwslclszDO.setCllxzl( dto.getCllxzl() );
        actJcYwslclszDO.setCllxxl( dto.getCllxxl() );
        actJcYwslclszDO.setCllxdm( dto.getCllxdm() );
        actJcYwslclszDO.setCllxmc( dto.getCllxmc() );
        actJcYwslclszDO.setQysj( dto.getQysj() );
        actJcYwslclszDO.setJssj( dto.getJssj() );
        actJcYwslclszDO.setYxbz( dto.getYxbz() );
        actJcYwslclszDO.setQstgclbz( dto.getQstgclbz() );
        actJcYwslclszDO.setBdsj( dto.getBdsj() );

        return actJcYwslclszDO;
    }

    @Override
    public ActJcYwslclszPageResp convertToPageResp(ActJcYwslclszDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ActJcYwslclszPageResp actJcYwslclszPageResp = new ActJcYwslclszPageResp();

        actJcYwslclszPageResp.setNbbh( dto.getNbbh() );
        actJcYwslclszPageResp.setLcywlx( dto.getLcywlx() );
        actJcYwslclszPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        actJcYwslclszPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        actJcYwslclszPageResp.setSjgsdwddm( dto.getSjgsdwddm() );
        actJcYwslclszPageResp.setXh( dto.getXh() );
        actJcYwslclszPageResp.setCllxdl( dto.getCllxdl() );
        actJcYwslclszPageResp.setCllxzl( dto.getCllxzl() );
        actJcYwslclszPageResp.setCllxxl( dto.getCllxxl() );
        actJcYwslclszPageResp.setCllxdm( dto.getCllxdm() );
        actJcYwslclszPageResp.setCllxmc( dto.getCllxmc() );
        actJcYwslclszPageResp.setQysj( dto.getQysj() );
        actJcYwslclszPageResp.setJssj( dto.getJssj() );
        actJcYwslclszPageResp.setQstgclbz( dto.getQstgclbz() );
        actJcYwslclszPageResp.setBdsj( dto.getBdsj() );

        return actJcYwslclszPageResp;
    }

    @Override
    public List<ActJcYwslclszPageResp> convertToPageResp(List<ActJcYwslclszDTO> dto) {
        if ( dto == null ) {
            return null;
        }

        List<ActJcYwslclszPageResp> list = new ArrayList<ActJcYwslclszPageResp>( dto.size() );
        for ( ActJcYwslclszDTO actJcYwslclszDTO : dto ) {
            list.add( convertToPageResp( actJcYwslclszDTO ) );
        }

        return list;
    }
}
