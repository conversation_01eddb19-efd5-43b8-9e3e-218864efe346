package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsxtHjlzmsqxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtHjlzmsqxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtHjlzmsqxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtHjlzmsqxxbViewResp;
import com.zjjcnt.project.ck.base.entity.KsxtHjlzmsqxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:46+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsxtHjlzmsqxxbConvertImpl implements KsxtHjlzmsqxxbConvert {

    @Override
    public KsxtHjlzmsqxxbDTO convert(KsxtHjlzmsqxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsxtHjlzmsqxxbDTO ksxtHjlzmsqxxbDTO = new KsxtHjlzmsqxxbDTO();

        ksxtHjlzmsqxxbDTO.setId( entity.getId() );
        ksxtHjlzmsqxxbDTO.setKsxthjlzmid( entity.getKsxthjlzmid() );
        ksxtHjlzmsqxxbDTO.setKsxtid( entity.getKsxtid() );
        ksxtHjlzmsqxxbDTO.setZaglywxtbh( entity.getZaglywxtbh() );
        ksxtHjlzmsqxxbDTO.setGmsfhm( entity.getGmsfhm() );
        ksxtHjlzmsqxxbDTO.setXm( entity.getXm() );
        ksxtHjlzmsqxxbDTO.setHjdzssxqdm( entity.getHjdzssxqdm() );
        ksxtHjlzmsqxxbDTO.setHjdzqhnxxdz( entity.getHjdzqhnxxdz() );
        ksxtHjlzmsqxxbDTO.setLxdh( entity.getLxdh() );
        ksxtHjlzmsqxxbDTO.setHjzmlxjsxdm( entity.getHjzmlxjsxdm() );
        ksxtHjlzmsqxxbDTO.setXyzmsxnrjyqk( entity.getXyzmsxnrjyqk() );
        ksxtHjlzmsqxxbDTO.setKjhjlzmywlsh( entity.getKjhjlzmywlsh() );
        ksxtHjlzmsqxxbDTO.setBz( entity.getBz() );
        ksxtHjlzmsqxxbDTO.setHjxcjglxdm( entity.getHjxcjglxdm() );
        ksxtHjlzmsqxxbDTO.setCzjgmsjyqk( entity.getCzjgmsjyqk() );
        ksxtHjlzmsqxxbDTO.setBz1( entity.getBz1() );
        ksxtHjlzmsqxxbDTO.setSqrq( entity.getSqrq() );
        ksxtHjlzmsqxxbDTO.setSqdgajgjgdm( entity.getSqdgajgjgdm() );
        ksxtHjlzmsqxxbDTO.setSqdgajgmc( entity.getSqdgajgmc() );
        ksxtHjlzmsqxxbDTO.setSqdlxdh( entity.getSqdlxdh() );
        ksxtHjlzmsqxxbDTO.setFkrq( entity.getFkrq() );
        ksxtHjlzmsqxxbDTO.setXcdgajgjgdm( entity.getXcdgajgjgdm() );
        ksxtHjlzmsqxxbDTO.setXcdgajgmc( entity.getXcdgajgmc() );
        ksxtHjlzmsqxxbDTO.setXcdlxdh( entity.getXcdlxdh() );
        ksxtHjlzmsqxxbDTO.setXcrxm( entity.getXcrxm() );
        ksxtHjlzmsqxxbDTO.setXcrqsj( entity.getXcrqsj() );
        ksxtHjlzmsqxxbDTO.setQyfwdm( entity.getQyfwdm() );
        ksxtHjlzmsqxxbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        ksxtHjlzmsqxxbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        ksxtHjlzmsqxxbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        ksxtHjlzmsqxxbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        ksxtHjlzmsqxxbDTO.setRksj( entity.getRksj() );

        return ksxtHjlzmsqxxbDTO;
    }

    @Override
    public KsxtHjlzmsqxxbDO convertToDO(KsxtHjlzmsqxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtHjlzmsqxxbDO ksxtHjlzmsqxxbDO = new KsxtHjlzmsqxxbDO();

        ksxtHjlzmsqxxbDO.setId( dto.getId() );
        ksxtHjlzmsqxxbDO.setKsxthjlzmid( dto.getKsxthjlzmid() );
        ksxtHjlzmsqxxbDO.setKsxtid( dto.getKsxtid() );
        ksxtHjlzmsqxxbDO.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtHjlzmsqxxbDO.setGmsfhm( dto.getGmsfhm() );
        ksxtHjlzmsqxxbDO.setXm( dto.getXm() );
        ksxtHjlzmsqxxbDO.setHjdzssxqdm( dto.getHjdzssxqdm() );
        ksxtHjlzmsqxxbDO.setHjdzqhnxxdz( dto.getHjdzqhnxxdz() );
        ksxtHjlzmsqxxbDO.setLxdh( dto.getLxdh() );
        ksxtHjlzmsqxxbDO.setHjzmlxjsxdm( dto.getHjzmlxjsxdm() );
        ksxtHjlzmsqxxbDO.setXyzmsxnrjyqk( dto.getXyzmsxnrjyqk() );
        ksxtHjlzmsqxxbDO.setKjhjlzmywlsh( dto.getKjhjlzmywlsh() );
        ksxtHjlzmsqxxbDO.setBz( dto.getBz() );
        ksxtHjlzmsqxxbDO.setHjxcjglxdm( dto.getHjxcjglxdm() );
        ksxtHjlzmsqxxbDO.setCzjgmsjyqk( dto.getCzjgmsjyqk() );
        ksxtHjlzmsqxxbDO.setBz1( dto.getBz1() );
        ksxtHjlzmsqxxbDO.setSqrq( dto.getSqrq() );
        ksxtHjlzmsqxxbDO.setSqdgajgjgdm( dto.getSqdgajgjgdm() );
        ksxtHjlzmsqxxbDO.setSqdgajgmc( dto.getSqdgajgmc() );
        ksxtHjlzmsqxxbDO.setSqdlxdh( dto.getSqdlxdh() );
        ksxtHjlzmsqxxbDO.setFkrq( dto.getFkrq() );
        ksxtHjlzmsqxxbDO.setXcdgajgjgdm( dto.getXcdgajgjgdm() );
        ksxtHjlzmsqxxbDO.setXcdgajgmc( dto.getXcdgajgmc() );
        ksxtHjlzmsqxxbDO.setXcdlxdh( dto.getXcdlxdh() );
        ksxtHjlzmsqxxbDO.setXcrxm( dto.getXcrxm() );
        ksxtHjlzmsqxxbDO.setXcrqsj( dto.getXcrqsj() );
        ksxtHjlzmsqxxbDO.setQyfwdm( dto.getQyfwdm() );
        ksxtHjlzmsqxxbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtHjlzmsqxxbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtHjlzmsqxxbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtHjlzmsqxxbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtHjlzmsqxxbDO.setRksj( dto.getRksj() );

        return ksxtHjlzmsqxxbDO;
    }

    @Override
    public KsxtHjlzmsqxxbDTO convertToDTO(KsxtHjlzmsqxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsxtHjlzmsqxxbDTO ksxtHjlzmsqxxbDTO = new KsxtHjlzmsqxxbDTO();

        ksxtHjlzmsqxxbDTO.setKsxtid( req.getKsxtid() );

        return ksxtHjlzmsqxxbDTO;
    }

    @Override
    public KsxtHjlzmsqxxbPageResp convertToPageResp(KsxtHjlzmsqxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtHjlzmsqxxbPageResp ksxtHjlzmsqxxbPageResp = new KsxtHjlzmsqxxbPageResp();

        ksxtHjlzmsqxxbPageResp.setKsxthjlzmid( dto.getKsxthjlzmid() );
        ksxtHjlzmsqxxbPageResp.setKsxtid( dto.getKsxtid() );
        ksxtHjlzmsqxxbPageResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtHjlzmsqxxbPageResp.setGmsfhm( dto.getGmsfhm() );
        ksxtHjlzmsqxxbPageResp.setXm( dto.getXm() );
        ksxtHjlzmsqxxbPageResp.setHjdzssxqdm( dto.getHjdzssxqdm() );
        ksxtHjlzmsqxxbPageResp.setHjdzqhnxxdz( dto.getHjdzqhnxxdz() );
        ksxtHjlzmsqxxbPageResp.setLxdh( dto.getLxdh() );
        ksxtHjlzmsqxxbPageResp.setHjzmlxjsxdm( dto.getHjzmlxjsxdm() );
        ksxtHjlzmsqxxbPageResp.setXyzmsxnrjyqk( dto.getXyzmsxnrjyqk() );
        ksxtHjlzmsqxxbPageResp.setKjhjlzmywlsh( dto.getKjhjlzmywlsh() );
        ksxtHjlzmsqxxbPageResp.setBz( dto.getBz() );
        ksxtHjlzmsqxxbPageResp.setHjxcjglxdm( dto.getHjxcjglxdm() );
        ksxtHjlzmsqxxbPageResp.setCzjgmsjyqk( dto.getCzjgmsjyqk() );
        ksxtHjlzmsqxxbPageResp.setBz1( dto.getBz1() );
        ksxtHjlzmsqxxbPageResp.setSqrq( dto.getSqrq() );
        ksxtHjlzmsqxxbPageResp.setSqdgajgjgdm( dto.getSqdgajgjgdm() );
        ksxtHjlzmsqxxbPageResp.setSqdgajgmc( dto.getSqdgajgmc() );
        ksxtHjlzmsqxxbPageResp.setSqdlxdh( dto.getSqdlxdh() );
        ksxtHjlzmsqxxbPageResp.setFkrq( dto.getFkrq() );
        ksxtHjlzmsqxxbPageResp.setXcdgajgjgdm( dto.getXcdgajgjgdm() );
        ksxtHjlzmsqxxbPageResp.setXcdgajgmc( dto.getXcdgajgmc() );
        ksxtHjlzmsqxxbPageResp.setXcdlxdh( dto.getXcdlxdh() );
        ksxtHjlzmsqxxbPageResp.setXcrxm( dto.getXcrxm() );
        ksxtHjlzmsqxxbPageResp.setXcrqsj( dto.getXcrqsj() );
        ksxtHjlzmsqxxbPageResp.setQyfwdm( dto.getQyfwdm() );
        ksxtHjlzmsqxxbPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtHjlzmsqxxbPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtHjlzmsqxxbPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtHjlzmsqxxbPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtHjlzmsqxxbPageResp.setRksj( dto.getRksj() );

        return ksxtHjlzmsqxxbPageResp;
    }

    @Override
    public KsxtHjlzmsqxxbViewResp convertToViewResp(KsxtHjlzmsqxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtHjlzmsqxxbViewResp ksxtHjlzmsqxxbViewResp = new KsxtHjlzmsqxxbViewResp();

        ksxtHjlzmsqxxbViewResp.setKsxthjlzmid( dto.getKsxthjlzmid() );
        ksxtHjlzmsqxxbViewResp.setKsxtid( dto.getKsxtid() );
        ksxtHjlzmsqxxbViewResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtHjlzmsqxxbViewResp.setGmsfhm( dto.getGmsfhm() );
        ksxtHjlzmsqxxbViewResp.setXm( dto.getXm() );
        ksxtHjlzmsqxxbViewResp.setHjdzssxqdm( dto.getHjdzssxqdm() );
        ksxtHjlzmsqxxbViewResp.setHjdzqhnxxdz( dto.getHjdzqhnxxdz() );
        ksxtHjlzmsqxxbViewResp.setLxdh( dto.getLxdh() );
        ksxtHjlzmsqxxbViewResp.setHjzmlxjsxdm( dto.getHjzmlxjsxdm() );
        ksxtHjlzmsqxxbViewResp.setXyzmsxnrjyqk( dto.getXyzmsxnrjyqk() );
        ksxtHjlzmsqxxbViewResp.setKjhjlzmywlsh( dto.getKjhjlzmywlsh() );
        ksxtHjlzmsqxxbViewResp.setBz( dto.getBz() );
        ksxtHjlzmsqxxbViewResp.setHjxcjglxdm( dto.getHjxcjglxdm() );
        ksxtHjlzmsqxxbViewResp.setCzjgmsjyqk( dto.getCzjgmsjyqk() );
        ksxtHjlzmsqxxbViewResp.setBz1( dto.getBz1() );
        ksxtHjlzmsqxxbViewResp.setSqrq( dto.getSqrq() );
        ksxtHjlzmsqxxbViewResp.setSqdgajgjgdm( dto.getSqdgajgjgdm() );
        ksxtHjlzmsqxxbViewResp.setSqdgajgmc( dto.getSqdgajgmc() );
        ksxtHjlzmsqxxbViewResp.setSqdlxdh( dto.getSqdlxdh() );
        ksxtHjlzmsqxxbViewResp.setFkrq( dto.getFkrq() );
        ksxtHjlzmsqxxbViewResp.setXcdgajgjgdm( dto.getXcdgajgjgdm() );
        ksxtHjlzmsqxxbViewResp.setXcdgajgmc( dto.getXcdgajgmc() );
        ksxtHjlzmsqxxbViewResp.setXcdlxdh( dto.getXcdlxdh() );
        ksxtHjlzmsqxxbViewResp.setXcrxm( dto.getXcrxm() );
        ksxtHjlzmsqxxbViewResp.setXcrqsj( dto.getXcrqsj() );
        ksxtHjlzmsqxxbViewResp.setQyfwdm( dto.getQyfwdm() );
        ksxtHjlzmsqxxbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        ksxtHjlzmsqxxbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        ksxtHjlzmsqxxbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtHjlzmsqxxbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtHjlzmsqxxbViewResp.setRksj( dto.getRksj() );

        return ksxtHjlzmsqxxbViewResp;
    }
}
