package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktHjslQcDTO;
import com.zjjcnt.project.ck.base.dto.req.RktHjslQcCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslQcPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslQcUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslQcCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslQcPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslQcViewResp;
import com.zjjcnt.project.ck.base.entity.RktHjslQcDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:41+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktHjslQcConvertImpl implements RktHjslQcConvert {

    @Override
    public RktHjslQcDTO convert(RktHjslQcDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktHjslQcDTO rktHjslQcDTO = new RktHjslQcDTO();

        rktHjslQcDTO.setId( entity.getId() );
        rktHjslQcDTO.setYwslnbbh( entity.getYwslnbbh() );
        rktHjslQcDTO.setHhnbid( entity.getHhnbid() );
        rktHjslQcDTO.setRyid( entity.getRyid() );
        rktHjslQcDTO.setRynbid( entity.getRynbid() );
        rktHjslQcDTO.setYwslh( entity.getYwslh() );
        rktHjslQcDTO.setHh( entity.getHh() );
        rktHjslQcDTO.setHhid( entity.getHhid() );
        rktHjslQcDTO.setHlx( entity.getHlx() );
        rktHjslQcDTO.setHmc( entity.getHmc() );
        rktHjslQcDTO.setHjddzbm( entity.getHjddzbm() );
        rktHjslQcDTO.setHjdssxq( entity.getHjdssxq() );
        rktHjslQcDTO.setHjdxxdz( entity.getHjdxxdz() );
        rktHjslQcDTO.setHjdrhyzbs( entity.getHjdrhyzbs() );
        rktHjslQcDTO.setJlx( entity.getJlx() );
        rktHjslQcDTO.setMlph( entity.getMlph() );
        rktHjslQcDTO.setMlxz( entity.getMlxz() );
        rktHjslQcDTO.setLcywlx( entity.getLcywlx() );
        rktHjslQcDTO.setLcdyid( entity.getLcdyid() );
        rktHjslQcDTO.setLcmc( entity.getLcmc() );
        rktHjslQcDTO.setLcslid( entity.getLcslid() );
        rktHjslQcDTO.setLcywbt( entity.getLcywbt() );
        rktHjslQcDTO.setLcrwjd( entity.getLcrwjd() );
        rktHjslQcDTO.setLczt( entity.getLczt() );
        rktHjslQcDTO.setBlzt( entity.getBlzt() );
        rktHjslQcDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        rktHjslQcDTO.setSqrxm( entity.getSqrxm() );
        rktHjslQcDTO.setSqrxb( entity.getSqrxb() );
        rktHjslQcDTO.setSqrlxdh( entity.getSqrlxdh() );
        rktHjslQcDTO.setSqrybdrgx( entity.getSqrybdrgx() );
        rktHjslQcDTO.setSqrzzssxq( entity.getSqrzzssxq() );
        rktHjslQcDTO.setSqrzzxz( entity.getSqrzzxz() );
        rktHjslQcDTO.setSqrhkdjjg( entity.getSqrhkdjjg() );
        rktHjslQcDTO.setSqrq( entity.getSqrq() );
        rktHjslQcDTO.setSpdwgajgjgdm( entity.getSpdwgajgjgdm() );
        rktHjslQcDTO.setSpdwgajgmc( entity.getSpdwgajgmc() );
        rktHjslQcDTO.setSprxm( entity.getSprxm() );
        rktHjslQcDTO.setSpsj( entity.getSpsj() );
        rktHjslQcDTO.setSpjgdm( entity.getSpjgdm() );
        rktHjslQcDTO.setSpyj( entity.getSpyj() );
        rktHjslQcDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        rktHjslQcDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        rktHjslQcDTO.setSlrxm( entity.getSlrxm() );
        rktHjslQcDTO.setSlsj( entity.getSlsj() );
        rktHjslQcDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        rktHjslQcDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        rktHjslQcDTO.setJcwh( entity.getJcwh() );
        rktHjslQcDTO.setBz( entity.getBz() );
        rktHjslQcDTO.setXh( entity.getXh() );
        rktHjslQcDTO.setHkxz( entity.getHkxz() );
        rktHjslQcDTO.setHb( entity.getHb() );
        rktHjslQcDTO.setYhzgx( entity.getYhzgx() );
        rktHjslQcDTO.setCxsx( entity.getCxsx() );
        rktHjslQcDTO.setGmsfhm( entity.getGmsfhm() );
        rktHjslQcDTO.setXm( entity.getXm() );
        rktHjslQcDTO.setX( entity.getX() );
        rktHjslQcDTO.setM( entity.getM() );
        rktHjslQcDTO.setCym( entity.getCym() );
        rktHjslQcDTO.setXmpy( entity.getXmpy() );
        rktHjslQcDTO.setCympy( entity.getCympy() );
        rktHjslQcDTO.setXb( entity.getXb() );
        rktHjslQcDTO.setMz( entity.getMz() );
        rktHjslQcDTO.setJggjdq( entity.getJggjdq() );
        rktHjslQcDTO.setJgssxq( entity.getJgssxq() );
        rktHjslQcDTO.setJgxz( entity.getJgxz() );
        rktHjslQcDTO.setCsrq( entity.getCsrq() );
        rktHjslQcDTO.setCssj( entity.getCssj() );
        rktHjslQcDTO.setCsdgjdq( entity.getCsdgjdq() );
        rktHjslQcDTO.setCsdssxq( entity.getCsdssxq() );
        rktHjslQcDTO.setCsdxz( entity.getCsdxz() );
        rktHjslQcDTO.setWhcd( entity.getWhcd() );
        rktHjslQcDTO.setHyzk( entity.getHyzk() );
        rktHjslQcDTO.setCyzkdwbm( entity.getCyzkdwbm() );
        rktHjslQcDTO.setCyzkdwmc( entity.getCyzkdwmc() );
        rktHjslQcDTO.setZy( entity.getZy() );
        rktHjslQcDTO.setZylb( entity.getZylb() );
        rktHjslQcDTO.setZjxy( entity.getZjxy() );
        rktHjslQcDTO.setSg( entity.getSg() );
        rktHjslQcDTO.setXx( entity.getXx() );
        rktHjslQcDTO.setByzk( entity.getByzk() );
        rktHjslQcDTO.setXxjb( entity.getXxjb() );
        rktHjslQcDTO.setLxdh( entity.getLxdh() );
        rktHjslQcDTO.setFqxm( entity.getFqxm() );
        rktHjslQcDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        rktHjslQcDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        rktHjslQcDTO.setFqzjhm( entity.getFqzjhm() );
        rktHjslQcDTO.setFqwwx( entity.getFqwwx() );
        rktHjslQcDTO.setFqwwm( entity.getFqwwm() );
        rktHjslQcDTO.setMqxm( entity.getMqxm() );
        rktHjslQcDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        rktHjslQcDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        rktHjslQcDTO.setMqzjhm( entity.getMqzjhm() );
        rktHjslQcDTO.setMqwwx( entity.getMqwwx() );
        rktHjslQcDTO.setMqwwm( entity.getMqwwm() );
        rktHjslQcDTO.setPoxm( entity.getPoxm() );
        rktHjslQcDTO.setPogmsfhm( entity.getPogmsfhm() );
        rktHjslQcDTO.setPocyzjdm( entity.getPocyzjdm() );
        rktHjslQcDTO.setPozjhm( entity.getPozjhm() );
        rktHjslQcDTO.setPowwx( entity.getPowwx() );
        rktHjslQcDTO.setPowwm( entity.getPowwm() );
        rktHjslQcDTO.setJhryxm( entity.getJhryxm() );
        rktHjslQcDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        rktHjslQcDTO.setJhryjhgx( entity.getJhryjhgx() );
        rktHjslQcDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        rktHjslQcDTO.setJhryzjhm( entity.getJhryzjhm() );
        rktHjslQcDTO.setJhrywwx( entity.getJhrywwx() );
        rktHjslQcDTO.setJhrywwm( entity.getJhrywwm() );
        rktHjslQcDTO.setJhrylxdh( entity.getJhrylxdh() );
        rktHjslQcDTO.setJhrexm( entity.getJhrexm() );
        rktHjslQcDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        rktHjslQcDTO.setJhrejhgx( entity.getJhrejhgx() );
        rktHjslQcDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        rktHjslQcDTO.setJhrezjhm( entity.getJhrezjhm() );
        rktHjslQcDTO.setJhrewwx( entity.getJhrewwx() );
        rktHjslQcDTO.setJhrewwm( entity.getJhrewwm() );
        rktHjslQcDTO.setJhrelxdh( entity.getJhrelxdh() );
        rktHjslQcDTO.setZjlb( entity.getZjlb() );
        rktHjslQcDTO.setQfjg( entity.getQfjg() );
        rktHjslQcDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        rktHjslQcDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        rktHjslQcDTO.setQclb( entity.getQclb() );
        rktHjslQcDTO.setQcrq( entity.getQcrq() );
        rktHjslQcDTO.setQwdgjdq( entity.getQwdgjdq() );
        rktHjslQcDTO.setQwdssxq( entity.getQwdssxq() );
        rktHjslQcDTO.setQwdxz( entity.getQwdxz() );
        rktHjslQcDTO.setZqzbh( entity.getZqzbh() );
        rktHjslQcDTO.setQyzbh( entity.getQyzbh() );
        rktHjslQcDTO.setBdfw( entity.getBdfw() );
        rktHjslQcDTO.setQyyyxflb( entity.getQyyyxflb() );
        rktHjslQcDTO.setQyyybcsm( entity.getQyyybcsm() );
        rktHjslQcDTO.setQyldyy( entity.getQyldyy() );
        rktHjslQcDTO.setBlhjywid( entity.getBlhjywid() );
        rktHjslQcDTO.setHjywblsj( entity.getHjywblsj() );
        rktHjslQcDTO.setQtssxq( entity.getQtssxq() );
        rktHjslQcDTO.setQtzz( entity.getQtzz() );

        return rktHjslQcDTO;
    }

    @Override
    public RktHjslQcDO convertToDO(RktHjslQcDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslQcDO rktHjslQcDO = new RktHjslQcDO();

        rktHjslQcDO.setId( dto.getId() );
        rktHjslQcDO.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslQcDO.setHhnbid( dto.getHhnbid() );
        rktHjslQcDO.setRyid( dto.getRyid() );
        rktHjslQcDO.setRynbid( dto.getRynbid() );
        rktHjslQcDO.setYwslh( dto.getYwslh() );
        rktHjslQcDO.setHh( dto.getHh() );
        rktHjslQcDO.setHhid( dto.getHhid() );
        rktHjslQcDO.setHlx( dto.getHlx() );
        rktHjslQcDO.setHmc( dto.getHmc() );
        rktHjslQcDO.setHjddzbm( dto.getHjddzbm() );
        rktHjslQcDO.setHjdssxq( dto.getHjdssxq() );
        rktHjslQcDO.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslQcDO.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslQcDO.setJlx( dto.getJlx() );
        rktHjslQcDO.setMlph( dto.getMlph() );
        rktHjslQcDO.setMlxz( dto.getMlxz() );
        rktHjslQcDO.setLcywlx( dto.getLcywlx() );
        rktHjslQcDO.setLcdyid( dto.getLcdyid() );
        rktHjslQcDO.setLcmc( dto.getLcmc() );
        rktHjslQcDO.setLcslid( dto.getLcslid() );
        rktHjslQcDO.setLcywbt( dto.getLcywbt() );
        rktHjslQcDO.setLcrwjd( dto.getLcrwjd() );
        rktHjslQcDO.setLczt( dto.getLczt() );
        rktHjslQcDO.setBlzt( dto.getBlzt() );
        rktHjslQcDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslQcDO.setSqrxm( dto.getSqrxm() );
        rktHjslQcDO.setSqrxb( dto.getSqrxb() );
        rktHjslQcDO.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslQcDO.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslQcDO.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslQcDO.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslQcDO.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslQcDO.setSqrq( dto.getSqrq() );
        rktHjslQcDO.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslQcDO.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslQcDO.setSprxm( dto.getSprxm() );
        rktHjslQcDO.setSpsj( dto.getSpsj() );
        rktHjslQcDO.setSpjgdm( dto.getSpjgdm() );
        rktHjslQcDO.setSpyj( dto.getSpyj() );
        rktHjslQcDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslQcDO.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslQcDO.setSlrxm( dto.getSlrxm() );
        rktHjslQcDO.setSlsj( dto.getSlsj() );
        rktHjslQcDO.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslQcDO.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslQcDO.setJcwh( dto.getJcwh() );
        rktHjslQcDO.setBz( dto.getBz() );
        rktHjslQcDO.setXh( dto.getXh() );
        rktHjslQcDO.setHkxz( dto.getHkxz() );
        rktHjslQcDO.setHb( dto.getHb() );
        rktHjslQcDO.setYhzgx( dto.getYhzgx() );
        rktHjslQcDO.setCxsx( dto.getCxsx() );
        rktHjslQcDO.setGmsfhm( dto.getGmsfhm() );
        rktHjslQcDO.setXm( dto.getXm() );
        rktHjslQcDO.setX( dto.getX() );
        rktHjslQcDO.setM( dto.getM() );
        rktHjslQcDO.setCym( dto.getCym() );
        rktHjslQcDO.setXmpy( dto.getXmpy() );
        rktHjslQcDO.setCympy( dto.getCympy() );
        rktHjslQcDO.setXb( dto.getXb() );
        rktHjslQcDO.setMz( dto.getMz() );
        rktHjslQcDO.setJggjdq( dto.getJggjdq() );
        rktHjslQcDO.setJgssxq( dto.getJgssxq() );
        rktHjslQcDO.setJgxz( dto.getJgxz() );
        rktHjslQcDO.setCsrq( dto.getCsrq() );
        rktHjslQcDO.setCssj( dto.getCssj() );
        rktHjslQcDO.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslQcDO.setCsdssxq( dto.getCsdssxq() );
        rktHjslQcDO.setCsdxz( dto.getCsdxz() );
        rktHjslQcDO.setWhcd( dto.getWhcd() );
        rktHjslQcDO.setHyzk( dto.getHyzk() );
        rktHjslQcDO.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslQcDO.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslQcDO.setZy( dto.getZy() );
        rktHjslQcDO.setZylb( dto.getZylb() );
        rktHjslQcDO.setZjxy( dto.getZjxy() );
        rktHjslQcDO.setSg( dto.getSg() );
        rktHjslQcDO.setXx( dto.getXx() );
        rktHjslQcDO.setByzk( dto.getByzk() );
        rktHjslQcDO.setXxjb( dto.getXxjb() );
        rktHjslQcDO.setLxdh( dto.getLxdh() );
        rktHjslQcDO.setFqxm( dto.getFqxm() );
        rktHjslQcDO.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslQcDO.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslQcDO.setFqzjhm( dto.getFqzjhm() );
        rktHjslQcDO.setFqwwx( dto.getFqwwx() );
        rktHjslQcDO.setFqwwm( dto.getFqwwm() );
        rktHjslQcDO.setMqxm( dto.getMqxm() );
        rktHjslQcDO.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslQcDO.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslQcDO.setMqzjhm( dto.getMqzjhm() );
        rktHjslQcDO.setMqwwx( dto.getMqwwx() );
        rktHjslQcDO.setMqwwm( dto.getMqwwm() );
        rktHjslQcDO.setPoxm( dto.getPoxm() );
        rktHjslQcDO.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslQcDO.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslQcDO.setPozjhm( dto.getPozjhm() );
        rktHjslQcDO.setPowwx( dto.getPowwx() );
        rktHjslQcDO.setPowwm( dto.getPowwm() );
        rktHjslQcDO.setJhryxm( dto.getJhryxm() );
        rktHjslQcDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslQcDO.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslQcDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslQcDO.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslQcDO.setJhrywwx( dto.getJhrywwx() );
        rktHjslQcDO.setJhrywwm( dto.getJhrywwm() );
        rktHjslQcDO.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslQcDO.setJhrexm( dto.getJhrexm() );
        rktHjslQcDO.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslQcDO.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslQcDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslQcDO.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslQcDO.setJhrewwx( dto.getJhrewwx() );
        rktHjslQcDO.setJhrewwm( dto.getJhrewwm() );
        rktHjslQcDO.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslQcDO.setZjlb( dto.getZjlb() );
        rktHjslQcDO.setQfjg( dto.getQfjg() );
        rktHjslQcDO.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslQcDO.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslQcDO.setQclb( dto.getQclb() );
        rktHjslQcDO.setQcrq( dto.getQcrq() );
        rktHjslQcDO.setQwdgjdq( dto.getQwdgjdq() );
        rktHjslQcDO.setQwdssxq( dto.getQwdssxq() );
        rktHjslQcDO.setQwdxz( dto.getQwdxz() );
        rktHjslQcDO.setZqzbh( dto.getZqzbh() );
        rktHjslQcDO.setQyzbh( dto.getQyzbh() );
        rktHjslQcDO.setBdfw( dto.getBdfw() );
        rktHjslQcDO.setQyyyxflb( dto.getQyyyxflb() );
        rktHjslQcDO.setQyyybcsm( dto.getQyyybcsm() );
        rktHjslQcDO.setQyldyy( dto.getQyldyy() );
        rktHjslQcDO.setBlhjywid( dto.getBlhjywid() );
        rktHjslQcDO.setHjywblsj( dto.getHjywblsj() );
        rktHjslQcDO.setQtssxq( dto.getQtssxq() );
        rktHjslQcDO.setQtzz( dto.getQtzz() );

        return rktHjslQcDO;
    }

    @Override
    public RktHjslQcDTO convertToDTO(RktHjslQcPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslQcDTO rktHjslQcDTO = new RktHjslQcDTO();

        rktHjslQcDTO.setHhnbid( req.getHhnbid() );
        rktHjslQcDTO.setRyid( req.getRyid() );
        rktHjslQcDTO.setRynbid( req.getRynbid() );
        rktHjslQcDTO.setYwslh( req.getYwslh() );
        rktHjslQcDTO.setHh( req.getHh() );
        rktHjslQcDTO.setHhid( req.getHhid() );
        rktHjslQcDTO.setHlx( req.getHlx() );
        rktHjslQcDTO.setHmc( req.getHmc() );
        rktHjslQcDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslQcDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslQcDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslQcDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslQcDTO.setJlx( req.getJlx() );
        rktHjslQcDTO.setMlph( req.getMlph() );
        rktHjslQcDTO.setMlxz( req.getMlxz() );
        rktHjslQcDTO.setLcywlx( req.getLcywlx() );
        rktHjslQcDTO.setLcdyid( req.getLcdyid() );
        rktHjslQcDTO.setLcmc( req.getLcmc() );
        rktHjslQcDTO.setLcslid( req.getLcslid() );
        rktHjslQcDTO.setLcywbt( req.getLcywbt() );
        rktHjslQcDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslQcDTO.setLczt( req.getLczt() );
        rktHjslQcDTO.setBlzt( req.getBlzt() );
        rktHjslQcDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslQcDTO.setSqrxm( req.getSqrxm() );
        rktHjslQcDTO.setSqrxb( req.getSqrxb() );
        rktHjslQcDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslQcDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslQcDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslQcDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslQcDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslQcDTO.setSqrq( req.getSqrq() );
        rktHjslQcDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslQcDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslQcDTO.setSprxm( req.getSprxm() );
        rktHjslQcDTO.setSpsj( req.getSpsj() );
        rktHjslQcDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslQcDTO.setSpyj( req.getSpyj() );
        rktHjslQcDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslQcDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslQcDTO.setSlrxm( req.getSlrxm() );
        rktHjslQcDTO.setSlsj( req.getSlsj() );
        rktHjslQcDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslQcDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslQcDTO.setJcwh( req.getJcwh() );
        rktHjslQcDTO.setBz( req.getBz() );
        rktHjslQcDTO.setXh( req.getXh() );
        rktHjslQcDTO.setHkxz( req.getHkxz() );
        rktHjslQcDTO.setHb( req.getHb() );
        rktHjslQcDTO.setYhzgx( req.getYhzgx() );
        rktHjslQcDTO.setCxsx( req.getCxsx() );
        rktHjslQcDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslQcDTO.setXm( req.getXm() );
        rktHjslQcDTO.setX( req.getX() );
        rktHjslQcDTO.setM( req.getM() );
        rktHjslQcDTO.setCym( req.getCym() );
        rktHjslQcDTO.setXmpy( req.getXmpy() );
        rktHjslQcDTO.setCympy( req.getCympy() );
        rktHjslQcDTO.setXb( req.getXb() );
        rktHjslQcDTO.setMz( req.getMz() );
        rktHjslQcDTO.setJggjdq( req.getJggjdq() );
        rktHjslQcDTO.setJgssxq( req.getJgssxq() );
        rktHjslQcDTO.setJgxz( req.getJgxz() );
        rktHjslQcDTO.setCsrq( req.getCsrq() );
        rktHjslQcDTO.setCssj( req.getCssj() );
        rktHjslQcDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslQcDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslQcDTO.setCsdxz( req.getCsdxz() );
        rktHjslQcDTO.setWhcd( req.getWhcd() );
        rktHjslQcDTO.setHyzk( req.getHyzk() );
        rktHjslQcDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslQcDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslQcDTO.setZy( req.getZy() );
        rktHjslQcDTO.setZylb( req.getZylb() );
        rktHjslQcDTO.setZjxy( req.getZjxy() );
        rktHjslQcDTO.setSg( req.getSg() );
        rktHjslQcDTO.setXx( req.getXx() );
        rktHjslQcDTO.setByzk( req.getByzk() );
        rktHjslQcDTO.setXxjb( req.getXxjb() );
        rktHjslQcDTO.setLxdh( req.getLxdh() );
        rktHjslQcDTO.setFqxm( req.getFqxm() );
        rktHjslQcDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslQcDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslQcDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslQcDTO.setFqwwx( req.getFqwwx() );
        rktHjslQcDTO.setFqwwm( req.getFqwwm() );
        rktHjslQcDTO.setMqxm( req.getMqxm() );
        rktHjslQcDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslQcDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslQcDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslQcDTO.setMqwwx( req.getMqwwx() );
        rktHjslQcDTO.setMqwwm( req.getMqwwm() );
        rktHjslQcDTO.setPoxm( req.getPoxm() );
        rktHjslQcDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslQcDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslQcDTO.setPozjhm( req.getPozjhm() );
        rktHjslQcDTO.setPowwx( req.getPowwx() );
        rktHjslQcDTO.setPowwm( req.getPowwm() );
        rktHjslQcDTO.setJhryxm( req.getJhryxm() );
        rktHjslQcDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslQcDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslQcDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslQcDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslQcDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslQcDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslQcDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslQcDTO.setJhrexm( req.getJhrexm() );
        rktHjslQcDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslQcDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslQcDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslQcDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslQcDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslQcDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslQcDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslQcDTO.setZjlb( req.getZjlb() );
        rktHjslQcDTO.setQfjg( req.getQfjg() );
        rktHjslQcDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslQcDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslQcDTO.setQclb( req.getQclb() );
        rktHjslQcDTO.setQcrq( req.getQcrq() );
        rktHjslQcDTO.setQwdgjdq( req.getQwdgjdq() );
        rktHjslQcDTO.setQwdssxq( req.getQwdssxq() );
        rktHjslQcDTO.setQwdxz( req.getQwdxz() );
        rktHjslQcDTO.setZqzbh( req.getZqzbh() );
        rktHjslQcDTO.setQyzbh( req.getQyzbh() );
        rktHjslQcDTO.setBdfw( req.getBdfw() );
        rktHjslQcDTO.setQyyyxflb( req.getQyyyxflb() );
        rktHjslQcDTO.setQyyybcsm( req.getQyyybcsm() );
        rktHjslQcDTO.setQyldyy( req.getQyldyy() );
        rktHjslQcDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslQcDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslQcDTO.setQtssxq( req.getQtssxq() );
        rktHjslQcDTO.setQtzz( req.getQtzz() );

        return rktHjslQcDTO;
    }

    @Override
    public RktHjslQcDTO convertToDTO(RktHjslQcCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslQcDTO rktHjslQcDTO = new RktHjslQcDTO();

        rktHjslQcDTO.setHhnbid( req.getHhnbid() );
        rktHjslQcDTO.setRyid( req.getRyid() );
        rktHjslQcDTO.setRynbid( req.getRynbid() );
        rktHjslQcDTO.setYwslh( req.getYwslh() );
        rktHjslQcDTO.setHh( req.getHh() );
        rktHjslQcDTO.setHhid( req.getHhid() );
        rktHjslQcDTO.setHlx( req.getHlx() );
        rktHjslQcDTO.setHmc( req.getHmc() );
        rktHjslQcDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslQcDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslQcDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslQcDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslQcDTO.setJlx( req.getJlx() );
        rktHjslQcDTO.setMlph( req.getMlph() );
        rktHjslQcDTO.setMlxz( req.getMlxz() );
        rktHjslQcDTO.setLcywlx( req.getLcywlx() );
        rktHjslQcDTO.setLcdyid( req.getLcdyid() );
        rktHjslQcDTO.setLcmc( req.getLcmc() );
        rktHjslQcDTO.setLcslid( req.getLcslid() );
        rktHjslQcDTO.setLcywbt( req.getLcywbt() );
        rktHjslQcDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslQcDTO.setLczt( req.getLczt() );
        rktHjslQcDTO.setBlzt( req.getBlzt() );
        rktHjslQcDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslQcDTO.setSqrxm( req.getSqrxm() );
        rktHjslQcDTO.setSqrxb( req.getSqrxb() );
        rktHjslQcDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslQcDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslQcDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslQcDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslQcDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslQcDTO.setSqrq( req.getSqrq() );
        rktHjslQcDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslQcDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslQcDTO.setSprxm( req.getSprxm() );
        rktHjslQcDTO.setSpsj( req.getSpsj() );
        rktHjslQcDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslQcDTO.setSpyj( req.getSpyj() );
        rktHjslQcDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslQcDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslQcDTO.setSlrxm( req.getSlrxm() );
        rktHjslQcDTO.setSlsj( req.getSlsj() );
        rktHjslQcDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslQcDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslQcDTO.setJcwh( req.getJcwh() );
        rktHjslQcDTO.setBz( req.getBz() );
        rktHjslQcDTO.setXh( req.getXh() );
        rktHjslQcDTO.setHkxz( req.getHkxz() );
        rktHjslQcDTO.setHb( req.getHb() );
        rktHjslQcDTO.setYhzgx( req.getYhzgx() );
        rktHjslQcDTO.setCxsx( req.getCxsx() );
        rktHjslQcDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslQcDTO.setXm( req.getXm() );
        rktHjslQcDTO.setX( req.getX() );
        rktHjslQcDTO.setM( req.getM() );
        rktHjslQcDTO.setCym( req.getCym() );
        rktHjslQcDTO.setXmpy( req.getXmpy() );
        rktHjslQcDTO.setCympy( req.getCympy() );
        rktHjslQcDTO.setXb( req.getXb() );
        rktHjslQcDTO.setMz( req.getMz() );
        rktHjslQcDTO.setJggjdq( req.getJggjdq() );
        rktHjslQcDTO.setJgssxq( req.getJgssxq() );
        rktHjslQcDTO.setJgxz( req.getJgxz() );
        rktHjslQcDTO.setCsrq( req.getCsrq() );
        rktHjslQcDTO.setCssj( req.getCssj() );
        rktHjslQcDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslQcDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslQcDTO.setCsdxz( req.getCsdxz() );
        rktHjslQcDTO.setWhcd( req.getWhcd() );
        rktHjslQcDTO.setHyzk( req.getHyzk() );
        rktHjslQcDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslQcDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslQcDTO.setZy( req.getZy() );
        rktHjslQcDTO.setZylb( req.getZylb() );
        rktHjslQcDTO.setZjxy( req.getZjxy() );
        rktHjslQcDTO.setSg( req.getSg() );
        rktHjslQcDTO.setXx( req.getXx() );
        rktHjslQcDTO.setByzk( req.getByzk() );
        rktHjslQcDTO.setXxjb( req.getXxjb() );
        rktHjslQcDTO.setLxdh( req.getLxdh() );
        rktHjslQcDTO.setFqxm( req.getFqxm() );
        rktHjslQcDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslQcDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslQcDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslQcDTO.setFqwwx( req.getFqwwx() );
        rktHjslQcDTO.setFqwwm( req.getFqwwm() );
        rktHjslQcDTO.setMqxm( req.getMqxm() );
        rktHjslQcDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslQcDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslQcDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslQcDTO.setMqwwx( req.getMqwwx() );
        rktHjslQcDTO.setMqwwm( req.getMqwwm() );
        rktHjslQcDTO.setPoxm( req.getPoxm() );
        rktHjslQcDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslQcDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslQcDTO.setPozjhm( req.getPozjhm() );
        rktHjslQcDTO.setPowwx( req.getPowwx() );
        rktHjslQcDTO.setPowwm( req.getPowwm() );
        rktHjslQcDTO.setJhryxm( req.getJhryxm() );
        rktHjslQcDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslQcDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslQcDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslQcDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslQcDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslQcDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslQcDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslQcDTO.setJhrexm( req.getJhrexm() );
        rktHjslQcDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslQcDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslQcDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslQcDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslQcDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslQcDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslQcDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslQcDTO.setZjlb( req.getZjlb() );
        rktHjslQcDTO.setQfjg( req.getQfjg() );
        rktHjslQcDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslQcDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslQcDTO.setQclb( req.getQclb() );
        rktHjslQcDTO.setQcrq( req.getQcrq() );
        rktHjslQcDTO.setQwdgjdq( req.getQwdgjdq() );
        rktHjslQcDTO.setQwdssxq( req.getQwdssxq() );
        rktHjslQcDTO.setQwdxz( req.getQwdxz() );
        rktHjslQcDTO.setZqzbh( req.getZqzbh() );
        rktHjslQcDTO.setQyzbh( req.getQyzbh() );
        rktHjslQcDTO.setBdfw( req.getBdfw() );
        rktHjslQcDTO.setQyyyxflb( req.getQyyyxflb() );
        rktHjslQcDTO.setQyyybcsm( req.getQyyybcsm() );
        rktHjslQcDTO.setQyldyy( req.getQyldyy() );
        rktHjslQcDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslQcDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslQcDTO.setQtssxq( req.getQtssxq() );
        rktHjslQcDTO.setQtzz( req.getQtzz() );

        return rktHjslQcDTO;
    }

    @Override
    public RktHjslQcDTO convertToDTO(RktHjslQcUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslQcDTO rktHjslQcDTO = new RktHjslQcDTO();

        rktHjslQcDTO.setYwslnbbh( req.getYwslnbbh() );
        rktHjslQcDTO.setHhnbid( req.getHhnbid() );
        rktHjslQcDTO.setRyid( req.getRyid() );
        rktHjslQcDTO.setRynbid( req.getRynbid() );
        rktHjslQcDTO.setYwslh( req.getYwslh() );
        rktHjslQcDTO.setHh( req.getHh() );
        rktHjslQcDTO.setHhid( req.getHhid() );
        rktHjslQcDTO.setHlx( req.getHlx() );
        rktHjslQcDTO.setHmc( req.getHmc() );
        rktHjslQcDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslQcDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslQcDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslQcDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslQcDTO.setJlx( req.getJlx() );
        rktHjslQcDTO.setMlph( req.getMlph() );
        rktHjslQcDTO.setMlxz( req.getMlxz() );
        rktHjslQcDTO.setLcywlx( req.getLcywlx() );
        rktHjslQcDTO.setLcdyid( req.getLcdyid() );
        rktHjslQcDTO.setLcmc( req.getLcmc() );
        rktHjslQcDTO.setLcslid( req.getLcslid() );
        rktHjslQcDTO.setLcywbt( req.getLcywbt() );
        rktHjslQcDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslQcDTO.setLczt( req.getLczt() );
        rktHjslQcDTO.setBlzt( req.getBlzt() );
        rktHjslQcDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslQcDTO.setSqrxm( req.getSqrxm() );
        rktHjslQcDTO.setSqrxb( req.getSqrxb() );
        rktHjslQcDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslQcDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslQcDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslQcDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslQcDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslQcDTO.setSqrq( req.getSqrq() );
        rktHjslQcDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslQcDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslQcDTO.setSprxm( req.getSprxm() );
        rktHjslQcDTO.setSpsj( req.getSpsj() );
        rktHjslQcDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslQcDTO.setSpyj( req.getSpyj() );
        rktHjslQcDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslQcDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslQcDTO.setSlrxm( req.getSlrxm() );
        rktHjslQcDTO.setSlsj( req.getSlsj() );
        rktHjslQcDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslQcDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslQcDTO.setJcwh( req.getJcwh() );
        rktHjslQcDTO.setBz( req.getBz() );
        rktHjslQcDTO.setXh( req.getXh() );
        rktHjslQcDTO.setHkxz( req.getHkxz() );
        rktHjslQcDTO.setHb( req.getHb() );
        rktHjslQcDTO.setYhzgx( req.getYhzgx() );
        rktHjslQcDTO.setCxsx( req.getCxsx() );
        rktHjslQcDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslQcDTO.setXm( req.getXm() );
        rktHjslQcDTO.setX( req.getX() );
        rktHjslQcDTO.setM( req.getM() );
        rktHjslQcDTO.setCym( req.getCym() );
        rktHjslQcDTO.setXmpy( req.getXmpy() );
        rktHjslQcDTO.setCympy( req.getCympy() );
        rktHjslQcDTO.setXb( req.getXb() );
        rktHjslQcDTO.setMz( req.getMz() );
        rktHjslQcDTO.setJggjdq( req.getJggjdq() );
        rktHjslQcDTO.setJgssxq( req.getJgssxq() );
        rktHjslQcDTO.setJgxz( req.getJgxz() );
        rktHjslQcDTO.setCsrq( req.getCsrq() );
        rktHjslQcDTO.setCssj( req.getCssj() );
        rktHjslQcDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslQcDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslQcDTO.setCsdxz( req.getCsdxz() );
        rktHjslQcDTO.setWhcd( req.getWhcd() );
        rktHjslQcDTO.setHyzk( req.getHyzk() );
        rktHjslQcDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslQcDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslQcDTO.setZy( req.getZy() );
        rktHjslQcDTO.setZylb( req.getZylb() );
        rktHjslQcDTO.setZjxy( req.getZjxy() );
        rktHjslQcDTO.setSg( req.getSg() );
        rktHjslQcDTO.setXx( req.getXx() );
        rktHjslQcDTO.setByzk( req.getByzk() );
        rktHjslQcDTO.setXxjb( req.getXxjb() );
        rktHjslQcDTO.setLxdh( req.getLxdh() );
        rktHjslQcDTO.setFqxm( req.getFqxm() );
        rktHjslQcDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslQcDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslQcDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslQcDTO.setFqwwx( req.getFqwwx() );
        rktHjslQcDTO.setFqwwm( req.getFqwwm() );
        rktHjslQcDTO.setMqxm( req.getMqxm() );
        rktHjslQcDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslQcDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslQcDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslQcDTO.setMqwwx( req.getMqwwx() );
        rktHjslQcDTO.setMqwwm( req.getMqwwm() );
        rktHjslQcDTO.setPoxm( req.getPoxm() );
        rktHjslQcDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslQcDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslQcDTO.setPozjhm( req.getPozjhm() );
        rktHjslQcDTO.setPowwx( req.getPowwx() );
        rktHjslQcDTO.setPowwm( req.getPowwm() );
        rktHjslQcDTO.setJhryxm( req.getJhryxm() );
        rktHjslQcDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslQcDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslQcDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslQcDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslQcDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslQcDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslQcDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslQcDTO.setJhrexm( req.getJhrexm() );
        rktHjslQcDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslQcDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslQcDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslQcDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslQcDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslQcDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslQcDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslQcDTO.setZjlb( req.getZjlb() );
        rktHjslQcDTO.setQfjg( req.getQfjg() );
        rktHjslQcDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslQcDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslQcDTO.setQclb( req.getQclb() );
        rktHjslQcDTO.setQcrq( req.getQcrq() );
        rktHjslQcDTO.setQwdgjdq( req.getQwdgjdq() );
        rktHjslQcDTO.setQwdssxq( req.getQwdssxq() );
        rktHjslQcDTO.setQwdxz( req.getQwdxz() );
        rktHjslQcDTO.setZqzbh( req.getZqzbh() );
        rktHjslQcDTO.setQyzbh( req.getQyzbh() );
        rktHjslQcDTO.setBdfw( req.getBdfw() );
        rktHjslQcDTO.setQyyyxflb( req.getQyyyxflb() );
        rktHjslQcDTO.setQyyybcsm( req.getQyyybcsm() );
        rktHjslQcDTO.setQyldyy( req.getQyldyy() );
        rktHjslQcDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslQcDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslQcDTO.setQtssxq( req.getQtssxq() );
        rktHjslQcDTO.setQtzz( req.getQtzz() );

        return rktHjslQcDTO;
    }

    @Override
    public RktHjslQcPageResp convertToPageResp(RktHjslQcDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslQcPageResp rktHjslQcPageResp = new RktHjslQcPageResp();

        rktHjslQcPageResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslQcPageResp.setHhnbid( dto.getHhnbid() );
        rktHjslQcPageResp.setRyid( dto.getRyid() );
        rktHjslQcPageResp.setRynbid( dto.getRynbid() );
        rktHjslQcPageResp.setYwslh( dto.getYwslh() );
        rktHjslQcPageResp.setHh( dto.getHh() );
        rktHjslQcPageResp.setHhid( dto.getHhid() );
        rktHjslQcPageResp.setHlx( dto.getHlx() );
        rktHjslQcPageResp.setHmc( dto.getHmc() );
        rktHjslQcPageResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslQcPageResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslQcPageResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslQcPageResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslQcPageResp.setJlx( dto.getJlx() );
        rktHjslQcPageResp.setMlph( dto.getMlph() );
        rktHjslQcPageResp.setMlxz( dto.getMlxz() );
        rktHjslQcPageResp.setLcywlx( dto.getLcywlx() );
        rktHjslQcPageResp.setLcdyid( dto.getLcdyid() );
        rktHjslQcPageResp.setLcmc( dto.getLcmc() );
        rktHjslQcPageResp.setLcslid( dto.getLcslid() );
        rktHjslQcPageResp.setLcywbt( dto.getLcywbt() );
        rktHjslQcPageResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslQcPageResp.setLczt( dto.getLczt() );
        rktHjslQcPageResp.setBlzt( dto.getBlzt() );
        rktHjslQcPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslQcPageResp.setSqrxm( dto.getSqrxm() );
        rktHjslQcPageResp.setSqrxb( dto.getSqrxb() );
        rktHjslQcPageResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslQcPageResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslQcPageResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslQcPageResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslQcPageResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslQcPageResp.setSqrq( dto.getSqrq() );
        rktHjslQcPageResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslQcPageResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslQcPageResp.setSprxm( dto.getSprxm() );
        rktHjslQcPageResp.setSpsj( dto.getSpsj() );
        rktHjslQcPageResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslQcPageResp.setSpyj( dto.getSpyj() );
        rktHjslQcPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslQcPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslQcPageResp.setSlrxm( dto.getSlrxm() );
        rktHjslQcPageResp.setSlsj( dto.getSlsj() );
        rktHjslQcPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslQcPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslQcPageResp.setJcwh( dto.getJcwh() );
        rktHjslQcPageResp.setBz( dto.getBz() );
        rktHjslQcPageResp.setXh( dto.getXh() );
        rktHjslQcPageResp.setHkxz( dto.getHkxz() );
        rktHjslQcPageResp.setHb( dto.getHb() );
        rktHjslQcPageResp.setYhzgx( dto.getYhzgx() );
        rktHjslQcPageResp.setCxsx( dto.getCxsx() );
        rktHjslQcPageResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslQcPageResp.setXm( dto.getXm() );
        rktHjslQcPageResp.setX( dto.getX() );
        rktHjslQcPageResp.setM( dto.getM() );
        rktHjslQcPageResp.setCym( dto.getCym() );
        rktHjslQcPageResp.setXmpy( dto.getXmpy() );
        rktHjslQcPageResp.setCympy( dto.getCympy() );
        rktHjslQcPageResp.setXb( dto.getXb() );
        rktHjslQcPageResp.setMz( dto.getMz() );
        rktHjslQcPageResp.setJggjdq( dto.getJggjdq() );
        rktHjslQcPageResp.setJgssxq( dto.getJgssxq() );
        rktHjslQcPageResp.setJgxz( dto.getJgxz() );
        rktHjslQcPageResp.setCsrq( dto.getCsrq() );
        rktHjslQcPageResp.setCssj( dto.getCssj() );
        rktHjslQcPageResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslQcPageResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslQcPageResp.setCsdxz( dto.getCsdxz() );
        rktHjslQcPageResp.setWhcd( dto.getWhcd() );
        rktHjslQcPageResp.setHyzk( dto.getHyzk() );
        rktHjslQcPageResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslQcPageResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslQcPageResp.setZy( dto.getZy() );
        rktHjslQcPageResp.setZylb( dto.getZylb() );
        rktHjslQcPageResp.setZjxy( dto.getZjxy() );
        rktHjslQcPageResp.setSg( dto.getSg() );
        rktHjslQcPageResp.setXx( dto.getXx() );
        rktHjslQcPageResp.setByzk( dto.getByzk() );
        rktHjslQcPageResp.setXxjb( dto.getXxjb() );
        rktHjslQcPageResp.setLxdh( dto.getLxdh() );
        rktHjslQcPageResp.setFqxm( dto.getFqxm() );
        rktHjslQcPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslQcPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslQcPageResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslQcPageResp.setFqwwx( dto.getFqwwx() );
        rktHjslQcPageResp.setFqwwm( dto.getFqwwm() );
        rktHjslQcPageResp.setMqxm( dto.getMqxm() );
        rktHjslQcPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslQcPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslQcPageResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslQcPageResp.setMqwwx( dto.getMqwwx() );
        rktHjslQcPageResp.setMqwwm( dto.getMqwwm() );
        rktHjslQcPageResp.setPoxm( dto.getPoxm() );
        rktHjslQcPageResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslQcPageResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslQcPageResp.setPozjhm( dto.getPozjhm() );
        rktHjslQcPageResp.setPowwx( dto.getPowwx() );
        rktHjslQcPageResp.setPowwm( dto.getPowwm() );
        rktHjslQcPageResp.setJhryxm( dto.getJhryxm() );
        rktHjslQcPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslQcPageResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslQcPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslQcPageResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslQcPageResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslQcPageResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslQcPageResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslQcPageResp.setJhrexm( dto.getJhrexm() );
        rktHjslQcPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslQcPageResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslQcPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslQcPageResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslQcPageResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslQcPageResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslQcPageResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslQcPageResp.setZjlb( dto.getZjlb() );
        rktHjslQcPageResp.setQfjg( dto.getQfjg() );
        rktHjslQcPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslQcPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslQcPageResp.setQclb( dto.getQclb() );
        rktHjslQcPageResp.setQcrq( dto.getQcrq() );
        rktHjslQcPageResp.setQwdgjdq( dto.getQwdgjdq() );
        rktHjslQcPageResp.setQwdssxq( dto.getQwdssxq() );
        rktHjslQcPageResp.setQwdxz( dto.getQwdxz() );
        rktHjslQcPageResp.setZqzbh( dto.getZqzbh() );
        rktHjslQcPageResp.setQyzbh( dto.getQyzbh() );
        rktHjslQcPageResp.setBdfw( dto.getBdfw() );
        rktHjslQcPageResp.setQyyyxflb( dto.getQyyyxflb() );
        rktHjslQcPageResp.setQyyybcsm( dto.getQyyybcsm() );
        rktHjslQcPageResp.setQyldyy( dto.getQyldyy() );
        rktHjslQcPageResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslQcPageResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslQcPageResp.setQtssxq( dto.getQtssxq() );
        rktHjslQcPageResp.setQtzz( dto.getQtzz() );

        return rktHjslQcPageResp;
    }

    @Override
    public RktHjslQcViewResp convertToViewResp(RktHjslQcDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslQcViewResp rktHjslQcViewResp = new RktHjslQcViewResp();

        rktHjslQcViewResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslQcViewResp.setHhnbid( dto.getHhnbid() );
        rktHjslQcViewResp.setRyid( dto.getRyid() );
        rktHjslQcViewResp.setRynbid( dto.getRynbid() );
        rktHjslQcViewResp.setYwslh( dto.getYwslh() );
        rktHjslQcViewResp.setHh( dto.getHh() );
        rktHjslQcViewResp.setHhid( dto.getHhid() );
        rktHjslQcViewResp.setHlx( dto.getHlx() );
        rktHjslQcViewResp.setHmc( dto.getHmc() );
        rktHjslQcViewResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslQcViewResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslQcViewResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslQcViewResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslQcViewResp.setJlx( dto.getJlx() );
        rktHjslQcViewResp.setMlph( dto.getMlph() );
        rktHjslQcViewResp.setMlxz( dto.getMlxz() );
        rktHjslQcViewResp.setLcywlx( dto.getLcywlx() );
        rktHjslQcViewResp.setLcdyid( dto.getLcdyid() );
        rktHjslQcViewResp.setLcmc( dto.getLcmc() );
        rktHjslQcViewResp.setLcslid( dto.getLcslid() );
        rktHjslQcViewResp.setLcywbt( dto.getLcywbt() );
        rktHjslQcViewResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslQcViewResp.setLczt( dto.getLczt() );
        rktHjslQcViewResp.setBlzt( dto.getBlzt() );
        rktHjslQcViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslQcViewResp.setSqrxm( dto.getSqrxm() );
        rktHjslQcViewResp.setSqrxb( dto.getSqrxb() );
        rktHjslQcViewResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslQcViewResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslQcViewResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslQcViewResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslQcViewResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslQcViewResp.setSqrq( dto.getSqrq() );
        rktHjslQcViewResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslQcViewResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslQcViewResp.setSprxm( dto.getSprxm() );
        rktHjslQcViewResp.setSpsj( dto.getSpsj() );
        rktHjslQcViewResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslQcViewResp.setSpyj( dto.getSpyj() );
        rktHjslQcViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslQcViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslQcViewResp.setSlrxm( dto.getSlrxm() );
        rktHjslQcViewResp.setSlsj( dto.getSlsj() );
        rktHjslQcViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslQcViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslQcViewResp.setJcwh( dto.getJcwh() );
        rktHjslQcViewResp.setBz( dto.getBz() );
        rktHjslQcViewResp.setXh( dto.getXh() );
        rktHjslQcViewResp.setHkxz( dto.getHkxz() );
        rktHjslQcViewResp.setHb( dto.getHb() );
        rktHjslQcViewResp.setYhzgx( dto.getYhzgx() );
        rktHjslQcViewResp.setCxsx( dto.getCxsx() );
        rktHjslQcViewResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslQcViewResp.setXm( dto.getXm() );
        rktHjslQcViewResp.setX( dto.getX() );
        rktHjslQcViewResp.setM( dto.getM() );
        rktHjslQcViewResp.setCym( dto.getCym() );
        rktHjslQcViewResp.setXmpy( dto.getXmpy() );
        rktHjslQcViewResp.setCympy( dto.getCympy() );
        rktHjslQcViewResp.setXb( dto.getXb() );
        rktHjslQcViewResp.setMz( dto.getMz() );
        rktHjslQcViewResp.setJggjdq( dto.getJggjdq() );
        rktHjslQcViewResp.setJgssxq( dto.getJgssxq() );
        rktHjslQcViewResp.setJgxz( dto.getJgxz() );
        rktHjslQcViewResp.setCsrq( dto.getCsrq() );
        rktHjslQcViewResp.setCssj( dto.getCssj() );
        rktHjslQcViewResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslQcViewResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslQcViewResp.setCsdxz( dto.getCsdxz() );
        rktHjslQcViewResp.setWhcd( dto.getWhcd() );
        rktHjslQcViewResp.setHyzk( dto.getHyzk() );
        rktHjslQcViewResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslQcViewResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslQcViewResp.setZy( dto.getZy() );
        rktHjslQcViewResp.setZylb( dto.getZylb() );
        rktHjslQcViewResp.setZjxy( dto.getZjxy() );
        rktHjslQcViewResp.setSg( dto.getSg() );
        rktHjslQcViewResp.setXx( dto.getXx() );
        rktHjslQcViewResp.setByzk( dto.getByzk() );
        rktHjslQcViewResp.setXxjb( dto.getXxjb() );
        rktHjslQcViewResp.setLxdh( dto.getLxdh() );
        rktHjslQcViewResp.setFqxm( dto.getFqxm() );
        rktHjslQcViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslQcViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslQcViewResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslQcViewResp.setFqwwx( dto.getFqwwx() );
        rktHjslQcViewResp.setFqwwm( dto.getFqwwm() );
        rktHjslQcViewResp.setMqxm( dto.getMqxm() );
        rktHjslQcViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslQcViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslQcViewResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslQcViewResp.setMqwwx( dto.getMqwwx() );
        rktHjslQcViewResp.setMqwwm( dto.getMqwwm() );
        rktHjslQcViewResp.setPoxm( dto.getPoxm() );
        rktHjslQcViewResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslQcViewResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslQcViewResp.setPozjhm( dto.getPozjhm() );
        rktHjslQcViewResp.setPowwx( dto.getPowwx() );
        rktHjslQcViewResp.setPowwm( dto.getPowwm() );
        rktHjslQcViewResp.setJhryxm( dto.getJhryxm() );
        rktHjslQcViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslQcViewResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslQcViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslQcViewResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslQcViewResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslQcViewResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslQcViewResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslQcViewResp.setJhrexm( dto.getJhrexm() );
        rktHjslQcViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslQcViewResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslQcViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslQcViewResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslQcViewResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslQcViewResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslQcViewResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslQcViewResp.setZjlb( dto.getZjlb() );
        rktHjslQcViewResp.setQfjg( dto.getQfjg() );
        rktHjslQcViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslQcViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslQcViewResp.setQclb( dto.getQclb() );
        rktHjslQcViewResp.setQcrq( dto.getQcrq() );
        rktHjslQcViewResp.setQwdgjdq( dto.getQwdgjdq() );
        rktHjslQcViewResp.setQwdssxq( dto.getQwdssxq() );
        rktHjslQcViewResp.setQwdxz( dto.getQwdxz() );
        rktHjslQcViewResp.setZqzbh( dto.getZqzbh() );
        rktHjslQcViewResp.setQyzbh( dto.getQyzbh() );
        rktHjslQcViewResp.setBdfw( dto.getBdfw() );
        rktHjslQcViewResp.setQyyyxflb( dto.getQyyyxflb() );
        rktHjslQcViewResp.setQyyybcsm( dto.getQyyybcsm() );
        rktHjslQcViewResp.setQyldyy( dto.getQyldyy() );
        rktHjslQcViewResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslQcViewResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslQcViewResp.setQtssxq( dto.getQtssxq() );
        rktHjslQcViewResp.setQtzz( dto.getQtzz() );

        return rktHjslQcViewResp;
    }

    @Override
    public RktHjslQcCreateResp convertToCreateResp(RktHjslQcDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslQcCreateResp rktHjslQcCreateResp = new RktHjslQcCreateResp();

        rktHjslQcCreateResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslQcCreateResp.setHhnbid( dto.getHhnbid() );
        rktHjslQcCreateResp.setRyid( dto.getRyid() );
        rktHjslQcCreateResp.setRynbid( dto.getRynbid() );
        rktHjslQcCreateResp.setYwslh( dto.getYwslh() );
        rktHjslQcCreateResp.setHh( dto.getHh() );
        rktHjslQcCreateResp.setHhid( dto.getHhid() );
        rktHjslQcCreateResp.setHlx( dto.getHlx() );
        rktHjslQcCreateResp.setHmc( dto.getHmc() );
        rktHjslQcCreateResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslQcCreateResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslQcCreateResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslQcCreateResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslQcCreateResp.setJlx( dto.getJlx() );
        rktHjslQcCreateResp.setMlph( dto.getMlph() );
        rktHjslQcCreateResp.setMlxz( dto.getMlxz() );
        rktHjslQcCreateResp.setLcywlx( dto.getLcywlx() );
        rktHjslQcCreateResp.setLcdyid( dto.getLcdyid() );
        rktHjslQcCreateResp.setLcmc( dto.getLcmc() );
        rktHjslQcCreateResp.setLcslid( dto.getLcslid() );
        rktHjslQcCreateResp.setLcywbt( dto.getLcywbt() );
        rktHjslQcCreateResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslQcCreateResp.setLczt( dto.getLczt() );
        rktHjslQcCreateResp.setBlzt( dto.getBlzt() );
        rktHjslQcCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslQcCreateResp.setSqrxm( dto.getSqrxm() );
        rktHjslQcCreateResp.setSqrxb( dto.getSqrxb() );
        rktHjslQcCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslQcCreateResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslQcCreateResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslQcCreateResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslQcCreateResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslQcCreateResp.setSqrq( dto.getSqrq() );
        rktHjslQcCreateResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslQcCreateResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslQcCreateResp.setSprxm( dto.getSprxm() );
        rktHjslQcCreateResp.setSpsj( dto.getSpsj() );
        rktHjslQcCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslQcCreateResp.setSpyj( dto.getSpyj() );
        rktHjslQcCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslQcCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslQcCreateResp.setSlrxm( dto.getSlrxm() );
        rktHjslQcCreateResp.setSlsj( dto.getSlsj() );
        rktHjslQcCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslQcCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslQcCreateResp.setJcwh( dto.getJcwh() );
        rktHjslQcCreateResp.setBz( dto.getBz() );
        rktHjslQcCreateResp.setXh( dto.getXh() );
        rktHjslQcCreateResp.setHkxz( dto.getHkxz() );
        rktHjslQcCreateResp.setHb( dto.getHb() );
        rktHjslQcCreateResp.setYhzgx( dto.getYhzgx() );
        rktHjslQcCreateResp.setCxsx( dto.getCxsx() );
        rktHjslQcCreateResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslQcCreateResp.setXm( dto.getXm() );
        rktHjslQcCreateResp.setX( dto.getX() );
        rktHjslQcCreateResp.setM( dto.getM() );
        rktHjslQcCreateResp.setCym( dto.getCym() );
        rktHjslQcCreateResp.setXmpy( dto.getXmpy() );
        rktHjslQcCreateResp.setCympy( dto.getCympy() );
        rktHjslQcCreateResp.setXb( dto.getXb() );
        rktHjslQcCreateResp.setMz( dto.getMz() );
        rktHjslQcCreateResp.setJggjdq( dto.getJggjdq() );
        rktHjslQcCreateResp.setJgssxq( dto.getJgssxq() );
        rktHjslQcCreateResp.setJgxz( dto.getJgxz() );
        rktHjslQcCreateResp.setCsrq( dto.getCsrq() );
        rktHjslQcCreateResp.setCssj( dto.getCssj() );
        rktHjslQcCreateResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslQcCreateResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslQcCreateResp.setCsdxz( dto.getCsdxz() );
        rktHjslQcCreateResp.setWhcd( dto.getWhcd() );
        rktHjslQcCreateResp.setHyzk( dto.getHyzk() );
        rktHjslQcCreateResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslQcCreateResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslQcCreateResp.setZy( dto.getZy() );
        rktHjslQcCreateResp.setZylb( dto.getZylb() );
        rktHjslQcCreateResp.setZjxy( dto.getZjxy() );
        rktHjslQcCreateResp.setSg( dto.getSg() );
        rktHjslQcCreateResp.setXx( dto.getXx() );
        rktHjslQcCreateResp.setByzk( dto.getByzk() );
        rktHjslQcCreateResp.setXxjb( dto.getXxjb() );
        rktHjslQcCreateResp.setLxdh( dto.getLxdh() );
        rktHjslQcCreateResp.setFqxm( dto.getFqxm() );
        rktHjslQcCreateResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslQcCreateResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslQcCreateResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslQcCreateResp.setFqwwx( dto.getFqwwx() );
        rktHjslQcCreateResp.setFqwwm( dto.getFqwwm() );
        rktHjslQcCreateResp.setMqxm( dto.getMqxm() );
        rktHjslQcCreateResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslQcCreateResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslQcCreateResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslQcCreateResp.setMqwwx( dto.getMqwwx() );
        rktHjslQcCreateResp.setMqwwm( dto.getMqwwm() );
        rktHjslQcCreateResp.setPoxm( dto.getPoxm() );
        rktHjslQcCreateResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslQcCreateResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslQcCreateResp.setPozjhm( dto.getPozjhm() );
        rktHjslQcCreateResp.setPowwx( dto.getPowwx() );
        rktHjslQcCreateResp.setPowwm( dto.getPowwm() );
        rktHjslQcCreateResp.setJhryxm( dto.getJhryxm() );
        rktHjslQcCreateResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslQcCreateResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslQcCreateResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslQcCreateResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslQcCreateResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslQcCreateResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslQcCreateResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslQcCreateResp.setJhrexm( dto.getJhrexm() );
        rktHjslQcCreateResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslQcCreateResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslQcCreateResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslQcCreateResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslQcCreateResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslQcCreateResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslQcCreateResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslQcCreateResp.setZjlb( dto.getZjlb() );
        rktHjslQcCreateResp.setQfjg( dto.getQfjg() );
        rktHjslQcCreateResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslQcCreateResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslQcCreateResp.setQclb( dto.getQclb() );
        rktHjslQcCreateResp.setQcrq( dto.getQcrq() );
        rktHjslQcCreateResp.setQwdgjdq( dto.getQwdgjdq() );
        rktHjslQcCreateResp.setQwdssxq( dto.getQwdssxq() );
        rktHjslQcCreateResp.setQwdxz( dto.getQwdxz() );
        rktHjslQcCreateResp.setZqzbh( dto.getZqzbh() );
        rktHjslQcCreateResp.setQyzbh( dto.getQyzbh() );
        rktHjslQcCreateResp.setBdfw( dto.getBdfw() );
        rktHjslQcCreateResp.setQyyyxflb( dto.getQyyyxflb() );
        rktHjslQcCreateResp.setQyyybcsm( dto.getQyyybcsm() );
        rktHjslQcCreateResp.setQyldyy( dto.getQyldyy() );
        rktHjslQcCreateResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslQcCreateResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslQcCreateResp.setQtssxq( dto.getQtssxq() );
        rktHjslQcCreateResp.setQtzz( dto.getQtzz() );

        return rktHjslQcCreateResp;
    }
}
