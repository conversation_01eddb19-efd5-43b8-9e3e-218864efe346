package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.CkhlwHjsqSqczbDTO;
import com.zjjcnt.project.ck.base.entity.CkhlwHjsqSqczbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:41+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class CkhlwHjsqSqczbConvertImpl implements CkhlwHjsqSqczbConvert {

    @Override
    public CkhlwHjsqSqczbDTO convert(CkhlwHjsqSqczbDO entity) {
        if ( entity == null ) {
            return null;
        }

        CkhlwHjsqSqczbDTO ckhlwHjsqSqczbDTO = new CkhlwHjsqSqczbDTO();

        ckhlwHjsqSqczbDTO.setId( entity.getId() );
        ckhlwHjsqSqczbDTO.setSqczlsid( entity.getSqczlsid() );
        ckhlwHjsqSqczbDTO.setHlwsqid( entity.getHlwsqid() );
        ckhlwHjsqSqczbDTO.setProjectid( entity.getProjectid() );
        ckhlwHjsqSqczbDTO.setSqzt( entity.getSqzt() );
        ckhlwHjsqSqczbDTO.setShbz( entity.getShbz() );
        ckhlwHjsqSqczbDTO.setShjg( entity.getShjg() );
        ckhlwHjsqSqczbDTO.setRwlx( entity.getRwlx() );
        ckhlwHjsqSqczbDTO.setCzlx( entity.getCzlx() );
        ckhlwHjsqSqczbDTO.setCzjg( entity.getCzjg() );
        ckhlwHjsqSqczbDTO.setCzjgms( entity.getCzjgms() );
        ckhlwHjsqSqczbDTO.setCzbz( entity.getCzbz() );
        ckhlwHjsqSqczbDTO.setCzbz2( entity.getCzbz2() );
        ckhlwHjsqSqczbDTO.setCzbz3( entity.getCzbz3() );
        ckhlwHjsqSqczbDTO.setCzrid( entity.getCzrid() );
        ckhlwHjsqSqczbDTO.setCzrxm( entity.getCzrxm() );
        ckhlwHjsqSqczbDTO.setCzrip( entity.getCzrip() );
        ckhlwHjsqSqczbDTO.setCzsj( entity.getCzsj() );
        ckhlwHjsqSqczbDTO.setCjr( entity.getCjr() );
        ckhlwHjsqSqczbDTO.setCjrip( entity.getCjrip() );
        ckhlwHjsqSqczbDTO.setCjsj( entity.getCjsj() );
        ckhlwHjsqSqczbDTO.setXgr( entity.getXgr() );
        ckhlwHjsqSqczbDTO.setXgrip( entity.getXgrip() );
        ckhlwHjsqSqczbDTO.setXgsj( entity.getXgsj() );

        return ckhlwHjsqSqczbDTO;
    }

    @Override
    public CkhlwHjsqSqczbDO convertToDO(CkhlwHjsqSqczbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CkhlwHjsqSqczbDO ckhlwHjsqSqczbDO = new CkhlwHjsqSqczbDO();

        ckhlwHjsqSqczbDO.setId( dto.getId() );
        ckhlwHjsqSqczbDO.setSqczlsid( dto.getSqczlsid() );
        ckhlwHjsqSqczbDO.setHlwsqid( dto.getHlwsqid() );
        ckhlwHjsqSqczbDO.setProjectid( dto.getProjectid() );
        ckhlwHjsqSqczbDO.setSqzt( dto.getSqzt() );
        ckhlwHjsqSqczbDO.setShbz( dto.getShbz() );
        ckhlwHjsqSqczbDO.setShjg( dto.getShjg() );
        ckhlwHjsqSqczbDO.setRwlx( dto.getRwlx() );
        ckhlwHjsqSqczbDO.setCzlx( dto.getCzlx() );
        ckhlwHjsqSqczbDO.setCzjg( dto.getCzjg() );
        ckhlwHjsqSqczbDO.setCzjgms( dto.getCzjgms() );
        ckhlwHjsqSqczbDO.setCzbz( dto.getCzbz() );
        ckhlwHjsqSqczbDO.setCzbz2( dto.getCzbz2() );
        ckhlwHjsqSqczbDO.setCzbz3( dto.getCzbz3() );
        ckhlwHjsqSqczbDO.setCzrid( dto.getCzrid() );
        ckhlwHjsqSqczbDO.setCzrxm( dto.getCzrxm() );
        ckhlwHjsqSqczbDO.setCzrip( dto.getCzrip() );
        ckhlwHjsqSqczbDO.setCzsj( dto.getCzsj() );
        ckhlwHjsqSqczbDO.setCjr( dto.getCjr() );
        ckhlwHjsqSqczbDO.setCjrip( dto.getCjrip() );
        ckhlwHjsqSqczbDO.setCjsj( dto.getCjsj() );
        ckhlwHjsqSqczbDO.setXgr( dto.getXgr() );
        ckhlwHjsqSqczbDO.setXgrip( dto.getXgrip() );
        ckhlwHjsqSqczbDO.setXgsj( dto.getXgsj() );

        return ckhlwHjsqSqczbDO;
    }
}
