package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtFyzflsbDTO;
import com.zjjcnt.project.ck.base.dto.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtFyzflsbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtFyzflsbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtFyzflsbViewResp;
import com.zjjcnt.project.ck.base.entity.ZjtFyzflsbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtFyzflsbConvertImpl implements ZjtFyzflsbConvert {

    @Override
    public ZjtFyzflsbDTO convert(ZjtFyzflsbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtFyzflsbDTO zjtFyzflsbDTO = new ZjtFyzflsbDTO();

        zjtFyzflsbDTO.setId( entity.getId() );
        zjtFyzflsbDTO.setSflsid( entity.getSflsid() );
        zjtFyzflsbDTO.setYwbmc( entity.getYwbmc() );
        zjtFyzflsbDTO.setYwbbh( entity.getYwbbh() );
        zjtFyzflsbDTO.setRyid( entity.getRyid() );
        zjtFyzflsbDTO.setRynbid( entity.getRynbid() );
        zjtFyzflsbDTO.setGmsfhm( entity.getGmsfhm() );
        zjtFyzflsbDTO.setXm( entity.getXm() );
        zjtFyzflsbDTO.setSfje( entity.getSfje() );
        zjtFyzflsbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        zjtFyzflsbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        zjtFyzflsbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        zjtFyzflsbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        zjtFyzflsbDTO.setCzsj( entity.getCzsj() );
        zjtFyzflsbDTO.setCzyid( entity.getCzyid() );
        zjtFyzflsbDTO.setCzyxm( entity.getCzyxm() );
        zjtFyzflsbDTO.setCzydwdm( entity.getCzydwdm() );
        zjtFyzflsbDTO.setCzydwmc( entity.getCzydwmc() );
        zjtFyzflsbDTO.setBz( entity.getBz() );
        zjtFyzflsbDTO.setSfsf( entity.getSfsf() );
        zjtFyzflsbDTO.setSfywlx( entity.getSfywlx() );
        zjtFyzflsbDTO.setJkdh( entity.getJkdh() );
        zjtFyzflsbDTO.setJkrsfhm( entity.getJkrsfhm() );
        zjtFyzflsbDTO.setJkrlxdh( entity.getJkrlxdh() );
        zjtFyzflsbDTO.setSfywlxmc( entity.getSfywlxmc() );
        zjtFyzflsbDTO.setHbjkdh( entity.getHbjkdh() );
        zjtFyzflsbDTO.setSldfjsjgsdwdm( entity.getSldfjsjgsdwdm() );
        zjtFyzflsbDTO.setSldfjsjgsdwmc( entity.getSldfjsjgsdwmc() );
        zjtFyzflsbDTO.setZsdwdm( entity.getZsdwdm() );
        zjtFyzflsbDTO.setZsdwmc( entity.getZsdwmc() );
        zjtFyzflsbDTO.setXzqhdm( entity.getXzqhdm() );
        zjtFyzflsbDTO.setZsxmbm( entity.getZsxmbm() );
        zjtFyzflsbDTO.setZsxmmc( entity.getZsxmmc() );
        zjtFyzflsbDTO.setSfdj( entity.getSfdj() );
        zjtFyzflsbDTO.setSfsl( entity.getSfsl() );
        zjtFyzflsbDTO.setTfryid( entity.getTfryid() );
        zjtFyzflsbDTO.setTfryxm( entity.getTfryxm() );
        zjtFyzflsbDTO.setTfrydwdm( entity.getTfrydwdm() );
        zjtFyzflsbDTO.setTfrydwmc( entity.getTfrydwmc() );
        zjtFyzflsbDTO.setTfsj( entity.getTfsj() );
        zjtFyzflsbDTO.setJkdlyqdbh( entity.getJkdlyqdbh() );
        zjtFyzflsbDTO.setFwdx( entity.getFwdx() );
        zjtFyzflsbDTO.setFxjzt( entity.getFxjzt() );
        zjtFyzflsbDTO.setSfxxly( entity.getSfxxly() );

        return zjtFyzflsbDTO;
    }

    @Override
    public ZjtFyzflsbDO convertToDO(ZjtFyzflsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtFyzflsbDO zjtFyzflsbDO = new ZjtFyzflsbDO();

        zjtFyzflsbDO.setId( dto.getId() );
        zjtFyzflsbDO.setSflsid( dto.getSflsid() );
        zjtFyzflsbDO.setYwbmc( dto.getYwbmc() );
        zjtFyzflsbDO.setYwbbh( dto.getYwbbh() );
        zjtFyzflsbDO.setRyid( dto.getRyid() );
        zjtFyzflsbDO.setRynbid( dto.getRynbid() );
        zjtFyzflsbDO.setGmsfhm( dto.getGmsfhm() );
        zjtFyzflsbDO.setXm( dto.getXm() );
        zjtFyzflsbDO.setSfje( dto.getSfje() );
        zjtFyzflsbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtFyzflsbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtFyzflsbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtFyzflsbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtFyzflsbDO.setCzsj( dto.getCzsj() );
        zjtFyzflsbDO.setCzyid( dto.getCzyid() );
        zjtFyzflsbDO.setCzyxm( dto.getCzyxm() );
        zjtFyzflsbDO.setCzydwdm( dto.getCzydwdm() );
        zjtFyzflsbDO.setCzydwmc( dto.getCzydwmc() );
        zjtFyzflsbDO.setBz( dto.getBz() );
        zjtFyzflsbDO.setSfsf( dto.getSfsf() );
        zjtFyzflsbDO.setSfywlx( dto.getSfywlx() );
        zjtFyzflsbDO.setJkdh( dto.getJkdh() );
        zjtFyzflsbDO.setJkrsfhm( dto.getJkrsfhm() );
        zjtFyzflsbDO.setJkrlxdh( dto.getJkrlxdh() );
        zjtFyzflsbDO.setSfywlxmc( dto.getSfywlxmc() );
        zjtFyzflsbDO.setHbjkdh( dto.getHbjkdh() );
        zjtFyzflsbDO.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtFyzflsbDO.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtFyzflsbDO.setZsdwdm( dto.getZsdwdm() );
        zjtFyzflsbDO.setZsdwmc( dto.getZsdwmc() );
        zjtFyzflsbDO.setXzqhdm( dto.getXzqhdm() );
        zjtFyzflsbDO.setZsxmbm( dto.getZsxmbm() );
        zjtFyzflsbDO.setZsxmmc( dto.getZsxmmc() );
        zjtFyzflsbDO.setSfdj( dto.getSfdj() );
        zjtFyzflsbDO.setSfsl( dto.getSfsl() );
        zjtFyzflsbDO.setTfryid( dto.getTfryid() );
        zjtFyzflsbDO.setTfryxm( dto.getTfryxm() );
        zjtFyzflsbDO.setTfrydwdm( dto.getTfrydwdm() );
        zjtFyzflsbDO.setTfrydwmc( dto.getTfrydwmc() );
        zjtFyzflsbDO.setTfsj( dto.getTfsj() );
        zjtFyzflsbDO.setJkdlyqdbh( dto.getJkdlyqdbh() );
        zjtFyzflsbDO.setFwdx( dto.getFwdx() );
        zjtFyzflsbDO.setFxjzt( dto.getFxjzt() );
        zjtFyzflsbDO.setSfxxly( dto.getSfxxly() );

        return zjtFyzflsbDO;
    }

    @Override
    public ZjtFyzflsbDTO convertToDTO(ZjtFyzflsbPageReq req) {
        if ( req == null ) {
            return null;
        }

        ZjtFyzflsbDTO zjtFyzflsbDTO = new ZjtFyzflsbDTO();

        zjtFyzflsbDTO.setSflsid( req.getSflsid() );
        zjtFyzflsbDTO.setYwbmc( req.getYwbmc() );
        zjtFyzflsbDTO.setYwbbh( req.getYwbbh() );
        zjtFyzflsbDTO.setRyid( req.getRyid() );
        zjtFyzflsbDTO.setRynbid( req.getRynbid() );
        zjtFyzflsbDTO.setGmsfhm( req.getGmsfhm() );
        zjtFyzflsbDTO.setXm( req.getXm() );
        zjtFyzflsbDTO.setSfje( req.getSfje() );
        zjtFyzflsbDTO.setHjdsjgsdwdm( req.getHjdsjgsdwdm() );
        zjtFyzflsbDTO.setHjdsjgsdwmc( req.getHjdsjgsdwmc() );
        zjtFyzflsbDTO.setSldsjgsdwdm( req.getSldsjgsdwdm() );
        zjtFyzflsbDTO.setSldsjgsdwmc( req.getSldsjgsdwmc() );
        zjtFyzflsbDTO.setCzsj( req.getCzsj() );
        zjtFyzflsbDTO.setCzyid( req.getCzyid() );
        zjtFyzflsbDTO.setCzyxm( req.getCzyxm() );
        zjtFyzflsbDTO.setCzydwdm( req.getCzydwdm() );
        zjtFyzflsbDTO.setCzydwmc( req.getCzydwmc() );
        zjtFyzflsbDTO.setBz( req.getBz() );
        zjtFyzflsbDTO.setSfsf( req.getSfsf() );
        zjtFyzflsbDTO.setSfywlx( req.getSfywlx() );
        zjtFyzflsbDTO.setJkdh( req.getJkdh() );
        zjtFyzflsbDTO.setJkrsfhm( req.getJkrsfhm() );
        zjtFyzflsbDTO.setJkrlxdh( req.getJkrlxdh() );
        zjtFyzflsbDTO.setSfywlxmc( req.getSfywlxmc() );
        zjtFyzflsbDTO.setHbjkdh( req.getHbjkdh() );
        zjtFyzflsbDTO.setSldfjsjgsdwdm( req.getSldfjsjgsdwdm() );
        zjtFyzflsbDTO.setSldfjsjgsdwmc( req.getSldfjsjgsdwmc() );
        zjtFyzflsbDTO.setZsdwdm( req.getZsdwdm() );
        zjtFyzflsbDTO.setZsdwmc( req.getZsdwmc() );
        zjtFyzflsbDTO.setXzqhdm( req.getXzqhdm() );
        zjtFyzflsbDTO.setZsxmbm( req.getZsxmbm() );
        zjtFyzflsbDTO.setZsxmmc( req.getZsxmmc() );
        zjtFyzflsbDTO.setSfdj( req.getSfdj() );
        zjtFyzflsbDTO.setSfsl( req.getSfsl() );
        zjtFyzflsbDTO.setTfryid( req.getTfryid() );
        zjtFyzflsbDTO.setTfryxm( req.getTfryxm() );
        zjtFyzflsbDTO.setTfrydwdm( req.getTfrydwdm() );
        zjtFyzflsbDTO.setTfrydwmc( req.getTfrydwmc() );
        zjtFyzflsbDTO.setTfsj( req.getTfsj() );
        zjtFyzflsbDTO.setJkdlyqdbh( req.getJkdlyqdbh() );
        zjtFyzflsbDTO.setFwdx( req.getFwdx() );

        return zjtFyzflsbDTO;
    }

    @Override
    public ZjtFyzflsbDTO convertToDTO(ZjtSlxxbDTO slxxbDTO) {
        if ( slxxbDTO == null ) {
            return null;
        }

        ZjtFyzflsbDTO zjtFyzflsbDTO = new ZjtFyzflsbDTO();

        zjtFyzflsbDTO.setId( slxxbDTO.getId() );
        zjtFyzflsbDTO.setRyid( slxxbDTO.getRyid() );
        zjtFyzflsbDTO.setRynbid( slxxbDTO.getRynbid() );
        zjtFyzflsbDTO.setGmsfhm( slxxbDTO.getGmsfhm() );
        zjtFyzflsbDTO.setXm( slxxbDTO.getXm() );
        zjtFyzflsbDTO.setSfje( slxxbDTO.getSfje() );
        zjtFyzflsbDTO.setHjdsjgsdwdm( slxxbDTO.getHjdsjgsdwdm() );
        zjtFyzflsbDTO.setHjdsjgsdwmc( slxxbDTO.getHjdsjgsdwmc() );
        zjtFyzflsbDTO.setSldsjgsdwdm( slxxbDTO.getSldsjgsdwdm() );
        zjtFyzflsbDTO.setSldsjgsdwmc( slxxbDTO.getSldsjgsdwmc() );
        zjtFyzflsbDTO.setCzsj( slxxbDTO.getCzsj() );
        zjtFyzflsbDTO.setCzyid( slxxbDTO.getCzyid() );
        zjtFyzflsbDTO.setCzyxm( slxxbDTO.getCzyxm() );
        zjtFyzflsbDTO.setCzydwmc( slxxbDTO.getCzydwmc() );
        zjtFyzflsbDTO.setBz( slxxbDTO.getBz() );
        zjtFyzflsbDTO.setSldfjsjgsdwdm( slxxbDTO.getSldfjsjgsdwdm() );
        zjtFyzflsbDTO.setSldfjsjgsdwmc( slxxbDTO.getSldfjsjgsdwmc() );
        zjtFyzflsbDTO.setFwdx( slxxbDTO.getFwdx() );
        zjtFyzflsbDTO.setCzsjStart( slxxbDTO.getCzsjStart() );
        zjtFyzflsbDTO.setCzsjEnd( slxxbDTO.getCzsjEnd() );

        return zjtFyzflsbDTO;
    }

    @Override
    public ZjtFyzflsbPageResp convertToPageResp(ZjtFyzflsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtFyzflsbPageResp zjtFyzflsbPageResp = new ZjtFyzflsbPageResp();

        zjtFyzflsbPageResp.setSflsid( dto.getSflsid() );
        zjtFyzflsbPageResp.setYwbmc( dto.getYwbmc() );
        zjtFyzflsbPageResp.setYwbbh( dto.getYwbbh() );
        zjtFyzflsbPageResp.setRyid( dto.getRyid() );
        zjtFyzflsbPageResp.setRynbid( dto.getRynbid() );
        zjtFyzflsbPageResp.setGmsfhm( dto.getGmsfhm() );
        zjtFyzflsbPageResp.setXm( dto.getXm() );
        zjtFyzflsbPageResp.setSfje( dto.getSfje() );
        zjtFyzflsbPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtFyzflsbPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtFyzflsbPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtFyzflsbPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtFyzflsbPageResp.setCzsj( dto.getCzsj() );
        zjtFyzflsbPageResp.setCzyid( dto.getCzyid() );
        zjtFyzflsbPageResp.setCzyxm( dto.getCzyxm() );
        zjtFyzflsbPageResp.setCzydwdm( dto.getCzydwdm() );
        zjtFyzflsbPageResp.setCzydwmc( dto.getCzydwmc() );
        zjtFyzflsbPageResp.setBz( dto.getBz() );
        zjtFyzflsbPageResp.setSfsf( dto.getSfsf() );
        zjtFyzflsbPageResp.setSfywlx( dto.getSfywlx() );
        zjtFyzflsbPageResp.setJkdh( dto.getJkdh() );
        zjtFyzflsbPageResp.setJkrsfhm( dto.getJkrsfhm() );
        zjtFyzflsbPageResp.setJkrlxdh( dto.getJkrlxdh() );
        zjtFyzflsbPageResp.setSfywlxmc( dto.getSfywlxmc() );
        zjtFyzflsbPageResp.setHbjkdh( dto.getHbjkdh() );
        zjtFyzflsbPageResp.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtFyzflsbPageResp.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtFyzflsbPageResp.setZsdwdm( dto.getZsdwdm() );
        zjtFyzflsbPageResp.setZsdwmc( dto.getZsdwmc() );
        zjtFyzflsbPageResp.setXzqhdm( dto.getXzqhdm() );
        zjtFyzflsbPageResp.setZsxmbm( dto.getZsxmbm() );
        zjtFyzflsbPageResp.setZsxmmc( dto.getZsxmmc() );
        zjtFyzflsbPageResp.setSfdj( dto.getSfdj() );
        zjtFyzflsbPageResp.setSfsl( dto.getSfsl() );
        zjtFyzflsbPageResp.setTfryid( dto.getTfryid() );
        zjtFyzflsbPageResp.setTfryxm( dto.getTfryxm() );
        zjtFyzflsbPageResp.setTfrydwdm( dto.getTfrydwdm() );
        zjtFyzflsbPageResp.setTfrydwmc( dto.getTfrydwmc() );
        zjtFyzflsbPageResp.setTfsj( dto.getTfsj() );
        zjtFyzflsbPageResp.setJkdlyqdbh( dto.getJkdlyqdbh() );
        zjtFyzflsbPageResp.setFwdx( dto.getFwdx() );

        return zjtFyzflsbPageResp;
    }

    @Override
    public ZjtFyzflsbViewResp convertToViewResp(ZjtFyzflsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtFyzflsbViewResp zjtFyzflsbViewResp = new ZjtFyzflsbViewResp();

        zjtFyzflsbViewResp.setSflsid( dto.getSflsid() );
        zjtFyzflsbViewResp.setYwbmc( dto.getYwbmc() );
        zjtFyzflsbViewResp.setYwbbh( dto.getYwbbh() );
        zjtFyzflsbViewResp.setRyid( dto.getRyid() );
        zjtFyzflsbViewResp.setRynbid( dto.getRynbid() );
        zjtFyzflsbViewResp.setGmsfhm( dto.getGmsfhm() );
        zjtFyzflsbViewResp.setXm( dto.getXm() );
        zjtFyzflsbViewResp.setSfje( dto.getSfje() );
        zjtFyzflsbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtFyzflsbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtFyzflsbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtFyzflsbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtFyzflsbViewResp.setCzsj( dto.getCzsj() );
        zjtFyzflsbViewResp.setCzyid( dto.getCzyid() );
        zjtFyzflsbViewResp.setCzyxm( dto.getCzyxm() );
        zjtFyzflsbViewResp.setCzydwdm( dto.getCzydwdm() );
        zjtFyzflsbViewResp.setCzydwmc( dto.getCzydwmc() );
        zjtFyzflsbViewResp.setBz( dto.getBz() );
        zjtFyzflsbViewResp.setSfsf( dto.getSfsf() );
        zjtFyzflsbViewResp.setSfywlx( dto.getSfywlx() );
        zjtFyzflsbViewResp.setJkdh( dto.getJkdh() );
        zjtFyzflsbViewResp.setJkrsfhm( dto.getJkrsfhm() );
        zjtFyzflsbViewResp.setJkrlxdh( dto.getJkrlxdh() );
        zjtFyzflsbViewResp.setSfywlxmc( dto.getSfywlxmc() );
        zjtFyzflsbViewResp.setHbjkdh( dto.getHbjkdh() );
        zjtFyzflsbViewResp.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtFyzflsbViewResp.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtFyzflsbViewResp.setZsdwdm( dto.getZsdwdm() );
        zjtFyzflsbViewResp.setZsdwmc( dto.getZsdwmc() );
        zjtFyzflsbViewResp.setXzqhdm( dto.getXzqhdm() );
        zjtFyzflsbViewResp.setZsxmbm( dto.getZsxmbm() );
        zjtFyzflsbViewResp.setZsxmmc( dto.getZsxmmc() );
        zjtFyzflsbViewResp.setSfdj( dto.getSfdj() );
        zjtFyzflsbViewResp.setSfsl( dto.getSfsl() );
        zjtFyzflsbViewResp.setTfryid( dto.getTfryid() );
        zjtFyzflsbViewResp.setTfryxm( dto.getTfryxm() );
        zjtFyzflsbViewResp.setTfrydwdm( dto.getTfrydwdm() );
        zjtFyzflsbViewResp.setTfrydwmc( dto.getTfrydwmc() );
        zjtFyzflsbViewResp.setTfsj( dto.getTfsj() );
        zjtFyzflsbViewResp.setJkdlyqdbh( dto.getJkdlyqdbh() );
        zjtFyzflsbViewResp.setFwdx( dto.getFwdx() );

        return zjtFyzflsbViewResp;
    }
}
