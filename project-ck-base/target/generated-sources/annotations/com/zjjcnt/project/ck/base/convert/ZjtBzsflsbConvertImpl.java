package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtBzsflsbDTO;
import com.zjjcnt.project.ck.base.dto.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtBzsflsbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtBzsflsbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtBzsflsbViewResp;
import com.zjjcnt.project.ck.base.entity.ZjtBzsflsbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:42+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtBzsflsbConvertImpl implements ZjtBzsflsbConvert {

    @Override
    public ZjtBzsflsbDTO convert(ZjtBzsflsbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtBzsflsbDTO zjtBzsflsbDTO = new ZjtBzsflsbDTO();

        zjtBzsflsbDTO.setId( entity.getId() );
        zjtBzsflsbDTO.setSflsid( entity.getSflsid() );
        zjtBzsflsbDTO.setNbslid( entity.getNbslid() );
        zjtBzsflsbDTO.setYwslh( entity.getYwslh() );
        zjtBzsflsbDTO.setSlh( entity.getSlh() );
        zjtBzsflsbDTO.setRyid( entity.getRyid() );
        zjtBzsflsbDTO.setRynbid( entity.getRynbid() );
        zjtBzsflsbDTO.setGmsfhm( entity.getGmsfhm() );
        zjtBzsflsbDTO.setXm( entity.getXm() );
        zjtBzsflsbDTO.setSlyy( entity.getSlyy() );
        zjtBzsflsbDTO.setSlzt( entity.getSlzt() );
        zjtBzsflsbDTO.setSfje( entity.getSfje() );
        zjtBzsflsbDTO.setQfjg( entity.getQfjg() );
        zjtBzsflsbDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        zjtBzsflsbDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        zjtBzsflsbDTO.setSflx( entity.getSflx() );
        zjtBzsflsbDTO.setSlfs( entity.getSlfs() );
        zjtBzsflsbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        zjtBzsflsbDTO.setSsxq( entity.getSsxq() );
        zjtBzsflsbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        zjtBzsflsbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        zjtBzsflsbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        zjtBzsflsbDTO.setSldfjsjgsdwdm( entity.getSldfjsjgsdwdm() );
        zjtBzsflsbDTO.setSldfjsjgsdwmc( entity.getSldfjsjgsdwmc() );
        zjtBzsflsbDTO.setCzsj( entity.getCzsj() );
        zjtBzsflsbDTO.setCzyid( entity.getCzyid() );
        zjtBzsflsbDTO.setCzyxm( entity.getCzyxm() );
        zjtBzsflsbDTO.setDwdm( entity.getDwdm() );
        zjtBzsflsbDTO.setCzydwmc( entity.getCzydwmc() );
        zjtBzsflsbDTO.setSfdjh( entity.getSfdjh() );
        zjtBzsflsbDTO.setBz( entity.getBz() );
        zjtBzsflsbDTO.setZfcje( entity.getZfcje() );
        zjtBzsflsbDTO.setQxfcje( entity.getQxfcje() );
        zjtBzsflsbDTO.setDsfcje( entity.getDsfcje() );
        zjtBzsflsbDTO.setZxfcje( entity.getZxfcje() );

        return zjtBzsflsbDTO;
    }

    @Override
    public ZjtBzsflsbDO convertToDO(ZjtBzsflsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtBzsflsbDO zjtBzsflsbDO = new ZjtBzsflsbDO();

        zjtBzsflsbDO.setId( dto.getId() );
        zjtBzsflsbDO.setSflsid( dto.getSflsid() );
        zjtBzsflsbDO.setNbslid( dto.getNbslid() );
        zjtBzsflsbDO.setYwslh( dto.getYwslh() );
        zjtBzsflsbDO.setSlh( dto.getSlh() );
        zjtBzsflsbDO.setRyid( dto.getRyid() );
        zjtBzsflsbDO.setRynbid( dto.getRynbid() );
        zjtBzsflsbDO.setGmsfhm( dto.getGmsfhm() );
        zjtBzsflsbDO.setXm( dto.getXm() );
        zjtBzsflsbDO.setSlyy( dto.getSlyy() );
        zjtBzsflsbDO.setSlzt( dto.getSlzt() );
        zjtBzsflsbDO.setSfje( dto.getSfje() );
        zjtBzsflsbDO.setQfjg( dto.getQfjg() );
        zjtBzsflsbDO.setYxqxqsrq( dto.getYxqxqsrq() );
        zjtBzsflsbDO.setYxqxjzrq( dto.getYxqxjzrq() );
        zjtBzsflsbDO.setSflx( dto.getSflx() );
        zjtBzsflsbDO.setSlfs( dto.getSlfs() );
        zjtBzsflsbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtBzsflsbDO.setSsxq( dto.getSsxq() );
        zjtBzsflsbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtBzsflsbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtBzsflsbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtBzsflsbDO.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtBzsflsbDO.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtBzsflsbDO.setCzsj( dto.getCzsj() );
        zjtBzsflsbDO.setCzyid( dto.getCzyid() );
        zjtBzsflsbDO.setCzyxm( dto.getCzyxm() );
        zjtBzsflsbDO.setDwdm( dto.getDwdm() );
        zjtBzsflsbDO.setCzydwmc( dto.getCzydwmc() );
        zjtBzsflsbDO.setSfdjh( dto.getSfdjh() );
        zjtBzsflsbDO.setBz( dto.getBz() );
        zjtBzsflsbDO.setZfcje( dto.getZfcje() );
        zjtBzsflsbDO.setQxfcje( dto.getQxfcje() );
        zjtBzsflsbDO.setDsfcje( dto.getDsfcje() );
        zjtBzsflsbDO.setZxfcje( dto.getZxfcje() );

        return zjtBzsflsbDO;
    }

    @Override
    public ZjtBzsflsbDTO convertToDTO(ZjtBzsflsbPageReq req) {
        if ( req == null ) {
            return null;
        }

        ZjtBzsflsbDTO zjtBzsflsbDTO = new ZjtBzsflsbDTO();

        zjtBzsflsbDTO.setSlh( req.getSlh() );

        return zjtBzsflsbDTO;
    }

    @Override
    public ZjtBzsflsbDTO convertToDTO(ZjtSlxxbDTO zjtSlxxb) {
        if ( zjtSlxxb == null ) {
            return null;
        }

        ZjtBzsflsbDTO zjtBzsflsbDTO = new ZjtBzsflsbDTO();

        zjtBzsflsbDTO.setNbslid( zjtSlxxb.getNbslid() );
        zjtBzsflsbDTO.setYwslh( zjtSlxxb.getYwslh() );
        zjtBzsflsbDTO.setSlh( zjtSlxxb.getSlh() );
        zjtBzsflsbDTO.setRyid( zjtSlxxb.getRyid() );
        zjtBzsflsbDTO.setRynbid( zjtSlxxb.getRynbid() );
        zjtBzsflsbDTO.setGmsfhm( zjtSlxxb.getGmsfhm() );
        zjtBzsflsbDTO.setXm( zjtSlxxb.getXm() );
        zjtBzsflsbDTO.setSlyy( zjtSlxxb.getSlyy() );
        zjtBzsflsbDTO.setSlzt( zjtSlxxb.getSlzt() );
        zjtBzsflsbDTO.setSfje( zjtSlxxb.getSfje() );
        zjtBzsflsbDTO.setQfjg( zjtSlxxb.getQfjg() );
        zjtBzsflsbDTO.setYxqxqsrq( zjtSlxxb.getYxqxqsrq() );
        zjtBzsflsbDTO.setYxqxjzrq( zjtSlxxb.getYxqxjzrq() );
        zjtBzsflsbDTO.setSflx( zjtSlxxb.getSflx() );
        zjtBzsflsbDTO.setSlfs( zjtSlxxb.getSlfs() );
        zjtBzsflsbDTO.setHjdsjgsdwdm( zjtSlxxb.getHjdsjgsdwdm() );
        zjtBzsflsbDTO.setSsxq( zjtSlxxb.getSsxq() );
        zjtBzsflsbDTO.setHjdsjgsdwmc( zjtSlxxb.getHjdsjgsdwmc() );
        zjtBzsflsbDTO.setSldsjgsdwdm( zjtSlxxb.getSldsjgsdwdm() );
        zjtBzsflsbDTO.setSldsjgsdwmc( zjtSlxxb.getSldsjgsdwmc() );
        zjtBzsflsbDTO.setSldfjsjgsdwdm( zjtSlxxb.getSldfjsjgsdwdm() );
        zjtBzsflsbDTO.setSldfjsjgsdwmc( zjtSlxxb.getSldfjsjgsdwmc() );
        zjtBzsflsbDTO.setCzsj( zjtSlxxb.getCzsj() );
        zjtBzsflsbDTO.setCzyid( zjtSlxxb.getCzyid() );
        zjtBzsflsbDTO.setCzyxm( zjtSlxxb.getCzyxm() );
        zjtBzsflsbDTO.setDwdm( zjtSlxxb.getDwdm() );
        zjtBzsflsbDTO.setCzydwmc( zjtSlxxb.getCzydwmc() );
        zjtBzsflsbDTO.setSfdjh( zjtSlxxb.getSfdjh() );
        zjtBzsflsbDTO.setBz( zjtSlxxb.getBz() );
        zjtBzsflsbDTO.setZfcje( zjtSlxxb.getZfcje() );
        zjtBzsflsbDTO.setQxfcje( zjtSlxxb.getQxfcje() );
        zjtBzsflsbDTO.setDsfcje( zjtSlxxb.getDsfcje() );
        zjtBzsflsbDTO.setZxfcje( zjtSlxxb.getZxfcje() );
        zjtBzsflsbDTO.setYxqxqsrqStart( zjtSlxxb.getYxqxqsrqStart() );
        zjtBzsflsbDTO.setYxqxqsrqEnd( zjtSlxxb.getYxqxqsrqEnd() );
        zjtBzsflsbDTO.setYxqxjzrqStart( zjtSlxxb.getYxqxjzrqStart() );
        zjtBzsflsbDTO.setYxqxjzrqEnd( zjtSlxxb.getYxqxjzrqEnd() );
        zjtBzsflsbDTO.setCzsjStart( zjtSlxxb.getCzsjStart() );
        zjtBzsflsbDTO.setCzsjEnd( zjtSlxxb.getCzsjEnd() );

        return zjtBzsflsbDTO;
    }

    @Override
    public ZjtBzsflsbPageResp convertToPageResp(ZjtBzsflsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtBzsflsbPageResp zjtBzsflsbPageResp = new ZjtBzsflsbPageResp();

        zjtBzsflsbPageResp.setSflsid( dto.getSflsid() );
        zjtBzsflsbPageResp.setNbslid( dto.getNbslid() );
        zjtBzsflsbPageResp.setYwslh( dto.getYwslh() );
        zjtBzsflsbPageResp.setSlh( dto.getSlh() );
        zjtBzsflsbPageResp.setRyid( dto.getRyid() );
        zjtBzsflsbPageResp.setRynbid( dto.getRynbid() );
        zjtBzsflsbPageResp.setGmsfhm( dto.getGmsfhm() );
        zjtBzsflsbPageResp.setXm( dto.getXm() );
        zjtBzsflsbPageResp.setSlyy( dto.getSlyy() );
        zjtBzsflsbPageResp.setSlzt( dto.getSlzt() );
        zjtBzsflsbPageResp.setSfje( dto.getSfje() );
        zjtBzsflsbPageResp.setQfjg( dto.getQfjg() );
        zjtBzsflsbPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        zjtBzsflsbPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        zjtBzsflsbPageResp.setSflx( dto.getSflx() );
        zjtBzsflsbPageResp.setSlfs( dto.getSlfs() );
        zjtBzsflsbPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtBzsflsbPageResp.setSsxq( dto.getSsxq() );
        zjtBzsflsbPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtBzsflsbPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtBzsflsbPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtBzsflsbPageResp.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtBzsflsbPageResp.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtBzsflsbPageResp.setCzsj( dto.getCzsj() );
        zjtBzsflsbPageResp.setCzyid( dto.getCzyid() );
        zjtBzsflsbPageResp.setCzyxm( dto.getCzyxm() );
        zjtBzsflsbPageResp.setDwdm( dto.getDwdm() );
        zjtBzsflsbPageResp.setCzydwmc( dto.getCzydwmc() );
        zjtBzsflsbPageResp.setSfdjh( dto.getSfdjh() );
        zjtBzsflsbPageResp.setBz( dto.getBz() );
        zjtBzsflsbPageResp.setZfcje( dto.getZfcje() );
        zjtBzsflsbPageResp.setQxfcje( dto.getQxfcje() );
        zjtBzsflsbPageResp.setDsfcje( dto.getDsfcje() );
        zjtBzsflsbPageResp.setZxfcje( dto.getZxfcje() );

        return zjtBzsflsbPageResp;
    }

    @Override
    public ZjtBzsflsbViewResp convertToViewResp(ZjtBzsflsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtBzsflsbViewResp zjtBzsflsbViewResp = new ZjtBzsflsbViewResp();

        zjtBzsflsbViewResp.setSflsid( dto.getSflsid() );
        zjtBzsflsbViewResp.setNbslid( dto.getNbslid() );
        zjtBzsflsbViewResp.setYwslh( dto.getYwslh() );
        zjtBzsflsbViewResp.setSlh( dto.getSlh() );
        zjtBzsflsbViewResp.setRyid( dto.getRyid() );
        zjtBzsflsbViewResp.setRynbid( dto.getRynbid() );
        zjtBzsflsbViewResp.setGmsfhm( dto.getGmsfhm() );
        zjtBzsflsbViewResp.setXm( dto.getXm() );
        zjtBzsflsbViewResp.setSlyy( dto.getSlyy() );
        zjtBzsflsbViewResp.setSlzt( dto.getSlzt() );
        zjtBzsflsbViewResp.setSfje( dto.getSfje() );
        zjtBzsflsbViewResp.setQfjg( dto.getQfjg() );
        zjtBzsflsbViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        zjtBzsflsbViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        zjtBzsflsbViewResp.setSflx( dto.getSflx() );
        zjtBzsflsbViewResp.setSlfs( dto.getSlfs() );
        zjtBzsflsbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtBzsflsbViewResp.setSsxq( dto.getSsxq() );
        zjtBzsflsbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtBzsflsbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtBzsflsbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtBzsflsbViewResp.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtBzsflsbViewResp.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtBzsflsbViewResp.setCzsj( dto.getCzsj() );
        zjtBzsflsbViewResp.setCzyid( dto.getCzyid() );
        zjtBzsflsbViewResp.setCzyxm( dto.getCzyxm() );
        zjtBzsflsbViewResp.setDwdm( dto.getDwdm() );
        zjtBzsflsbViewResp.setCzydwmc( dto.getCzydwmc() );
        zjtBzsflsbViewResp.setSfdjh( dto.getSfdjh() );
        zjtBzsflsbViewResp.setBz( dto.getBz() );
        zjtBzsflsbViewResp.setZfcje( dto.getZfcje() );
        zjtBzsflsbViewResp.setQxfcje( dto.getQxfcje() );
        zjtBzsflsbViewResp.setDsfcje( dto.getDsfcje() );
        zjtBzsflsbViewResp.setZxfcje( dto.getZxfcje() );

        return zjtBzsflsbViewResp;
    }
}
