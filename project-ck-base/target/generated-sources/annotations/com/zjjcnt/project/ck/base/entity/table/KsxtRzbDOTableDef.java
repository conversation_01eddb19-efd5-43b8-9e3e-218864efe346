package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class KsxtRzbDOTableDef extends TableDef {

    /**
     * 跨省协同日志表DO

 <AUTHOR>
 @date 2025-08-05 13:59:15
 @see com.zjjcnt.project.ck.base.dto.KsxtRzbDTO
     */
    public static final KsxtRzbDOTableDef KSXT_RZB_DO = new KsxtRzbDOTableDef();

    /**
     * 备注
     */
    public final QueryColumn BZ = new QueryColumn(this, "bz");

    /**
     * 主键
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 业务类型
     */
    public final QueryColumn BLZT = new QueryColumn(this, "blzt");

    /**
     * 操作IP
     */
    public final QueryColumn CZIP = new QueryColumn(this, "czip");

    /**
     * 入库时间
     */
    public final QueryColumn CZSJ = new QueryColumn(this, "czsj");

    /**
     * 操作员ID
     */
    public final QueryColumn CZYID = new QueryColumn(this, "czyid");

    /**
     * 操作员姓名
     */
    public final QueryColumn CZYXM = new QueryColumn(this, "czyxm");

    /**
     * 跨省协同号
     */
    public final QueryColumn KSXTID = new QueryColumn(this, "ksxtid");

    /**
     * 操作员单位代码
     */
    public final QueryColumn CZYDWDM = new QueryColumn(this, "czydwdm");

    /**
     * 操作员单位名称
     */
    public final QueryColumn CZYDWMC = new QueryColumn(this, "czydwmc");

    /**
     * 治安管理业务协同编号
     */
    public final QueryColumn ZAGLYWXTBH = new QueryColumn(this, "zaglywxtbh");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{BZ, ID, BLZT, CZIP, CZSJ, CZYID, CZYXM, KSXTID, CZYDWDM, CZYDWMC, ZAGLYWXTBH};

    public KsxtRzbDOTableDef() {
        super("", "ksxt_rzb");
    }

    private KsxtRzbDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public KsxtRzbDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new KsxtRzbDOTableDef("", "ksxt_rzb", alias));
    }

}
