package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.base.dto.HjxxRhflxxbDTO;
import com.zjjcnt.project.ck.base.dto.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.base.entity.HjxxRhflxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:46+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjxxRhflxxbConvertImpl implements HjxxRhflxxbConvert {

    @Override
    public HjxxRhflxxbDTO convert(HjxxRhflxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjxxRhflxxbDTO hjxxRhflxxbDTO = new HjxxRhflxxbDTO();

        hjxxRhflxxbDTO.setId( entity.getId() );
        hjxxRhflxxbDTO.setRhflid( entity.getRhflid() );
        hjxxRhflxxbDTO.setRyid( entity.getRyid() );
        hjxxRhflxxbDTO.setRynbid( entity.getRynbid() );
        hjxxRhflxxbDTO.setGmsfhm( entity.getGmsfhm() );
        hjxxRhflxxbDTO.setXm( entity.getXm() );
        hjxxRhflxxbDTO.setXb( entity.getXb() );
        hjxxRhflxxbDTO.setCsrq( entity.getCsrq() );
        hjxxRhflxxbDTO.setXzdfw( entity.getXzdfw() );
        hjxxRhflxxbDTO.setRhflSsxq( entity.getRhflSsxq() );
        hjxxRhflxxbDTO.setRhflJlx( entity.getRhflJlx() );
        hjxxRhflxxbDTO.setRhflMlph( entity.getRhflMlph() );
        hjxxRhflxxbDTO.setRhflMlxz( entity.getRhflMlxz() );
        hjxxRhflxxbDTO.setRhflPcs( entity.getRhflPcs() );
        hjxxRhflxxbDTO.setRhflZrq( entity.getRhflZrq() );
        hjxxRhflxxbDTO.setRhflXzjd( entity.getRhflXzjd() );
        hjxxRhflxxbDTO.setRhflJcwh( entity.getRhflJcwh() );
        hjxxRhflxxbDTO.setSlsj( entity.getSlsj() );
        hjxxRhflxxbDTO.setSldw( entity.getSldw() );
        hjxxRhflxxbDTO.setSlrid( entity.getSlrid() );
        hjxxRhflxxbDTO.setZxsj( entity.getZxsj() );
        hjxxRhflxxbDTO.setZxdw( entity.getZxdw() );
        hjxxRhflxxbDTO.setZxrid( entity.getZxrid() );
        hjxxRhflxxbDTO.setZxyy( entity.getZxyy() );
        hjxxRhflxxbDTO.setBz( entity.getBz() );
        hjxxRhflxxbDTO.setCjhjywid( entity.getCjhjywid() );
        hjxxRhflxxbDTO.setCchjywid( entity.getCchjywid() );
        hjxxRhflxxbDTO.setRhflzt( entity.getRhflzt() );
        hjxxRhflxxbDTO.setThirdpartyid( entity.getThirdpartyid() );
        hjxxRhflxxbDTO.setSjly( entity.getSjly() );
        hjxxRhflxxbDTO.setDhhm( entity.getDhhm() );
        hjxxRhflxxbDTO.setJccs( entity.getJccs() );
        hjxxRhflxxbDTO.setZjycjcsj( entity.getZjycjcsj() );
        hjxxRhflxxbDTO.setJcr( entity.getJcr() );
        hjxxRhflxxbDTO.setDzlx( entity.getDzlx() );
        hjxxRhflxxbDTO.setZlfwdah( entity.getZlfwdah() );
        hjxxRhflxxbDTO.setSlrjyh( entity.getSlrjyh() );
        hjxxRhflxxbDTO.setDsfzxrbh( entity.getDsfzxrbh() );
        hjxxRhflxxbDTO.setDsfzxrxm( entity.getDsfzxrxm() );

        return hjxxRhflxxbDTO;
    }

    @Override
    public HjxxRhflxxbDO convertToDO(HjxxRhflxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxRhflxxbDO hjxxRhflxxbDO = new HjxxRhflxxbDO();

        hjxxRhflxxbDO.setId( dto.getId() );
        hjxxRhflxxbDO.setRhflid( dto.getRhflid() );
        hjxxRhflxxbDO.setRyid( dto.getRyid() );
        hjxxRhflxxbDO.setRynbid( dto.getRynbid() );
        hjxxRhflxxbDO.setGmsfhm( dto.getGmsfhm() );
        hjxxRhflxxbDO.setXm( dto.getXm() );
        hjxxRhflxxbDO.setXb( dto.getXb() );
        hjxxRhflxxbDO.setCsrq( dto.getCsrq() );
        hjxxRhflxxbDO.setXzdfw( dto.getXzdfw() );
        hjxxRhflxxbDO.setRhflSsxq( dto.getRhflSsxq() );
        hjxxRhflxxbDO.setRhflJlx( dto.getRhflJlx() );
        hjxxRhflxxbDO.setRhflMlph( dto.getRhflMlph() );
        hjxxRhflxxbDO.setRhflMlxz( dto.getRhflMlxz() );
        hjxxRhflxxbDO.setRhflPcs( dto.getRhflPcs() );
        hjxxRhflxxbDO.setRhflZrq( dto.getRhflZrq() );
        hjxxRhflxxbDO.setRhflXzjd( dto.getRhflXzjd() );
        hjxxRhflxxbDO.setRhflJcwh( dto.getRhflJcwh() );
        hjxxRhflxxbDO.setSlsj( dto.getSlsj() );
        hjxxRhflxxbDO.setSldw( dto.getSldw() );
        hjxxRhflxxbDO.setSlrid( dto.getSlrid() );
        hjxxRhflxxbDO.setZxsj( dto.getZxsj() );
        hjxxRhflxxbDO.setZxdw( dto.getZxdw() );
        hjxxRhflxxbDO.setZxrid( dto.getZxrid() );
        hjxxRhflxxbDO.setZxyy( dto.getZxyy() );
        hjxxRhflxxbDO.setBz( dto.getBz() );
        hjxxRhflxxbDO.setCjhjywid( dto.getCjhjywid() );
        hjxxRhflxxbDO.setCchjywid( dto.getCchjywid() );
        hjxxRhflxxbDO.setRhflzt( dto.getRhflzt() );
        hjxxRhflxxbDO.setThirdpartyid( dto.getThirdpartyid() );
        hjxxRhflxxbDO.setSjly( dto.getSjly() );
        hjxxRhflxxbDO.setDhhm( dto.getDhhm() );
        hjxxRhflxxbDO.setJccs( dto.getJccs() );
        hjxxRhflxxbDO.setZjycjcsj( dto.getZjycjcsj() );
        hjxxRhflxxbDO.setJcr( dto.getJcr() );
        hjxxRhflxxbDO.setDzlx( dto.getDzlx() );
        hjxxRhflxxbDO.setZlfwdah( dto.getZlfwdah() );
        hjxxRhflxxbDO.setSlrjyh( dto.getSlrjyh() );
        hjxxRhflxxbDO.setDsfzxrbh( dto.getDsfzxrbh() );
        hjxxRhflxxbDO.setDsfzxrxm( dto.getDsfzxrxm() );

        return hjxxRhflxxbDO;
    }

    @Override
    public HjxxRhflxxbDTO convertToDTO(ZjtSlxxbDTO slxxbDTO) {
        if ( slxxbDTO == null ) {
            return null;
        }

        HjxxRhflxxbDTO hjxxRhflxxbDTO = new HjxxRhflxxbDTO();

        if ( slxxbDTO.getRyid() != null ) {
            hjxxRhflxxbDTO.setRyid( Long.parseLong( slxxbDTO.getRyid() ) );
        }
        if ( slxxbDTO.getRynbid() != null ) {
            hjxxRhflxxbDTO.setRynbid( Long.parseLong( slxxbDTO.getRynbid() ) );
        }
        hjxxRhflxxbDTO.setGmsfhm( slxxbDTO.getGmsfhm() );
        hjxxRhflxxbDTO.setXm( slxxbDTO.getXm() );
        hjxxRhflxxbDTO.setXb( slxxbDTO.getXb() );
        hjxxRhflxxbDTO.setCsrq( slxxbDTO.getCsrq() );
        hjxxRhflxxbDTO.setBz( slxxbDTO.getBz() );
        hjxxRhflxxbDTO.setCsrqStart( slxxbDTO.getCsrqStart() );
        hjxxRhflxxbDTO.setCsrqEnd( slxxbDTO.getCsrqEnd() );

        return hjxxRhflxxbDTO;
    }

    @Override
    public void copyCzrkxx(HjxxRhflxxbDTO dto, HjxxCzrkjbxxbDTO hjxxCzrkjbxxbDTO) {
        if ( hjxxCzrkjbxxbDTO == null ) {
            return;
        }

        dto.setRyid( hjxxCzrkjbxxbDTO.getRyid() );
        dto.setRynbid( hjxxCzrkjbxxbDTO.getRynbid() );
        dto.setGmsfhm( hjxxCzrkjbxxbDTO.getGmsfhm() );
        dto.setXm( hjxxCzrkjbxxbDTO.getXm() );
        dto.setXb( hjxxCzrkjbxxbDTO.getXb() );
        dto.setCsrq( hjxxCzrkjbxxbDTO.getCsrq() );
        dto.setZxsj( hjxxCzrkjbxxbDTO.getZxsj() );
        dto.setBz( hjxxCzrkjbxxbDTO.getBz() );
        dto.setCjhjywid( hjxxCzrkjbxxbDTO.getCjhjywid() );
        dto.setCchjywid( hjxxCzrkjbxxbDTO.getCchjywid() );
        dto.setDhhm( hjxxCzrkjbxxbDTO.getDhhm() );
        dto.setCsrqStart( hjxxCzrkjbxxbDTO.getCsrqStart() );
        dto.setCsrqEnd( hjxxCzrkjbxxbDTO.getCsrqEnd() );
        dto.setZxsjStart( hjxxCzrkjbxxbDTO.getZxsjStart() );
        dto.setZxsjEnd( hjxxCzrkjbxxbDTO.getZxsjEnd() );
    }
}
