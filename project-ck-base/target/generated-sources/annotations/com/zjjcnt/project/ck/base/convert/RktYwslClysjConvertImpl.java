package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.req.RktYwslClysjPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktYwslClysjReq;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslClysjPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslClysjViewResp;
import com.zjjcnt.project.ck.sysadmin.dto.RktYwslClysjDTO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:46+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktYwslClysjConvertImpl implements RktYwslClysjConvert {

    @Override
    public RktYwslClysjDTO convertToDTO(RktYwslClysjPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktYwslClysjDTO rktYwslClysjDTO = new RktYwslClysjDTO();

        rktYwslClysjDTO.setSlclbh( req.getSlclbh() );
        rktYwslClysjDTO.setSlclqdbh( req.getSlclqdbh() );
        rktYwslClysjDTO.setYwslh( req.getYwslh() );
        rktYwslClysjDTO.setLcywlx( req.getLcywlx() );
        rktYwslClysjDTO.setClmc( req.getClmc() );
        rktYwslClysjDTO.setCllxdl( req.getCllxdl() );
        rktYwslClysjDTO.setCllxzl( req.getCllxzl() );
        rktYwslClysjDTO.setCllxxl( req.getCllxxl() );
        rktYwslClysjDTO.setCllxdm( req.getCllxdm() );
        rktYwslClysjDTO.setCllxmc( req.getCllxmc() );
        rktYwslClysjDTO.setWjlx( req.getWjlx() );
        rktYwslClysjDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktYwslClysjDTO.setJcwh( req.getJcwh() );
        rktYwslClysjDTO.setClcjr( req.getClcjr() );
        rktYwslClysjDTO.setClcjsj( req.getClcjsj() );
        rktYwslClysjDTO.setYxbz( req.getYxbz() );
        rktYwslClysjDTO.setYyslclbh( req.getYyslclbh() );
        rktYwslClysjDTO.setFycs( req.getFycs() );
        rktYwslClysjDTO.setCllylx( req.getCllylx() );
        rktYwslClysjDTO.setSfyqz( req.getSfyqz() );
        rktYwslClysjDTO.setSfbcqmzj( req.getSfbcqmzj() );
        rktYwslClysjDTO.setGmsfhm( req.getGmsfhm() );
        rktYwslClysjDTO.setAll( req.getAll() );
        rktYwslClysjDTO.setSfdzqzwj( req.getSfdzqzwj() );

        return rktYwslClysjDTO;
    }

    @Override
    public RktYwslClysjPageResp convertToPageResp(RktYwslClysjDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslClysjPageResp rktYwslClysjPageResp = new RktYwslClysjPageResp();

        rktYwslClysjPageResp.setSlclbh( dto.getSlclbh() );
        rktYwslClysjPageResp.setSlclqdbh( dto.getSlclqdbh() );
        rktYwslClysjPageResp.setYwslh( dto.getYwslh() );
        rktYwslClysjPageResp.setLcywlx( dto.getLcywlx() );
        rktYwslClysjPageResp.setXh( dto.getXh() );
        rktYwslClysjPageResp.setClmc( dto.getClmc() );
        rktYwslClysjPageResp.setCllxdl( dto.getCllxdl() );
        rktYwslClysjPageResp.setCllxzl( dto.getCllxzl() );
        rktYwslClysjPageResp.setCllxxl( dto.getCllxxl() );
        rktYwslClysjPageResp.setCllxdm( dto.getCllxdm() );
        rktYwslClysjPageResp.setCllxmc( dto.getCllxmc() );
        rktYwslClysjPageResp.setWjdx( dto.getWjdx() );
        rktYwslClysjPageResp.setWjlx( dto.getWjlx() );
        rktYwslClysjPageResp.setWjsjdz( dto.getWjsjdz() );
        rktYwslClysjPageResp.setHash( dto.getHash() );
        rktYwslClysjPageResp.setLng( dto.getLng() );
        rktYwslClysjPageResp.setLat( dto.getLat() );
        rktYwslClysjPageResp.setAlt( dto.getAlt() );
        rktYwslClysjPageResp.setQtsx( dto.getQtsx() );
        rktYwslClysjPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktYwslClysjPageResp.setJcwh( dto.getJcwh() );
        rktYwslClysjPageResp.setClcjr( dto.getClcjr() );
        rktYwslClysjPageResp.setClcjsj( dto.getClcjsj() );
        rktYwslClysjPageResp.setYyslclbh( dto.getYyslclbh() );
        rktYwslClysjPageResp.setFycs( dto.getFycs() );
        rktYwslClysjPageResp.setClcjrid( dto.getClcjrid() );
        rktYwslClysjPageResp.setClcjrip( dto.getClcjrip() );
        rktYwslClysjPageResp.setClxgr( dto.getClxgr() );
        rktYwslClysjPageResp.setClxgrid( dto.getClxgrid() );
        rktYwslClysjPageResp.setClxgrip( dto.getClxgrip() );
        rktYwslClysjPageResp.setClxgsj( dto.getClxgsj() );
        rktYwslClysjPageResp.setCllylx( dto.getCllylx() );
        rktYwslClysjPageResp.setSfyqz( dto.getSfyqz() );
        rktYwslClysjPageResp.setDycs( dto.getDycs() );
        rktYwslClysjPageResp.setGaggsjLogWjbh( dto.getGaggsjLogWjbh() );
        rktYwslClysjPageResp.setGaggsjIndex( dto.getGaggsjIndex() );
        rktYwslClysjPageResp.setGaggsjOrgiUrl( dto.getGaggsjOrgiUrl() );
        rktYwslClysjPageResp.setGaggsjOrgiWjbh( dto.getGaggsjOrgiWjbh() );
        rktYwslClysjPageResp.setZwZwqrtxid( dto.getZwZwqrtxid() );
        rktYwslClysjPageResp.setSfbcqmzj( dto.getSfbcqmzj() );
        rktYwslClysjPageResp.setGmsfhm( dto.getGmsfhm() );
        rktYwslClysjPageResp.setBase64Data( dto.getBase64Data() );

        return rktYwslClysjPageResp;
    }

    @Override
    public RktYwslClysjViewResp convertToViewResp(RktYwslClysjDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslClysjViewResp rktYwslClysjViewResp = new RktYwslClysjViewResp();

        rktYwslClysjViewResp.setSlclbh( dto.getSlclbh() );
        rktYwslClysjViewResp.setSlclqdbh( dto.getSlclqdbh() );
        rktYwslClysjViewResp.setYwslh( dto.getYwslh() );
        rktYwslClysjViewResp.setLcywlx( dto.getLcywlx() );
        rktYwslClysjViewResp.setXh( dto.getXh() );
        rktYwslClysjViewResp.setClmc( dto.getClmc() );
        rktYwslClysjViewResp.setCllxdl( dto.getCllxdl() );
        rktYwslClysjViewResp.setCllxzl( dto.getCllxzl() );
        rktYwslClysjViewResp.setCllxxl( dto.getCllxxl() );
        rktYwslClysjViewResp.setCllxdm( dto.getCllxdm() );
        rktYwslClysjViewResp.setCllxmc( dto.getCllxmc() );
        rktYwslClysjViewResp.setWjdx( dto.getWjdx() );
        rktYwslClysjViewResp.setWjlx( dto.getWjlx() );
        rktYwslClysjViewResp.setWjsjdz( dto.getWjsjdz() );
        rktYwslClysjViewResp.setHash( dto.getHash() );
        rktYwslClysjViewResp.setLng( dto.getLng() );
        rktYwslClysjViewResp.setLat( dto.getLat() );
        rktYwslClysjViewResp.setAlt( dto.getAlt() );
        rktYwslClysjViewResp.setQtsx( dto.getQtsx() );
        rktYwslClysjViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktYwslClysjViewResp.setJcwh( dto.getJcwh() );
        rktYwslClysjViewResp.setClcjr( dto.getClcjr() );
        rktYwslClysjViewResp.setClcjsj( dto.getClcjsj() );
        rktYwslClysjViewResp.setYyslclbh( dto.getYyslclbh() );
        rktYwslClysjViewResp.setFycs( dto.getFycs() );
        rktYwslClysjViewResp.setClcjrid( dto.getClcjrid() );
        rktYwslClysjViewResp.setClcjrip( dto.getClcjrip() );
        rktYwslClysjViewResp.setClxgr( dto.getClxgr() );
        rktYwslClysjViewResp.setClxgrid( dto.getClxgrid() );
        rktYwslClysjViewResp.setClxgrip( dto.getClxgrip() );
        rktYwslClysjViewResp.setClxgsj( dto.getClxgsj() );
        rktYwslClysjViewResp.setCllylx( dto.getCllylx() );
        rktYwslClysjViewResp.setSfyqz( dto.getSfyqz() );
        rktYwslClysjViewResp.setDycs( dto.getDycs() );
        rktYwslClysjViewResp.setGaggsjLogWjbh( dto.getGaggsjLogWjbh() );
        rktYwslClysjViewResp.setGaggsjIndex( dto.getGaggsjIndex() );
        rktYwslClysjViewResp.setGaggsjOrgiUrl( dto.getGaggsjOrgiUrl() );
        rktYwslClysjViewResp.setGaggsjOrgiWjbh( dto.getGaggsjOrgiWjbh() );
        rktYwslClysjViewResp.setZwZwqrtxid( dto.getZwZwqrtxid() );
        rktYwslClysjViewResp.setSfbcqmzj( dto.getSfbcqmzj() );
        rktYwslClysjViewResp.setGmsfhm( dto.getGmsfhm() );

        return rktYwslClysjViewResp;
    }

    @Override
    public RktYwslClysjDTO convertToDTO(RktYwslClysjReq req) {
        if ( req == null ) {
            return null;
        }

        RktYwslClysjDTO rktYwslClysjDTO = new RktYwslClysjDTO();

        rktYwslClysjDTO.setBase64Data( req.getBase64Data() );
        rktYwslClysjDTO.setSlclbh( req.getSlclbh() );
        rktYwslClysjDTO.setYwslh( req.getYwslh() );
        rktYwslClysjDTO.setLcywlx( req.getLcywlx() );
        rktYwslClysjDTO.setClmc( req.getClmc() );
        rktYwslClysjDTO.setCllxdm( req.getCllxdm() );
        rktYwslClysjDTO.setCllxmc( req.getCllxmc() );
        rktYwslClysjDTO.setWjlx( req.getWjlx() );
        rktYwslClysjDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktYwslClysjDTO.setJcwh( req.getJcwh() );
        rktYwslClysjDTO.setCllylx( req.getCllylx() );
        rktYwslClysjDTO.setSfyqz( req.getSfyqz() );
        rktYwslClysjDTO.setGmsfhm( req.getGmsfhm() );

        return rktYwslClysjDTO;
    }

    @Override
    public List<RktYwslClysjDTO> convertToDTO(List<RktYwslClysjReq> req) {
        if ( req == null ) {
            return null;
        }

        List<RktYwslClysjDTO> list = new ArrayList<RktYwslClysjDTO>( req.size() );
        for ( RktYwslClysjReq rktYwslClysjReq : req ) {
            list.add( convertToDTO( rktYwslClysjReq ) );
        }

        return list;
    }
}
