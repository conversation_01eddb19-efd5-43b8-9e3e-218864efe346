package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsxtQcfkxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtQcfkxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtQcfkxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtQcfkxxbViewResp;
import com.zjjcnt.project.ck.base.entity.KsxtQcfkxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:42+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsxtQcfkxxbConvertImpl implements KsxtQcfkxxbConvert {

    @Override
    public KsxtQcfkxxbDTO convert(KsxtQcfkxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsxtQcfkxxbDTO ksxtQcfkxxbDTO = new KsxtQcfkxxbDTO();

        ksxtQcfkxxbDTO.setId( entity.getId() );
        ksxtQcfkxxbDTO.setKsxtqcfkid( entity.getKsxtqcfkid() );
        ksxtQcfkxxbDTO.setKsxtid( entity.getKsxtid() );
        ksxtQcfkxxbDTO.setZaglywxtbh( entity.getZaglywxtbh() );
        ksxtQcfkxxbDTO.setCzrgmsfhm( entity.getCzrgmsfhm() );
        ksxtQcfkxxbDTO.setCzrxm( entity.getCzrxm() );
        ksxtQcfkxxbDTO.setYzzssxqdm( entity.getYzzssxqdm() );
        ksxtQcfkxxbDTO.setYzzqhnxxdz( entity.getYzzqhnxxdz() );
        ksxtQcfkxxbDTO.setYzzcxfldm( entity.getYzzcxfldm() );
        ksxtQcfkxxbDTO.setQwdssxqdm( entity.getQwdssxqdm() );
        ksxtQcfkxxbDTO.setQwdqhnxxdz( entity.getQwdqhnxxdz() );
        ksxtQcfkxxbDTO.setQwdhkdjjggajgjgdm( entity.getQwdhkdjjggajgjgdm() );
        ksxtQcfkxxbDTO.setQwdhkdjjggajgmc( entity.getQwdhkdjjggajgmc() );
        ksxtQcfkxxbDTO.setQyzbh( entity.getQyzbh() );
        ksxtQcfkxxbDTO.setQyzqfjggajgjgdm( entity.getQyzqfjggajgjgdm() );
        ksxtQcfkxxbDTO.setQyzqfjggajgmc( entity.getQyzqfjggajgmc() );
        ksxtQcfkxxbDTO.setQyzqfrq( entity.getQyzqfrq() );
        ksxtQcfkxxbDTO.setQyzyxqjzrq( entity.getQyzyxqjzrq() );
        ksxtQcfkxxbDTO.setDzqydzzzbz( entity.getDzqydzzzbz() );
        ksxtQcfkxxbDTO.setDzqyzagldzzzbh( entity.getDzqyzagldzzzbh() );
        ksxtQcfkxxbDTO.setZqzbh( entity.getZqzbh() );
        ksxtQcfkxxbDTO.setDzzqdzzzbz( entity.getDzzqdzzzbz() );
        ksxtQcfkxxbDTO.setDzzqzagldzzzbh( entity.getDzzqzagldzzzbh() );
        ksxtQcfkxxbDTO.setBz( entity.getBz() );
        ksxtQcfkxxbDTO.setYczrgxjtgxdm( entity.getYczrgxjtgxdm() );
        ksxtQcfkxxbDTO.setGmsfhm( entity.getGmsfhm() );
        ksxtQcfkxxbDTO.setXm( entity.getXm() );
        ksxtQcfkxxbDTO.setCym( entity.getCym() );
        ksxtQcfkxxbDTO.setXbdm( entity.getXbdm() );
        ksxtQcfkxxbDTO.setMzdm( entity.getMzdm() );
        ksxtQcfkxxbDTO.setCsrq( entity.getCsrq() );
        ksxtQcfkxxbDTO.setCsdgjhdqdm( entity.getCsdgjhdqdm() );
        ksxtQcfkxxbDTO.setCsdssxqdm( entity.getCsdssxqdm() );
        ksxtQcfkxxbDTO.setCsdqhnxxdz( entity.getCsdqhnxxdz() );
        ksxtQcfkxxbDTO.setJggjhdqdm( entity.getJggjhdqdm() );
        ksxtQcfkxxbDTO.setJgssxqdm( entity.getJgssxqdm() );
        ksxtQcfkxxbDTO.setJgqhnxxdz( entity.getJgqhnxxdz() );
        ksxtQcfkxxbDTO.setXldm( entity.getXldm() );
        ksxtQcfkxxbDTO.setHyzkdm( entity.getHyzkdm() );
        ksxtQcfkxxbDTO.setZy( entity.getZy() );
        ksxtQcfkxxbDTO.setQyldyydm( entity.getQyldyydm() );
        ksxtQcfkxxbDTO.setHjdgajgjgdm( entity.getHjdgajgjgdm() );
        ksxtQcfkxxbDTO.setHjdgajgmc( entity.getHjdgajgmc() );
        ksxtQcfkxxbDTO.setHjdlxdh( entity.getHjdlxdh() );
        ksxtQcfkxxbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        ksxtQcfkxxbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        ksxtQcfkxxbDTO.setBlrxm( entity.getBlrxm() );
        ksxtQcfkxxbDTO.setBlsj( entity.getBlsj() );
        ksxtQcfkxxbDTO.setQyfwdm( entity.getQyfwdm() );
        ksxtQcfkxxbDTO.setFsdwsjgsdwdm( entity.getFsdwsjgsdwdm() );
        ksxtQcfkxxbDTO.setFsdwsjgsdwmc( entity.getFsdwsjgsdwmc() );
        ksxtQcfkxxbDTO.setJsdwsjgsdwdm( entity.getJsdwsjgsdwdm() );
        ksxtQcfkxxbDTO.setJsdwsjgsdwmc( entity.getJsdwsjgsdwmc() );
        ksxtQcfkxxbDTO.setRksj( entity.getRksj() );

        return ksxtQcfkxxbDTO;
    }

    @Override
    public KsxtQcfkxxbDO convertToDO(KsxtQcfkxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtQcfkxxbDO ksxtQcfkxxbDO = new KsxtQcfkxxbDO();

        ksxtQcfkxxbDO.setId( dto.getId() );
        ksxtQcfkxxbDO.setKsxtqcfkid( dto.getKsxtqcfkid() );
        ksxtQcfkxxbDO.setKsxtid( dto.getKsxtid() );
        ksxtQcfkxxbDO.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtQcfkxxbDO.setCzrgmsfhm( dto.getCzrgmsfhm() );
        ksxtQcfkxxbDO.setCzrxm( dto.getCzrxm() );
        ksxtQcfkxxbDO.setYzzssxqdm( dto.getYzzssxqdm() );
        ksxtQcfkxxbDO.setYzzqhnxxdz( dto.getYzzqhnxxdz() );
        ksxtQcfkxxbDO.setYzzcxfldm( dto.getYzzcxfldm() );
        ksxtQcfkxxbDO.setQwdssxqdm( dto.getQwdssxqdm() );
        ksxtQcfkxxbDO.setQwdqhnxxdz( dto.getQwdqhnxxdz() );
        ksxtQcfkxxbDO.setQwdhkdjjggajgjgdm( dto.getQwdhkdjjggajgjgdm() );
        ksxtQcfkxxbDO.setQwdhkdjjggajgmc( dto.getQwdhkdjjggajgmc() );
        ksxtQcfkxxbDO.setQyzbh( dto.getQyzbh() );
        ksxtQcfkxxbDO.setQyzqfjggajgjgdm( dto.getQyzqfjggajgjgdm() );
        ksxtQcfkxxbDO.setQyzqfjggajgmc( dto.getQyzqfjggajgmc() );
        ksxtQcfkxxbDO.setQyzqfrq( dto.getQyzqfrq() );
        ksxtQcfkxxbDO.setQyzyxqjzrq( dto.getQyzyxqjzrq() );
        ksxtQcfkxxbDO.setDzqydzzzbz( dto.getDzqydzzzbz() );
        ksxtQcfkxxbDO.setDzqyzagldzzzbh( dto.getDzqyzagldzzzbh() );
        ksxtQcfkxxbDO.setZqzbh( dto.getZqzbh() );
        ksxtQcfkxxbDO.setDzzqdzzzbz( dto.getDzzqdzzzbz() );
        ksxtQcfkxxbDO.setDzzqzagldzzzbh( dto.getDzzqzagldzzzbh() );
        ksxtQcfkxxbDO.setBz( dto.getBz() );
        ksxtQcfkxxbDO.setYczrgxjtgxdm( dto.getYczrgxjtgxdm() );
        ksxtQcfkxxbDO.setGmsfhm( dto.getGmsfhm() );
        ksxtQcfkxxbDO.setXm( dto.getXm() );
        ksxtQcfkxxbDO.setCym( dto.getCym() );
        ksxtQcfkxxbDO.setXbdm( dto.getXbdm() );
        ksxtQcfkxxbDO.setMzdm( dto.getMzdm() );
        ksxtQcfkxxbDO.setCsrq( dto.getCsrq() );
        ksxtQcfkxxbDO.setCsdgjhdqdm( dto.getCsdgjhdqdm() );
        ksxtQcfkxxbDO.setCsdssxqdm( dto.getCsdssxqdm() );
        ksxtQcfkxxbDO.setCsdqhnxxdz( dto.getCsdqhnxxdz() );
        ksxtQcfkxxbDO.setJggjhdqdm( dto.getJggjhdqdm() );
        ksxtQcfkxxbDO.setJgssxqdm( dto.getJgssxqdm() );
        ksxtQcfkxxbDO.setJgqhnxxdz( dto.getJgqhnxxdz() );
        ksxtQcfkxxbDO.setXldm( dto.getXldm() );
        ksxtQcfkxxbDO.setHyzkdm( dto.getHyzkdm() );
        ksxtQcfkxxbDO.setZy( dto.getZy() );
        ksxtQcfkxxbDO.setQyldyydm( dto.getQyldyydm() );
        ksxtQcfkxxbDO.setHjdgajgjgdm( dto.getHjdgajgjgdm() );
        ksxtQcfkxxbDO.setHjdgajgmc( dto.getHjdgajgmc() );
        ksxtQcfkxxbDO.setHjdlxdh( dto.getHjdlxdh() );
        ksxtQcfkxxbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtQcfkxxbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtQcfkxxbDO.setBlrxm( dto.getBlrxm() );
        ksxtQcfkxxbDO.setBlsj( dto.getBlsj() );
        ksxtQcfkxxbDO.setQyfwdm( dto.getQyfwdm() );
        ksxtQcfkxxbDO.setFsdwsjgsdwdm( dto.getFsdwsjgsdwdm() );
        ksxtQcfkxxbDO.setFsdwsjgsdwmc( dto.getFsdwsjgsdwmc() );
        ksxtQcfkxxbDO.setJsdwsjgsdwdm( dto.getJsdwsjgsdwdm() );
        ksxtQcfkxxbDO.setJsdwsjgsdwmc( dto.getJsdwsjgsdwmc() );
        ksxtQcfkxxbDO.setRksj( dto.getRksj() );

        return ksxtQcfkxxbDO;
    }

    @Override
    public KsxtQcfkxxbDTO convertToDTO(KsxtQcfkxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsxtQcfkxxbDTO ksxtQcfkxxbDTO = new KsxtQcfkxxbDTO();

        ksxtQcfkxxbDTO.setKsxtid( req.getKsxtid() );

        return ksxtQcfkxxbDTO;
    }

    @Override
    public KsxtQcfkxxbPageResp convertToPageResp(KsxtQcfkxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtQcfkxxbPageResp ksxtQcfkxxbPageResp = new KsxtQcfkxxbPageResp();

        ksxtQcfkxxbPageResp.setKsxtqcfkid( dto.getKsxtqcfkid() );
        ksxtQcfkxxbPageResp.setKsxtid( dto.getKsxtid() );
        ksxtQcfkxxbPageResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtQcfkxxbPageResp.setCzrgmsfhm( dto.getCzrgmsfhm() );
        ksxtQcfkxxbPageResp.setCzrxm( dto.getCzrxm() );
        ksxtQcfkxxbPageResp.setYzzssxqdm( dto.getYzzssxqdm() );
        ksxtQcfkxxbPageResp.setYzzqhnxxdz( dto.getYzzqhnxxdz() );
        ksxtQcfkxxbPageResp.setYzzcxfldm( dto.getYzzcxfldm() );
        ksxtQcfkxxbPageResp.setQwdssxqdm( dto.getQwdssxqdm() );
        ksxtQcfkxxbPageResp.setQwdqhnxxdz( dto.getQwdqhnxxdz() );
        ksxtQcfkxxbPageResp.setQwdhkdjjggajgjgdm( dto.getQwdhkdjjggajgjgdm() );
        ksxtQcfkxxbPageResp.setQwdhkdjjggajgmc( dto.getQwdhkdjjggajgmc() );
        ksxtQcfkxxbPageResp.setQyzbh( dto.getQyzbh() );
        ksxtQcfkxxbPageResp.setQyzqfjggajgjgdm( dto.getQyzqfjggajgjgdm() );
        ksxtQcfkxxbPageResp.setQyzqfjggajgmc( dto.getQyzqfjggajgmc() );
        ksxtQcfkxxbPageResp.setQyzqfrq( dto.getQyzqfrq() );
        ksxtQcfkxxbPageResp.setQyzyxqjzrq( dto.getQyzyxqjzrq() );
        ksxtQcfkxxbPageResp.setDzqydzzzbz( dto.getDzqydzzzbz() );
        ksxtQcfkxxbPageResp.setDzqyzagldzzzbh( dto.getDzqyzagldzzzbh() );
        ksxtQcfkxxbPageResp.setZqzbh( dto.getZqzbh() );
        ksxtQcfkxxbPageResp.setDzzqdzzzbz( dto.getDzzqdzzzbz() );
        ksxtQcfkxxbPageResp.setDzzqzagldzzzbh( dto.getDzzqzagldzzzbh() );
        ksxtQcfkxxbPageResp.setBz( dto.getBz() );
        ksxtQcfkxxbPageResp.setYczrgxjtgxdm( dto.getYczrgxjtgxdm() );
        ksxtQcfkxxbPageResp.setGmsfhm( dto.getGmsfhm() );
        ksxtQcfkxxbPageResp.setXm( dto.getXm() );
        ksxtQcfkxxbPageResp.setCym( dto.getCym() );
        ksxtQcfkxxbPageResp.setXbdm( dto.getXbdm() );
        ksxtQcfkxxbPageResp.setMzdm( dto.getMzdm() );
        ksxtQcfkxxbPageResp.setCsrq( dto.getCsrq() );
        ksxtQcfkxxbPageResp.setCsdgjhdqdm( dto.getCsdgjhdqdm() );
        ksxtQcfkxxbPageResp.setCsdssxqdm( dto.getCsdssxqdm() );
        ksxtQcfkxxbPageResp.setCsdqhnxxdz( dto.getCsdqhnxxdz() );
        ksxtQcfkxxbPageResp.setJggjhdqdm( dto.getJggjhdqdm() );
        ksxtQcfkxxbPageResp.setJgssxqdm( dto.getJgssxqdm() );
        ksxtQcfkxxbPageResp.setJgqhnxxdz( dto.getJgqhnxxdz() );
        ksxtQcfkxxbPageResp.setXldm( dto.getXldm() );
        ksxtQcfkxxbPageResp.setHyzkdm( dto.getHyzkdm() );
        ksxtQcfkxxbPageResp.setZy( dto.getZy() );
        ksxtQcfkxxbPageResp.setQyldyydm( dto.getQyldyydm() );
        ksxtQcfkxxbPageResp.setHjdgajgjgdm( dto.getHjdgajgjgdm() );
        ksxtQcfkxxbPageResp.setHjdgajgmc( dto.getHjdgajgmc() );
        ksxtQcfkxxbPageResp.setHjdlxdh( dto.getHjdlxdh() );
        ksxtQcfkxxbPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtQcfkxxbPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtQcfkxxbPageResp.setBlrxm( dto.getBlrxm() );
        ksxtQcfkxxbPageResp.setBlsj( dto.getBlsj() );
        ksxtQcfkxxbPageResp.setQyfwdm( dto.getQyfwdm() );
        ksxtQcfkxxbPageResp.setFsdwsjgsdwdm( dto.getFsdwsjgsdwdm() );
        ksxtQcfkxxbPageResp.setFsdwsjgsdwmc( dto.getFsdwsjgsdwmc() );
        ksxtQcfkxxbPageResp.setJsdwsjgsdwdm( dto.getJsdwsjgsdwdm() );
        ksxtQcfkxxbPageResp.setJsdwsjgsdwmc( dto.getJsdwsjgsdwmc() );
        ksxtQcfkxxbPageResp.setRksj( dto.getRksj() );

        return ksxtQcfkxxbPageResp;
    }

    @Override
    public KsxtQcfkxxbViewResp convertToViewResp(KsxtQcfkxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtQcfkxxbViewResp ksxtQcfkxxbViewResp = new KsxtQcfkxxbViewResp();

        ksxtQcfkxxbViewResp.setKsxtqcfkid( dto.getKsxtqcfkid() );
        ksxtQcfkxxbViewResp.setKsxtid( dto.getKsxtid() );
        ksxtQcfkxxbViewResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtQcfkxxbViewResp.setCzrgmsfhm( dto.getCzrgmsfhm() );
        ksxtQcfkxxbViewResp.setCzrxm( dto.getCzrxm() );
        ksxtQcfkxxbViewResp.setYzzssxqdm( dto.getYzzssxqdm() );
        ksxtQcfkxxbViewResp.setYzzqhnxxdz( dto.getYzzqhnxxdz() );
        ksxtQcfkxxbViewResp.setYzzcxfldm( dto.getYzzcxfldm() );
        ksxtQcfkxxbViewResp.setQwdssxqdm( dto.getQwdssxqdm() );
        ksxtQcfkxxbViewResp.setQwdqhnxxdz( dto.getQwdqhnxxdz() );
        ksxtQcfkxxbViewResp.setQwdhkdjjggajgjgdm( dto.getQwdhkdjjggajgjgdm() );
        ksxtQcfkxxbViewResp.setQwdhkdjjggajgmc( dto.getQwdhkdjjggajgmc() );
        ksxtQcfkxxbViewResp.setQyzbh( dto.getQyzbh() );
        ksxtQcfkxxbViewResp.setQyzqfjggajgjgdm( dto.getQyzqfjggajgjgdm() );
        ksxtQcfkxxbViewResp.setQyzqfjggajgmc( dto.getQyzqfjggajgmc() );
        ksxtQcfkxxbViewResp.setQyzqfrq( dto.getQyzqfrq() );
        ksxtQcfkxxbViewResp.setQyzyxqjzrq( dto.getQyzyxqjzrq() );
        ksxtQcfkxxbViewResp.setDzqydzzzbz( dto.getDzqydzzzbz() );
        ksxtQcfkxxbViewResp.setDzqyzagldzzzbh( dto.getDzqyzagldzzzbh() );
        ksxtQcfkxxbViewResp.setZqzbh( dto.getZqzbh() );
        ksxtQcfkxxbViewResp.setDzzqdzzzbz( dto.getDzzqdzzzbz() );
        ksxtQcfkxxbViewResp.setDzzqzagldzzzbh( dto.getDzzqzagldzzzbh() );
        ksxtQcfkxxbViewResp.setBz( dto.getBz() );
        ksxtQcfkxxbViewResp.setYczrgxjtgxdm( dto.getYczrgxjtgxdm() );
        ksxtQcfkxxbViewResp.setGmsfhm( dto.getGmsfhm() );
        ksxtQcfkxxbViewResp.setXm( dto.getXm() );
        ksxtQcfkxxbViewResp.setCym( dto.getCym() );
        ksxtQcfkxxbViewResp.setXbdm( dto.getXbdm() );
        ksxtQcfkxxbViewResp.setMzdm( dto.getMzdm() );
        ksxtQcfkxxbViewResp.setCsrq( dto.getCsrq() );
        ksxtQcfkxxbViewResp.setCsdgjhdqdm( dto.getCsdgjhdqdm() );
        ksxtQcfkxxbViewResp.setCsdssxqdm( dto.getCsdssxqdm() );
        ksxtQcfkxxbViewResp.setCsdqhnxxdz( dto.getCsdqhnxxdz() );
        ksxtQcfkxxbViewResp.setJggjhdqdm( dto.getJggjhdqdm() );
        ksxtQcfkxxbViewResp.setJgssxqdm( dto.getJgssxqdm() );
        ksxtQcfkxxbViewResp.setJgqhnxxdz( dto.getJgqhnxxdz() );
        ksxtQcfkxxbViewResp.setXldm( dto.getXldm() );
        ksxtQcfkxxbViewResp.setHyzkdm( dto.getHyzkdm() );
        ksxtQcfkxxbViewResp.setZy( dto.getZy() );
        ksxtQcfkxxbViewResp.setQyldyydm( dto.getQyldyydm() );
        ksxtQcfkxxbViewResp.setHjdgajgjgdm( dto.getHjdgajgjgdm() );
        ksxtQcfkxxbViewResp.setHjdgajgmc( dto.getHjdgajgmc() );
        ksxtQcfkxxbViewResp.setHjdlxdh( dto.getHjdlxdh() );
        ksxtQcfkxxbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        ksxtQcfkxxbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        ksxtQcfkxxbViewResp.setBlrxm( dto.getBlrxm() );
        ksxtQcfkxxbViewResp.setBlsj( dto.getBlsj() );
        ksxtQcfkxxbViewResp.setQyfwdm( dto.getQyfwdm() );
        ksxtQcfkxxbViewResp.setFsdwsjgsdwdm( dto.getFsdwsjgsdwdm() );
        ksxtQcfkxxbViewResp.setFsdwsjgsdwmc( dto.getFsdwsjgsdwmc() );
        ksxtQcfkxxbViewResp.setJsdwsjgsdwdm( dto.getJsdwsjgsdwdm() );
        ksxtQcfkxxbViewResp.setJsdwsjgsdwmc( dto.getJsdwsjgsdwmc() );
        ksxtQcfkxxbViewResp.setRksj( dto.getRksj() );

        return ksxtQcfkxxbViewResp;
    }
}
