package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsywRzbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsywRzbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsywRzbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsywRzbViewResp;
import com.zjjcnt.project.ck.base.entity.KsywRzbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsywRzbConvertImpl implements KsywRzbConvert {

    @Override
    public KsywRzbDTO convert(KsywRzbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsywRzbDTO ksywRzbDTO = new KsywRzbDTO();

        ksywRzbDTO.setId( entity.getId() );
        ksywRzbDTO.setYwslh( entity.getYwslh() );
        ksywRzbDTO.setYwlx( entity.getYwlx() );
        ksywRzbDTO.setYwbz( entity.getYwbz() );
        ksywRzbDTO.setZqzbh( entity.getZqzbh() );
        ksywRzbDTO.setQyzbh( entity.getQyzbh() );
        ksywRzbDTO.setRksj( entity.getRksj() );
        ksywRzbDTO.setCode( entity.getCode() );
        ksywRzbDTO.setMsg( entity.getMsg() );
        ksywRzbDTO.setSjdata( entity.getSjdata() );
        ksywRzbDTO.setBz( entity.getBz() );
        ksywRzbDTO.setCzyid( entity.getCzyid() );
        ksywRzbDTO.setCzyxm( entity.getCzyxm() );
        ksywRzbDTO.setCzip( entity.getCzip() );
        ksywRzbDTO.setCzydwdm( entity.getCzydwdm() );
        ksywRzbDTO.setCzydwmc( entity.getCzydwmc() );

        return ksywRzbDTO;
    }

    @Override
    public KsywRzbDO convertToDO(KsywRzbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywRzbDO ksywRzbDO = new KsywRzbDO();

        ksywRzbDO.setId( dto.getId() );
        ksywRzbDO.setYwslh( dto.getYwslh() );
        ksywRzbDO.setYwlx( dto.getYwlx() );
        ksywRzbDO.setYwbz( dto.getYwbz() );
        ksywRzbDO.setZqzbh( dto.getZqzbh() );
        ksywRzbDO.setQyzbh( dto.getQyzbh() );
        ksywRzbDO.setRksj( dto.getRksj() );
        ksywRzbDO.setCode( dto.getCode() );
        ksywRzbDO.setMsg( dto.getMsg() );
        ksywRzbDO.setSjdata( dto.getSjdata() );
        ksywRzbDO.setBz( dto.getBz() );
        ksywRzbDO.setCzyid( dto.getCzyid() );
        ksywRzbDO.setCzyxm( dto.getCzyxm() );
        ksywRzbDO.setCzip( dto.getCzip() );
        ksywRzbDO.setCzydwdm( dto.getCzydwdm() );
        ksywRzbDO.setCzydwmc( dto.getCzydwmc() );

        return ksywRzbDO;
    }

    @Override
    public KsywRzbDTO convertToDTO(KsywRzbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsywRzbDTO ksywRzbDTO = new KsywRzbDTO();

        ksywRzbDTO.setYwslh( req.getYwslh() );

        return ksywRzbDTO;
    }

    @Override
    public KsywRzbPageResp convertToPageResp(KsywRzbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywRzbPageResp ksywRzbPageResp = new KsywRzbPageResp();

        ksywRzbPageResp.setId( dto.getId() );
        ksywRzbPageResp.setYwslh( dto.getYwslh() );
        ksywRzbPageResp.setYwlx( dto.getYwlx() );
        ksywRzbPageResp.setYwbz( dto.getYwbz() );
        ksywRzbPageResp.setZqzbh( dto.getZqzbh() );
        ksywRzbPageResp.setQyzbh( dto.getQyzbh() );
        ksywRzbPageResp.setRksj( dto.getRksj() );
        ksywRzbPageResp.setCode( dto.getCode() );
        ksywRzbPageResp.setMsg( dto.getMsg() );
        ksywRzbPageResp.setSjdata( dto.getSjdata() );
        ksywRzbPageResp.setBz( dto.getBz() );
        ksywRzbPageResp.setCzyid( dto.getCzyid() );
        ksywRzbPageResp.setCzyxm( dto.getCzyxm() );
        ksywRzbPageResp.setCzip( dto.getCzip() );
        ksywRzbPageResp.setCzydwdm( dto.getCzydwdm() );
        ksywRzbPageResp.setCzydwmc( dto.getCzydwmc() );

        return ksywRzbPageResp;
    }

    @Override
    public KsywRzbViewResp convertToViewResp(KsywRzbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsywRzbViewResp ksywRzbViewResp = new KsywRzbViewResp();

        ksywRzbViewResp.setId( dto.getId() );
        ksywRzbViewResp.setYwslh( dto.getYwslh() );
        ksywRzbViewResp.setYwlx( dto.getYwlx() );
        ksywRzbViewResp.setYwbz( dto.getYwbz() );
        ksywRzbViewResp.setZqzbh( dto.getZqzbh() );
        ksywRzbViewResp.setQyzbh( dto.getQyzbh() );
        ksywRzbViewResp.setRksj( dto.getRksj() );
        ksywRzbViewResp.setCode( dto.getCode() );
        ksywRzbViewResp.setMsg( dto.getMsg() );
        ksywRzbViewResp.setSjdata( dto.getSjdata() );
        ksywRzbViewResp.setBz( dto.getBz() );
        ksywRzbViewResp.setCzyid( dto.getCzyid() );
        ksywRzbViewResp.setCzyxm( dto.getCzyxm() );
        ksywRzbViewResp.setCzip( dto.getCzip() );
        ksywRzbViewResp.setCzydwdm( dto.getCzydwdm() );
        ksywRzbViewResp.setCzydwmc( dto.getCzydwmc() );

        return ksywRzbViewResp;
    }
}
