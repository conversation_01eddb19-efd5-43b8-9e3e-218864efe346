package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktYwslSprzDTO;
import com.zjjcnt.project.ck.base.dto.req.RktYwslSprzCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktYwslSprzPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktYwslSprzUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslSprzCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslSprzPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslSprzViewResp;
import com.zjjcnt.project.ck.base.entity.RktYwslSprzDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:47+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktYwslSprzConvertImpl implements RktYwslSprzConvert {

    @Override
    public RktYwslSprzDTO convert(RktYwslSprzDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktYwslSprzDTO rktYwslSprzDTO = new RktYwslSprzDTO();

        rktYwslSprzDTO.setId( entity.getId() );
        rktYwslSprzDTO.setNbbh( entity.getNbbh() );
        rktYwslSprzDTO.setYwslh( entity.getYwslh() );
        rktYwslSprzDTO.setBlzt( entity.getBlzt() );
        rktYwslSprzDTO.setCzsj( entity.getCzsj() );
        rktYwslSprzDTO.setCzyid( entity.getCzyid() );
        rktYwslSprzDTO.setCzyxm( entity.getCzyxm() );
        rktYwslSprzDTO.setCzip( entity.getCzip() );
        rktYwslSprzDTO.setCzydwmc( entity.getCzydwmc() );
        rktYwslSprzDTO.setCzydwdm( entity.getCzydwdm() );
        rktYwslSprzDTO.setBz( entity.getBz() );
        rktYwslSprzDTO.setSpjgdm( entity.getSpjgdm() );
        rktYwslSprzDTO.setCzylxdh( entity.getCzylxdh() );

        return rktYwslSprzDTO;
    }

    @Override
    public RktYwslSprzDO convertToDO(RktYwslSprzDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslSprzDO rktYwslSprzDO = new RktYwslSprzDO();

        rktYwslSprzDO.setId( dto.getId() );
        rktYwslSprzDO.setNbbh( dto.getNbbh() );
        rktYwslSprzDO.setYwslh( dto.getYwslh() );
        rktYwslSprzDO.setBlzt( dto.getBlzt() );
        rktYwslSprzDO.setCzsj( dto.getCzsj() );
        rktYwslSprzDO.setCzyid( dto.getCzyid() );
        rktYwslSprzDO.setCzyxm( dto.getCzyxm() );
        rktYwslSprzDO.setCzip( dto.getCzip() );
        rktYwslSprzDO.setCzydwmc( dto.getCzydwmc() );
        rktYwslSprzDO.setCzydwdm( dto.getCzydwdm() );
        rktYwslSprzDO.setBz( dto.getBz() );
        rktYwslSprzDO.setSpjgdm( dto.getSpjgdm() );
        rktYwslSprzDO.setCzylxdh( dto.getCzylxdh() );

        return rktYwslSprzDO;
    }

    @Override
    public RktYwslSprzDTO convertToDTO(RktYwslSprzPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktYwslSprzDTO rktYwslSprzDTO = new RktYwslSprzDTO();

        rktYwslSprzDTO.setYwslh( req.getYwslh() );
        rktYwslSprzDTO.setBlzt( req.getBlzt() );
        rktYwslSprzDTO.setCzsj( req.getCzsj() );
        rktYwslSprzDTO.setCzyid( req.getCzyid() );
        rktYwslSprzDTO.setCzyxm( req.getCzyxm() );
        rktYwslSprzDTO.setCzip( req.getCzip() );
        rktYwslSprzDTO.setCzydwmc( req.getCzydwmc() );
        rktYwslSprzDTO.setCzydwdm( req.getCzydwdm() );
        rktYwslSprzDTO.setBz( req.getBz() );
        rktYwslSprzDTO.setSpjgdm( req.getSpjgdm() );
        rktYwslSprzDTO.setCzylxdh( req.getCzylxdh() );

        return rktYwslSprzDTO;
    }

    @Override
    public RktYwslSprzDTO convertToDTO(RktYwslSprzCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktYwslSprzDTO rktYwslSprzDTO = new RktYwslSprzDTO();

        rktYwslSprzDTO.setYwslh( req.getYwslh() );
        rktYwslSprzDTO.setBlzt( req.getBlzt() );
        rktYwslSprzDTO.setCzsj( req.getCzsj() );
        rktYwslSprzDTO.setCzyid( req.getCzyid() );
        rktYwslSprzDTO.setCzyxm( req.getCzyxm() );
        rktYwslSprzDTO.setCzip( req.getCzip() );
        rktYwslSprzDTO.setCzydwmc( req.getCzydwmc() );
        rktYwslSprzDTO.setCzydwdm( req.getCzydwdm() );
        rktYwslSprzDTO.setBz( req.getBz() );
        rktYwslSprzDTO.setSpjgdm( req.getSpjgdm() );
        rktYwslSprzDTO.setCzylxdh( req.getCzylxdh() );

        return rktYwslSprzDTO;
    }

    @Override
    public RktYwslSprzDTO convertToDTO(RktYwslSprzUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktYwslSprzDTO rktYwslSprzDTO = new RktYwslSprzDTO();

        rktYwslSprzDTO.setNbbh( req.getNbbh() );
        rktYwslSprzDTO.setYwslh( req.getYwslh() );
        rktYwslSprzDTO.setBlzt( req.getBlzt() );
        rktYwslSprzDTO.setCzsj( req.getCzsj() );
        rktYwslSprzDTO.setCzyid( req.getCzyid() );
        rktYwslSprzDTO.setCzyxm( req.getCzyxm() );
        rktYwslSprzDTO.setCzip( req.getCzip() );
        rktYwslSprzDTO.setCzydwmc( req.getCzydwmc() );
        rktYwslSprzDTO.setCzydwdm( req.getCzydwdm() );
        rktYwslSprzDTO.setBz( req.getBz() );
        rktYwslSprzDTO.setSpjgdm( req.getSpjgdm() );
        rktYwslSprzDTO.setCzylxdh( req.getCzylxdh() );

        return rktYwslSprzDTO;
    }

    @Override
    public RktYwslSprzPageResp convertToPageResp(RktYwslSprzDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslSprzPageResp rktYwslSprzPageResp = new RktYwslSprzPageResp();

        rktYwslSprzPageResp.setNbbh( dto.getNbbh() );
        rktYwslSprzPageResp.setYwslh( dto.getYwslh() );
        rktYwslSprzPageResp.setBlzt( dto.getBlzt() );
        rktYwslSprzPageResp.setCzsj( dto.getCzsj() );
        rktYwslSprzPageResp.setCzyid( dto.getCzyid() );
        rktYwslSprzPageResp.setCzyxm( dto.getCzyxm() );
        rktYwslSprzPageResp.setCzip( dto.getCzip() );
        rktYwslSprzPageResp.setCzydwmc( dto.getCzydwmc() );
        rktYwslSprzPageResp.setCzydwdm( dto.getCzydwdm() );
        rktYwslSprzPageResp.setBz( dto.getBz() );
        rktYwslSprzPageResp.setSpjgdm( dto.getSpjgdm() );
        rktYwslSprzPageResp.setCzylxdh( dto.getCzylxdh() );

        return rktYwslSprzPageResp;
    }

    @Override
    public RktYwslSprzViewResp convertToViewResp(RktYwslSprzDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslSprzViewResp rktYwslSprzViewResp = new RktYwslSprzViewResp();

        rktYwslSprzViewResp.setNbbh( dto.getNbbh() );
        rktYwslSprzViewResp.setYwslh( dto.getYwslh() );
        rktYwslSprzViewResp.setBlzt( dto.getBlzt() );
        rktYwslSprzViewResp.setCzsj( dto.getCzsj() );
        rktYwslSprzViewResp.setCzyid( dto.getCzyid() );
        rktYwslSprzViewResp.setCzyxm( dto.getCzyxm() );
        rktYwslSprzViewResp.setCzip( dto.getCzip() );
        rktYwslSprzViewResp.setCzydwmc( dto.getCzydwmc() );
        rktYwslSprzViewResp.setCzydwdm( dto.getCzydwdm() );
        rktYwslSprzViewResp.setBz( dto.getBz() );
        rktYwslSprzViewResp.setSpjgdm( dto.getSpjgdm() );
        rktYwslSprzViewResp.setCzylxdh( dto.getCzylxdh() );

        return rktYwslSprzViewResp;
    }

    @Override
    public RktYwslSprzCreateResp convertToCreateResp(RktYwslSprzDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslSprzCreateResp rktYwslSprzCreateResp = new RktYwslSprzCreateResp();

        rktYwslSprzCreateResp.setNbbh( dto.getNbbh() );
        rktYwslSprzCreateResp.setYwslh( dto.getYwslh() );
        rktYwslSprzCreateResp.setBlzt( dto.getBlzt() );
        rktYwslSprzCreateResp.setCzsj( dto.getCzsj() );
        rktYwslSprzCreateResp.setCzyid( dto.getCzyid() );
        rktYwslSprzCreateResp.setCzyxm( dto.getCzyxm() );
        rktYwslSprzCreateResp.setCzip( dto.getCzip() );
        rktYwslSprzCreateResp.setCzydwmc( dto.getCzydwmc() );
        rktYwslSprzCreateResp.setCzydwdm( dto.getCzydwdm() );
        rktYwslSprzCreateResp.setBz( dto.getBz() );
        rktYwslSprzCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktYwslSprzCreateResp.setCzylxdh( dto.getCzylxdh() );

        return rktYwslSprzCreateResp;
    }
}
