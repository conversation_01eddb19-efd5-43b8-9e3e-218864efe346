package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.req.RktYwslClqdPageReq;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslClqdPageResp;
import com.zjjcnt.project.ck.sysadmin.dto.RktYwslClqdDTO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:41+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktYwslClqdConvertImpl implements RktYwslClqdConvert {

    @Override
    public RktYwslClqdDTO convertToDTO(RktYwslClqdPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktYwslClqdDTO rktYwslClqdDTO = new RktYwslClqdDTO();

        rktYwslClqdDTO.setYwslh( req.getYwslh() );
        rktYwslClqdDTO.setLcywlx( req.getLcywlx() );
        rktYwslClqdDTO.setXh( req.getXh() );
        rktYwslClqdDTO.setCllxdl( req.getCllxdl() );
        rktYwslClqdDTO.setCllxzl( req.getCllxzl() );
        rktYwslClqdDTO.setCllxxl( req.getCllxxl() );
        rktYwslClqdDTO.setCllxdm( req.getCllxdm() );
        rktYwslClqdDTO.setCllxmc( req.getCllxmc() );
        rktYwslClqdDTO.setYjfs( req.getYjfs() );
        rktYwslClqdDTO.setFyjfs( req.getFyjfs() );
        rktYwslClqdDTO.setClsl( req.getClsl() );
        rktYwslClqdDTO.setQtsx( req.getQtsx() );
        rktYwslClqdDTO.setSfdzqzwj( req.getSfdzqzwj() );

        return rktYwslClqdDTO;
    }

    @Override
    public RktYwslClqdPageResp convertToPageResp(RktYwslClqdDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslClqdPageResp rktYwslClqdPageResp = new RktYwslClqdPageResp();

        rktYwslClqdPageResp.setSlclqdbh( dto.getSlclqdbh() );
        rktYwslClqdPageResp.setYwslh( dto.getYwslh() );
        rktYwslClqdPageResp.setLcywlx( dto.getLcywlx() );
        rktYwslClqdPageResp.setXh( dto.getXh() );
        rktYwslClqdPageResp.setCllxdl( dto.getCllxdl() );
        rktYwslClqdPageResp.setCllxzl( dto.getCllxzl() );
        rktYwslClqdPageResp.setCllxxl( dto.getCllxxl() );
        rktYwslClqdPageResp.setCllxdm( dto.getCllxdm() );
        rktYwslClqdPageResp.setCllxmc( dto.getCllxmc() );
        rktYwslClqdPageResp.setYjfs( dto.getYjfs() );
        rktYwslClqdPageResp.setFyjfs( dto.getFyjfs() );
        rktYwslClqdPageResp.setClsl( dto.getClsl() );
        rktYwslClqdPageResp.setQtsx( dto.getQtsx() );

        return rktYwslClqdPageResp;
    }
}
