package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.req.RktYwslJbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslJbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslJbViewResp;
import com.zjjcnt.project.ck.sysadmin.dto.RktYwslJbDTO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktYwslJbBaseConvertImpl implements RktYwslJbBaseConvert {

    @Override
    public RktYwslJbDTO convertToDTO(RktYwslJbPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktYwslJbDTO rktYwslJbDTO = new RktYwslJbDTO();

        rktYwslJbDTO.setGmsfhm( req.getGmsfhm() );
        rktYwslJbDTO.setLcywlx( req.getLcywlx() );
        rktYwslJbDTO.setBlzt( req.getBlzt() );
        rktYwslJbDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktYwslJbDTO.setSlsjStart( req.getSlsjStart() );
        rktYwslJbDTO.setSlsjEnd( req.getSlsjEnd() );
        rktYwslJbDTO.setSfcx( req.getSfcx() );

        return rktYwslJbDTO;
    }

    @Override
    public RktYwslJbPageResp convertToPageResp(RktYwslJbDTO rktYwslJbDTO) {
        if ( rktYwslJbDTO == null ) {
            return null;
        }

        RktYwslJbPageResp rktYwslJbPageResp = new RktYwslJbPageResp();

        rktYwslJbPageResp.setId( rktYwslJbDTO.getId() );
        rktYwslJbPageResp.setYwslh( rktYwslJbDTO.getYwslh() );
        rktYwslJbPageResp.setLcdyid( rktYwslJbDTO.getLcdyid() );
        rktYwslJbPageResp.setLcmc( rktYwslJbDTO.getLcmc() );
        rktYwslJbPageResp.setSpyj( rktYwslJbDTO.getSpyj() );
        if ( rktYwslJbDTO.getSlrs() != null ) {
            rktYwslJbPageResp.setSlrs( String.valueOf( rktYwslJbDTO.getSlrs() ) );
        }
        rktYwslJbPageResp.setGmsfhm( rktYwslJbDTO.getGmsfhm() );
        rktYwslJbPageResp.setXm( rktYwslJbDTO.getXm() );
        rktYwslJbPageResp.setLcywlx( rktYwslJbDTO.getLcywlx() );
        rktYwslJbPageResp.setLcywbt( rktYwslJbDTO.getLcywbt() );
        rktYwslJbPageResp.setLczt( rktYwslJbDTO.getLczt() );
        rktYwslJbPageResp.setBlzt( rktYwslJbDTO.getBlzt() );
        rktYwslJbPageResp.setSqrgmsfhm( rktYwslJbDTO.getSqrgmsfhm() );
        rktYwslJbPageResp.setSqrxm( rktYwslJbDTO.getSqrxm() );
        rktYwslJbPageResp.setSqrlxdh( rktYwslJbDTO.getSqrlxdh() );
        rktYwslJbPageResp.setSqrybdrgx( rktYwslJbDTO.getSqrybdrgx() );
        rktYwslJbPageResp.setSqrzzssxq( rktYwslJbDTO.getSqrzzssxq() );
        rktYwslJbPageResp.setSqrzzxz( rktYwslJbDTO.getSqrzzxz() );
        rktYwslJbPageResp.setSqrhkdjjg( rktYwslJbDTO.getSqrhkdjjg() );
        rktYwslJbPageResp.setSjrxm( rktYwslJbDTO.getSjrxm() );
        rktYwslJbPageResp.setSjrlxdh( rktYwslJbDTO.getSjrlxdh() );
        rktYwslJbPageResp.setSqrtxdz( rktYwslJbDTO.getSqrtxdz() );
        rktYwslJbPageResp.setSfscdzqzhky( rktYwslJbDTO.getSfscdzqzhky() );
        rktYwslJbPageResp.setSlrlxdh( rktYwslJbDTO.getSlrlxdh() );
        rktYwslJbPageResp.setSprlxdh( rktYwslJbDTO.getSprlxdh() );
        rktYwslJbPageResp.setSfxyyjzj( rktYwslJbDTO.getSfxyyjzj() );
        rktYwslJbPageResp.setSfyjyjzj( rktYwslJbDTO.getSfyjyjzj() );
        rktYwslJbPageResp.setSpxxlylb( rktYwslJbDTO.getSpxxlylb() );
        rktYwslJbPageResp.setSqrq( rktYwslJbDTO.getSqrq() );
        rktYwslJbPageResp.setSpdwgajgjgdm( rktYwslJbDTO.getSpdwgajgjgdm() );
        rktYwslJbPageResp.setSpdwgajgmc( rktYwslJbDTO.getSpdwgajgmc() );
        rktYwslJbPageResp.setSprxm( rktYwslJbDTO.getSprxm() );
        rktYwslJbPageResp.setSpsj( rktYwslJbDTO.getSpsj() );
        rktYwslJbPageResp.setSldwgajgjgdm( rktYwslJbDTO.getSldwgajgjgdm() );
        rktYwslJbPageResp.setSldwgajgmc( rktYwslJbDTO.getSldwgajgmc() );
        rktYwslJbPageResp.setSlrxm( rktYwslJbDTO.getSlrxm() );
        rktYwslJbPageResp.setSlsj( rktYwslJbDTO.getSlsj() );
        rktYwslJbPageResp.setSjgsdwdm( rktYwslJbDTO.getSjgsdwdm() );
        rktYwslJbPageResp.setSjgsdwmc( rktYwslJbDTO.getSjgsdwmc() );
        rktYwslJbPageResp.setJcwh( rktYwslJbDTO.getJcwh() );
        rktYwslJbPageResp.setSlrid( rktYwslJbDTO.getSlrid() );
        rktYwslJbPageResp.setPjjg( rktYwslJbDTO.getPjjg() );
        rktYwslJbPageResp.setPjpljc( rktYwslJbDTO.getPjpljc() );
        rktYwslJbPageResp.setSbsfxy( rktYwslJbDTO.getSbsfxy() );
        rktYwslJbPageResp.setSbzt( rktYwslJbDTO.getSbzt() );
        rktYwslJbPageResp.setSbywlx( rktYwslJbDTO.getSbywlx() );
        rktYwslJbPageResp.setSpxxcjfs( rktYwslJbDTO.getSpxxcjfs() );
        rktYwslJbPageResp.setHzxm( rktYwslJbDTO.getHzxm() );
        rktYwslJbPageResp.setHzgmsfhm( rktYwslJbDTO.getHzgmsfhm() );
        rktYwslJbPageResp.setDababz( rktYwslJbDTO.getDababz() );
        rktYwslJbPageResp.setRlhbz( rktYwslJbDTO.getRlhbz() );
        rktYwslJbPageResp.setDycjlx( rktYwslJbDTO.getDycjlx() );
        rktYwslJbPageResp.setHlwsqlx( rktYwslJbDTO.getHlwsqlx() );
        rktYwslJbPageResp.setApplyfrom( rktYwslJbDTO.getApplyfrom() );
        rktYwslJbPageResp.setHallusername( rktYwslJbDTO.getHallusername() );
        rktYwslJbPageResp.setHjsxcxlb( rktYwslJbDTO.getHjsxcxlb() );
        rktYwslJbPageResp.setSfksxtqr( rktYwslJbDTO.getSfksxtqr() );
        rktYwslJbPageResp.setZwwdtmc( rktYwslJbDTO.getZwwdtmc() );

        return rktYwslJbPageResp;
    }

    @Override
    public RktYwslJbViewResp convertToViewResp(RktYwslJbDTO rktYwslJbDTO) {
        if ( rktYwslJbDTO == null ) {
            return null;
        }

        RktYwslJbViewResp rktYwslJbViewResp = new RktYwslJbViewResp();

        rktYwslJbViewResp.setYwslh( rktYwslJbDTO.getYwslh() );
        rktYwslJbViewResp.setLcywlx( rktYwslJbDTO.getLcywlx() );
        rktYwslJbViewResp.setBlzt( rktYwslJbDTO.getBlzt() );
        rktYwslJbViewResp.setLcmc( rktYwslJbDTO.getLcmc() );
        rktYwslJbViewResp.setGmsfhm( rktYwslJbDTO.getGmsfhm() );
        rktYwslJbViewResp.setXm( rktYwslJbDTO.getXm() );
        rktYwslJbViewResp.setSqrxm( rktYwslJbDTO.getSqrxm() );
        rktYwslJbViewResp.setSqrgmsfhm( rktYwslJbDTO.getSqrgmsfhm() );
        rktYwslJbViewResp.setSqrq( rktYwslJbDTO.getSqrq() );
        rktYwslJbViewResp.setSqrlxdh( rktYwslJbDTO.getSqrlxdh() );
        rktYwslJbViewResp.setSqrhkdjjg( rktYwslJbDTO.getSqrhkdjjg() );
        rktYwslJbViewResp.setSqrzzssxq( rktYwslJbDTO.getSqrzzssxq() );
        rktYwslJbViewResp.setSqrzzxz( rktYwslJbDTO.getSqrzzxz() );
        rktYwslJbViewResp.setRlhbz( rktYwslJbDTO.getRlhbz() );
        rktYwslJbViewResp.setSqrzjmc( rktYwslJbDTO.getSqrzjmc() );
        rktYwslJbViewResp.setSqrzjhm( rktYwslJbDTO.getSqrzjhm() );
        rktYwslJbViewResp.setSqrgzdw( rktYwslJbDTO.getSqrgzdw() );
        rktYwslJbViewResp.setSlrs( rktYwslJbDTO.getSlrs() );
        rktYwslJbViewResp.setSqcxly( rktYwslJbDTO.getSqcxly() );
        rktYwslJbViewResp.setQtsqr( rktYwslJbDTO.getQtsqr() );
        rktYwslJbViewResp.setSqrrxbdsj( rktYwslJbDTO.getSqrrxbdsj() );
        rktYwslJbViewResp.setSqrrxbdxsd( rktYwslJbDTO.getSqrrxbdxsd() );
        rktYwslJbViewResp.setSqrrxbdjg( rktYwslJbDTO.getSqrrxbdjg() );
        rktYwslJbViewResp.setSpxxlylb( rktYwslJbDTO.getSpxxlylb() );
        rktYwslJbViewResp.setDycjlx( rktYwslJbDTO.getDycjlx() );
        rktYwslJbViewResp.setSldwgajgjgdm( rktYwslJbDTO.getSldwgajgjgdm() );
        rktYwslJbViewResp.setSldwgajgmc( rktYwslJbDTO.getSldwgajgmc() );
        rktYwslJbViewResp.setSjgsdwdm( rktYwslJbDTO.getSjgsdwdm() );
        rktYwslJbViewResp.setSjgsdwmc( rktYwslJbDTO.getSjgsdwmc() );
        rktYwslJbViewResp.setSlsj( rktYwslJbDTO.getSlsj() );
        rktYwslJbViewResp.setLczt( rktYwslJbDTO.getLczt() );
        rktYwslJbViewResp.setSpxxcjfs( rktYwslJbDTO.getSpxxcjfs() );
        rktYwslJbViewResp.setHlwsqlx( rktYwslJbDTO.getHlwsqlx() );
        rktYwslJbViewResp.setZwwdtmc( rktYwslJbDTO.getZwwdtmc() );
        rktYwslJbViewResp.setApplyfrom( rktYwslJbDTO.getApplyfrom() );
        rktYwslJbViewResp.setBustype( rktYwslJbDTO.getBustype() );
        rktYwslJbViewResp.setSftqhlwcl( rktYwslJbDTO.getSftqhlwcl() );
        rktYwslJbViewResp.setHjsxcxlb( rktYwslJbDTO.getHjsxcxlb() );
        rktYwslJbViewResp.setSfksxtqr( rktYwslJbDTO.getSfksxtqr() );
        rktYwslJbViewResp.setSfxyyjzj( rktYwslJbDTO.getSfxyyjzj() );
        rktYwslJbViewResp.setSfyjyjzj( rktYwslJbDTO.getSfyjyjzj() );
        rktYwslJbViewResp.setSjrxm( rktYwslJbDTO.getSjrxm() );
        rktYwslJbViewResp.setSjrlxdh( rktYwslJbDTO.getSjrlxdh() );
        rktYwslJbViewResp.setSqrtxdz( rktYwslJbDTO.getSqrtxdz() );
        rktYwslJbViewResp.setKddh( rktYwslJbDTO.getKddh() );
        rktYwslJbViewResp.setSpdwgajgjgdm( rktYwslJbDTO.getSpdwgajgjgdm() );
        rktYwslJbViewResp.setSpdwgajgmc( rktYwslJbDTO.getSpdwgajgmc() );
        rktYwslJbViewResp.setSprxm( rktYwslJbDTO.getSprxm() );
        rktYwslJbViewResp.setSpsj( rktYwslJbDTO.getSpsj() );
        rktYwslJbViewResp.setSprlxdh( rktYwslJbDTO.getSprlxdh() );
        rktYwslJbViewResp.setSpjgdm( rktYwslJbDTO.getSpjgdm() );
        rktYwslJbViewResp.setSpyj( rktYwslJbDTO.getSpyj() );
        rktYwslJbViewResp.setSlrxm( rktYwslJbDTO.getSlrxm() );
        rktYwslJbViewResp.setSlrlxdh( rktYwslJbDTO.getSlrlxdh() );
        rktYwslJbViewResp.setLcywbt( rktYwslJbDTO.getLcywbt() );
        rktYwslJbViewResp.setBlhjywid( rktYwslJbDTO.getBlhjywid() );
        rktYwslJbViewResp.setHjywblsj( rktYwslJbDTO.getHjywblsj() );
        rktYwslJbViewResp.setSfscdzqzhky( rktYwslJbDTO.getSfscdzqzhky() );
        rktYwslJbViewResp.setSfdyhkbsy( rktYwslJbDTO.getSfdyhkbsy() );
        rktYwslJbViewResp.setSfazjthfsdy( rktYwslJbDTO.getSfazjthfsdy() );
        rktYwslJbViewResp.setSfdyqhry( rktYwslJbDTO.getSfdyqhry() );
        rktYwslJbViewResp.setSfdycsyy( rktYwslJbDTO.getSfdycsyy() );
        rktYwslJbViewResp.setYsbxtywwjlx( rktYwslJbDTO.getYsbxtywwjlx() );
        rktYwslJbViewResp.setYsbxxbh( rktYwslJbDTO.getYsbxxbh() );
        rktYwslJbViewResp.setHzxm( rktYwslJbDTO.getHzxm() );
        rktYwslJbViewResp.setHzgmsfhm( rktYwslJbDTO.getHzgmsfhm() );
        rktYwslJbViewResp.setSqbsfbzqm( rktYwslJbDTO.getSqbsfbzqm() );
        rktYwslJbViewResp.setCbsfbzqm( rktYwslJbDTO.getCbsfbzqm() );
        rktYwslJbViewResp.setBz( rktYwslJbDTO.getBz() );
        rktYwslJbViewResp.setDababz( rktYwslJbDTO.getDababz() );
        rktYwslJbViewResp.setDabasj( rktYwslJbDTO.getDabasj() );
        rktYwslJbViewResp.setBgyjssbbz( rktYwslJbDTO.getBgyjssbbz() );
        rktYwslJbViewResp.setBgyjssbsj( rktYwslJbDTO.getBgyjssbsj() );
        rktYwslJbViewResp.setPjjg( rktYwslJbDTO.getPjjg() );
        rktYwslJbViewResp.setPjpljc( rktYwslJbDTO.getPjpljc() );
        rktYwslJbViewResp.setPjsj( rktYwslJbDTO.getPjsj() );
        rktYwslJbViewResp.setMjscshhs( rktYwslJbDTO.getMjscshhs() );
        rktYwslJbViewResp.setSbsfxy( rktYwslJbDTO.getSbsfxy() );
        rktYwslJbViewResp.setSbzt( rktYwslJbDTO.getSbzt() );
        rktYwslJbViewResp.setSbywlx( rktYwslJbDTO.getSbywlx() );
        rktYwslJbViewResp.setSbywmc( rktYwslJbDTO.getSbywmc() );
        rktYwslJbViewResp.setSbsbsj( rktYwslJbDTO.getSbsbsj() );
        rktYwslJbViewResp.setSbslsj( rktYwslJbDTO.getSbslsj() );
        rktYwslJbViewResp.setSbshsj( rktYwslJbDTO.getSbshsj() );
        rktYwslJbViewResp.setSbbjsj( rktYwslJbDTO.getSbbjsj() );
        rktYwslJbViewResp.setSbspkssj( rktYwslJbDTO.getSbspkssj() );
        rktYwslJbViewResp.setSbspjssj( rktYwslJbDTO.getSbspjssj() );
        rktYwslJbViewResp.setSbsprxm( rktYwslJbDTO.getSbsprxm() );
        rktYwslJbViewResp.setSbsprdwdm( rktYwslJbDTO.getSbsprdwdm() );
        rktYwslJbViewResp.setSbspjgdm( rktYwslJbDTO.getSbspjgdm() );
        rktYwslJbViewResp.setSbspyj( rktYwslJbDTO.getSbspyj() );
        rktYwslJbViewResp.setGjnzsbh( rktYwslJbDTO.getGjnzsbh() );
        rktYwslJbViewResp.setRsjshjg( rktYwslJbDTO.getRsjshjg() );
        rktYwslJbViewResp.setRsjshly( rktYwslJbDTO.getRsjshly() );
        rktYwslJbViewResp.setRsjshsj( rktYwslJbDTO.getRsjshsj() );
        rktYwslJbViewResp.setRsjshr( rktYwslJbDTO.getRsjshr() );

        return rktYwslJbViewResp;
    }
}
