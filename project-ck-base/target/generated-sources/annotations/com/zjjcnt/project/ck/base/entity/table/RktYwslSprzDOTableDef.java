package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class RktYwslSprzDOTableDef extends TableDef {

    /**
     * 业务受理审批日志DO

 <AUTHOR>
 @date 2025-07-21 09:14:48
 @see com.zjjcnt.project.ck.base.dto.RktYwslSprzDTO
     */
    public static final RktYwslSprzDOTableDef RKT_YWSL_SPRZ_DO = new RktYwslSprzDOTableDef();

    /**
     * 备注
     */
    public final QueryColumn BZ = new QueryColumn(this, "bz");

    public final QueryColumn BLZT = new QueryColumn(this, "blzt");

    /**
     * 操作IP
     */
    public final QueryColumn CZIP = new QueryColumn(this, "czip");

    public final QueryColumn CZSJ = new QueryColumn(this, "czsj");

    public final QueryColumn NBBH = new QueryColumn(this, "nbbh");

    /**
     * 操作员ID
     */
    public final QueryColumn CZYID = new QueryColumn(this, "czyid");

    /**
     * 操作员姓名
     */
    public final QueryColumn CZYXM = new QueryColumn(this, "czyxm");

    public final QueryColumn YWSLH = new QueryColumn(this, "ywslh");

    public final QueryColumn SPJGDM = new QueryColumn(this, "spjgdm");

    public final QueryColumn CZYDWDM = new QueryColumn(this, "czydwdm");

    public final QueryColumn CZYDWMC = new QueryColumn(this, "czydwmc");

    /**
     * 操作员联系电话
     */
    public final QueryColumn CZYLXDH = new QueryColumn(this, "czylxdh");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{BZ, BLZT, CZIP, CZSJ, NBBH, CZYID, CZYXM, YWSLH, SPJGDM, CZYDWDM, CZYDWMC, CZYLXDH};

    public RktYwslSprzDOTableDef() {
        super("", "rkt_ywsl_sprz");
    }

    private RktYwslSprzDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public RktYwslSprzDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new RktYwslSprzDOTableDef("", "rkt_ywsl_sprz", alias));
    }

}
