package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktHjslSzryxhhfDTO;
import com.zjjcnt.project.ck.base.dto.req.RktHjslSzryxhhfCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslSzryxhhfPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslSzryxhhfUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslSzryxhhfCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslSzryxhhfPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslSzryxhhfViewResp;
import com.zjjcnt.project.ck.base.entity.RktHjslSzryxhhfDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:43+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktHjslSzryxhhfConvertImpl implements RktHjslSzryxhhfConvert {

    @Override
    public RktHjslSzryxhhfDTO convert(RktHjslSzryxhhfDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktHjslSzryxhhfDTO rktHjslSzryxhhfDTO = new RktHjslSzryxhhfDTO();

        rktHjslSzryxhhfDTO.setId( entity.getId() );
        rktHjslSzryxhhfDTO.setYwslnbbh( entity.getYwslnbbh() );
        rktHjslSzryxhhfDTO.setHhnbid( entity.getHhnbid() );
        rktHjslSzryxhhfDTO.setYwslh( entity.getYwslh() );
        rktHjslSzryxhhfDTO.setHh( entity.getHh() );
        rktHjslSzryxhhfDTO.setHhid( entity.getHhid() );
        rktHjslSzryxhhfDTO.setHlx( entity.getHlx() );
        rktHjslSzryxhhfDTO.setHmc( entity.getHmc() );
        rktHjslSzryxhhfDTO.setHjddzbm( entity.getHjddzbm() );
        rktHjslSzryxhhfDTO.setHjdssxq( entity.getHjdssxq() );
        rktHjslSzryxhhfDTO.setHjdxxdz( entity.getHjdxxdz() );
        rktHjslSzryxhhfDTO.setHjdrhyzbs( entity.getHjdrhyzbs() );
        rktHjslSzryxhhfDTO.setJlx( entity.getJlx() );
        rktHjslSzryxhhfDTO.setMlph( entity.getMlph() );
        rktHjslSzryxhhfDTO.setMlxz( entity.getMlxz() );
        rktHjslSzryxhhfDTO.setLcywlx( entity.getLcywlx() );
        rktHjslSzryxhhfDTO.setLcdyid( entity.getLcdyid() );
        rktHjslSzryxhhfDTO.setLcmc( entity.getLcmc() );
        rktHjslSzryxhhfDTO.setLcslid( entity.getLcslid() );
        rktHjslSzryxhhfDTO.setLcywbt( entity.getLcywbt() );
        rktHjslSzryxhhfDTO.setLcrwjd( entity.getLcrwjd() );
        rktHjslSzryxhhfDTO.setLczt( entity.getLczt() );
        rktHjslSzryxhhfDTO.setBlzt( entity.getBlzt() );
        rktHjslSzryxhhfDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        rktHjslSzryxhhfDTO.setSqrxm( entity.getSqrxm() );
        rktHjslSzryxhhfDTO.setSqrxb( entity.getSqrxb() );
        rktHjslSzryxhhfDTO.setSqrlxdh( entity.getSqrlxdh() );
        rktHjslSzryxhhfDTO.setSqrybdrgx( entity.getSqrybdrgx() );
        rktHjslSzryxhhfDTO.setSqrzzssxq( entity.getSqrzzssxq() );
        rktHjslSzryxhhfDTO.setSqrzzxz( entity.getSqrzzxz() );
        rktHjslSzryxhhfDTO.setSqrhkdjjg( entity.getSqrhkdjjg() );
        rktHjslSzryxhhfDTO.setSqrq( entity.getSqrq() );
        rktHjslSzryxhhfDTO.setSpdwgajgjgdm( entity.getSpdwgajgjgdm() );
        rktHjslSzryxhhfDTO.setSpdwgajgmc( entity.getSpdwgajgmc() );
        rktHjslSzryxhhfDTO.setSprxm( entity.getSprxm() );
        rktHjslSzryxhhfDTO.setSpsj( entity.getSpsj() );
        rktHjslSzryxhhfDTO.setSpjgdm( entity.getSpjgdm() );
        rktHjslSzryxhhfDTO.setSpyj( entity.getSpyj() );
        rktHjslSzryxhhfDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        rktHjslSzryxhhfDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        rktHjslSzryxhhfDTO.setSlrxm( entity.getSlrxm() );
        rktHjslSzryxhhfDTO.setSlsj( entity.getSlsj() );
        rktHjslSzryxhhfDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        rktHjslSzryxhhfDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        rktHjslSzryxhhfDTO.setJcwh( entity.getJcwh() );
        rktHjslSzryxhhfDTO.setBz( entity.getBz() );
        rktHjslSzryxhhfDTO.setXh( entity.getXh() );
        rktHjslSzryxhhfDTO.setHkxz( entity.getHkxz() );
        rktHjslSzryxhhfDTO.setHb( entity.getHb() );
        rktHjslSzryxhhfDTO.setYhzgx( entity.getYhzgx() );
        rktHjslSzryxhhfDTO.setCxsx( entity.getCxsx() );
        rktHjslSzryxhhfDTO.setGmsfhm( entity.getGmsfhm() );
        rktHjslSzryxhhfDTO.setXm( entity.getXm() );
        rktHjslSzryxhhfDTO.setX( entity.getX() );
        rktHjslSzryxhhfDTO.setM( entity.getM() );
        rktHjslSzryxhhfDTO.setCym( entity.getCym() );
        rktHjslSzryxhhfDTO.setXmpy( entity.getXmpy() );
        rktHjslSzryxhhfDTO.setCympy( entity.getCympy() );
        rktHjslSzryxhhfDTO.setXb( entity.getXb() );
        rktHjslSzryxhhfDTO.setMz( entity.getMz() );
        rktHjslSzryxhhfDTO.setJggjdq( entity.getJggjdq() );
        rktHjslSzryxhhfDTO.setJgssxq( entity.getJgssxq() );
        rktHjslSzryxhhfDTO.setJgxz( entity.getJgxz() );
        rktHjslSzryxhhfDTO.setCsrq( entity.getCsrq() );
        rktHjslSzryxhhfDTO.setCssj( entity.getCssj() );
        rktHjslSzryxhhfDTO.setCsdgjdq( entity.getCsdgjdq() );
        rktHjslSzryxhhfDTO.setCsdssxq( entity.getCsdssxq() );
        rktHjslSzryxhhfDTO.setCsdxz( entity.getCsdxz() );
        rktHjslSzryxhhfDTO.setWhcd( entity.getWhcd() );
        rktHjslSzryxhhfDTO.setHyzk( entity.getHyzk() );
        rktHjslSzryxhhfDTO.setCyzkdwbm( entity.getCyzkdwbm() );
        rktHjslSzryxhhfDTO.setCyzkdwmc( entity.getCyzkdwmc() );
        rktHjslSzryxhhfDTO.setZy( entity.getZy() );
        rktHjslSzryxhhfDTO.setZylb( entity.getZylb() );
        rktHjslSzryxhhfDTO.setZjxy( entity.getZjxy() );
        rktHjslSzryxhhfDTO.setSg( entity.getSg() );
        rktHjslSzryxhhfDTO.setXx( entity.getXx() );
        rktHjslSzryxhhfDTO.setByzk( entity.getByzk() );
        rktHjslSzryxhhfDTO.setXxjb( entity.getXxjb() );
        rktHjslSzryxhhfDTO.setLxdh( entity.getLxdh() );
        rktHjslSzryxhhfDTO.setFqxm( entity.getFqxm() );
        rktHjslSzryxhhfDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        rktHjslSzryxhhfDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        rktHjslSzryxhhfDTO.setFqzjhm( entity.getFqzjhm() );
        rktHjslSzryxhhfDTO.setFqwwx( entity.getFqwwx() );
        rktHjslSzryxhhfDTO.setFqwwm( entity.getFqwwm() );
        rktHjslSzryxhhfDTO.setMqxm( entity.getMqxm() );
        rktHjslSzryxhhfDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        rktHjslSzryxhhfDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        rktHjslSzryxhhfDTO.setMqzjhm( entity.getMqzjhm() );
        rktHjslSzryxhhfDTO.setMqwwx( entity.getMqwwx() );
        rktHjslSzryxhhfDTO.setMqwwm( entity.getMqwwm() );
        rktHjslSzryxhhfDTO.setPoxm( entity.getPoxm() );
        rktHjslSzryxhhfDTO.setPogmsfhm( entity.getPogmsfhm() );
        rktHjslSzryxhhfDTO.setPocyzjdm( entity.getPocyzjdm() );
        rktHjslSzryxhhfDTO.setPozjhm( entity.getPozjhm() );
        rktHjslSzryxhhfDTO.setPowwx( entity.getPowwx() );
        rktHjslSzryxhhfDTO.setPowwm( entity.getPowwm() );
        rktHjslSzryxhhfDTO.setJhryxm( entity.getJhryxm() );
        rktHjslSzryxhhfDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        rktHjslSzryxhhfDTO.setJhryjhgx( entity.getJhryjhgx() );
        rktHjslSzryxhhfDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        rktHjslSzryxhhfDTO.setJhryzjhm( entity.getJhryzjhm() );
        rktHjslSzryxhhfDTO.setJhrywwx( entity.getJhrywwx() );
        rktHjslSzryxhhfDTO.setJhrywwm( entity.getJhrywwm() );
        rktHjslSzryxhhfDTO.setJhrylxdh( entity.getJhrylxdh() );
        rktHjslSzryxhhfDTO.setJhrexm( entity.getJhrexm() );
        rktHjslSzryxhhfDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        rktHjslSzryxhhfDTO.setJhrejhgx( entity.getJhrejhgx() );
        rktHjslSzryxhhfDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        rktHjslSzryxhhfDTO.setJhrezjhm( entity.getJhrezjhm() );
        rktHjslSzryxhhfDTO.setJhrewwx( entity.getJhrewwx() );
        rktHjslSzryxhhfDTO.setJhrewwm( entity.getJhrewwm() );
        rktHjslSzryxhhfDTO.setJhrelxdh( entity.getJhrelxdh() );
        rktHjslSzryxhhfDTO.setZjlb( entity.getZjlb() );
        rktHjslSzryxhhfDTO.setQfjg( entity.getQfjg() );
        rktHjslSzryxhhfDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        rktHjslSzryxhhfDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        rktHjslSzryxhhfDTO.setSzqhkszdssxqdm( entity.getSzqhkszdssxqdm() );
        rktHjslSzryxhhfDTO.setSzqhkszdqhnxxdz( entity.getSzqhkszdqhnxxdz() );
        rktHjslSzryxhhfDTO.setBdfw( entity.getBdfw() );
        rktHjslSzryxhhfDTO.setBlhjywid( entity.getBlhjywid() );
        rktHjslSzryxhhfDTO.setHjywblsj( entity.getHjywblsj() );
        rktHjslSzryxhhfDTO.setQtssxq( entity.getQtssxq() );
        rktHjslSzryxhhfDTO.setQtzz( entity.getQtzz() );

        return rktHjslSzryxhhfDTO;
    }

    @Override
    public RktHjslSzryxhhfDO convertToDO(RktHjslSzryxhhfDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslSzryxhhfDO rktHjslSzryxhhfDO = new RktHjslSzryxhhfDO();

        rktHjslSzryxhhfDO.setId( dto.getId() );
        rktHjslSzryxhhfDO.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslSzryxhhfDO.setHhnbid( dto.getHhnbid() );
        rktHjslSzryxhhfDO.setYwslh( dto.getYwslh() );
        rktHjslSzryxhhfDO.setHh( dto.getHh() );
        rktHjslSzryxhhfDO.setHhid( dto.getHhid() );
        rktHjslSzryxhhfDO.setHlx( dto.getHlx() );
        rktHjslSzryxhhfDO.setHmc( dto.getHmc() );
        rktHjslSzryxhhfDO.setHjddzbm( dto.getHjddzbm() );
        rktHjslSzryxhhfDO.setHjdssxq( dto.getHjdssxq() );
        rktHjslSzryxhhfDO.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslSzryxhhfDO.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslSzryxhhfDO.setJlx( dto.getJlx() );
        rktHjslSzryxhhfDO.setMlph( dto.getMlph() );
        rktHjslSzryxhhfDO.setMlxz( dto.getMlxz() );
        rktHjslSzryxhhfDO.setLcywlx( dto.getLcywlx() );
        rktHjslSzryxhhfDO.setLcdyid( dto.getLcdyid() );
        rktHjslSzryxhhfDO.setLcmc( dto.getLcmc() );
        rktHjslSzryxhhfDO.setLcslid( dto.getLcslid() );
        rktHjslSzryxhhfDO.setLcywbt( dto.getLcywbt() );
        rktHjslSzryxhhfDO.setLcrwjd( dto.getLcrwjd() );
        rktHjslSzryxhhfDO.setLczt( dto.getLczt() );
        rktHjslSzryxhhfDO.setBlzt( dto.getBlzt() );
        rktHjslSzryxhhfDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslSzryxhhfDO.setSqrxm( dto.getSqrxm() );
        rktHjslSzryxhhfDO.setSqrxb( dto.getSqrxb() );
        rktHjslSzryxhhfDO.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslSzryxhhfDO.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslSzryxhhfDO.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslSzryxhhfDO.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslSzryxhhfDO.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslSzryxhhfDO.setSqrq( dto.getSqrq() );
        rktHjslSzryxhhfDO.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslSzryxhhfDO.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslSzryxhhfDO.setSprxm( dto.getSprxm() );
        rktHjslSzryxhhfDO.setSpsj( dto.getSpsj() );
        rktHjslSzryxhhfDO.setSpjgdm( dto.getSpjgdm() );
        rktHjslSzryxhhfDO.setSpyj( dto.getSpyj() );
        rktHjslSzryxhhfDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslSzryxhhfDO.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslSzryxhhfDO.setSlrxm( dto.getSlrxm() );
        rktHjslSzryxhhfDO.setSlsj( dto.getSlsj() );
        rktHjslSzryxhhfDO.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslSzryxhhfDO.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslSzryxhhfDO.setJcwh( dto.getJcwh() );
        rktHjslSzryxhhfDO.setBz( dto.getBz() );
        rktHjslSzryxhhfDO.setXh( dto.getXh() );
        rktHjslSzryxhhfDO.setHkxz( dto.getHkxz() );
        rktHjslSzryxhhfDO.setHb( dto.getHb() );
        rktHjslSzryxhhfDO.setYhzgx( dto.getYhzgx() );
        rktHjslSzryxhhfDO.setCxsx( dto.getCxsx() );
        rktHjslSzryxhhfDO.setGmsfhm( dto.getGmsfhm() );
        rktHjslSzryxhhfDO.setXm( dto.getXm() );
        rktHjslSzryxhhfDO.setX( dto.getX() );
        rktHjslSzryxhhfDO.setM( dto.getM() );
        rktHjslSzryxhhfDO.setCym( dto.getCym() );
        rktHjslSzryxhhfDO.setXmpy( dto.getXmpy() );
        rktHjslSzryxhhfDO.setCympy( dto.getCympy() );
        rktHjslSzryxhhfDO.setXb( dto.getXb() );
        rktHjslSzryxhhfDO.setMz( dto.getMz() );
        rktHjslSzryxhhfDO.setJggjdq( dto.getJggjdq() );
        rktHjslSzryxhhfDO.setJgssxq( dto.getJgssxq() );
        rktHjslSzryxhhfDO.setJgxz( dto.getJgxz() );
        rktHjslSzryxhhfDO.setCsrq( dto.getCsrq() );
        rktHjslSzryxhhfDO.setCssj( dto.getCssj() );
        rktHjslSzryxhhfDO.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslSzryxhhfDO.setCsdssxq( dto.getCsdssxq() );
        rktHjslSzryxhhfDO.setCsdxz( dto.getCsdxz() );
        rktHjslSzryxhhfDO.setWhcd( dto.getWhcd() );
        rktHjslSzryxhhfDO.setHyzk( dto.getHyzk() );
        rktHjslSzryxhhfDO.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslSzryxhhfDO.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslSzryxhhfDO.setZy( dto.getZy() );
        rktHjslSzryxhhfDO.setZylb( dto.getZylb() );
        rktHjslSzryxhhfDO.setZjxy( dto.getZjxy() );
        rktHjslSzryxhhfDO.setSg( dto.getSg() );
        rktHjslSzryxhhfDO.setXx( dto.getXx() );
        rktHjslSzryxhhfDO.setByzk( dto.getByzk() );
        rktHjslSzryxhhfDO.setXxjb( dto.getXxjb() );
        rktHjslSzryxhhfDO.setLxdh( dto.getLxdh() );
        rktHjslSzryxhhfDO.setFqxm( dto.getFqxm() );
        rktHjslSzryxhhfDO.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslSzryxhhfDO.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslSzryxhhfDO.setFqzjhm( dto.getFqzjhm() );
        rktHjslSzryxhhfDO.setFqwwx( dto.getFqwwx() );
        rktHjslSzryxhhfDO.setFqwwm( dto.getFqwwm() );
        rktHjslSzryxhhfDO.setMqxm( dto.getMqxm() );
        rktHjslSzryxhhfDO.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslSzryxhhfDO.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslSzryxhhfDO.setMqzjhm( dto.getMqzjhm() );
        rktHjslSzryxhhfDO.setMqwwx( dto.getMqwwx() );
        rktHjslSzryxhhfDO.setMqwwm( dto.getMqwwm() );
        rktHjslSzryxhhfDO.setPoxm( dto.getPoxm() );
        rktHjslSzryxhhfDO.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslSzryxhhfDO.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslSzryxhhfDO.setPozjhm( dto.getPozjhm() );
        rktHjslSzryxhhfDO.setPowwx( dto.getPowwx() );
        rktHjslSzryxhhfDO.setPowwm( dto.getPowwm() );
        rktHjslSzryxhhfDO.setJhryxm( dto.getJhryxm() );
        rktHjslSzryxhhfDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslSzryxhhfDO.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslSzryxhhfDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslSzryxhhfDO.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslSzryxhhfDO.setJhrywwx( dto.getJhrywwx() );
        rktHjslSzryxhhfDO.setJhrywwm( dto.getJhrywwm() );
        rktHjslSzryxhhfDO.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslSzryxhhfDO.setJhrexm( dto.getJhrexm() );
        rktHjslSzryxhhfDO.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslSzryxhhfDO.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslSzryxhhfDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslSzryxhhfDO.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslSzryxhhfDO.setJhrewwx( dto.getJhrewwx() );
        rktHjslSzryxhhfDO.setJhrewwm( dto.getJhrewwm() );
        rktHjslSzryxhhfDO.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslSzryxhhfDO.setZjlb( dto.getZjlb() );
        rktHjslSzryxhhfDO.setQfjg( dto.getQfjg() );
        rktHjslSzryxhhfDO.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslSzryxhhfDO.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslSzryxhhfDO.setSzqhkszdssxqdm( dto.getSzqhkszdssxqdm() );
        rktHjslSzryxhhfDO.setSzqhkszdqhnxxdz( dto.getSzqhkszdqhnxxdz() );
        rktHjslSzryxhhfDO.setBdfw( dto.getBdfw() );
        rktHjslSzryxhhfDO.setBlhjywid( dto.getBlhjywid() );
        rktHjslSzryxhhfDO.setHjywblsj( dto.getHjywblsj() );
        rktHjslSzryxhhfDO.setQtssxq( dto.getQtssxq() );
        rktHjslSzryxhhfDO.setQtzz( dto.getQtzz() );

        return rktHjslSzryxhhfDO;
    }

    @Override
    public RktHjslSzryxhhfDTO convertToDTO(RktHjslSzryxhhfPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslSzryxhhfDTO rktHjslSzryxhhfDTO = new RktHjslSzryxhhfDTO();

        rktHjslSzryxhhfDTO.setHhnbid( req.getHhnbid() );
        rktHjslSzryxhhfDTO.setYwslh( req.getYwslh() );
        rktHjslSzryxhhfDTO.setHh( req.getHh() );
        rktHjslSzryxhhfDTO.setHhid( req.getHhid() );
        rktHjslSzryxhhfDTO.setHlx( req.getHlx() );
        rktHjslSzryxhhfDTO.setHmc( req.getHmc() );
        rktHjslSzryxhhfDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslSzryxhhfDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslSzryxhhfDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslSzryxhhfDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslSzryxhhfDTO.setJlx( req.getJlx() );
        rktHjslSzryxhhfDTO.setMlph( req.getMlph() );
        rktHjslSzryxhhfDTO.setMlxz( req.getMlxz() );
        rktHjslSzryxhhfDTO.setLcywlx( req.getLcywlx() );
        rktHjslSzryxhhfDTO.setLcdyid( req.getLcdyid() );
        rktHjslSzryxhhfDTO.setLcmc( req.getLcmc() );
        rktHjslSzryxhhfDTO.setLcslid( req.getLcslid() );
        rktHjslSzryxhhfDTO.setLcywbt( req.getLcywbt() );
        rktHjslSzryxhhfDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslSzryxhhfDTO.setLczt( req.getLczt() );
        rktHjslSzryxhhfDTO.setBlzt( req.getBlzt() );
        rktHjslSzryxhhfDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslSzryxhhfDTO.setSqrxm( req.getSqrxm() );
        rktHjslSzryxhhfDTO.setSqrxb( req.getSqrxb() );
        rktHjslSzryxhhfDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslSzryxhhfDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslSzryxhhfDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslSzryxhhfDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslSzryxhhfDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslSzryxhhfDTO.setSqrq( req.getSqrq() );
        rktHjslSzryxhhfDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslSzryxhhfDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslSzryxhhfDTO.setSprxm( req.getSprxm() );
        rktHjslSzryxhhfDTO.setSpsj( req.getSpsj() );
        rktHjslSzryxhhfDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslSzryxhhfDTO.setSpyj( req.getSpyj() );
        rktHjslSzryxhhfDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslSzryxhhfDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslSzryxhhfDTO.setSlrxm( req.getSlrxm() );
        rktHjslSzryxhhfDTO.setSlsj( req.getSlsj() );
        rktHjslSzryxhhfDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslSzryxhhfDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslSzryxhhfDTO.setJcwh( req.getJcwh() );
        rktHjslSzryxhhfDTO.setBz( req.getBz() );
        rktHjslSzryxhhfDTO.setXh( req.getXh() );
        rktHjslSzryxhhfDTO.setHkxz( req.getHkxz() );
        rktHjslSzryxhhfDTO.setHb( req.getHb() );
        rktHjslSzryxhhfDTO.setYhzgx( req.getYhzgx() );
        rktHjslSzryxhhfDTO.setCxsx( req.getCxsx() );
        rktHjslSzryxhhfDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslSzryxhhfDTO.setXm( req.getXm() );
        rktHjslSzryxhhfDTO.setX( req.getX() );
        rktHjslSzryxhhfDTO.setM( req.getM() );
        rktHjslSzryxhhfDTO.setCym( req.getCym() );
        rktHjslSzryxhhfDTO.setXmpy( req.getXmpy() );
        rktHjslSzryxhhfDTO.setCympy( req.getCympy() );
        rktHjslSzryxhhfDTO.setXb( req.getXb() );
        rktHjslSzryxhhfDTO.setMz( req.getMz() );
        rktHjslSzryxhhfDTO.setJggjdq( req.getJggjdq() );
        rktHjslSzryxhhfDTO.setJgssxq( req.getJgssxq() );
        rktHjslSzryxhhfDTO.setJgxz( req.getJgxz() );
        rktHjslSzryxhhfDTO.setCsrq( req.getCsrq() );
        rktHjslSzryxhhfDTO.setCssj( req.getCssj() );
        rktHjslSzryxhhfDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslSzryxhhfDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslSzryxhhfDTO.setCsdxz( req.getCsdxz() );
        rktHjslSzryxhhfDTO.setWhcd( req.getWhcd() );
        rktHjslSzryxhhfDTO.setHyzk( req.getHyzk() );
        rktHjslSzryxhhfDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslSzryxhhfDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslSzryxhhfDTO.setZy( req.getZy() );
        rktHjslSzryxhhfDTO.setZylb( req.getZylb() );
        rktHjslSzryxhhfDTO.setZjxy( req.getZjxy() );
        rktHjslSzryxhhfDTO.setSg( req.getSg() );
        rktHjslSzryxhhfDTO.setXx( req.getXx() );
        rktHjslSzryxhhfDTO.setByzk( req.getByzk() );
        rktHjslSzryxhhfDTO.setXxjb( req.getXxjb() );
        rktHjslSzryxhhfDTO.setLxdh( req.getLxdh() );
        rktHjslSzryxhhfDTO.setFqxm( req.getFqxm() );
        rktHjslSzryxhhfDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslSzryxhhfDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslSzryxhhfDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslSzryxhhfDTO.setFqwwx( req.getFqwwx() );
        rktHjslSzryxhhfDTO.setFqwwm( req.getFqwwm() );
        rktHjslSzryxhhfDTO.setMqxm( req.getMqxm() );
        rktHjslSzryxhhfDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslSzryxhhfDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslSzryxhhfDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslSzryxhhfDTO.setMqwwx( req.getMqwwx() );
        rktHjslSzryxhhfDTO.setMqwwm( req.getMqwwm() );
        rktHjslSzryxhhfDTO.setPoxm( req.getPoxm() );
        rktHjslSzryxhhfDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslSzryxhhfDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslSzryxhhfDTO.setPozjhm( req.getPozjhm() );
        rktHjslSzryxhhfDTO.setPowwx( req.getPowwx() );
        rktHjslSzryxhhfDTO.setPowwm( req.getPowwm() );
        rktHjslSzryxhhfDTO.setJhryxm( req.getJhryxm() );
        rktHjslSzryxhhfDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslSzryxhhfDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslSzryxhhfDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslSzryxhhfDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslSzryxhhfDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslSzryxhhfDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslSzryxhhfDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslSzryxhhfDTO.setJhrexm( req.getJhrexm() );
        rktHjslSzryxhhfDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslSzryxhhfDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslSzryxhhfDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslSzryxhhfDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslSzryxhhfDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslSzryxhhfDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslSzryxhhfDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslSzryxhhfDTO.setZjlb( req.getZjlb() );
        rktHjslSzryxhhfDTO.setQfjg( req.getQfjg() );
        rktHjslSzryxhhfDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslSzryxhhfDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslSzryxhhfDTO.setSzqhkszdssxqdm( req.getSzqhkszdssxqdm() );
        rktHjslSzryxhhfDTO.setSzqhkszdqhnxxdz( req.getSzqhkszdqhnxxdz() );
        rktHjslSzryxhhfDTO.setBdfw( req.getBdfw() );
        rktHjslSzryxhhfDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslSzryxhhfDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslSzryxhhfDTO.setQtssxq( req.getQtssxq() );
        rktHjslSzryxhhfDTO.setQtzz( req.getQtzz() );

        return rktHjslSzryxhhfDTO;
    }

    @Override
    public RktHjslSzryxhhfDTO convertToDTO(RktHjslSzryxhhfCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslSzryxhhfDTO rktHjslSzryxhhfDTO = new RktHjslSzryxhhfDTO();

        rktHjslSzryxhhfDTO.setHhnbid( req.getHhnbid() );
        rktHjslSzryxhhfDTO.setYwslh( req.getYwslh() );
        rktHjslSzryxhhfDTO.setHh( req.getHh() );
        rktHjslSzryxhhfDTO.setHhid( req.getHhid() );
        rktHjslSzryxhhfDTO.setHlx( req.getHlx() );
        rktHjslSzryxhhfDTO.setHmc( req.getHmc() );
        rktHjslSzryxhhfDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslSzryxhhfDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslSzryxhhfDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslSzryxhhfDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslSzryxhhfDTO.setJlx( req.getJlx() );
        rktHjslSzryxhhfDTO.setMlph( req.getMlph() );
        rktHjslSzryxhhfDTO.setMlxz( req.getMlxz() );
        rktHjslSzryxhhfDTO.setLcywlx( req.getLcywlx() );
        rktHjslSzryxhhfDTO.setLcdyid( req.getLcdyid() );
        rktHjslSzryxhhfDTO.setLcmc( req.getLcmc() );
        rktHjslSzryxhhfDTO.setLcslid( req.getLcslid() );
        rktHjslSzryxhhfDTO.setLcywbt( req.getLcywbt() );
        rktHjslSzryxhhfDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslSzryxhhfDTO.setLczt( req.getLczt() );
        rktHjslSzryxhhfDTO.setBlzt( req.getBlzt() );
        rktHjslSzryxhhfDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslSzryxhhfDTO.setSqrxm( req.getSqrxm() );
        rktHjslSzryxhhfDTO.setSqrxb( req.getSqrxb() );
        rktHjslSzryxhhfDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslSzryxhhfDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslSzryxhhfDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslSzryxhhfDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslSzryxhhfDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslSzryxhhfDTO.setSqrq( req.getSqrq() );
        rktHjslSzryxhhfDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslSzryxhhfDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslSzryxhhfDTO.setSprxm( req.getSprxm() );
        rktHjslSzryxhhfDTO.setSpsj( req.getSpsj() );
        rktHjslSzryxhhfDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslSzryxhhfDTO.setSpyj( req.getSpyj() );
        rktHjslSzryxhhfDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslSzryxhhfDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslSzryxhhfDTO.setSlrxm( req.getSlrxm() );
        rktHjslSzryxhhfDTO.setSlsj( req.getSlsj() );
        rktHjslSzryxhhfDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslSzryxhhfDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslSzryxhhfDTO.setJcwh( req.getJcwh() );
        rktHjslSzryxhhfDTO.setBz( req.getBz() );
        rktHjslSzryxhhfDTO.setXh( req.getXh() );
        rktHjslSzryxhhfDTO.setHkxz( req.getHkxz() );
        rktHjslSzryxhhfDTO.setHb( req.getHb() );
        rktHjslSzryxhhfDTO.setYhzgx( req.getYhzgx() );
        rktHjslSzryxhhfDTO.setCxsx( req.getCxsx() );
        rktHjslSzryxhhfDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslSzryxhhfDTO.setXm( req.getXm() );
        rktHjslSzryxhhfDTO.setX( req.getX() );
        rktHjslSzryxhhfDTO.setM( req.getM() );
        rktHjslSzryxhhfDTO.setCym( req.getCym() );
        rktHjslSzryxhhfDTO.setXmpy( req.getXmpy() );
        rktHjslSzryxhhfDTO.setCympy( req.getCympy() );
        rktHjslSzryxhhfDTO.setXb( req.getXb() );
        rktHjslSzryxhhfDTO.setMz( req.getMz() );
        rktHjslSzryxhhfDTO.setJggjdq( req.getJggjdq() );
        rktHjslSzryxhhfDTO.setJgssxq( req.getJgssxq() );
        rktHjslSzryxhhfDTO.setJgxz( req.getJgxz() );
        rktHjslSzryxhhfDTO.setCsrq( req.getCsrq() );
        rktHjslSzryxhhfDTO.setCssj( req.getCssj() );
        rktHjslSzryxhhfDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslSzryxhhfDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslSzryxhhfDTO.setCsdxz( req.getCsdxz() );
        rktHjslSzryxhhfDTO.setWhcd( req.getWhcd() );
        rktHjslSzryxhhfDTO.setHyzk( req.getHyzk() );
        rktHjslSzryxhhfDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslSzryxhhfDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslSzryxhhfDTO.setZy( req.getZy() );
        rktHjslSzryxhhfDTO.setZylb( req.getZylb() );
        rktHjslSzryxhhfDTO.setZjxy( req.getZjxy() );
        rktHjslSzryxhhfDTO.setSg( req.getSg() );
        rktHjslSzryxhhfDTO.setXx( req.getXx() );
        rktHjslSzryxhhfDTO.setByzk( req.getByzk() );
        rktHjslSzryxhhfDTO.setXxjb( req.getXxjb() );
        rktHjslSzryxhhfDTO.setLxdh( req.getLxdh() );
        rktHjslSzryxhhfDTO.setFqxm( req.getFqxm() );
        rktHjslSzryxhhfDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslSzryxhhfDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslSzryxhhfDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslSzryxhhfDTO.setFqwwx( req.getFqwwx() );
        rktHjslSzryxhhfDTO.setFqwwm( req.getFqwwm() );
        rktHjslSzryxhhfDTO.setMqxm( req.getMqxm() );
        rktHjslSzryxhhfDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslSzryxhhfDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslSzryxhhfDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslSzryxhhfDTO.setMqwwx( req.getMqwwx() );
        rktHjslSzryxhhfDTO.setMqwwm( req.getMqwwm() );
        rktHjslSzryxhhfDTO.setPoxm( req.getPoxm() );
        rktHjslSzryxhhfDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslSzryxhhfDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslSzryxhhfDTO.setPozjhm( req.getPozjhm() );
        rktHjslSzryxhhfDTO.setPowwx( req.getPowwx() );
        rktHjslSzryxhhfDTO.setPowwm( req.getPowwm() );
        rktHjslSzryxhhfDTO.setJhryxm( req.getJhryxm() );
        rktHjslSzryxhhfDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslSzryxhhfDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslSzryxhhfDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslSzryxhhfDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslSzryxhhfDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslSzryxhhfDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslSzryxhhfDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslSzryxhhfDTO.setJhrexm( req.getJhrexm() );
        rktHjslSzryxhhfDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslSzryxhhfDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslSzryxhhfDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslSzryxhhfDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslSzryxhhfDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslSzryxhhfDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslSzryxhhfDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslSzryxhhfDTO.setZjlb( req.getZjlb() );
        rktHjslSzryxhhfDTO.setQfjg( req.getQfjg() );
        rktHjslSzryxhhfDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslSzryxhhfDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslSzryxhhfDTO.setSzqhkszdssxqdm( req.getSzqhkszdssxqdm() );
        rktHjslSzryxhhfDTO.setSzqhkszdqhnxxdz( req.getSzqhkszdqhnxxdz() );
        rktHjslSzryxhhfDTO.setBdfw( req.getBdfw() );
        rktHjslSzryxhhfDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslSzryxhhfDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslSzryxhhfDTO.setQtssxq( req.getQtssxq() );
        rktHjslSzryxhhfDTO.setQtzz( req.getQtzz() );

        return rktHjslSzryxhhfDTO;
    }

    @Override
    public RktHjslSzryxhhfDTO convertToDTO(RktHjslSzryxhhfUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslSzryxhhfDTO rktHjslSzryxhhfDTO = new RktHjslSzryxhhfDTO();

        rktHjslSzryxhhfDTO.setYwslnbbh( req.getYwslnbbh() );
        rktHjslSzryxhhfDTO.setHhnbid( req.getHhnbid() );
        rktHjslSzryxhhfDTO.setYwslh( req.getYwslh() );
        rktHjslSzryxhhfDTO.setHh( req.getHh() );
        rktHjslSzryxhhfDTO.setHhid( req.getHhid() );
        rktHjslSzryxhhfDTO.setHlx( req.getHlx() );
        rktHjslSzryxhhfDTO.setHmc( req.getHmc() );
        rktHjslSzryxhhfDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslSzryxhhfDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslSzryxhhfDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslSzryxhhfDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslSzryxhhfDTO.setJlx( req.getJlx() );
        rktHjslSzryxhhfDTO.setMlph( req.getMlph() );
        rktHjslSzryxhhfDTO.setMlxz( req.getMlxz() );
        rktHjslSzryxhhfDTO.setLcywlx( req.getLcywlx() );
        rktHjslSzryxhhfDTO.setLcdyid( req.getLcdyid() );
        rktHjslSzryxhhfDTO.setLcmc( req.getLcmc() );
        rktHjslSzryxhhfDTO.setLcslid( req.getLcslid() );
        rktHjslSzryxhhfDTO.setLcywbt( req.getLcywbt() );
        rktHjslSzryxhhfDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslSzryxhhfDTO.setLczt( req.getLczt() );
        rktHjslSzryxhhfDTO.setBlzt( req.getBlzt() );
        rktHjslSzryxhhfDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslSzryxhhfDTO.setSqrxm( req.getSqrxm() );
        rktHjslSzryxhhfDTO.setSqrxb( req.getSqrxb() );
        rktHjslSzryxhhfDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslSzryxhhfDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslSzryxhhfDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslSzryxhhfDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslSzryxhhfDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslSzryxhhfDTO.setSqrq( req.getSqrq() );
        rktHjslSzryxhhfDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslSzryxhhfDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslSzryxhhfDTO.setSprxm( req.getSprxm() );
        rktHjslSzryxhhfDTO.setSpsj( req.getSpsj() );
        rktHjslSzryxhhfDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslSzryxhhfDTO.setSpyj( req.getSpyj() );
        rktHjslSzryxhhfDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslSzryxhhfDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslSzryxhhfDTO.setSlrxm( req.getSlrxm() );
        rktHjslSzryxhhfDTO.setSlsj( req.getSlsj() );
        rktHjslSzryxhhfDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslSzryxhhfDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslSzryxhhfDTO.setJcwh( req.getJcwh() );
        rktHjslSzryxhhfDTO.setBz( req.getBz() );
        rktHjslSzryxhhfDTO.setXh( req.getXh() );
        rktHjslSzryxhhfDTO.setHkxz( req.getHkxz() );
        rktHjslSzryxhhfDTO.setHb( req.getHb() );
        rktHjslSzryxhhfDTO.setYhzgx( req.getYhzgx() );
        rktHjslSzryxhhfDTO.setCxsx( req.getCxsx() );
        rktHjslSzryxhhfDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslSzryxhhfDTO.setXm( req.getXm() );
        rktHjslSzryxhhfDTO.setX( req.getX() );
        rktHjslSzryxhhfDTO.setM( req.getM() );
        rktHjslSzryxhhfDTO.setCym( req.getCym() );
        rktHjslSzryxhhfDTO.setXmpy( req.getXmpy() );
        rktHjslSzryxhhfDTO.setCympy( req.getCympy() );
        rktHjslSzryxhhfDTO.setXb( req.getXb() );
        rktHjslSzryxhhfDTO.setMz( req.getMz() );
        rktHjslSzryxhhfDTO.setJggjdq( req.getJggjdq() );
        rktHjslSzryxhhfDTO.setJgssxq( req.getJgssxq() );
        rktHjslSzryxhhfDTO.setJgxz( req.getJgxz() );
        rktHjslSzryxhhfDTO.setCsrq( req.getCsrq() );
        rktHjslSzryxhhfDTO.setCssj( req.getCssj() );
        rktHjslSzryxhhfDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslSzryxhhfDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslSzryxhhfDTO.setCsdxz( req.getCsdxz() );
        rktHjslSzryxhhfDTO.setWhcd( req.getWhcd() );
        rktHjslSzryxhhfDTO.setHyzk( req.getHyzk() );
        rktHjslSzryxhhfDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslSzryxhhfDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslSzryxhhfDTO.setZy( req.getZy() );
        rktHjslSzryxhhfDTO.setZylb( req.getZylb() );
        rktHjslSzryxhhfDTO.setZjxy( req.getZjxy() );
        rktHjslSzryxhhfDTO.setSg( req.getSg() );
        rktHjslSzryxhhfDTO.setXx( req.getXx() );
        rktHjslSzryxhhfDTO.setByzk( req.getByzk() );
        rktHjslSzryxhhfDTO.setXxjb( req.getXxjb() );
        rktHjslSzryxhhfDTO.setLxdh( req.getLxdh() );
        rktHjslSzryxhhfDTO.setFqxm( req.getFqxm() );
        rktHjslSzryxhhfDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslSzryxhhfDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslSzryxhhfDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslSzryxhhfDTO.setFqwwx( req.getFqwwx() );
        rktHjslSzryxhhfDTO.setFqwwm( req.getFqwwm() );
        rktHjslSzryxhhfDTO.setMqxm( req.getMqxm() );
        rktHjslSzryxhhfDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslSzryxhhfDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslSzryxhhfDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslSzryxhhfDTO.setMqwwx( req.getMqwwx() );
        rktHjslSzryxhhfDTO.setMqwwm( req.getMqwwm() );
        rktHjslSzryxhhfDTO.setPoxm( req.getPoxm() );
        rktHjslSzryxhhfDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslSzryxhhfDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslSzryxhhfDTO.setPozjhm( req.getPozjhm() );
        rktHjslSzryxhhfDTO.setPowwx( req.getPowwx() );
        rktHjslSzryxhhfDTO.setPowwm( req.getPowwm() );
        rktHjslSzryxhhfDTO.setJhryxm( req.getJhryxm() );
        rktHjslSzryxhhfDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslSzryxhhfDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslSzryxhhfDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslSzryxhhfDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslSzryxhhfDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslSzryxhhfDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslSzryxhhfDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslSzryxhhfDTO.setJhrexm( req.getJhrexm() );
        rktHjslSzryxhhfDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslSzryxhhfDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslSzryxhhfDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslSzryxhhfDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslSzryxhhfDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslSzryxhhfDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslSzryxhhfDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslSzryxhhfDTO.setZjlb( req.getZjlb() );
        rktHjslSzryxhhfDTO.setQfjg( req.getQfjg() );
        rktHjslSzryxhhfDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslSzryxhhfDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslSzryxhhfDTO.setSzqhkszdssxqdm( req.getSzqhkszdssxqdm() );
        rktHjslSzryxhhfDTO.setSzqhkszdqhnxxdz( req.getSzqhkszdqhnxxdz() );
        rktHjslSzryxhhfDTO.setBdfw( req.getBdfw() );
        rktHjslSzryxhhfDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslSzryxhhfDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslSzryxhhfDTO.setQtssxq( req.getQtssxq() );
        rktHjslSzryxhhfDTO.setQtzz( req.getQtzz() );

        return rktHjslSzryxhhfDTO;
    }

    @Override
    public RktHjslSzryxhhfPageResp convertToPageResp(RktHjslSzryxhhfDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslSzryxhhfPageResp rktHjslSzryxhhfPageResp = new RktHjslSzryxhhfPageResp();

        rktHjslSzryxhhfPageResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslSzryxhhfPageResp.setHhnbid( dto.getHhnbid() );
        rktHjslSzryxhhfPageResp.setYwslh( dto.getYwslh() );
        rktHjslSzryxhhfPageResp.setHh( dto.getHh() );
        rktHjslSzryxhhfPageResp.setHhid( dto.getHhid() );
        rktHjslSzryxhhfPageResp.setHlx( dto.getHlx() );
        rktHjslSzryxhhfPageResp.setHmc( dto.getHmc() );
        rktHjslSzryxhhfPageResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslSzryxhhfPageResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslSzryxhhfPageResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslSzryxhhfPageResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslSzryxhhfPageResp.setJlx( dto.getJlx() );
        rktHjslSzryxhhfPageResp.setMlph( dto.getMlph() );
        rktHjslSzryxhhfPageResp.setMlxz( dto.getMlxz() );
        rktHjslSzryxhhfPageResp.setLcywlx( dto.getLcywlx() );
        rktHjslSzryxhhfPageResp.setLcdyid( dto.getLcdyid() );
        rktHjslSzryxhhfPageResp.setLcmc( dto.getLcmc() );
        rktHjslSzryxhhfPageResp.setLcslid( dto.getLcslid() );
        rktHjslSzryxhhfPageResp.setLcywbt( dto.getLcywbt() );
        rktHjslSzryxhhfPageResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslSzryxhhfPageResp.setLczt( dto.getLczt() );
        rktHjslSzryxhhfPageResp.setBlzt( dto.getBlzt() );
        rktHjslSzryxhhfPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslSzryxhhfPageResp.setSqrxm( dto.getSqrxm() );
        rktHjslSzryxhhfPageResp.setSqrxb( dto.getSqrxb() );
        rktHjslSzryxhhfPageResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslSzryxhhfPageResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslSzryxhhfPageResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslSzryxhhfPageResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslSzryxhhfPageResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslSzryxhhfPageResp.setSqrq( dto.getSqrq() );
        rktHjslSzryxhhfPageResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslSzryxhhfPageResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslSzryxhhfPageResp.setSprxm( dto.getSprxm() );
        rktHjslSzryxhhfPageResp.setSpsj( dto.getSpsj() );
        rktHjslSzryxhhfPageResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslSzryxhhfPageResp.setSpyj( dto.getSpyj() );
        rktHjslSzryxhhfPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslSzryxhhfPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslSzryxhhfPageResp.setSlrxm( dto.getSlrxm() );
        rktHjslSzryxhhfPageResp.setSlsj( dto.getSlsj() );
        rktHjslSzryxhhfPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslSzryxhhfPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslSzryxhhfPageResp.setJcwh( dto.getJcwh() );
        rktHjslSzryxhhfPageResp.setBz( dto.getBz() );
        rktHjslSzryxhhfPageResp.setXh( dto.getXh() );
        rktHjslSzryxhhfPageResp.setHkxz( dto.getHkxz() );
        rktHjslSzryxhhfPageResp.setHb( dto.getHb() );
        rktHjslSzryxhhfPageResp.setYhzgx( dto.getYhzgx() );
        rktHjslSzryxhhfPageResp.setCxsx( dto.getCxsx() );
        rktHjslSzryxhhfPageResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslSzryxhhfPageResp.setXm( dto.getXm() );
        rktHjslSzryxhhfPageResp.setX( dto.getX() );
        rktHjslSzryxhhfPageResp.setM( dto.getM() );
        rktHjslSzryxhhfPageResp.setCym( dto.getCym() );
        rktHjslSzryxhhfPageResp.setXmpy( dto.getXmpy() );
        rktHjslSzryxhhfPageResp.setCympy( dto.getCympy() );
        rktHjslSzryxhhfPageResp.setXb( dto.getXb() );
        rktHjslSzryxhhfPageResp.setMz( dto.getMz() );
        rktHjslSzryxhhfPageResp.setJggjdq( dto.getJggjdq() );
        rktHjslSzryxhhfPageResp.setJgssxq( dto.getJgssxq() );
        rktHjslSzryxhhfPageResp.setJgxz( dto.getJgxz() );
        rktHjslSzryxhhfPageResp.setCsrq( dto.getCsrq() );
        rktHjslSzryxhhfPageResp.setCssj( dto.getCssj() );
        rktHjslSzryxhhfPageResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslSzryxhhfPageResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslSzryxhhfPageResp.setCsdxz( dto.getCsdxz() );
        rktHjslSzryxhhfPageResp.setWhcd( dto.getWhcd() );
        rktHjslSzryxhhfPageResp.setHyzk( dto.getHyzk() );
        rktHjslSzryxhhfPageResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslSzryxhhfPageResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslSzryxhhfPageResp.setZy( dto.getZy() );
        rktHjslSzryxhhfPageResp.setZylb( dto.getZylb() );
        rktHjslSzryxhhfPageResp.setZjxy( dto.getZjxy() );
        rktHjslSzryxhhfPageResp.setSg( dto.getSg() );
        rktHjslSzryxhhfPageResp.setXx( dto.getXx() );
        rktHjslSzryxhhfPageResp.setByzk( dto.getByzk() );
        rktHjslSzryxhhfPageResp.setXxjb( dto.getXxjb() );
        rktHjslSzryxhhfPageResp.setLxdh( dto.getLxdh() );
        rktHjslSzryxhhfPageResp.setFqxm( dto.getFqxm() );
        rktHjslSzryxhhfPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslSzryxhhfPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslSzryxhhfPageResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslSzryxhhfPageResp.setFqwwx( dto.getFqwwx() );
        rktHjslSzryxhhfPageResp.setFqwwm( dto.getFqwwm() );
        rktHjslSzryxhhfPageResp.setMqxm( dto.getMqxm() );
        rktHjslSzryxhhfPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslSzryxhhfPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslSzryxhhfPageResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslSzryxhhfPageResp.setMqwwx( dto.getMqwwx() );
        rktHjslSzryxhhfPageResp.setMqwwm( dto.getMqwwm() );
        rktHjslSzryxhhfPageResp.setPoxm( dto.getPoxm() );
        rktHjslSzryxhhfPageResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslSzryxhhfPageResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslSzryxhhfPageResp.setPozjhm( dto.getPozjhm() );
        rktHjslSzryxhhfPageResp.setPowwx( dto.getPowwx() );
        rktHjslSzryxhhfPageResp.setPowwm( dto.getPowwm() );
        rktHjslSzryxhhfPageResp.setJhryxm( dto.getJhryxm() );
        rktHjslSzryxhhfPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslSzryxhhfPageResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslSzryxhhfPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslSzryxhhfPageResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslSzryxhhfPageResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslSzryxhhfPageResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslSzryxhhfPageResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslSzryxhhfPageResp.setJhrexm( dto.getJhrexm() );
        rktHjslSzryxhhfPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslSzryxhhfPageResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslSzryxhhfPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslSzryxhhfPageResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslSzryxhhfPageResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslSzryxhhfPageResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslSzryxhhfPageResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslSzryxhhfPageResp.setZjlb( dto.getZjlb() );
        rktHjslSzryxhhfPageResp.setQfjg( dto.getQfjg() );
        rktHjslSzryxhhfPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslSzryxhhfPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslSzryxhhfPageResp.setSzqhkszdssxqdm( dto.getSzqhkszdssxqdm() );
        rktHjslSzryxhhfPageResp.setSzqhkszdqhnxxdz( dto.getSzqhkszdqhnxxdz() );
        rktHjslSzryxhhfPageResp.setBdfw( dto.getBdfw() );
        rktHjslSzryxhhfPageResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslSzryxhhfPageResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslSzryxhhfPageResp.setQtssxq( dto.getQtssxq() );
        rktHjslSzryxhhfPageResp.setQtzz( dto.getQtzz() );

        return rktHjslSzryxhhfPageResp;
    }

    @Override
    public RktHjslSzryxhhfViewResp convertToViewResp(RktHjslSzryxhhfDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslSzryxhhfViewResp rktHjslSzryxhhfViewResp = new RktHjslSzryxhhfViewResp();

        rktHjslSzryxhhfViewResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslSzryxhhfViewResp.setHhnbid( dto.getHhnbid() );
        rktHjslSzryxhhfViewResp.setYwslh( dto.getYwslh() );
        rktHjslSzryxhhfViewResp.setHh( dto.getHh() );
        rktHjslSzryxhhfViewResp.setHhid( dto.getHhid() );
        rktHjslSzryxhhfViewResp.setHlx( dto.getHlx() );
        rktHjslSzryxhhfViewResp.setHmc( dto.getHmc() );
        rktHjslSzryxhhfViewResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslSzryxhhfViewResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslSzryxhhfViewResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslSzryxhhfViewResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslSzryxhhfViewResp.setJlx( dto.getJlx() );
        rktHjslSzryxhhfViewResp.setMlph( dto.getMlph() );
        rktHjslSzryxhhfViewResp.setMlxz( dto.getMlxz() );
        rktHjslSzryxhhfViewResp.setLcywlx( dto.getLcywlx() );
        rktHjslSzryxhhfViewResp.setLcdyid( dto.getLcdyid() );
        rktHjslSzryxhhfViewResp.setLcmc( dto.getLcmc() );
        rktHjslSzryxhhfViewResp.setLcslid( dto.getLcslid() );
        rktHjslSzryxhhfViewResp.setLcywbt( dto.getLcywbt() );
        rktHjslSzryxhhfViewResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslSzryxhhfViewResp.setLczt( dto.getLczt() );
        rktHjslSzryxhhfViewResp.setBlzt( dto.getBlzt() );
        rktHjslSzryxhhfViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslSzryxhhfViewResp.setSqrxm( dto.getSqrxm() );
        rktHjslSzryxhhfViewResp.setSqrxb( dto.getSqrxb() );
        rktHjslSzryxhhfViewResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslSzryxhhfViewResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslSzryxhhfViewResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslSzryxhhfViewResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslSzryxhhfViewResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslSzryxhhfViewResp.setSqrq( dto.getSqrq() );
        rktHjslSzryxhhfViewResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslSzryxhhfViewResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslSzryxhhfViewResp.setSprxm( dto.getSprxm() );
        rktHjslSzryxhhfViewResp.setSpsj( dto.getSpsj() );
        rktHjslSzryxhhfViewResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslSzryxhhfViewResp.setSpyj( dto.getSpyj() );
        rktHjslSzryxhhfViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslSzryxhhfViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslSzryxhhfViewResp.setSlrxm( dto.getSlrxm() );
        rktHjslSzryxhhfViewResp.setSlsj( dto.getSlsj() );
        rktHjslSzryxhhfViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslSzryxhhfViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslSzryxhhfViewResp.setJcwh( dto.getJcwh() );
        rktHjslSzryxhhfViewResp.setBz( dto.getBz() );
        rktHjslSzryxhhfViewResp.setXh( dto.getXh() );
        rktHjslSzryxhhfViewResp.setHkxz( dto.getHkxz() );
        rktHjslSzryxhhfViewResp.setHb( dto.getHb() );
        rktHjslSzryxhhfViewResp.setYhzgx( dto.getYhzgx() );
        rktHjslSzryxhhfViewResp.setCxsx( dto.getCxsx() );
        rktHjslSzryxhhfViewResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslSzryxhhfViewResp.setXm( dto.getXm() );
        rktHjslSzryxhhfViewResp.setX( dto.getX() );
        rktHjslSzryxhhfViewResp.setM( dto.getM() );
        rktHjslSzryxhhfViewResp.setCym( dto.getCym() );
        rktHjslSzryxhhfViewResp.setXmpy( dto.getXmpy() );
        rktHjslSzryxhhfViewResp.setCympy( dto.getCympy() );
        rktHjslSzryxhhfViewResp.setXb( dto.getXb() );
        rktHjslSzryxhhfViewResp.setMz( dto.getMz() );
        rktHjslSzryxhhfViewResp.setJggjdq( dto.getJggjdq() );
        rktHjslSzryxhhfViewResp.setJgssxq( dto.getJgssxq() );
        rktHjslSzryxhhfViewResp.setJgxz( dto.getJgxz() );
        rktHjslSzryxhhfViewResp.setCsrq( dto.getCsrq() );
        rktHjslSzryxhhfViewResp.setCssj( dto.getCssj() );
        rktHjslSzryxhhfViewResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslSzryxhhfViewResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslSzryxhhfViewResp.setCsdxz( dto.getCsdxz() );
        rktHjslSzryxhhfViewResp.setWhcd( dto.getWhcd() );
        rktHjslSzryxhhfViewResp.setHyzk( dto.getHyzk() );
        rktHjslSzryxhhfViewResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslSzryxhhfViewResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslSzryxhhfViewResp.setZy( dto.getZy() );
        rktHjslSzryxhhfViewResp.setZylb( dto.getZylb() );
        rktHjslSzryxhhfViewResp.setZjxy( dto.getZjxy() );
        rktHjslSzryxhhfViewResp.setSg( dto.getSg() );
        rktHjslSzryxhhfViewResp.setXx( dto.getXx() );
        rktHjslSzryxhhfViewResp.setByzk( dto.getByzk() );
        rktHjslSzryxhhfViewResp.setXxjb( dto.getXxjb() );
        rktHjslSzryxhhfViewResp.setLxdh( dto.getLxdh() );
        rktHjslSzryxhhfViewResp.setFqxm( dto.getFqxm() );
        rktHjslSzryxhhfViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslSzryxhhfViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslSzryxhhfViewResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslSzryxhhfViewResp.setFqwwx( dto.getFqwwx() );
        rktHjslSzryxhhfViewResp.setFqwwm( dto.getFqwwm() );
        rktHjslSzryxhhfViewResp.setMqxm( dto.getMqxm() );
        rktHjslSzryxhhfViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslSzryxhhfViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslSzryxhhfViewResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslSzryxhhfViewResp.setMqwwx( dto.getMqwwx() );
        rktHjslSzryxhhfViewResp.setMqwwm( dto.getMqwwm() );
        rktHjslSzryxhhfViewResp.setPoxm( dto.getPoxm() );
        rktHjslSzryxhhfViewResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslSzryxhhfViewResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslSzryxhhfViewResp.setPozjhm( dto.getPozjhm() );
        rktHjslSzryxhhfViewResp.setPowwx( dto.getPowwx() );
        rktHjslSzryxhhfViewResp.setPowwm( dto.getPowwm() );
        rktHjslSzryxhhfViewResp.setJhryxm( dto.getJhryxm() );
        rktHjslSzryxhhfViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslSzryxhhfViewResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslSzryxhhfViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslSzryxhhfViewResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslSzryxhhfViewResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslSzryxhhfViewResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslSzryxhhfViewResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslSzryxhhfViewResp.setJhrexm( dto.getJhrexm() );
        rktHjslSzryxhhfViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslSzryxhhfViewResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslSzryxhhfViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslSzryxhhfViewResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslSzryxhhfViewResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslSzryxhhfViewResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslSzryxhhfViewResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslSzryxhhfViewResp.setZjlb( dto.getZjlb() );
        rktHjslSzryxhhfViewResp.setQfjg( dto.getQfjg() );
        rktHjslSzryxhhfViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslSzryxhhfViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslSzryxhhfViewResp.setSzqhkszdssxqdm( dto.getSzqhkszdssxqdm() );
        rktHjslSzryxhhfViewResp.setSzqhkszdqhnxxdz( dto.getSzqhkszdqhnxxdz() );
        rktHjslSzryxhhfViewResp.setBdfw( dto.getBdfw() );
        rktHjslSzryxhhfViewResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslSzryxhhfViewResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslSzryxhhfViewResp.setQtssxq( dto.getQtssxq() );
        rktHjslSzryxhhfViewResp.setQtzz( dto.getQtzz() );

        return rktHjslSzryxhhfViewResp;
    }

    @Override
    public RktHjslSzryxhhfCreateResp convertToCreateResp(RktHjslSzryxhhfDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslSzryxhhfCreateResp rktHjslSzryxhhfCreateResp = new RktHjslSzryxhhfCreateResp();

        rktHjslSzryxhhfCreateResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslSzryxhhfCreateResp.setHhnbid( dto.getHhnbid() );
        rktHjslSzryxhhfCreateResp.setYwslh( dto.getYwslh() );
        rktHjslSzryxhhfCreateResp.setHh( dto.getHh() );
        rktHjslSzryxhhfCreateResp.setHhid( dto.getHhid() );
        rktHjslSzryxhhfCreateResp.setHlx( dto.getHlx() );
        rktHjslSzryxhhfCreateResp.setHmc( dto.getHmc() );
        rktHjslSzryxhhfCreateResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslSzryxhhfCreateResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslSzryxhhfCreateResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslSzryxhhfCreateResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslSzryxhhfCreateResp.setJlx( dto.getJlx() );
        rktHjslSzryxhhfCreateResp.setMlph( dto.getMlph() );
        rktHjslSzryxhhfCreateResp.setMlxz( dto.getMlxz() );
        rktHjslSzryxhhfCreateResp.setLcywlx( dto.getLcywlx() );
        rktHjslSzryxhhfCreateResp.setLcdyid( dto.getLcdyid() );
        rktHjslSzryxhhfCreateResp.setLcmc( dto.getLcmc() );
        rktHjslSzryxhhfCreateResp.setLcslid( dto.getLcslid() );
        rktHjslSzryxhhfCreateResp.setLcywbt( dto.getLcywbt() );
        rktHjslSzryxhhfCreateResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslSzryxhhfCreateResp.setLczt( dto.getLczt() );
        rktHjslSzryxhhfCreateResp.setBlzt( dto.getBlzt() );
        rktHjslSzryxhhfCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslSzryxhhfCreateResp.setSqrxm( dto.getSqrxm() );
        rktHjslSzryxhhfCreateResp.setSqrxb( dto.getSqrxb() );
        rktHjslSzryxhhfCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslSzryxhhfCreateResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslSzryxhhfCreateResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslSzryxhhfCreateResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslSzryxhhfCreateResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslSzryxhhfCreateResp.setSqrq( dto.getSqrq() );
        rktHjslSzryxhhfCreateResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslSzryxhhfCreateResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslSzryxhhfCreateResp.setSprxm( dto.getSprxm() );
        rktHjslSzryxhhfCreateResp.setSpsj( dto.getSpsj() );
        rktHjslSzryxhhfCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslSzryxhhfCreateResp.setSpyj( dto.getSpyj() );
        rktHjslSzryxhhfCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslSzryxhhfCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslSzryxhhfCreateResp.setSlrxm( dto.getSlrxm() );
        rktHjslSzryxhhfCreateResp.setSlsj( dto.getSlsj() );
        rktHjslSzryxhhfCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslSzryxhhfCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslSzryxhhfCreateResp.setJcwh( dto.getJcwh() );
        rktHjslSzryxhhfCreateResp.setBz( dto.getBz() );
        rktHjslSzryxhhfCreateResp.setXh( dto.getXh() );
        rktHjslSzryxhhfCreateResp.setHkxz( dto.getHkxz() );
        rktHjslSzryxhhfCreateResp.setHb( dto.getHb() );
        rktHjslSzryxhhfCreateResp.setYhzgx( dto.getYhzgx() );
        rktHjslSzryxhhfCreateResp.setCxsx( dto.getCxsx() );
        rktHjslSzryxhhfCreateResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslSzryxhhfCreateResp.setXm( dto.getXm() );
        rktHjslSzryxhhfCreateResp.setX( dto.getX() );
        rktHjslSzryxhhfCreateResp.setM( dto.getM() );
        rktHjslSzryxhhfCreateResp.setCym( dto.getCym() );
        rktHjslSzryxhhfCreateResp.setXmpy( dto.getXmpy() );
        rktHjslSzryxhhfCreateResp.setCympy( dto.getCympy() );
        rktHjslSzryxhhfCreateResp.setXb( dto.getXb() );
        rktHjslSzryxhhfCreateResp.setMz( dto.getMz() );
        rktHjslSzryxhhfCreateResp.setJggjdq( dto.getJggjdq() );
        rktHjslSzryxhhfCreateResp.setJgssxq( dto.getJgssxq() );
        rktHjslSzryxhhfCreateResp.setJgxz( dto.getJgxz() );
        rktHjslSzryxhhfCreateResp.setCsrq( dto.getCsrq() );
        rktHjslSzryxhhfCreateResp.setCssj( dto.getCssj() );
        rktHjslSzryxhhfCreateResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslSzryxhhfCreateResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslSzryxhhfCreateResp.setCsdxz( dto.getCsdxz() );
        rktHjslSzryxhhfCreateResp.setWhcd( dto.getWhcd() );
        rktHjslSzryxhhfCreateResp.setHyzk( dto.getHyzk() );
        rktHjslSzryxhhfCreateResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslSzryxhhfCreateResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslSzryxhhfCreateResp.setZy( dto.getZy() );
        rktHjslSzryxhhfCreateResp.setZylb( dto.getZylb() );
        rktHjslSzryxhhfCreateResp.setZjxy( dto.getZjxy() );
        rktHjslSzryxhhfCreateResp.setSg( dto.getSg() );
        rktHjslSzryxhhfCreateResp.setXx( dto.getXx() );
        rktHjslSzryxhhfCreateResp.setByzk( dto.getByzk() );
        rktHjslSzryxhhfCreateResp.setXxjb( dto.getXxjb() );
        rktHjslSzryxhhfCreateResp.setLxdh( dto.getLxdh() );
        rktHjslSzryxhhfCreateResp.setFqxm( dto.getFqxm() );
        rktHjslSzryxhhfCreateResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslSzryxhhfCreateResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslSzryxhhfCreateResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslSzryxhhfCreateResp.setFqwwx( dto.getFqwwx() );
        rktHjslSzryxhhfCreateResp.setFqwwm( dto.getFqwwm() );
        rktHjslSzryxhhfCreateResp.setMqxm( dto.getMqxm() );
        rktHjslSzryxhhfCreateResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslSzryxhhfCreateResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslSzryxhhfCreateResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslSzryxhhfCreateResp.setMqwwx( dto.getMqwwx() );
        rktHjslSzryxhhfCreateResp.setMqwwm( dto.getMqwwm() );
        rktHjslSzryxhhfCreateResp.setPoxm( dto.getPoxm() );
        rktHjslSzryxhhfCreateResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslSzryxhhfCreateResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslSzryxhhfCreateResp.setPozjhm( dto.getPozjhm() );
        rktHjslSzryxhhfCreateResp.setPowwx( dto.getPowwx() );
        rktHjslSzryxhhfCreateResp.setPowwm( dto.getPowwm() );
        rktHjslSzryxhhfCreateResp.setJhryxm( dto.getJhryxm() );
        rktHjslSzryxhhfCreateResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslSzryxhhfCreateResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslSzryxhhfCreateResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslSzryxhhfCreateResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslSzryxhhfCreateResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslSzryxhhfCreateResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslSzryxhhfCreateResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslSzryxhhfCreateResp.setJhrexm( dto.getJhrexm() );
        rktHjslSzryxhhfCreateResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslSzryxhhfCreateResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslSzryxhhfCreateResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslSzryxhhfCreateResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslSzryxhhfCreateResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslSzryxhhfCreateResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslSzryxhhfCreateResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslSzryxhhfCreateResp.setZjlb( dto.getZjlb() );
        rktHjslSzryxhhfCreateResp.setQfjg( dto.getQfjg() );
        rktHjslSzryxhhfCreateResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslSzryxhhfCreateResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslSzryxhhfCreateResp.setSzqhkszdssxqdm( dto.getSzqhkszdssxqdm() );
        rktHjslSzryxhhfCreateResp.setSzqhkszdqhnxxdz( dto.getSzqhkszdqhnxxdz() );
        rktHjslSzryxhhfCreateResp.setBdfw( dto.getBdfw() );
        rktHjslSzryxhhfCreateResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslSzryxhhfCreateResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslSzryxhhfCreateResp.setQtssxq( dto.getQtssxq() );
        rktHjslSzryxhhfCreateResp.setQtzz( dto.getQtzz() );

        return rktHjslSzryxhhfCreateResp;
    }
}
