package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktHjslBlDTO;
import com.zjjcnt.project.ck.base.dto.req.RktHjslBlCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslBlPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslBlUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslBlCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslBlPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslBlViewResp;
import com.zjjcnt.project.ck.base.entity.RktHjslBlDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktHjslBlConvertImpl implements RktHjslBlConvert {

    @Override
    public RktHjslBlDTO convert(RktHjslBlDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktHjslBlDTO rktHjslBlDTO = new RktHjslBlDTO();

        rktHjslBlDTO.setId( entity.getId() );
        rktHjslBlDTO.setYwslnbbh( entity.getYwslnbbh() );
        rktHjslBlDTO.setHhnbid( entity.getHhnbid() );
        rktHjslBlDTO.setYwslh( entity.getYwslh() );
        rktHjslBlDTO.setHmc( entity.getHmc() );
        rktHjslBlDTO.setHjddzbm( entity.getHjddzbm() );
        rktHjslBlDTO.setHjdssxq( entity.getHjdssxq() );
        rktHjslBlDTO.setHjdxxdz( entity.getHjdxxdz() );
        rktHjslBlDTO.setHjdrhyzbs( entity.getHjdrhyzbs() );
        rktHjslBlDTO.setJlx( entity.getJlx() );
        rktHjslBlDTO.setMlph( entity.getMlph() );
        rktHjslBlDTO.setMlxz( entity.getMlxz() );
        rktHjslBlDTO.setLcdyid( entity.getLcdyid() );
        rktHjslBlDTO.setLcmc( entity.getLcmc() );
        rktHjslBlDTO.setSpyj( entity.getSpyj() );
        rktHjslBlDTO.setXh( entity.getXh() );
        rktHjslBlDTO.setZjlb( entity.getZjlb() );
        rktHjslBlDTO.setQfjg( entity.getQfjg() );
        rktHjslBlDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        rktHjslBlDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        rktHjslBlDTO.setLdrkbllb( entity.getLdrkbllb() );
        rktHjslBlDTO.setLbsqrrq( entity.getLbsqrrq() );
        rktHjslBlDTO.setLbsqlyy( entity.getLbsqlyy() );
        rktHjslBlDTO.setLbsqyldyy( entity.getLbsqyldyy() );
        rktHjslBlDTO.setLbslzdgjdq( entity.getLbslzdgjdq() );
        rktHjslBlDTO.setLbslzdssxq( entity.getLbslzdssxq() );
        rktHjslBlDTO.setLbslzdxz( entity.getLbslzdxz() );
        rktHjslBlDTO.setHh( entity.getHh() );
        rktHjslBlDTO.setHhid( entity.getHhid() );
        rktHjslBlDTO.setHlx( entity.getHlx() );
        rktHjslBlDTO.setHb( entity.getHb() );
        rktHjslBlDTO.setYhzgx( entity.getYhzgx() );
        rktHjslBlDTO.setHkxz( entity.getHkxz() );
        rktHjslBlDTO.setCxsx( entity.getCxsx() );
        rktHjslBlDTO.setGmsfhm( entity.getGmsfhm() );
        rktHjslBlDTO.setXm( entity.getXm() );
        rktHjslBlDTO.setX( entity.getX() );
        rktHjslBlDTO.setM( entity.getM() );
        rktHjslBlDTO.setCym( entity.getCym() );
        rktHjslBlDTO.setXmpy( entity.getXmpy() );
        rktHjslBlDTO.setCympy( entity.getCympy() );
        rktHjslBlDTO.setXb( entity.getXb() );
        rktHjslBlDTO.setMz( entity.getMz() );
        rktHjslBlDTO.setJggjdq( entity.getJggjdq() );
        rktHjslBlDTO.setJgssxq( entity.getJgssxq() );
        rktHjslBlDTO.setJgxz( entity.getJgxz() );
        rktHjslBlDTO.setCsrq( entity.getCsrq() );
        rktHjslBlDTO.setCssj( entity.getCssj() );
        rktHjslBlDTO.setCsdgjdq( entity.getCsdgjdq() );
        rktHjslBlDTO.setCsdssxq( entity.getCsdssxq() );
        rktHjslBlDTO.setCsdxz( entity.getCsdxz() );
        rktHjslBlDTO.setWhcd( entity.getWhcd() );
        rktHjslBlDTO.setHyzk( entity.getHyzk() );
        rktHjslBlDTO.setCyzkdwbm( entity.getCyzkdwbm() );
        rktHjslBlDTO.setCyzkdwmc( entity.getCyzkdwmc() );
        rktHjslBlDTO.setZy( entity.getZy() );
        rktHjslBlDTO.setZylb( entity.getZylb() );
        rktHjslBlDTO.setZjxy( entity.getZjxy() );
        rktHjslBlDTO.setSg( entity.getSg() );
        rktHjslBlDTO.setXx( entity.getXx() );
        rktHjslBlDTO.setByzk( entity.getByzk() );
        rktHjslBlDTO.setXxjb( entity.getXxjb() );
        rktHjslBlDTO.setLxdh( entity.getLxdh() );
        rktHjslBlDTO.setFqxm( entity.getFqxm() );
        rktHjslBlDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        rktHjslBlDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        rktHjslBlDTO.setFqzjhm( entity.getFqzjhm() );
        rktHjslBlDTO.setFqwwx( entity.getFqwwx() );
        rktHjslBlDTO.setFqwwm( entity.getFqwwm() );
        rktHjslBlDTO.setMqxm( entity.getMqxm() );
        rktHjslBlDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        rktHjslBlDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        rktHjslBlDTO.setMqzjhm( entity.getMqzjhm() );
        rktHjslBlDTO.setMqwwx( entity.getMqwwx() );
        rktHjslBlDTO.setMqwwm( entity.getMqwwm() );
        rktHjslBlDTO.setPoxm( entity.getPoxm() );
        rktHjslBlDTO.setPogmsfhm( entity.getPogmsfhm() );
        rktHjslBlDTO.setPocyzjdm( entity.getPocyzjdm() );
        rktHjslBlDTO.setPozjhm( entity.getPozjhm() );
        rktHjslBlDTO.setPowwx( entity.getPowwx() );
        rktHjslBlDTO.setPowwm( entity.getPowwm() );
        rktHjslBlDTO.setJhryxm( entity.getJhryxm() );
        rktHjslBlDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        rktHjslBlDTO.setJhryjhgx( entity.getJhryjhgx() );
        rktHjslBlDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        rktHjslBlDTO.setJhryzjhm( entity.getJhryzjhm() );
        rktHjslBlDTO.setJhrywwx( entity.getJhrywwx() );
        rktHjslBlDTO.setJhrywwm( entity.getJhrywwm() );
        rktHjslBlDTO.setJhrylxdh( entity.getJhrylxdh() );
        rktHjslBlDTO.setJhrexm( entity.getJhrexm() );
        rktHjslBlDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        rktHjslBlDTO.setJhrejhgx( entity.getJhrejhgx() );
        rktHjslBlDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        rktHjslBlDTO.setJhrezjhm( entity.getJhrezjhm() );
        rktHjslBlDTO.setJhrewwx( entity.getJhrewwx() );
        rktHjslBlDTO.setJhrewwm( entity.getJhrewwm() );
        rktHjslBlDTO.setJhrelxdh( entity.getJhrelxdh() );
        rktHjslBlDTO.setLcywlx( entity.getLcywlx() );
        rktHjslBlDTO.setLcslid( entity.getLcslid() );
        rktHjslBlDTO.setLcywbt( entity.getLcywbt() );
        rktHjslBlDTO.setLcrwjd( entity.getLcrwjd() );
        rktHjslBlDTO.setLczt( entity.getLczt() );
        rktHjslBlDTO.setBlzt( entity.getBlzt() );
        rktHjslBlDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        rktHjslBlDTO.setSqrxm( entity.getSqrxm() );
        rktHjslBlDTO.setSqrxb( entity.getSqrxb() );
        rktHjslBlDTO.setSqrlxdh( entity.getSqrlxdh() );
        rktHjslBlDTO.setSqrybdrgx( entity.getSqrybdrgx() );
        rktHjslBlDTO.setSqrzzssxq( entity.getSqrzzssxq() );
        rktHjslBlDTO.setSqrzzxz( entity.getSqrzzxz() );
        rktHjslBlDTO.setSqrhkdjjg( entity.getSqrhkdjjg() );
        rktHjslBlDTO.setSqrq( entity.getSqrq() );
        rktHjslBlDTO.setSpdwgajgjgdm( entity.getSpdwgajgjgdm() );
        rktHjslBlDTO.setSpdwgajgmc( entity.getSpdwgajgmc() );
        rktHjslBlDTO.setSprxm( entity.getSprxm() );
        rktHjslBlDTO.setSpsj( entity.getSpsj() );
        rktHjslBlDTO.setSpjgdm( entity.getSpjgdm() );
        rktHjslBlDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        rktHjslBlDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        rktHjslBlDTO.setSlrxm( entity.getSlrxm() );
        rktHjslBlDTO.setSlsj( entity.getSlsj() );
        rktHjslBlDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        rktHjslBlDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        rktHjslBlDTO.setJcwh( entity.getJcwh() );
        rktHjslBlDTO.setBz( entity.getBz() );
        rktHjslBlDTO.setBlhjywid( entity.getBlhjywid() );
        rktHjslBlDTO.setHjywblsj( entity.getHjywblsj() );
        rktHjslBlDTO.setQtssxq( entity.getQtssxq() );
        rktHjslBlDTO.setQtzz( entity.getQtzz() );

        return rktHjslBlDTO;
    }

    @Override
    public RktHjslBlDO convertToDO(RktHjslBlDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslBlDO rktHjslBlDO = new RktHjslBlDO();

        rktHjslBlDO.setId( dto.getId() );
        rktHjslBlDO.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslBlDO.setHhnbid( dto.getHhnbid() );
        rktHjslBlDO.setYwslh( dto.getYwslh() );
        rktHjslBlDO.setHmc( dto.getHmc() );
        rktHjslBlDO.setHjddzbm( dto.getHjddzbm() );
        rktHjslBlDO.setHjdssxq( dto.getHjdssxq() );
        rktHjslBlDO.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslBlDO.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslBlDO.setJlx( dto.getJlx() );
        rktHjslBlDO.setMlph( dto.getMlph() );
        rktHjslBlDO.setMlxz( dto.getMlxz() );
        rktHjslBlDO.setLcdyid( dto.getLcdyid() );
        rktHjslBlDO.setLcmc( dto.getLcmc() );
        rktHjslBlDO.setSpyj( dto.getSpyj() );
        rktHjslBlDO.setXh( dto.getXh() );
        rktHjslBlDO.setZjlb( dto.getZjlb() );
        rktHjslBlDO.setQfjg( dto.getQfjg() );
        rktHjslBlDO.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslBlDO.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslBlDO.setLdrkbllb( dto.getLdrkbllb() );
        rktHjslBlDO.setLbsqrrq( dto.getLbsqrrq() );
        rktHjslBlDO.setLbsqlyy( dto.getLbsqlyy() );
        rktHjslBlDO.setLbsqyldyy( dto.getLbsqyldyy() );
        rktHjslBlDO.setLbslzdgjdq( dto.getLbslzdgjdq() );
        rktHjslBlDO.setLbslzdssxq( dto.getLbslzdssxq() );
        rktHjslBlDO.setLbslzdxz( dto.getLbslzdxz() );
        rktHjslBlDO.setHh( dto.getHh() );
        rktHjslBlDO.setHhid( dto.getHhid() );
        rktHjslBlDO.setHlx( dto.getHlx() );
        rktHjslBlDO.setHb( dto.getHb() );
        rktHjslBlDO.setYhzgx( dto.getYhzgx() );
        rktHjslBlDO.setHkxz( dto.getHkxz() );
        rktHjslBlDO.setCxsx( dto.getCxsx() );
        rktHjslBlDO.setGmsfhm( dto.getGmsfhm() );
        rktHjslBlDO.setXm( dto.getXm() );
        rktHjslBlDO.setX( dto.getX() );
        rktHjslBlDO.setM( dto.getM() );
        rktHjslBlDO.setCym( dto.getCym() );
        rktHjslBlDO.setXmpy( dto.getXmpy() );
        rktHjslBlDO.setCympy( dto.getCympy() );
        rktHjslBlDO.setXb( dto.getXb() );
        rktHjslBlDO.setMz( dto.getMz() );
        rktHjslBlDO.setJggjdq( dto.getJggjdq() );
        rktHjslBlDO.setJgssxq( dto.getJgssxq() );
        rktHjslBlDO.setJgxz( dto.getJgxz() );
        rktHjslBlDO.setCsrq( dto.getCsrq() );
        rktHjslBlDO.setCssj( dto.getCssj() );
        rktHjslBlDO.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslBlDO.setCsdssxq( dto.getCsdssxq() );
        rktHjslBlDO.setCsdxz( dto.getCsdxz() );
        rktHjslBlDO.setWhcd( dto.getWhcd() );
        rktHjslBlDO.setHyzk( dto.getHyzk() );
        rktHjslBlDO.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslBlDO.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslBlDO.setZy( dto.getZy() );
        rktHjslBlDO.setZylb( dto.getZylb() );
        rktHjslBlDO.setZjxy( dto.getZjxy() );
        rktHjslBlDO.setSg( dto.getSg() );
        rktHjslBlDO.setXx( dto.getXx() );
        rktHjslBlDO.setByzk( dto.getByzk() );
        rktHjslBlDO.setXxjb( dto.getXxjb() );
        rktHjslBlDO.setLxdh( dto.getLxdh() );
        rktHjslBlDO.setFqxm( dto.getFqxm() );
        rktHjslBlDO.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslBlDO.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslBlDO.setFqzjhm( dto.getFqzjhm() );
        rktHjslBlDO.setFqwwx( dto.getFqwwx() );
        rktHjslBlDO.setFqwwm( dto.getFqwwm() );
        rktHjslBlDO.setMqxm( dto.getMqxm() );
        rktHjslBlDO.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslBlDO.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslBlDO.setMqzjhm( dto.getMqzjhm() );
        rktHjslBlDO.setMqwwx( dto.getMqwwx() );
        rktHjslBlDO.setMqwwm( dto.getMqwwm() );
        rktHjslBlDO.setPoxm( dto.getPoxm() );
        rktHjslBlDO.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslBlDO.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslBlDO.setPozjhm( dto.getPozjhm() );
        rktHjslBlDO.setPowwx( dto.getPowwx() );
        rktHjslBlDO.setPowwm( dto.getPowwm() );
        rktHjslBlDO.setJhryxm( dto.getJhryxm() );
        rktHjslBlDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslBlDO.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslBlDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslBlDO.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslBlDO.setJhrywwx( dto.getJhrywwx() );
        rktHjslBlDO.setJhrywwm( dto.getJhrywwm() );
        rktHjslBlDO.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslBlDO.setJhrexm( dto.getJhrexm() );
        rktHjslBlDO.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslBlDO.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslBlDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslBlDO.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslBlDO.setJhrewwx( dto.getJhrewwx() );
        rktHjslBlDO.setJhrewwm( dto.getJhrewwm() );
        rktHjslBlDO.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslBlDO.setLcywlx( dto.getLcywlx() );
        rktHjslBlDO.setLcslid( dto.getLcslid() );
        rktHjslBlDO.setLcywbt( dto.getLcywbt() );
        rktHjslBlDO.setLcrwjd( dto.getLcrwjd() );
        rktHjslBlDO.setLczt( dto.getLczt() );
        rktHjslBlDO.setBlzt( dto.getBlzt() );
        rktHjslBlDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslBlDO.setSqrxm( dto.getSqrxm() );
        rktHjslBlDO.setSqrxb( dto.getSqrxb() );
        rktHjslBlDO.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslBlDO.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslBlDO.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslBlDO.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslBlDO.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslBlDO.setSqrq( dto.getSqrq() );
        rktHjslBlDO.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslBlDO.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslBlDO.setSprxm( dto.getSprxm() );
        rktHjslBlDO.setSpsj( dto.getSpsj() );
        rktHjslBlDO.setSpjgdm( dto.getSpjgdm() );
        rktHjslBlDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslBlDO.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslBlDO.setSlrxm( dto.getSlrxm() );
        rktHjslBlDO.setSlsj( dto.getSlsj() );
        rktHjslBlDO.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslBlDO.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslBlDO.setJcwh( dto.getJcwh() );
        rktHjslBlDO.setBz( dto.getBz() );
        rktHjslBlDO.setBlhjywid( dto.getBlhjywid() );
        rktHjslBlDO.setHjywblsj( dto.getHjywblsj() );
        rktHjslBlDO.setQtssxq( dto.getQtssxq() );
        rktHjslBlDO.setQtzz( dto.getQtzz() );

        return rktHjslBlDO;
    }

    @Override
    public RktHjslBlDTO convertToDTO(RktHjslBlPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslBlDTO rktHjslBlDTO = new RktHjslBlDTO();

        rktHjslBlDTO.setHhnbid( req.getHhnbid() );
        rktHjslBlDTO.setYwslh( req.getYwslh() );
        rktHjslBlDTO.setHmc( req.getHmc() );
        rktHjslBlDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslBlDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslBlDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslBlDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslBlDTO.setJlx( req.getJlx() );
        rktHjslBlDTO.setMlph( req.getMlph() );
        rktHjslBlDTO.setMlxz( req.getMlxz() );
        rktHjslBlDTO.setLcdyid( req.getLcdyid() );
        rktHjslBlDTO.setLcmc( req.getLcmc() );
        rktHjslBlDTO.setSpyj( req.getSpyj() );
        rktHjslBlDTO.setXh( req.getXh() );
        rktHjslBlDTO.setZjlb( req.getZjlb() );
        rktHjslBlDTO.setQfjg( req.getQfjg() );
        rktHjslBlDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslBlDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslBlDTO.setLdrkbllb( req.getLdrkbllb() );
        rktHjslBlDTO.setLbsqrrq( req.getLbsqrrq() );
        rktHjslBlDTO.setLbsqlyy( req.getLbsqlyy() );
        rktHjslBlDTO.setLbsqyldyy( req.getLbsqyldyy() );
        rktHjslBlDTO.setLbslzdgjdq( req.getLbslzdgjdq() );
        rktHjslBlDTO.setLbslzdssxq( req.getLbslzdssxq() );
        rktHjslBlDTO.setLbslzdxz( req.getLbslzdxz() );
        rktHjslBlDTO.setHh( req.getHh() );
        rktHjslBlDTO.setHhid( req.getHhid() );
        rktHjslBlDTO.setHlx( req.getHlx() );
        rktHjslBlDTO.setHb( req.getHb() );
        rktHjslBlDTO.setYhzgx( req.getYhzgx() );
        rktHjslBlDTO.setHkxz( req.getHkxz() );
        rktHjslBlDTO.setCxsx( req.getCxsx() );
        rktHjslBlDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslBlDTO.setXm( req.getXm() );
        rktHjslBlDTO.setX( req.getX() );
        rktHjslBlDTO.setM( req.getM() );
        rktHjslBlDTO.setCym( req.getCym() );
        rktHjslBlDTO.setXmpy( req.getXmpy() );
        rktHjslBlDTO.setCympy( req.getCympy() );
        rktHjslBlDTO.setXb( req.getXb() );
        rktHjslBlDTO.setMz( req.getMz() );
        rktHjslBlDTO.setJggjdq( req.getJggjdq() );
        rktHjslBlDTO.setJgssxq( req.getJgssxq() );
        rktHjslBlDTO.setJgxz( req.getJgxz() );
        rktHjslBlDTO.setCsrq( req.getCsrq() );
        rktHjslBlDTO.setCssj( req.getCssj() );
        rktHjslBlDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslBlDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslBlDTO.setCsdxz( req.getCsdxz() );
        rktHjslBlDTO.setWhcd( req.getWhcd() );
        rktHjslBlDTO.setHyzk( req.getHyzk() );
        rktHjslBlDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslBlDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslBlDTO.setZy( req.getZy() );
        rktHjslBlDTO.setZylb( req.getZylb() );
        rktHjslBlDTO.setZjxy( req.getZjxy() );
        rktHjslBlDTO.setSg( req.getSg() );
        rktHjslBlDTO.setXx( req.getXx() );
        rktHjslBlDTO.setByzk( req.getByzk() );
        rktHjslBlDTO.setXxjb( req.getXxjb() );
        rktHjslBlDTO.setLxdh( req.getLxdh() );
        rktHjslBlDTO.setFqxm( req.getFqxm() );
        rktHjslBlDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslBlDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslBlDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslBlDTO.setFqwwx( req.getFqwwx() );
        rktHjslBlDTO.setFqwwm( req.getFqwwm() );
        rktHjslBlDTO.setMqxm( req.getMqxm() );
        rktHjslBlDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslBlDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslBlDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslBlDTO.setMqwwx( req.getMqwwx() );
        rktHjslBlDTO.setMqwwm( req.getMqwwm() );
        rktHjslBlDTO.setPoxm( req.getPoxm() );
        rktHjslBlDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslBlDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslBlDTO.setPozjhm( req.getPozjhm() );
        rktHjslBlDTO.setPowwx( req.getPowwx() );
        rktHjslBlDTO.setPowwm( req.getPowwm() );
        rktHjslBlDTO.setJhryxm( req.getJhryxm() );
        rktHjslBlDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslBlDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslBlDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslBlDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslBlDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslBlDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslBlDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslBlDTO.setJhrexm( req.getJhrexm() );
        rktHjslBlDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslBlDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslBlDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslBlDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslBlDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslBlDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslBlDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslBlDTO.setLcywlx( req.getLcywlx() );
        rktHjslBlDTO.setLcslid( req.getLcslid() );
        rktHjslBlDTO.setLcywbt( req.getLcywbt() );
        rktHjslBlDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslBlDTO.setLczt( req.getLczt() );
        rktHjslBlDTO.setBlzt( req.getBlzt() );
        rktHjslBlDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslBlDTO.setSqrxm( req.getSqrxm() );
        rktHjslBlDTO.setSqrxb( req.getSqrxb() );
        rktHjslBlDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslBlDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslBlDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslBlDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslBlDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslBlDTO.setSqrq( req.getSqrq() );
        rktHjslBlDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslBlDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslBlDTO.setSprxm( req.getSprxm() );
        rktHjslBlDTO.setSpsj( req.getSpsj() );
        rktHjslBlDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslBlDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslBlDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslBlDTO.setSlrxm( req.getSlrxm() );
        rktHjslBlDTO.setSlsj( req.getSlsj() );
        rktHjslBlDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslBlDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslBlDTO.setJcwh( req.getJcwh() );
        rktHjslBlDTO.setBz( req.getBz() );
        rktHjslBlDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslBlDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslBlDTO.setQtssxq( req.getQtssxq() );
        rktHjslBlDTO.setQtzz( req.getQtzz() );

        return rktHjslBlDTO;
    }

    @Override
    public RktHjslBlDTO convertToDTO(RktHjslBlCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslBlDTO rktHjslBlDTO = new RktHjslBlDTO();

        rktHjslBlDTO.setHhnbid( req.getHhnbid() );
        rktHjslBlDTO.setYwslh( req.getYwslh() );
        rktHjslBlDTO.setHmc( req.getHmc() );
        rktHjslBlDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslBlDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslBlDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslBlDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslBlDTO.setJlx( req.getJlx() );
        rktHjslBlDTO.setMlph( req.getMlph() );
        rktHjslBlDTO.setMlxz( req.getMlxz() );
        rktHjslBlDTO.setLcdyid( req.getLcdyid() );
        rktHjslBlDTO.setLcmc( req.getLcmc() );
        rktHjslBlDTO.setSpyj( req.getSpyj() );
        rktHjslBlDTO.setXh( req.getXh() );
        rktHjslBlDTO.setZjlb( req.getZjlb() );
        rktHjslBlDTO.setQfjg( req.getQfjg() );
        rktHjslBlDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslBlDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslBlDTO.setLdrkbllb( req.getLdrkbllb() );
        rktHjslBlDTO.setLbsqrrq( req.getLbsqrrq() );
        rktHjslBlDTO.setLbsqlyy( req.getLbsqlyy() );
        rktHjslBlDTO.setLbsqyldyy( req.getLbsqyldyy() );
        rktHjslBlDTO.setLbslzdgjdq( req.getLbslzdgjdq() );
        rktHjslBlDTO.setLbslzdssxq( req.getLbslzdssxq() );
        rktHjslBlDTO.setLbslzdxz( req.getLbslzdxz() );
        rktHjslBlDTO.setHh( req.getHh() );
        rktHjslBlDTO.setHhid( req.getHhid() );
        rktHjslBlDTO.setHlx( req.getHlx() );
        rktHjslBlDTO.setHb( req.getHb() );
        rktHjslBlDTO.setYhzgx( req.getYhzgx() );
        rktHjslBlDTO.setHkxz( req.getHkxz() );
        rktHjslBlDTO.setCxsx( req.getCxsx() );
        rktHjslBlDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslBlDTO.setXm( req.getXm() );
        rktHjslBlDTO.setX( req.getX() );
        rktHjslBlDTO.setM( req.getM() );
        rktHjslBlDTO.setCym( req.getCym() );
        rktHjslBlDTO.setXmpy( req.getXmpy() );
        rktHjslBlDTO.setCympy( req.getCympy() );
        rktHjslBlDTO.setXb( req.getXb() );
        rktHjslBlDTO.setMz( req.getMz() );
        rktHjslBlDTO.setJggjdq( req.getJggjdq() );
        rktHjslBlDTO.setJgssxq( req.getJgssxq() );
        rktHjslBlDTO.setJgxz( req.getJgxz() );
        rktHjslBlDTO.setCsrq( req.getCsrq() );
        rktHjslBlDTO.setCssj( req.getCssj() );
        rktHjslBlDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslBlDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslBlDTO.setCsdxz( req.getCsdxz() );
        rktHjslBlDTO.setWhcd( req.getWhcd() );
        rktHjslBlDTO.setHyzk( req.getHyzk() );
        rktHjslBlDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslBlDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslBlDTO.setZy( req.getZy() );
        rktHjslBlDTO.setZylb( req.getZylb() );
        rktHjslBlDTO.setZjxy( req.getZjxy() );
        rktHjslBlDTO.setSg( req.getSg() );
        rktHjslBlDTO.setXx( req.getXx() );
        rktHjslBlDTO.setByzk( req.getByzk() );
        rktHjslBlDTO.setXxjb( req.getXxjb() );
        rktHjslBlDTO.setLxdh( req.getLxdh() );
        rktHjslBlDTO.setFqxm( req.getFqxm() );
        rktHjslBlDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslBlDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslBlDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslBlDTO.setFqwwx( req.getFqwwx() );
        rktHjslBlDTO.setFqwwm( req.getFqwwm() );
        rktHjslBlDTO.setMqxm( req.getMqxm() );
        rktHjslBlDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslBlDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslBlDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslBlDTO.setMqwwx( req.getMqwwx() );
        rktHjslBlDTO.setMqwwm( req.getMqwwm() );
        rktHjslBlDTO.setPoxm( req.getPoxm() );
        rktHjslBlDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslBlDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslBlDTO.setPozjhm( req.getPozjhm() );
        rktHjslBlDTO.setPowwx( req.getPowwx() );
        rktHjslBlDTO.setPowwm( req.getPowwm() );
        rktHjslBlDTO.setJhryxm( req.getJhryxm() );
        rktHjslBlDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslBlDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslBlDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslBlDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslBlDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslBlDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslBlDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslBlDTO.setJhrexm( req.getJhrexm() );
        rktHjslBlDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslBlDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslBlDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslBlDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslBlDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslBlDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslBlDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslBlDTO.setLcywlx( req.getLcywlx() );
        rktHjslBlDTO.setLcslid( req.getLcslid() );
        rktHjslBlDTO.setLcywbt( req.getLcywbt() );
        rktHjslBlDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslBlDTO.setLczt( req.getLczt() );
        rktHjslBlDTO.setBlzt( req.getBlzt() );
        rktHjslBlDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslBlDTO.setSqrxm( req.getSqrxm() );
        rktHjslBlDTO.setSqrxb( req.getSqrxb() );
        rktHjslBlDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslBlDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslBlDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslBlDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslBlDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslBlDTO.setSqrq( req.getSqrq() );
        rktHjslBlDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslBlDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslBlDTO.setSprxm( req.getSprxm() );
        rktHjslBlDTO.setSpsj( req.getSpsj() );
        rktHjslBlDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslBlDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslBlDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslBlDTO.setSlrxm( req.getSlrxm() );
        rktHjslBlDTO.setSlsj( req.getSlsj() );
        rktHjslBlDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslBlDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslBlDTO.setJcwh( req.getJcwh() );
        rktHjslBlDTO.setBz( req.getBz() );
        rktHjslBlDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslBlDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslBlDTO.setQtssxq( req.getQtssxq() );
        rktHjslBlDTO.setQtzz( req.getQtzz() );

        return rktHjslBlDTO;
    }

    @Override
    public RktHjslBlDTO convertToDTO(RktHjslBlUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslBlDTO rktHjslBlDTO = new RktHjslBlDTO();

        rktHjslBlDTO.setYwslnbbh( req.getYwslnbbh() );
        rktHjslBlDTO.setHhnbid( req.getHhnbid() );
        rktHjslBlDTO.setYwslh( req.getYwslh() );
        rktHjslBlDTO.setHmc( req.getHmc() );
        rktHjslBlDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslBlDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslBlDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslBlDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslBlDTO.setJlx( req.getJlx() );
        rktHjslBlDTO.setMlph( req.getMlph() );
        rktHjslBlDTO.setMlxz( req.getMlxz() );
        rktHjslBlDTO.setLcdyid( req.getLcdyid() );
        rktHjslBlDTO.setLcmc( req.getLcmc() );
        rktHjslBlDTO.setSpyj( req.getSpyj() );
        rktHjslBlDTO.setXh( req.getXh() );
        rktHjslBlDTO.setZjlb( req.getZjlb() );
        rktHjslBlDTO.setQfjg( req.getQfjg() );
        rktHjslBlDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslBlDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslBlDTO.setLdrkbllb( req.getLdrkbllb() );
        rktHjslBlDTO.setLbsqrrq( req.getLbsqrrq() );
        rktHjslBlDTO.setLbsqlyy( req.getLbsqlyy() );
        rktHjslBlDTO.setLbsqyldyy( req.getLbsqyldyy() );
        rktHjslBlDTO.setLbslzdgjdq( req.getLbslzdgjdq() );
        rktHjslBlDTO.setLbslzdssxq( req.getLbslzdssxq() );
        rktHjslBlDTO.setLbslzdxz( req.getLbslzdxz() );
        rktHjslBlDTO.setHh( req.getHh() );
        rktHjslBlDTO.setHhid( req.getHhid() );
        rktHjslBlDTO.setHlx( req.getHlx() );
        rktHjslBlDTO.setHb( req.getHb() );
        rktHjslBlDTO.setYhzgx( req.getYhzgx() );
        rktHjslBlDTO.setHkxz( req.getHkxz() );
        rktHjslBlDTO.setCxsx( req.getCxsx() );
        rktHjslBlDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslBlDTO.setXm( req.getXm() );
        rktHjslBlDTO.setX( req.getX() );
        rktHjslBlDTO.setM( req.getM() );
        rktHjslBlDTO.setCym( req.getCym() );
        rktHjslBlDTO.setXmpy( req.getXmpy() );
        rktHjslBlDTO.setCympy( req.getCympy() );
        rktHjslBlDTO.setXb( req.getXb() );
        rktHjslBlDTO.setMz( req.getMz() );
        rktHjslBlDTO.setJggjdq( req.getJggjdq() );
        rktHjslBlDTO.setJgssxq( req.getJgssxq() );
        rktHjslBlDTO.setJgxz( req.getJgxz() );
        rktHjslBlDTO.setCsrq( req.getCsrq() );
        rktHjslBlDTO.setCssj( req.getCssj() );
        rktHjslBlDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslBlDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslBlDTO.setCsdxz( req.getCsdxz() );
        rktHjslBlDTO.setWhcd( req.getWhcd() );
        rktHjslBlDTO.setHyzk( req.getHyzk() );
        rktHjslBlDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslBlDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslBlDTO.setZy( req.getZy() );
        rktHjslBlDTO.setZylb( req.getZylb() );
        rktHjslBlDTO.setZjxy( req.getZjxy() );
        rktHjslBlDTO.setSg( req.getSg() );
        rktHjslBlDTO.setXx( req.getXx() );
        rktHjslBlDTO.setByzk( req.getByzk() );
        rktHjslBlDTO.setXxjb( req.getXxjb() );
        rktHjslBlDTO.setLxdh( req.getLxdh() );
        rktHjslBlDTO.setFqxm( req.getFqxm() );
        rktHjslBlDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslBlDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslBlDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslBlDTO.setFqwwx( req.getFqwwx() );
        rktHjslBlDTO.setFqwwm( req.getFqwwm() );
        rktHjslBlDTO.setMqxm( req.getMqxm() );
        rktHjslBlDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslBlDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslBlDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslBlDTO.setMqwwx( req.getMqwwx() );
        rktHjslBlDTO.setMqwwm( req.getMqwwm() );
        rktHjslBlDTO.setPoxm( req.getPoxm() );
        rktHjslBlDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslBlDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslBlDTO.setPozjhm( req.getPozjhm() );
        rktHjslBlDTO.setPowwx( req.getPowwx() );
        rktHjslBlDTO.setPowwm( req.getPowwm() );
        rktHjslBlDTO.setJhryxm( req.getJhryxm() );
        rktHjslBlDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslBlDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslBlDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslBlDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslBlDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslBlDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslBlDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslBlDTO.setJhrexm( req.getJhrexm() );
        rktHjslBlDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslBlDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslBlDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslBlDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslBlDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslBlDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslBlDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslBlDTO.setLcywlx( req.getLcywlx() );
        rktHjslBlDTO.setLcslid( req.getLcslid() );
        rktHjslBlDTO.setLcywbt( req.getLcywbt() );
        rktHjslBlDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslBlDTO.setLczt( req.getLczt() );
        rktHjslBlDTO.setBlzt( req.getBlzt() );
        rktHjslBlDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslBlDTO.setSqrxm( req.getSqrxm() );
        rktHjslBlDTO.setSqrxb( req.getSqrxb() );
        rktHjslBlDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslBlDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslBlDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslBlDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslBlDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslBlDTO.setSqrq( req.getSqrq() );
        rktHjslBlDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslBlDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslBlDTO.setSprxm( req.getSprxm() );
        rktHjslBlDTO.setSpsj( req.getSpsj() );
        rktHjslBlDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslBlDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslBlDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslBlDTO.setSlrxm( req.getSlrxm() );
        rktHjslBlDTO.setSlsj( req.getSlsj() );
        rktHjslBlDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslBlDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslBlDTO.setJcwh( req.getJcwh() );
        rktHjslBlDTO.setBz( req.getBz() );
        rktHjslBlDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslBlDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslBlDTO.setQtssxq( req.getQtssxq() );
        rktHjslBlDTO.setQtzz( req.getQtzz() );

        return rktHjslBlDTO;
    }

    @Override
    public RktHjslBlPageResp convertToPageResp(RktHjslBlDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslBlPageResp rktHjslBlPageResp = new RktHjslBlPageResp();

        rktHjslBlPageResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslBlPageResp.setHhnbid( dto.getHhnbid() );
        rktHjslBlPageResp.setYwslh( dto.getYwslh() );
        rktHjslBlPageResp.setHmc( dto.getHmc() );
        rktHjslBlPageResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslBlPageResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslBlPageResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslBlPageResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslBlPageResp.setJlx( dto.getJlx() );
        rktHjslBlPageResp.setMlph( dto.getMlph() );
        rktHjslBlPageResp.setMlxz( dto.getMlxz() );
        rktHjslBlPageResp.setLcdyid( dto.getLcdyid() );
        rktHjslBlPageResp.setLcmc( dto.getLcmc() );
        rktHjslBlPageResp.setSpyj( dto.getSpyj() );
        rktHjslBlPageResp.setXh( dto.getXh() );
        rktHjslBlPageResp.setZjlb( dto.getZjlb() );
        rktHjslBlPageResp.setQfjg( dto.getQfjg() );
        rktHjslBlPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslBlPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslBlPageResp.setLdrkbllb( dto.getLdrkbllb() );
        rktHjslBlPageResp.setLbsqrrq( dto.getLbsqrrq() );
        rktHjslBlPageResp.setLbsqlyy( dto.getLbsqlyy() );
        rktHjslBlPageResp.setLbsqyldyy( dto.getLbsqyldyy() );
        rktHjslBlPageResp.setLbslzdgjdq( dto.getLbslzdgjdq() );
        rktHjslBlPageResp.setLbslzdssxq( dto.getLbslzdssxq() );
        rktHjslBlPageResp.setLbslzdxz( dto.getLbslzdxz() );
        rktHjslBlPageResp.setHh( dto.getHh() );
        rktHjslBlPageResp.setHhid( dto.getHhid() );
        rktHjslBlPageResp.setHlx( dto.getHlx() );
        rktHjslBlPageResp.setHb( dto.getHb() );
        rktHjslBlPageResp.setYhzgx( dto.getYhzgx() );
        rktHjslBlPageResp.setHkxz( dto.getHkxz() );
        rktHjslBlPageResp.setCxsx( dto.getCxsx() );
        rktHjslBlPageResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslBlPageResp.setXm( dto.getXm() );
        rktHjslBlPageResp.setX( dto.getX() );
        rktHjslBlPageResp.setM( dto.getM() );
        rktHjslBlPageResp.setCym( dto.getCym() );
        rktHjslBlPageResp.setXmpy( dto.getXmpy() );
        rktHjslBlPageResp.setCympy( dto.getCympy() );
        rktHjslBlPageResp.setXb( dto.getXb() );
        rktHjslBlPageResp.setMz( dto.getMz() );
        rktHjslBlPageResp.setJggjdq( dto.getJggjdq() );
        rktHjslBlPageResp.setJgssxq( dto.getJgssxq() );
        rktHjslBlPageResp.setJgxz( dto.getJgxz() );
        rktHjslBlPageResp.setCsrq( dto.getCsrq() );
        rktHjslBlPageResp.setCssj( dto.getCssj() );
        rktHjslBlPageResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslBlPageResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslBlPageResp.setCsdxz( dto.getCsdxz() );
        rktHjslBlPageResp.setWhcd( dto.getWhcd() );
        rktHjslBlPageResp.setHyzk( dto.getHyzk() );
        rktHjslBlPageResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslBlPageResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslBlPageResp.setZy( dto.getZy() );
        rktHjslBlPageResp.setZylb( dto.getZylb() );
        rktHjslBlPageResp.setZjxy( dto.getZjxy() );
        rktHjslBlPageResp.setSg( dto.getSg() );
        rktHjslBlPageResp.setXx( dto.getXx() );
        rktHjslBlPageResp.setByzk( dto.getByzk() );
        rktHjslBlPageResp.setXxjb( dto.getXxjb() );
        rktHjslBlPageResp.setLxdh( dto.getLxdh() );
        rktHjslBlPageResp.setFqxm( dto.getFqxm() );
        rktHjslBlPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslBlPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslBlPageResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslBlPageResp.setFqwwx( dto.getFqwwx() );
        rktHjslBlPageResp.setFqwwm( dto.getFqwwm() );
        rktHjslBlPageResp.setMqxm( dto.getMqxm() );
        rktHjslBlPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslBlPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslBlPageResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslBlPageResp.setMqwwx( dto.getMqwwx() );
        rktHjslBlPageResp.setMqwwm( dto.getMqwwm() );
        rktHjslBlPageResp.setPoxm( dto.getPoxm() );
        rktHjslBlPageResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslBlPageResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslBlPageResp.setPozjhm( dto.getPozjhm() );
        rktHjslBlPageResp.setPowwx( dto.getPowwx() );
        rktHjslBlPageResp.setPowwm( dto.getPowwm() );
        rktHjslBlPageResp.setJhryxm( dto.getJhryxm() );
        rktHjslBlPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslBlPageResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslBlPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslBlPageResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslBlPageResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslBlPageResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslBlPageResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslBlPageResp.setJhrexm( dto.getJhrexm() );
        rktHjslBlPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslBlPageResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslBlPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslBlPageResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslBlPageResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslBlPageResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslBlPageResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslBlPageResp.setLcywlx( dto.getLcywlx() );
        rktHjslBlPageResp.setLcslid( dto.getLcslid() );
        rktHjslBlPageResp.setLcywbt( dto.getLcywbt() );
        rktHjslBlPageResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslBlPageResp.setLczt( dto.getLczt() );
        rktHjslBlPageResp.setBlzt( dto.getBlzt() );
        rktHjslBlPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslBlPageResp.setSqrxm( dto.getSqrxm() );
        rktHjslBlPageResp.setSqrxb( dto.getSqrxb() );
        rktHjslBlPageResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslBlPageResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslBlPageResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslBlPageResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslBlPageResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslBlPageResp.setSqrq( dto.getSqrq() );
        rktHjslBlPageResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslBlPageResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslBlPageResp.setSprxm( dto.getSprxm() );
        rktHjslBlPageResp.setSpsj( dto.getSpsj() );
        rktHjslBlPageResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslBlPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslBlPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslBlPageResp.setSlrxm( dto.getSlrxm() );
        rktHjslBlPageResp.setSlsj( dto.getSlsj() );
        rktHjslBlPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslBlPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslBlPageResp.setJcwh( dto.getJcwh() );
        rktHjslBlPageResp.setBz( dto.getBz() );
        rktHjslBlPageResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslBlPageResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslBlPageResp.setQtssxq( dto.getQtssxq() );
        rktHjslBlPageResp.setQtzz( dto.getQtzz() );

        return rktHjslBlPageResp;
    }

    @Override
    public RktHjslBlViewResp convertToViewResp(RktHjslBlDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslBlViewResp rktHjslBlViewResp = new RktHjslBlViewResp();

        rktHjslBlViewResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslBlViewResp.setHhnbid( dto.getHhnbid() );
        rktHjslBlViewResp.setYwslh( dto.getYwslh() );
        rktHjslBlViewResp.setHmc( dto.getHmc() );
        rktHjslBlViewResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslBlViewResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslBlViewResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslBlViewResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslBlViewResp.setJlx( dto.getJlx() );
        rktHjslBlViewResp.setMlph( dto.getMlph() );
        rktHjslBlViewResp.setMlxz( dto.getMlxz() );
        rktHjslBlViewResp.setLcdyid( dto.getLcdyid() );
        rktHjslBlViewResp.setLcmc( dto.getLcmc() );
        rktHjslBlViewResp.setSpyj( dto.getSpyj() );
        rktHjslBlViewResp.setXh( dto.getXh() );
        rktHjslBlViewResp.setZjlb( dto.getZjlb() );
        rktHjslBlViewResp.setQfjg( dto.getQfjg() );
        rktHjslBlViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslBlViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslBlViewResp.setLdrkbllb( dto.getLdrkbllb() );
        rktHjslBlViewResp.setLbsqrrq( dto.getLbsqrrq() );
        rktHjslBlViewResp.setLbsqlyy( dto.getLbsqlyy() );
        rktHjslBlViewResp.setLbsqyldyy( dto.getLbsqyldyy() );
        rktHjslBlViewResp.setLbslzdgjdq( dto.getLbslzdgjdq() );
        rktHjslBlViewResp.setLbslzdssxq( dto.getLbslzdssxq() );
        rktHjslBlViewResp.setLbslzdxz( dto.getLbslzdxz() );
        rktHjslBlViewResp.setHh( dto.getHh() );
        rktHjslBlViewResp.setHhid( dto.getHhid() );
        rktHjslBlViewResp.setHlx( dto.getHlx() );
        rktHjslBlViewResp.setHb( dto.getHb() );
        rktHjslBlViewResp.setYhzgx( dto.getYhzgx() );
        rktHjslBlViewResp.setHkxz( dto.getHkxz() );
        rktHjslBlViewResp.setCxsx( dto.getCxsx() );
        rktHjslBlViewResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslBlViewResp.setXm( dto.getXm() );
        rktHjslBlViewResp.setX( dto.getX() );
        rktHjslBlViewResp.setM( dto.getM() );
        rktHjslBlViewResp.setCym( dto.getCym() );
        rktHjslBlViewResp.setXmpy( dto.getXmpy() );
        rktHjslBlViewResp.setCympy( dto.getCympy() );
        rktHjslBlViewResp.setXb( dto.getXb() );
        rktHjslBlViewResp.setMz( dto.getMz() );
        rktHjslBlViewResp.setJggjdq( dto.getJggjdq() );
        rktHjslBlViewResp.setJgssxq( dto.getJgssxq() );
        rktHjslBlViewResp.setJgxz( dto.getJgxz() );
        rktHjslBlViewResp.setCsrq( dto.getCsrq() );
        rktHjslBlViewResp.setCssj( dto.getCssj() );
        rktHjslBlViewResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslBlViewResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslBlViewResp.setCsdxz( dto.getCsdxz() );
        rktHjslBlViewResp.setWhcd( dto.getWhcd() );
        rktHjslBlViewResp.setHyzk( dto.getHyzk() );
        rktHjslBlViewResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslBlViewResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslBlViewResp.setZy( dto.getZy() );
        rktHjslBlViewResp.setZylb( dto.getZylb() );
        rktHjslBlViewResp.setZjxy( dto.getZjxy() );
        rktHjslBlViewResp.setSg( dto.getSg() );
        rktHjslBlViewResp.setXx( dto.getXx() );
        rktHjslBlViewResp.setByzk( dto.getByzk() );
        rktHjslBlViewResp.setXxjb( dto.getXxjb() );
        rktHjslBlViewResp.setLxdh( dto.getLxdh() );
        rktHjslBlViewResp.setFqxm( dto.getFqxm() );
        rktHjslBlViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslBlViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslBlViewResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslBlViewResp.setFqwwx( dto.getFqwwx() );
        rktHjslBlViewResp.setFqwwm( dto.getFqwwm() );
        rktHjslBlViewResp.setMqxm( dto.getMqxm() );
        rktHjslBlViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslBlViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslBlViewResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslBlViewResp.setMqwwx( dto.getMqwwx() );
        rktHjslBlViewResp.setMqwwm( dto.getMqwwm() );
        rktHjslBlViewResp.setPoxm( dto.getPoxm() );
        rktHjslBlViewResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslBlViewResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslBlViewResp.setPozjhm( dto.getPozjhm() );
        rktHjslBlViewResp.setPowwx( dto.getPowwx() );
        rktHjslBlViewResp.setPowwm( dto.getPowwm() );
        rktHjslBlViewResp.setJhryxm( dto.getJhryxm() );
        rktHjslBlViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslBlViewResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslBlViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslBlViewResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslBlViewResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslBlViewResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslBlViewResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslBlViewResp.setJhrexm( dto.getJhrexm() );
        rktHjslBlViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslBlViewResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslBlViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslBlViewResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslBlViewResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslBlViewResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslBlViewResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslBlViewResp.setLcywlx( dto.getLcywlx() );
        rktHjslBlViewResp.setLcslid( dto.getLcslid() );
        rktHjslBlViewResp.setLcywbt( dto.getLcywbt() );
        rktHjslBlViewResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslBlViewResp.setLczt( dto.getLczt() );
        rktHjslBlViewResp.setBlzt( dto.getBlzt() );
        rktHjslBlViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslBlViewResp.setSqrxm( dto.getSqrxm() );
        rktHjslBlViewResp.setSqrxb( dto.getSqrxb() );
        rktHjslBlViewResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslBlViewResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslBlViewResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslBlViewResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslBlViewResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslBlViewResp.setSqrq( dto.getSqrq() );
        rktHjslBlViewResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslBlViewResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslBlViewResp.setSprxm( dto.getSprxm() );
        rktHjslBlViewResp.setSpsj( dto.getSpsj() );
        rktHjslBlViewResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslBlViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslBlViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslBlViewResp.setSlrxm( dto.getSlrxm() );
        rktHjslBlViewResp.setSlsj( dto.getSlsj() );
        rktHjslBlViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslBlViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslBlViewResp.setJcwh( dto.getJcwh() );
        rktHjslBlViewResp.setBz( dto.getBz() );
        rktHjslBlViewResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslBlViewResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslBlViewResp.setQtssxq( dto.getQtssxq() );
        rktHjslBlViewResp.setQtzz( dto.getQtzz() );

        return rktHjslBlViewResp;
    }

    @Override
    public RktHjslBlCreateResp convertToCreateResp(RktHjslBlDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslBlCreateResp rktHjslBlCreateResp = new RktHjslBlCreateResp();

        rktHjslBlCreateResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslBlCreateResp.setHhnbid( dto.getHhnbid() );
        rktHjslBlCreateResp.setYwslh( dto.getYwslh() );
        rktHjslBlCreateResp.setHmc( dto.getHmc() );
        rktHjslBlCreateResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslBlCreateResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslBlCreateResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslBlCreateResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslBlCreateResp.setJlx( dto.getJlx() );
        rktHjslBlCreateResp.setMlph( dto.getMlph() );
        rktHjslBlCreateResp.setMlxz( dto.getMlxz() );
        rktHjslBlCreateResp.setLcdyid( dto.getLcdyid() );
        rktHjslBlCreateResp.setLcmc( dto.getLcmc() );
        rktHjslBlCreateResp.setSpyj( dto.getSpyj() );
        rktHjslBlCreateResp.setXh( dto.getXh() );
        rktHjslBlCreateResp.setZjlb( dto.getZjlb() );
        rktHjslBlCreateResp.setQfjg( dto.getQfjg() );
        rktHjslBlCreateResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslBlCreateResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslBlCreateResp.setLdrkbllb( dto.getLdrkbllb() );
        rktHjslBlCreateResp.setLbsqrrq( dto.getLbsqrrq() );
        rktHjslBlCreateResp.setLbsqlyy( dto.getLbsqlyy() );
        rktHjslBlCreateResp.setLbsqyldyy( dto.getLbsqyldyy() );
        rktHjslBlCreateResp.setLbslzdgjdq( dto.getLbslzdgjdq() );
        rktHjslBlCreateResp.setLbslzdssxq( dto.getLbslzdssxq() );
        rktHjslBlCreateResp.setLbslzdxz( dto.getLbslzdxz() );
        rktHjslBlCreateResp.setHh( dto.getHh() );
        rktHjslBlCreateResp.setHhid( dto.getHhid() );
        rktHjslBlCreateResp.setHlx( dto.getHlx() );
        rktHjslBlCreateResp.setHb( dto.getHb() );
        rktHjslBlCreateResp.setYhzgx( dto.getYhzgx() );
        rktHjslBlCreateResp.setHkxz( dto.getHkxz() );
        rktHjslBlCreateResp.setCxsx( dto.getCxsx() );
        rktHjslBlCreateResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslBlCreateResp.setXm( dto.getXm() );
        rktHjslBlCreateResp.setX( dto.getX() );
        rktHjslBlCreateResp.setM( dto.getM() );
        rktHjslBlCreateResp.setCym( dto.getCym() );
        rktHjslBlCreateResp.setXmpy( dto.getXmpy() );
        rktHjslBlCreateResp.setCympy( dto.getCympy() );
        rktHjslBlCreateResp.setXb( dto.getXb() );
        rktHjslBlCreateResp.setMz( dto.getMz() );
        rktHjslBlCreateResp.setJggjdq( dto.getJggjdq() );
        rktHjslBlCreateResp.setJgssxq( dto.getJgssxq() );
        rktHjslBlCreateResp.setJgxz( dto.getJgxz() );
        rktHjslBlCreateResp.setCsrq( dto.getCsrq() );
        rktHjslBlCreateResp.setCssj( dto.getCssj() );
        rktHjslBlCreateResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslBlCreateResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslBlCreateResp.setCsdxz( dto.getCsdxz() );
        rktHjslBlCreateResp.setWhcd( dto.getWhcd() );
        rktHjslBlCreateResp.setHyzk( dto.getHyzk() );
        rktHjslBlCreateResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslBlCreateResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslBlCreateResp.setZy( dto.getZy() );
        rktHjslBlCreateResp.setZylb( dto.getZylb() );
        rktHjslBlCreateResp.setZjxy( dto.getZjxy() );
        rktHjslBlCreateResp.setSg( dto.getSg() );
        rktHjslBlCreateResp.setXx( dto.getXx() );
        rktHjslBlCreateResp.setByzk( dto.getByzk() );
        rktHjslBlCreateResp.setXxjb( dto.getXxjb() );
        rktHjslBlCreateResp.setLxdh( dto.getLxdh() );
        rktHjslBlCreateResp.setFqxm( dto.getFqxm() );
        rktHjslBlCreateResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslBlCreateResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslBlCreateResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslBlCreateResp.setFqwwx( dto.getFqwwx() );
        rktHjslBlCreateResp.setFqwwm( dto.getFqwwm() );
        rktHjslBlCreateResp.setMqxm( dto.getMqxm() );
        rktHjslBlCreateResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslBlCreateResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslBlCreateResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslBlCreateResp.setMqwwx( dto.getMqwwx() );
        rktHjslBlCreateResp.setMqwwm( dto.getMqwwm() );
        rktHjslBlCreateResp.setPoxm( dto.getPoxm() );
        rktHjslBlCreateResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslBlCreateResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslBlCreateResp.setPozjhm( dto.getPozjhm() );
        rktHjslBlCreateResp.setPowwx( dto.getPowwx() );
        rktHjslBlCreateResp.setPowwm( dto.getPowwm() );
        rktHjslBlCreateResp.setJhryxm( dto.getJhryxm() );
        rktHjslBlCreateResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslBlCreateResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslBlCreateResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslBlCreateResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslBlCreateResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslBlCreateResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslBlCreateResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslBlCreateResp.setJhrexm( dto.getJhrexm() );
        rktHjslBlCreateResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslBlCreateResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslBlCreateResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslBlCreateResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslBlCreateResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslBlCreateResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslBlCreateResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslBlCreateResp.setLcywlx( dto.getLcywlx() );
        rktHjslBlCreateResp.setLcslid( dto.getLcslid() );
        rktHjslBlCreateResp.setLcywbt( dto.getLcywbt() );
        rktHjslBlCreateResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslBlCreateResp.setLczt( dto.getLczt() );
        rktHjslBlCreateResp.setBlzt( dto.getBlzt() );
        rktHjslBlCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslBlCreateResp.setSqrxm( dto.getSqrxm() );
        rktHjslBlCreateResp.setSqrxb( dto.getSqrxb() );
        rktHjslBlCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslBlCreateResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslBlCreateResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslBlCreateResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslBlCreateResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslBlCreateResp.setSqrq( dto.getSqrq() );
        rktHjslBlCreateResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslBlCreateResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslBlCreateResp.setSprxm( dto.getSprxm() );
        rktHjslBlCreateResp.setSpsj( dto.getSpsj() );
        rktHjslBlCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslBlCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslBlCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslBlCreateResp.setSlrxm( dto.getSlrxm() );
        rktHjslBlCreateResp.setSlsj( dto.getSlsj() );
        rktHjslBlCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslBlCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslBlCreateResp.setJcwh( dto.getJcwh() );
        rktHjslBlCreateResp.setBz( dto.getBz() );
        rktHjslBlCreateResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslBlCreateResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslBlCreateResp.setQtssxq( dto.getQtssxq() );
        rktHjslBlCreateResp.setQtzz( dto.getQtzz() );

        return rktHjslBlCreateResp;
    }
}
