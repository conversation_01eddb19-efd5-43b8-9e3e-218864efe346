package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktHjslZxbgDTO;
import com.zjjcnt.project.ck.base.dto.req.RktHjslZxbgCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslZxbgPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslZxbgUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslZxbgCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslZxbgPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslZxbgViewResp;
import com.zjjcnt.project.ck.base.entity.RktHjslZxbgDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:45+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktHjslZxbgConvertImpl implements RktHjslZxbgConvert {

    @Override
    public RktHjslZxbgDTO convert(RktHjslZxbgDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktHjslZxbgDTO rktHjslZxbgDTO = new RktHjslZxbgDTO();

        rktHjslZxbgDTO.setId( entity.getId() );
        rktHjslZxbgDTO.setYwslnbbh( entity.getYwslnbbh() );
        rktHjslZxbgDTO.setYwslh( entity.getYwslh() );
        rktHjslZxbgDTO.setHh( entity.getHh() );
        rktHjslZxbgDTO.setHhid( entity.getHhid() );
        rktHjslZxbgDTO.setRyid( entity.getRyid() );
        rktHjslZxbgDTO.setRynbid( entity.getRynbid() );
        rktHjslZxbgDTO.setHlx( entity.getHlx() );
        rktHjslZxbgDTO.setHmc( entity.getHmc() );
        rktHjslZxbgDTO.setHjddzbm( entity.getHjddzbm() );
        rktHjslZxbgDTO.setHjdssxq( entity.getHjdssxq() );
        rktHjslZxbgDTO.setHjdxxdz( entity.getHjdxxdz() );
        rktHjslZxbgDTO.setHjdrhyzbs( entity.getHjdrhyzbs() );
        rktHjslZxbgDTO.setJlx( entity.getJlx() );
        rktHjslZxbgDTO.setMlph( entity.getMlph() );
        rktHjslZxbgDTO.setMlxz( entity.getMlxz() );
        rktHjslZxbgDTO.setLcdyid( entity.getLcdyid() );
        rktHjslZxbgDTO.setLcmc( entity.getLcmc() );
        rktHjslZxbgDTO.setSpyj( entity.getSpyj() );
        rktHjslZxbgDTO.setXh( entity.getXh() );
        rktHjslZxbgDTO.setHhnbid( entity.getHhnbid() );
        rktHjslZxbgDTO.setHb( entity.getHb() );
        rktHjslZxbgDTO.setYhzgx( entity.getYhzgx() );
        rktHjslZxbgDTO.setZjlb( entity.getZjlb() );
        rktHjslZxbgDTO.setQfjg( entity.getQfjg() );
        rktHjslZxbgDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        rktHjslZxbgDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        rktHjslZxbgDTO.setRkxxbggzlbdm( entity.getRkxxbggzlbdm() );
        rktHjslZxbgDTO.setBggzsjxbsf( entity.getBggzsjxbsf() );
        rktHjslZxbgDTO.setBggzsjxmc( entity.getBggzsjxmc() );
        rktHjslZxbgDTO.setBggzqnr( entity.getBggzqnr() );
        rktHjslZxbgDTO.setBggzhnr( entity.getBggzhnr() );
        rktHjslZxbgDTO.setHkxz( entity.getHkxz() );
        rktHjslZxbgDTO.setCxsx( entity.getCxsx() );
        rktHjslZxbgDTO.setGmsfhm( entity.getGmsfhm() );
        rktHjslZxbgDTO.setXm( entity.getXm() );
        rktHjslZxbgDTO.setX( entity.getX() );
        rktHjslZxbgDTO.setM( entity.getM() );
        rktHjslZxbgDTO.setCym( entity.getCym() );
        rktHjslZxbgDTO.setXmpy( entity.getXmpy() );
        rktHjslZxbgDTO.setCympy( entity.getCympy() );
        rktHjslZxbgDTO.setXb( entity.getXb() );
        rktHjslZxbgDTO.setMz( entity.getMz() );
        rktHjslZxbgDTO.setJggjdq( entity.getJggjdq() );
        rktHjslZxbgDTO.setJgssxq( entity.getJgssxq() );
        rktHjslZxbgDTO.setJgxz( entity.getJgxz() );
        rktHjslZxbgDTO.setCsrq( entity.getCsrq() );
        rktHjslZxbgDTO.setCssj( entity.getCssj() );
        rktHjslZxbgDTO.setCsdgjdq( entity.getCsdgjdq() );
        rktHjslZxbgDTO.setCsdssxq( entity.getCsdssxq() );
        rktHjslZxbgDTO.setCsdxz( entity.getCsdxz() );
        rktHjslZxbgDTO.setWhcd( entity.getWhcd() );
        rktHjslZxbgDTO.setHyzk( entity.getHyzk() );
        rktHjslZxbgDTO.setCyzkdwbm( entity.getCyzkdwbm() );
        rktHjslZxbgDTO.setCyzkdwmc( entity.getCyzkdwmc() );
        rktHjslZxbgDTO.setZy( entity.getZy() );
        rktHjslZxbgDTO.setZylb( entity.getZylb() );
        rktHjslZxbgDTO.setZjxy( entity.getZjxy() );
        rktHjslZxbgDTO.setSg( entity.getSg() );
        rktHjslZxbgDTO.setXx( entity.getXx() );
        rktHjslZxbgDTO.setByzk( entity.getByzk() );
        rktHjslZxbgDTO.setXxjb( entity.getXxjb() );
        rktHjslZxbgDTO.setLxdh( entity.getLxdh() );
        rktHjslZxbgDTO.setFqxm( entity.getFqxm() );
        rktHjslZxbgDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        rktHjslZxbgDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        rktHjslZxbgDTO.setFqzjhm( entity.getFqzjhm() );
        rktHjslZxbgDTO.setFqwwx( entity.getFqwwx() );
        rktHjslZxbgDTO.setFqwwm( entity.getFqwwm() );
        rktHjslZxbgDTO.setMqxm( entity.getMqxm() );
        rktHjslZxbgDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        rktHjslZxbgDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        rktHjslZxbgDTO.setMqzjhm( entity.getMqzjhm() );
        rktHjslZxbgDTO.setMqwwx( entity.getMqwwx() );
        rktHjslZxbgDTO.setMqwwm( entity.getMqwwm() );
        rktHjslZxbgDTO.setPoxm( entity.getPoxm() );
        rktHjslZxbgDTO.setPogmsfhm( entity.getPogmsfhm() );
        rktHjslZxbgDTO.setPocyzjdm( entity.getPocyzjdm() );
        rktHjslZxbgDTO.setPozjhm( entity.getPozjhm() );
        rktHjslZxbgDTO.setPowwx( entity.getPowwx() );
        rktHjslZxbgDTO.setPowwm( entity.getPowwm() );
        rktHjslZxbgDTO.setJhryxm( entity.getJhryxm() );
        rktHjslZxbgDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        rktHjslZxbgDTO.setJhryjhgx( entity.getJhryjhgx() );
        rktHjslZxbgDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        rktHjslZxbgDTO.setJhryzjhm( entity.getJhryzjhm() );
        rktHjslZxbgDTO.setJhrywwx( entity.getJhrywwx() );
        rktHjslZxbgDTO.setJhrywwm( entity.getJhrywwm() );
        rktHjslZxbgDTO.setJhrylxdh( entity.getJhrylxdh() );
        rktHjslZxbgDTO.setJhrexm( entity.getJhrexm() );
        rktHjslZxbgDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        rktHjslZxbgDTO.setJhrejhgx( entity.getJhrejhgx() );
        rktHjslZxbgDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        rktHjslZxbgDTO.setJhrezjhm( entity.getJhrezjhm() );
        rktHjslZxbgDTO.setJhrewwx( entity.getJhrewwx() );
        rktHjslZxbgDTO.setJhrewwm( entity.getJhrewwm() );
        rktHjslZxbgDTO.setJhrelxdh( entity.getJhrelxdh() );
        rktHjslZxbgDTO.setLcywlx( entity.getLcywlx() );
        rktHjslZxbgDTO.setLcslid( entity.getLcslid() );
        rktHjslZxbgDTO.setLcywbt( entity.getLcywbt() );
        rktHjslZxbgDTO.setLcrwjd( entity.getLcrwjd() );
        rktHjslZxbgDTO.setLczt( entity.getLczt() );
        rktHjslZxbgDTO.setBlzt( entity.getBlzt() );
        rktHjslZxbgDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        rktHjslZxbgDTO.setSqrxm( entity.getSqrxm() );
        rktHjslZxbgDTO.setSqrxb( entity.getSqrxb() );
        rktHjslZxbgDTO.setSqrlxdh( entity.getSqrlxdh() );
        rktHjslZxbgDTO.setSqrybdrgx( entity.getSqrybdrgx() );
        rktHjslZxbgDTO.setSqrzzssxq( entity.getSqrzzssxq() );
        rktHjslZxbgDTO.setSqrzzxz( entity.getSqrzzxz() );
        rktHjslZxbgDTO.setSqrhkdjjg( entity.getSqrhkdjjg() );
        rktHjslZxbgDTO.setSqrq( entity.getSqrq() );
        rktHjslZxbgDTO.setSpdwgajgjgdm( entity.getSpdwgajgjgdm() );
        rktHjslZxbgDTO.setSpdwgajgmc( entity.getSpdwgajgmc() );
        rktHjslZxbgDTO.setSprxm( entity.getSprxm() );
        rktHjslZxbgDTO.setSpsj( entity.getSpsj() );
        rktHjslZxbgDTO.setSpjgdm( entity.getSpjgdm() );
        rktHjslZxbgDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        rktHjslZxbgDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        rktHjslZxbgDTO.setSlrxm( entity.getSlrxm() );
        rktHjslZxbgDTO.setSlsj( entity.getSlsj() );
        rktHjslZxbgDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        rktHjslZxbgDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        rktHjslZxbgDTO.setJcwh( entity.getJcwh() );
        rktHjslZxbgDTO.setBz( entity.getBz() );
        rktHjslZxbgDTO.setBggzqnrmc( entity.getBggzqnrmc() );
        rktHjslZxbgDTO.setBggzhnrmc( entity.getBggzhnrmc() );
        rktHjslZxbgDTO.setBlhjywid( entity.getBlhjywid() );
        rktHjslZxbgDTO.setHjywblsj( entity.getHjywblsj() );
        rktHjslZxbgDTO.setQtssxq( entity.getQtssxq() );
        rktHjslZxbgDTO.setQtzz( entity.getQtzz() );

        return rktHjslZxbgDTO;
    }

    @Override
    public RktHjslZxbgDO convertToDO(RktHjslZxbgDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslZxbgDO rktHjslZxbgDO = new RktHjslZxbgDO();

        rktHjslZxbgDO.setId( dto.getId() );
        rktHjslZxbgDO.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslZxbgDO.setYwslh( dto.getYwslh() );
        rktHjslZxbgDO.setHh( dto.getHh() );
        rktHjslZxbgDO.setHhid( dto.getHhid() );
        rktHjslZxbgDO.setRyid( dto.getRyid() );
        rktHjslZxbgDO.setRynbid( dto.getRynbid() );
        rktHjslZxbgDO.setHlx( dto.getHlx() );
        rktHjslZxbgDO.setHmc( dto.getHmc() );
        rktHjslZxbgDO.setHjddzbm( dto.getHjddzbm() );
        rktHjslZxbgDO.setHjdssxq( dto.getHjdssxq() );
        rktHjslZxbgDO.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslZxbgDO.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslZxbgDO.setJlx( dto.getJlx() );
        rktHjslZxbgDO.setMlph( dto.getMlph() );
        rktHjslZxbgDO.setMlxz( dto.getMlxz() );
        rktHjslZxbgDO.setLcdyid( dto.getLcdyid() );
        rktHjslZxbgDO.setLcmc( dto.getLcmc() );
        rktHjslZxbgDO.setSpyj( dto.getSpyj() );
        rktHjslZxbgDO.setXh( dto.getXh() );
        rktHjslZxbgDO.setHhnbid( dto.getHhnbid() );
        rktHjslZxbgDO.setHb( dto.getHb() );
        rktHjslZxbgDO.setYhzgx( dto.getYhzgx() );
        rktHjslZxbgDO.setZjlb( dto.getZjlb() );
        rktHjslZxbgDO.setQfjg( dto.getQfjg() );
        rktHjslZxbgDO.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslZxbgDO.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslZxbgDO.setRkxxbggzlbdm( dto.getRkxxbggzlbdm() );
        rktHjslZxbgDO.setBggzsjxbsf( dto.getBggzsjxbsf() );
        rktHjslZxbgDO.setBggzsjxmc( dto.getBggzsjxmc() );
        rktHjslZxbgDO.setBggzqnr( dto.getBggzqnr() );
        rktHjslZxbgDO.setBggzhnr( dto.getBggzhnr() );
        rktHjslZxbgDO.setHkxz( dto.getHkxz() );
        rktHjslZxbgDO.setCxsx( dto.getCxsx() );
        rktHjslZxbgDO.setGmsfhm( dto.getGmsfhm() );
        rktHjslZxbgDO.setXm( dto.getXm() );
        rktHjslZxbgDO.setX( dto.getX() );
        rktHjslZxbgDO.setM( dto.getM() );
        rktHjslZxbgDO.setCym( dto.getCym() );
        rktHjslZxbgDO.setXmpy( dto.getXmpy() );
        rktHjslZxbgDO.setCympy( dto.getCympy() );
        rktHjslZxbgDO.setXb( dto.getXb() );
        rktHjslZxbgDO.setMz( dto.getMz() );
        rktHjslZxbgDO.setJggjdq( dto.getJggjdq() );
        rktHjslZxbgDO.setJgssxq( dto.getJgssxq() );
        rktHjslZxbgDO.setJgxz( dto.getJgxz() );
        rktHjslZxbgDO.setCsrq( dto.getCsrq() );
        rktHjslZxbgDO.setCssj( dto.getCssj() );
        rktHjslZxbgDO.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslZxbgDO.setCsdssxq( dto.getCsdssxq() );
        rktHjslZxbgDO.setCsdxz( dto.getCsdxz() );
        rktHjslZxbgDO.setWhcd( dto.getWhcd() );
        rktHjslZxbgDO.setHyzk( dto.getHyzk() );
        rktHjslZxbgDO.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslZxbgDO.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslZxbgDO.setZy( dto.getZy() );
        rktHjslZxbgDO.setZylb( dto.getZylb() );
        rktHjslZxbgDO.setZjxy( dto.getZjxy() );
        rktHjslZxbgDO.setSg( dto.getSg() );
        rktHjslZxbgDO.setXx( dto.getXx() );
        rktHjslZxbgDO.setByzk( dto.getByzk() );
        rktHjslZxbgDO.setXxjb( dto.getXxjb() );
        rktHjslZxbgDO.setLxdh( dto.getLxdh() );
        rktHjslZxbgDO.setFqxm( dto.getFqxm() );
        rktHjslZxbgDO.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslZxbgDO.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslZxbgDO.setFqzjhm( dto.getFqzjhm() );
        rktHjslZxbgDO.setFqwwx( dto.getFqwwx() );
        rktHjslZxbgDO.setFqwwm( dto.getFqwwm() );
        rktHjslZxbgDO.setMqxm( dto.getMqxm() );
        rktHjslZxbgDO.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslZxbgDO.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslZxbgDO.setMqzjhm( dto.getMqzjhm() );
        rktHjslZxbgDO.setMqwwx( dto.getMqwwx() );
        rktHjslZxbgDO.setMqwwm( dto.getMqwwm() );
        rktHjslZxbgDO.setPoxm( dto.getPoxm() );
        rktHjslZxbgDO.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslZxbgDO.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslZxbgDO.setPozjhm( dto.getPozjhm() );
        rktHjslZxbgDO.setPowwx( dto.getPowwx() );
        rktHjslZxbgDO.setPowwm( dto.getPowwm() );
        rktHjslZxbgDO.setJhryxm( dto.getJhryxm() );
        rktHjslZxbgDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslZxbgDO.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslZxbgDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslZxbgDO.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslZxbgDO.setJhrywwx( dto.getJhrywwx() );
        rktHjslZxbgDO.setJhrywwm( dto.getJhrywwm() );
        rktHjslZxbgDO.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslZxbgDO.setJhrexm( dto.getJhrexm() );
        rktHjslZxbgDO.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslZxbgDO.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslZxbgDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslZxbgDO.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslZxbgDO.setJhrewwx( dto.getJhrewwx() );
        rktHjslZxbgDO.setJhrewwm( dto.getJhrewwm() );
        rktHjslZxbgDO.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslZxbgDO.setLcywlx( dto.getLcywlx() );
        rktHjslZxbgDO.setLcslid( dto.getLcslid() );
        rktHjslZxbgDO.setLcywbt( dto.getLcywbt() );
        rktHjslZxbgDO.setLcrwjd( dto.getLcrwjd() );
        rktHjslZxbgDO.setLczt( dto.getLczt() );
        rktHjslZxbgDO.setBlzt( dto.getBlzt() );
        rktHjslZxbgDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslZxbgDO.setSqrxm( dto.getSqrxm() );
        rktHjslZxbgDO.setSqrxb( dto.getSqrxb() );
        rktHjslZxbgDO.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslZxbgDO.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslZxbgDO.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslZxbgDO.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslZxbgDO.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslZxbgDO.setSqrq( dto.getSqrq() );
        rktHjslZxbgDO.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslZxbgDO.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslZxbgDO.setSprxm( dto.getSprxm() );
        rktHjslZxbgDO.setSpsj( dto.getSpsj() );
        rktHjslZxbgDO.setSpjgdm( dto.getSpjgdm() );
        rktHjslZxbgDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslZxbgDO.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslZxbgDO.setSlrxm( dto.getSlrxm() );
        rktHjslZxbgDO.setSlsj( dto.getSlsj() );
        rktHjslZxbgDO.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslZxbgDO.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslZxbgDO.setJcwh( dto.getJcwh() );
        rktHjslZxbgDO.setBz( dto.getBz() );
        rktHjslZxbgDO.setBggzqnrmc( dto.getBggzqnrmc() );
        rktHjslZxbgDO.setBggzhnrmc( dto.getBggzhnrmc() );
        rktHjslZxbgDO.setBlhjywid( dto.getBlhjywid() );
        rktHjslZxbgDO.setHjywblsj( dto.getHjywblsj() );
        rktHjslZxbgDO.setQtssxq( dto.getQtssxq() );
        rktHjslZxbgDO.setQtzz( dto.getQtzz() );

        return rktHjslZxbgDO;
    }

    @Override
    public RktHjslZxbgDTO convertToDTO(RktHjslZxbgPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslZxbgDTO rktHjslZxbgDTO = new RktHjslZxbgDTO();

        rktHjslZxbgDTO.setYwslh( req.getYwslh() );
        rktHjslZxbgDTO.setHh( req.getHh() );
        rktHjslZxbgDTO.setHhid( req.getHhid() );
        rktHjslZxbgDTO.setRyid( req.getRyid() );
        rktHjslZxbgDTO.setRynbid( req.getRynbid() );
        rktHjslZxbgDTO.setHlx( req.getHlx() );
        rktHjslZxbgDTO.setHmc( req.getHmc() );
        rktHjslZxbgDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslZxbgDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslZxbgDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslZxbgDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslZxbgDTO.setJlx( req.getJlx() );
        rktHjslZxbgDTO.setMlph( req.getMlph() );
        rktHjslZxbgDTO.setMlxz( req.getMlxz() );
        rktHjslZxbgDTO.setLcdyid( req.getLcdyid() );
        rktHjslZxbgDTO.setLcmc( req.getLcmc() );
        rktHjslZxbgDTO.setSpyj( req.getSpyj() );
        rktHjslZxbgDTO.setXh( req.getXh() );
        rktHjslZxbgDTO.setHhnbid( req.getHhnbid() );
        rktHjslZxbgDTO.setHb( req.getHb() );
        rktHjslZxbgDTO.setYhzgx( req.getYhzgx() );
        rktHjslZxbgDTO.setZjlb( req.getZjlb() );
        rktHjslZxbgDTO.setQfjg( req.getQfjg() );
        rktHjslZxbgDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslZxbgDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslZxbgDTO.setRkxxbggzlbdm( req.getRkxxbggzlbdm() );
        rktHjslZxbgDTO.setBggzsjxbsf( req.getBggzsjxbsf() );
        rktHjslZxbgDTO.setBggzsjxmc( req.getBggzsjxmc() );
        rktHjslZxbgDTO.setBggzqnr( req.getBggzqnr() );
        rktHjslZxbgDTO.setBggzhnr( req.getBggzhnr() );
        rktHjslZxbgDTO.setHkxz( req.getHkxz() );
        rktHjslZxbgDTO.setCxsx( req.getCxsx() );
        rktHjslZxbgDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslZxbgDTO.setXm( req.getXm() );
        rktHjslZxbgDTO.setX( req.getX() );
        rktHjslZxbgDTO.setM( req.getM() );
        rktHjslZxbgDTO.setCym( req.getCym() );
        rktHjslZxbgDTO.setXmpy( req.getXmpy() );
        rktHjslZxbgDTO.setCympy( req.getCympy() );
        rktHjslZxbgDTO.setXb( req.getXb() );
        rktHjslZxbgDTO.setMz( req.getMz() );
        rktHjslZxbgDTO.setJggjdq( req.getJggjdq() );
        rktHjslZxbgDTO.setJgssxq( req.getJgssxq() );
        rktHjslZxbgDTO.setJgxz( req.getJgxz() );
        rktHjslZxbgDTO.setCsrq( req.getCsrq() );
        rktHjslZxbgDTO.setCssj( req.getCssj() );
        rktHjslZxbgDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslZxbgDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslZxbgDTO.setCsdxz( req.getCsdxz() );
        rktHjslZxbgDTO.setWhcd( req.getWhcd() );
        rktHjslZxbgDTO.setHyzk( req.getHyzk() );
        rktHjslZxbgDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslZxbgDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslZxbgDTO.setZy( req.getZy() );
        rktHjslZxbgDTO.setZylb( req.getZylb() );
        rktHjslZxbgDTO.setZjxy( req.getZjxy() );
        rktHjslZxbgDTO.setSg( req.getSg() );
        rktHjslZxbgDTO.setXx( req.getXx() );
        rktHjslZxbgDTO.setByzk( req.getByzk() );
        rktHjslZxbgDTO.setXxjb( req.getXxjb() );
        rktHjslZxbgDTO.setLxdh( req.getLxdh() );
        rktHjslZxbgDTO.setFqxm( req.getFqxm() );
        rktHjslZxbgDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslZxbgDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslZxbgDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslZxbgDTO.setFqwwx( req.getFqwwx() );
        rktHjslZxbgDTO.setFqwwm( req.getFqwwm() );
        rktHjslZxbgDTO.setMqxm( req.getMqxm() );
        rktHjslZxbgDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslZxbgDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslZxbgDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslZxbgDTO.setMqwwx( req.getMqwwx() );
        rktHjslZxbgDTO.setMqwwm( req.getMqwwm() );
        rktHjslZxbgDTO.setPoxm( req.getPoxm() );
        rktHjslZxbgDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslZxbgDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslZxbgDTO.setPozjhm( req.getPozjhm() );
        rktHjslZxbgDTO.setPowwx( req.getPowwx() );
        rktHjslZxbgDTO.setPowwm( req.getPowwm() );
        rktHjslZxbgDTO.setJhryxm( req.getJhryxm() );
        rktHjslZxbgDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslZxbgDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslZxbgDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslZxbgDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslZxbgDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslZxbgDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslZxbgDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslZxbgDTO.setJhrexm( req.getJhrexm() );
        rktHjslZxbgDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslZxbgDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslZxbgDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslZxbgDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslZxbgDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslZxbgDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslZxbgDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslZxbgDTO.setLcywlx( req.getLcywlx() );
        rktHjslZxbgDTO.setLcslid( req.getLcslid() );
        rktHjslZxbgDTO.setLcywbt( req.getLcywbt() );
        rktHjslZxbgDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslZxbgDTO.setLczt( req.getLczt() );
        rktHjslZxbgDTO.setBlzt( req.getBlzt() );
        rktHjslZxbgDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslZxbgDTO.setSqrxm( req.getSqrxm() );
        rktHjslZxbgDTO.setSqrxb( req.getSqrxb() );
        rktHjslZxbgDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslZxbgDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslZxbgDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslZxbgDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslZxbgDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslZxbgDTO.setSqrq( req.getSqrq() );
        rktHjslZxbgDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslZxbgDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslZxbgDTO.setSprxm( req.getSprxm() );
        rktHjslZxbgDTO.setSpsj( req.getSpsj() );
        rktHjslZxbgDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslZxbgDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslZxbgDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslZxbgDTO.setSlrxm( req.getSlrxm() );
        rktHjslZxbgDTO.setSlsj( req.getSlsj() );
        rktHjslZxbgDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslZxbgDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslZxbgDTO.setJcwh( req.getJcwh() );
        rktHjslZxbgDTO.setBz( req.getBz() );
        rktHjslZxbgDTO.setBggzqnrmc( req.getBggzqnrmc() );
        rktHjslZxbgDTO.setBggzhnrmc( req.getBggzhnrmc() );
        rktHjslZxbgDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslZxbgDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslZxbgDTO.setQtssxq( req.getQtssxq() );
        rktHjslZxbgDTO.setQtzz( req.getQtzz() );

        return rktHjslZxbgDTO;
    }

    @Override
    public RktHjslZxbgDTO convertToDTO(RktHjslZxbgCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslZxbgDTO rktHjslZxbgDTO = new RktHjslZxbgDTO();

        rktHjslZxbgDTO.setYwslh( req.getYwslh() );
        rktHjslZxbgDTO.setHh( req.getHh() );
        rktHjslZxbgDTO.setHhid( req.getHhid() );
        rktHjslZxbgDTO.setRyid( req.getRyid() );
        rktHjslZxbgDTO.setRynbid( req.getRynbid() );
        rktHjslZxbgDTO.setHlx( req.getHlx() );
        rktHjslZxbgDTO.setHmc( req.getHmc() );
        rktHjslZxbgDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslZxbgDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslZxbgDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslZxbgDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslZxbgDTO.setJlx( req.getJlx() );
        rktHjslZxbgDTO.setMlph( req.getMlph() );
        rktHjslZxbgDTO.setMlxz( req.getMlxz() );
        rktHjslZxbgDTO.setLcdyid( req.getLcdyid() );
        rktHjslZxbgDTO.setLcmc( req.getLcmc() );
        rktHjslZxbgDTO.setSpyj( req.getSpyj() );
        rktHjslZxbgDTO.setXh( req.getXh() );
        rktHjslZxbgDTO.setHhnbid( req.getHhnbid() );
        rktHjslZxbgDTO.setHb( req.getHb() );
        rktHjslZxbgDTO.setYhzgx( req.getYhzgx() );
        rktHjslZxbgDTO.setZjlb( req.getZjlb() );
        rktHjslZxbgDTO.setQfjg( req.getQfjg() );
        rktHjslZxbgDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslZxbgDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslZxbgDTO.setRkxxbggzlbdm( req.getRkxxbggzlbdm() );
        rktHjslZxbgDTO.setBggzsjxbsf( req.getBggzsjxbsf() );
        rktHjslZxbgDTO.setBggzsjxmc( req.getBggzsjxmc() );
        rktHjslZxbgDTO.setBggzqnr( req.getBggzqnr() );
        rktHjslZxbgDTO.setBggzhnr( req.getBggzhnr() );
        rktHjslZxbgDTO.setHkxz( req.getHkxz() );
        rktHjslZxbgDTO.setCxsx( req.getCxsx() );
        rktHjslZxbgDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslZxbgDTO.setXm( req.getXm() );
        rktHjslZxbgDTO.setX( req.getX() );
        rktHjslZxbgDTO.setM( req.getM() );
        rktHjslZxbgDTO.setCym( req.getCym() );
        rktHjslZxbgDTO.setXmpy( req.getXmpy() );
        rktHjslZxbgDTO.setCympy( req.getCympy() );
        rktHjslZxbgDTO.setXb( req.getXb() );
        rktHjslZxbgDTO.setMz( req.getMz() );
        rktHjslZxbgDTO.setJggjdq( req.getJggjdq() );
        rktHjslZxbgDTO.setJgssxq( req.getJgssxq() );
        rktHjslZxbgDTO.setJgxz( req.getJgxz() );
        rktHjslZxbgDTO.setCsrq( req.getCsrq() );
        rktHjslZxbgDTO.setCssj( req.getCssj() );
        rktHjslZxbgDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslZxbgDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslZxbgDTO.setCsdxz( req.getCsdxz() );
        rktHjslZxbgDTO.setWhcd( req.getWhcd() );
        rktHjslZxbgDTO.setHyzk( req.getHyzk() );
        rktHjslZxbgDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslZxbgDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslZxbgDTO.setZy( req.getZy() );
        rktHjslZxbgDTO.setZylb( req.getZylb() );
        rktHjslZxbgDTO.setZjxy( req.getZjxy() );
        rktHjslZxbgDTO.setSg( req.getSg() );
        rktHjslZxbgDTO.setXx( req.getXx() );
        rktHjslZxbgDTO.setByzk( req.getByzk() );
        rktHjslZxbgDTO.setXxjb( req.getXxjb() );
        rktHjslZxbgDTO.setLxdh( req.getLxdh() );
        rktHjslZxbgDTO.setFqxm( req.getFqxm() );
        rktHjslZxbgDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslZxbgDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslZxbgDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslZxbgDTO.setFqwwx( req.getFqwwx() );
        rktHjslZxbgDTO.setFqwwm( req.getFqwwm() );
        rktHjslZxbgDTO.setMqxm( req.getMqxm() );
        rktHjslZxbgDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslZxbgDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslZxbgDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslZxbgDTO.setMqwwx( req.getMqwwx() );
        rktHjslZxbgDTO.setMqwwm( req.getMqwwm() );
        rktHjslZxbgDTO.setPoxm( req.getPoxm() );
        rktHjslZxbgDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslZxbgDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslZxbgDTO.setPozjhm( req.getPozjhm() );
        rktHjslZxbgDTO.setPowwx( req.getPowwx() );
        rktHjslZxbgDTO.setPowwm( req.getPowwm() );
        rktHjslZxbgDTO.setJhryxm( req.getJhryxm() );
        rktHjslZxbgDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslZxbgDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslZxbgDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslZxbgDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslZxbgDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslZxbgDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslZxbgDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslZxbgDTO.setJhrexm( req.getJhrexm() );
        rktHjslZxbgDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslZxbgDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslZxbgDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslZxbgDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslZxbgDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslZxbgDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslZxbgDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslZxbgDTO.setLcywlx( req.getLcywlx() );
        rktHjslZxbgDTO.setLcslid( req.getLcslid() );
        rktHjslZxbgDTO.setLcywbt( req.getLcywbt() );
        rktHjslZxbgDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslZxbgDTO.setLczt( req.getLczt() );
        rktHjslZxbgDTO.setBlzt( req.getBlzt() );
        rktHjslZxbgDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslZxbgDTO.setSqrxm( req.getSqrxm() );
        rktHjslZxbgDTO.setSqrxb( req.getSqrxb() );
        rktHjslZxbgDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslZxbgDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslZxbgDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslZxbgDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslZxbgDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslZxbgDTO.setSqrq( req.getSqrq() );
        rktHjslZxbgDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslZxbgDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslZxbgDTO.setSprxm( req.getSprxm() );
        rktHjslZxbgDTO.setSpsj( req.getSpsj() );
        rktHjslZxbgDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslZxbgDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslZxbgDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslZxbgDTO.setSlrxm( req.getSlrxm() );
        rktHjslZxbgDTO.setSlsj( req.getSlsj() );
        rktHjslZxbgDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslZxbgDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslZxbgDTO.setJcwh( req.getJcwh() );
        rktHjslZxbgDTO.setBz( req.getBz() );
        rktHjslZxbgDTO.setBggzqnrmc( req.getBggzqnrmc() );
        rktHjslZxbgDTO.setBggzhnrmc( req.getBggzhnrmc() );
        rktHjslZxbgDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslZxbgDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslZxbgDTO.setQtssxq( req.getQtssxq() );
        rktHjslZxbgDTO.setQtzz( req.getQtzz() );

        return rktHjslZxbgDTO;
    }

    @Override
    public RktHjslZxbgDTO convertToDTO(RktHjslZxbgUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslZxbgDTO rktHjslZxbgDTO = new RktHjslZxbgDTO();

        rktHjslZxbgDTO.setYwslnbbh( req.getYwslnbbh() );
        rktHjslZxbgDTO.setYwslh( req.getYwslh() );
        rktHjslZxbgDTO.setHh( req.getHh() );
        rktHjslZxbgDTO.setHhid( req.getHhid() );
        rktHjslZxbgDTO.setRyid( req.getRyid() );
        rktHjslZxbgDTO.setRynbid( req.getRynbid() );
        rktHjslZxbgDTO.setHlx( req.getHlx() );
        rktHjslZxbgDTO.setHmc( req.getHmc() );
        rktHjslZxbgDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslZxbgDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslZxbgDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslZxbgDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslZxbgDTO.setJlx( req.getJlx() );
        rktHjslZxbgDTO.setMlph( req.getMlph() );
        rktHjslZxbgDTO.setMlxz( req.getMlxz() );
        rktHjslZxbgDTO.setLcdyid( req.getLcdyid() );
        rktHjslZxbgDTO.setLcmc( req.getLcmc() );
        rktHjslZxbgDTO.setSpyj( req.getSpyj() );
        rktHjslZxbgDTO.setXh( req.getXh() );
        rktHjslZxbgDTO.setHhnbid( req.getHhnbid() );
        rktHjslZxbgDTO.setHb( req.getHb() );
        rktHjslZxbgDTO.setYhzgx( req.getYhzgx() );
        rktHjslZxbgDTO.setZjlb( req.getZjlb() );
        rktHjslZxbgDTO.setQfjg( req.getQfjg() );
        rktHjslZxbgDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslZxbgDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslZxbgDTO.setRkxxbggzlbdm( req.getRkxxbggzlbdm() );
        rktHjslZxbgDTO.setBggzsjxbsf( req.getBggzsjxbsf() );
        rktHjslZxbgDTO.setBggzsjxmc( req.getBggzsjxmc() );
        rktHjslZxbgDTO.setBggzqnr( req.getBggzqnr() );
        rktHjslZxbgDTO.setBggzhnr( req.getBggzhnr() );
        rktHjslZxbgDTO.setHkxz( req.getHkxz() );
        rktHjslZxbgDTO.setCxsx( req.getCxsx() );
        rktHjslZxbgDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslZxbgDTO.setXm( req.getXm() );
        rktHjslZxbgDTO.setX( req.getX() );
        rktHjslZxbgDTO.setM( req.getM() );
        rktHjslZxbgDTO.setCym( req.getCym() );
        rktHjslZxbgDTO.setXmpy( req.getXmpy() );
        rktHjslZxbgDTO.setCympy( req.getCympy() );
        rktHjslZxbgDTO.setXb( req.getXb() );
        rktHjslZxbgDTO.setMz( req.getMz() );
        rktHjslZxbgDTO.setJggjdq( req.getJggjdq() );
        rktHjslZxbgDTO.setJgssxq( req.getJgssxq() );
        rktHjslZxbgDTO.setJgxz( req.getJgxz() );
        rktHjslZxbgDTO.setCsrq( req.getCsrq() );
        rktHjslZxbgDTO.setCssj( req.getCssj() );
        rktHjslZxbgDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslZxbgDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslZxbgDTO.setCsdxz( req.getCsdxz() );
        rktHjslZxbgDTO.setWhcd( req.getWhcd() );
        rktHjslZxbgDTO.setHyzk( req.getHyzk() );
        rktHjslZxbgDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslZxbgDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslZxbgDTO.setZy( req.getZy() );
        rktHjslZxbgDTO.setZylb( req.getZylb() );
        rktHjslZxbgDTO.setZjxy( req.getZjxy() );
        rktHjslZxbgDTO.setSg( req.getSg() );
        rktHjslZxbgDTO.setXx( req.getXx() );
        rktHjslZxbgDTO.setByzk( req.getByzk() );
        rktHjslZxbgDTO.setXxjb( req.getXxjb() );
        rktHjslZxbgDTO.setLxdh( req.getLxdh() );
        rktHjslZxbgDTO.setFqxm( req.getFqxm() );
        rktHjslZxbgDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslZxbgDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslZxbgDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslZxbgDTO.setFqwwx( req.getFqwwx() );
        rktHjslZxbgDTO.setFqwwm( req.getFqwwm() );
        rktHjslZxbgDTO.setMqxm( req.getMqxm() );
        rktHjslZxbgDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslZxbgDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslZxbgDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslZxbgDTO.setMqwwx( req.getMqwwx() );
        rktHjslZxbgDTO.setMqwwm( req.getMqwwm() );
        rktHjslZxbgDTO.setPoxm( req.getPoxm() );
        rktHjslZxbgDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslZxbgDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslZxbgDTO.setPozjhm( req.getPozjhm() );
        rktHjslZxbgDTO.setPowwx( req.getPowwx() );
        rktHjslZxbgDTO.setPowwm( req.getPowwm() );
        rktHjslZxbgDTO.setJhryxm( req.getJhryxm() );
        rktHjslZxbgDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslZxbgDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslZxbgDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslZxbgDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslZxbgDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslZxbgDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslZxbgDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslZxbgDTO.setJhrexm( req.getJhrexm() );
        rktHjslZxbgDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslZxbgDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslZxbgDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslZxbgDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslZxbgDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslZxbgDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslZxbgDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslZxbgDTO.setLcywlx( req.getLcywlx() );
        rktHjslZxbgDTO.setLcslid( req.getLcslid() );
        rktHjslZxbgDTO.setLcywbt( req.getLcywbt() );
        rktHjslZxbgDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslZxbgDTO.setLczt( req.getLczt() );
        rktHjslZxbgDTO.setBlzt( req.getBlzt() );
        rktHjslZxbgDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslZxbgDTO.setSqrxm( req.getSqrxm() );
        rktHjslZxbgDTO.setSqrxb( req.getSqrxb() );
        rktHjslZxbgDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslZxbgDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslZxbgDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslZxbgDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslZxbgDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslZxbgDTO.setSqrq( req.getSqrq() );
        rktHjslZxbgDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslZxbgDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslZxbgDTO.setSprxm( req.getSprxm() );
        rktHjslZxbgDTO.setSpsj( req.getSpsj() );
        rktHjslZxbgDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslZxbgDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslZxbgDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslZxbgDTO.setSlrxm( req.getSlrxm() );
        rktHjslZxbgDTO.setSlsj( req.getSlsj() );
        rktHjslZxbgDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslZxbgDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslZxbgDTO.setJcwh( req.getJcwh() );
        rktHjslZxbgDTO.setBz( req.getBz() );
        rktHjslZxbgDTO.setBggzqnrmc( req.getBggzqnrmc() );
        rktHjslZxbgDTO.setBggzhnrmc( req.getBggzhnrmc() );
        rktHjslZxbgDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslZxbgDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslZxbgDTO.setQtssxq( req.getQtssxq() );
        rktHjslZxbgDTO.setQtzz( req.getQtzz() );

        return rktHjslZxbgDTO;
    }

    @Override
    public RktHjslZxbgPageResp convertToPageResp(RktHjslZxbgDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslZxbgPageResp rktHjslZxbgPageResp = new RktHjslZxbgPageResp();

        rktHjslZxbgPageResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslZxbgPageResp.setYwslh( dto.getYwslh() );
        rktHjslZxbgPageResp.setHh( dto.getHh() );
        rktHjslZxbgPageResp.setHhid( dto.getHhid() );
        rktHjslZxbgPageResp.setRyid( dto.getRyid() );
        rktHjslZxbgPageResp.setRynbid( dto.getRynbid() );
        rktHjslZxbgPageResp.setHlx( dto.getHlx() );
        rktHjslZxbgPageResp.setHmc( dto.getHmc() );
        rktHjslZxbgPageResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslZxbgPageResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslZxbgPageResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslZxbgPageResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslZxbgPageResp.setJlx( dto.getJlx() );
        rktHjslZxbgPageResp.setMlph( dto.getMlph() );
        rktHjslZxbgPageResp.setMlxz( dto.getMlxz() );
        rktHjslZxbgPageResp.setLcdyid( dto.getLcdyid() );
        rktHjslZxbgPageResp.setLcmc( dto.getLcmc() );
        rktHjslZxbgPageResp.setSpyj( dto.getSpyj() );
        rktHjslZxbgPageResp.setXh( dto.getXh() );
        rktHjslZxbgPageResp.setHhnbid( dto.getHhnbid() );
        rktHjslZxbgPageResp.setHb( dto.getHb() );
        rktHjslZxbgPageResp.setYhzgx( dto.getYhzgx() );
        rktHjslZxbgPageResp.setZjlb( dto.getZjlb() );
        rktHjslZxbgPageResp.setQfjg( dto.getQfjg() );
        rktHjslZxbgPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslZxbgPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslZxbgPageResp.setRkxxbggzlbdm( dto.getRkxxbggzlbdm() );
        rktHjslZxbgPageResp.setBggzsjxbsf( dto.getBggzsjxbsf() );
        rktHjslZxbgPageResp.setBggzsjxmc( dto.getBggzsjxmc() );
        rktHjslZxbgPageResp.setBggzqnr( dto.getBggzqnr() );
        rktHjslZxbgPageResp.setBggzhnr( dto.getBggzhnr() );
        rktHjslZxbgPageResp.setHkxz( dto.getHkxz() );
        rktHjslZxbgPageResp.setCxsx( dto.getCxsx() );
        rktHjslZxbgPageResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslZxbgPageResp.setXm( dto.getXm() );
        rktHjslZxbgPageResp.setX( dto.getX() );
        rktHjslZxbgPageResp.setM( dto.getM() );
        rktHjslZxbgPageResp.setCym( dto.getCym() );
        rktHjslZxbgPageResp.setXmpy( dto.getXmpy() );
        rktHjslZxbgPageResp.setCympy( dto.getCympy() );
        rktHjslZxbgPageResp.setXb( dto.getXb() );
        rktHjslZxbgPageResp.setMz( dto.getMz() );
        rktHjslZxbgPageResp.setJggjdq( dto.getJggjdq() );
        rktHjslZxbgPageResp.setJgssxq( dto.getJgssxq() );
        rktHjslZxbgPageResp.setJgxz( dto.getJgxz() );
        rktHjslZxbgPageResp.setCsrq( dto.getCsrq() );
        rktHjslZxbgPageResp.setCssj( dto.getCssj() );
        rktHjslZxbgPageResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslZxbgPageResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslZxbgPageResp.setCsdxz( dto.getCsdxz() );
        rktHjslZxbgPageResp.setWhcd( dto.getWhcd() );
        rktHjslZxbgPageResp.setHyzk( dto.getHyzk() );
        rktHjslZxbgPageResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslZxbgPageResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslZxbgPageResp.setZy( dto.getZy() );
        rktHjslZxbgPageResp.setZylb( dto.getZylb() );
        rktHjslZxbgPageResp.setZjxy( dto.getZjxy() );
        rktHjslZxbgPageResp.setSg( dto.getSg() );
        rktHjslZxbgPageResp.setXx( dto.getXx() );
        rktHjslZxbgPageResp.setByzk( dto.getByzk() );
        rktHjslZxbgPageResp.setXxjb( dto.getXxjb() );
        rktHjslZxbgPageResp.setLxdh( dto.getLxdh() );
        rktHjslZxbgPageResp.setFqxm( dto.getFqxm() );
        rktHjslZxbgPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslZxbgPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslZxbgPageResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslZxbgPageResp.setFqwwx( dto.getFqwwx() );
        rktHjslZxbgPageResp.setFqwwm( dto.getFqwwm() );
        rktHjslZxbgPageResp.setMqxm( dto.getMqxm() );
        rktHjslZxbgPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslZxbgPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslZxbgPageResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslZxbgPageResp.setMqwwx( dto.getMqwwx() );
        rktHjslZxbgPageResp.setMqwwm( dto.getMqwwm() );
        rktHjslZxbgPageResp.setPoxm( dto.getPoxm() );
        rktHjslZxbgPageResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslZxbgPageResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslZxbgPageResp.setPozjhm( dto.getPozjhm() );
        rktHjslZxbgPageResp.setPowwx( dto.getPowwx() );
        rktHjslZxbgPageResp.setPowwm( dto.getPowwm() );
        rktHjslZxbgPageResp.setJhryxm( dto.getJhryxm() );
        rktHjslZxbgPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslZxbgPageResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslZxbgPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslZxbgPageResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslZxbgPageResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslZxbgPageResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslZxbgPageResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslZxbgPageResp.setJhrexm( dto.getJhrexm() );
        rktHjslZxbgPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslZxbgPageResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslZxbgPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslZxbgPageResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslZxbgPageResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslZxbgPageResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslZxbgPageResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslZxbgPageResp.setLcywlx( dto.getLcywlx() );
        rktHjslZxbgPageResp.setLcslid( dto.getLcslid() );
        rktHjslZxbgPageResp.setLcywbt( dto.getLcywbt() );
        rktHjslZxbgPageResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslZxbgPageResp.setLczt( dto.getLczt() );
        rktHjslZxbgPageResp.setBlzt( dto.getBlzt() );
        rktHjslZxbgPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslZxbgPageResp.setSqrxm( dto.getSqrxm() );
        rktHjslZxbgPageResp.setSqrxb( dto.getSqrxb() );
        rktHjslZxbgPageResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslZxbgPageResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslZxbgPageResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslZxbgPageResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslZxbgPageResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslZxbgPageResp.setSqrq( dto.getSqrq() );
        rktHjslZxbgPageResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslZxbgPageResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslZxbgPageResp.setSprxm( dto.getSprxm() );
        rktHjslZxbgPageResp.setSpsj( dto.getSpsj() );
        rktHjslZxbgPageResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslZxbgPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslZxbgPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslZxbgPageResp.setSlrxm( dto.getSlrxm() );
        rktHjslZxbgPageResp.setSlsj( dto.getSlsj() );
        rktHjslZxbgPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslZxbgPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslZxbgPageResp.setJcwh( dto.getJcwh() );
        rktHjslZxbgPageResp.setBz( dto.getBz() );
        rktHjslZxbgPageResp.setBggzqnrmc( dto.getBggzqnrmc() );
        rktHjslZxbgPageResp.setBggzhnrmc( dto.getBggzhnrmc() );
        rktHjslZxbgPageResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslZxbgPageResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslZxbgPageResp.setQtssxq( dto.getQtssxq() );
        rktHjslZxbgPageResp.setQtzz( dto.getQtzz() );

        return rktHjslZxbgPageResp;
    }

    @Override
    public RktHjslZxbgViewResp convertToViewResp(RktHjslZxbgDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslZxbgViewResp rktHjslZxbgViewResp = new RktHjslZxbgViewResp();

        rktHjslZxbgViewResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslZxbgViewResp.setYwslh( dto.getYwslh() );
        rktHjslZxbgViewResp.setHh( dto.getHh() );
        rktHjslZxbgViewResp.setHhid( dto.getHhid() );
        rktHjslZxbgViewResp.setRyid( dto.getRyid() );
        rktHjslZxbgViewResp.setRynbid( dto.getRynbid() );
        rktHjslZxbgViewResp.setHlx( dto.getHlx() );
        rktHjslZxbgViewResp.setHmc( dto.getHmc() );
        rktHjslZxbgViewResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslZxbgViewResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslZxbgViewResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslZxbgViewResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslZxbgViewResp.setJlx( dto.getJlx() );
        rktHjslZxbgViewResp.setMlph( dto.getMlph() );
        rktHjslZxbgViewResp.setMlxz( dto.getMlxz() );
        rktHjslZxbgViewResp.setLcdyid( dto.getLcdyid() );
        rktHjslZxbgViewResp.setLcmc( dto.getLcmc() );
        rktHjslZxbgViewResp.setSpyj( dto.getSpyj() );
        rktHjslZxbgViewResp.setXh( dto.getXh() );
        rktHjslZxbgViewResp.setHhnbid( dto.getHhnbid() );
        rktHjslZxbgViewResp.setHb( dto.getHb() );
        rktHjslZxbgViewResp.setYhzgx( dto.getYhzgx() );
        rktHjslZxbgViewResp.setZjlb( dto.getZjlb() );
        rktHjslZxbgViewResp.setQfjg( dto.getQfjg() );
        rktHjslZxbgViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslZxbgViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslZxbgViewResp.setRkxxbggzlbdm( dto.getRkxxbggzlbdm() );
        rktHjslZxbgViewResp.setBggzsjxbsf( dto.getBggzsjxbsf() );
        rktHjslZxbgViewResp.setBggzsjxmc( dto.getBggzsjxmc() );
        rktHjslZxbgViewResp.setBggzqnr( dto.getBggzqnr() );
        rktHjslZxbgViewResp.setBggzhnr( dto.getBggzhnr() );
        rktHjslZxbgViewResp.setHkxz( dto.getHkxz() );
        rktHjslZxbgViewResp.setCxsx( dto.getCxsx() );
        rktHjslZxbgViewResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslZxbgViewResp.setXm( dto.getXm() );
        rktHjslZxbgViewResp.setX( dto.getX() );
        rktHjslZxbgViewResp.setM( dto.getM() );
        rktHjslZxbgViewResp.setCym( dto.getCym() );
        rktHjslZxbgViewResp.setXmpy( dto.getXmpy() );
        rktHjslZxbgViewResp.setCympy( dto.getCympy() );
        rktHjslZxbgViewResp.setXb( dto.getXb() );
        rktHjslZxbgViewResp.setMz( dto.getMz() );
        rktHjslZxbgViewResp.setJggjdq( dto.getJggjdq() );
        rktHjslZxbgViewResp.setJgssxq( dto.getJgssxq() );
        rktHjslZxbgViewResp.setJgxz( dto.getJgxz() );
        rktHjslZxbgViewResp.setCsrq( dto.getCsrq() );
        rktHjslZxbgViewResp.setCssj( dto.getCssj() );
        rktHjslZxbgViewResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslZxbgViewResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslZxbgViewResp.setCsdxz( dto.getCsdxz() );
        rktHjslZxbgViewResp.setWhcd( dto.getWhcd() );
        rktHjslZxbgViewResp.setHyzk( dto.getHyzk() );
        rktHjslZxbgViewResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslZxbgViewResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslZxbgViewResp.setZy( dto.getZy() );
        rktHjslZxbgViewResp.setZylb( dto.getZylb() );
        rktHjslZxbgViewResp.setZjxy( dto.getZjxy() );
        rktHjslZxbgViewResp.setSg( dto.getSg() );
        rktHjslZxbgViewResp.setXx( dto.getXx() );
        rktHjslZxbgViewResp.setByzk( dto.getByzk() );
        rktHjslZxbgViewResp.setXxjb( dto.getXxjb() );
        rktHjslZxbgViewResp.setLxdh( dto.getLxdh() );
        rktHjslZxbgViewResp.setFqxm( dto.getFqxm() );
        rktHjslZxbgViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslZxbgViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslZxbgViewResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslZxbgViewResp.setFqwwx( dto.getFqwwx() );
        rktHjslZxbgViewResp.setFqwwm( dto.getFqwwm() );
        rktHjslZxbgViewResp.setMqxm( dto.getMqxm() );
        rktHjslZxbgViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslZxbgViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslZxbgViewResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslZxbgViewResp.setMqwwx( dto.getMqwwx() );
        rktHjslZxbgViewResp.setMqwwm( dto.getMqwwm() );
        rktHjslZxbgViewResp.setPoxm( dto.getPoxm() );
        rktHjslZxbgViewResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslZxbgViewResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslZxbgViewResp.setPozjhm( dto.getPozjhm() );
        rktHjslZxbgViewResp.setPowwx( dto.getPowwx() );
        rktHjslZxbgViewResp.setPowwm( dto.getPowwm() );
        rktHjslZxbgViewResp.setJhryxm( dto.getJhryxm() );
        rktHjslZxbgViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslZxbgViewResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslZxbgViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslZxbgViewResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslZxbgViewResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslZxbgViewResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslZxbgViewResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslZxbgViewResp.setJhrexm( dto.getJhrexm() );
        rktHjslZxbgViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslZxbgViewResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslZxbgViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslZxbgViewResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslZxbgViewResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslZxbgViewResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslZxbgViewResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslZxbgViewResp.setLcywlx( dto.getLcywlx() );
        rktHjslZxbgViewResp.setLcslid( dto.getLcslid() );
        rktHjslZxbgViewResp.setLcywbt( dto.getLcywbt() );
        rktHjslZxbgViewResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslZxbgViewResp.setLczt( dto.getLczt() );
        rktHjslZxbgViewResp.setBlzt( dto.getBlzt() );
        rktHjslZxbgViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslZxbgViewResp.setSqrxm( dto.getSqrxm() );
        rktHjslZxbgViewResp.setSqrxb( dto.getSqrxb() );
        rktHjslZxbgViewResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslZxbgViewResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslZxbgViewResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslZxbgViewResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslZxbgViewResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslZxbgViewResp.setSqrq( dto.getSqrq() );
        rktHjslZxbgViewResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslZxbgViewResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslZxbgViewResp.setSprxm( dto.getSprxm() );
        rktHjslZxbgViewResp.setSpsj( dto.getSpsj() );
        rktHjslZxbgViewResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslZxbgViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslZxbgViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslZxbgViewResp.setSlrxm( dto.getSlrxm() );
        rktHjslZxbgViewResp.setSlsj( dto.getSlsj() );
        rktHjslZxbgViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslZxbgViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslZxbgViewResp.setJcwh( dto.getJcwh() );
        rktHjslZxbgViewResp.setBz( dto.getBz() );
        rktHjslZxbgViewResp.setBggzqnrmc( dto.getBggzqnrmc() );
        rktHjslZxbgViewResp.setBggzhnrmc( dto.getBggzhnrmc() );
        rktHjslZxbgViewResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslZxbgViewResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslZxbgViewResp.setQtssxq( dto.getQtssxq() );
        rktHjslZxbgViewResp.setQtzz( dto.getQtzz() );

        return rktHjslZxbgViewResp;
    }

    @Override
    public RktHjslZxbgCreateResp convertToCreateResp(RktHjslZxbgDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslZxbgCreateResp rktHjslZxbgCreateResp = new RktHjslZxbgCreateResp();

        rktHjslZxbgCreateResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslZxbgCreateResp.setYwslh( dto.getYwslh() );
        rktHjslZxbgCreateResp.setHh( dto.getHh() );
        rktHjslZxbgCreateResp.setHhid( dto.getHhid() );
        rktHjslZxbgCreateResp.setRyid( dto.getRyid() );
        rktHjslZxbgCreateResp.setRynbid( dto.getRynbid() );
        rktHjslZxbgCreateResp.setHlx( dto.getHlx() );
        rktHjslZxbgCreateResp.setHmc( dto.getHmc() );
        rktHjslZxbgCreateResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslZxbgCreateResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslZxbgCreateResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslZxbgCreateResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslZxbgCreateResp.setJlx( dto.getJlx() );
        rktHjslZxbgCreateResp.setMlph( dto.getMlph() );
        rktHjslZxbgCreateResp.setMlxz( dto.getMlxz() );
        rktHjslZxbgCreateResp.setLcdyid( dto.getLcdyid() );
        rktHjslZxbgCreateResp.setLcmc( dto.getLcmc() );
        rktHjslZxbgCreateResp.setSpyj( dto.getSpyj() );
        rktHjslZxbgCreateResp.setXh( dto.getXh() );
        rktHjslZxbgCreateResp.setHhnbid( dto.getHhnbid() );
        rktHjslZxbgCreateResp.setHb( dto.getHb() );
        rktHjslZxbgCreateResp.setYhzgx( dto.getYhzgx() );
        rktHjslZxbgCreateResp.setZjlb( dto.getZjlb() );
        rktHjslZxbgCreateResp.setQfjg( dto.getQfjg() );
        rktHjslZxbgCreateResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslZxbgCreateResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslZxbgCreateResp.setRkxxbggzlbdm( dto.getRkxxbggzlbdm() );
        rktHjslZxbgCreateResp.setBggzsjxbsf( dto.getBggzsjxbsf() );
        rktHjslZxbgCreateResp.setBggzsjxmc( dto.getBggzsjxmc() );
        rktHjslZxbgCreateResp.setBggzqnr( dto.getBggzqnr() );
        rktHjslZxbgCreateResp.setBggzhnr( dto.getBggzhnr() );
        rktHjslZxbgCreateResp.setHkxz( dto.getHkxz() );
        rktHjslZxbgCreateResp.setCxsx( dto.getCxsx() );
        rktHjslZxbgCreateResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslZxbgCreateResp.setXm( dto.getXm() );
        rktHjslZxbgCreateResp.setX( dto.getX() );
        rktHjslZxbgCreateResp.setM( dto.getM() );
        rktHjslZxbgCreateResp.setCym( dto.getCym() );
        rktHjslZxbgCreateResp.setXmpy( dto.getXmpy() );
        rktHjslZxbgCreateResp.setCympy( dto.getCympy() );
        rktHjslZxbgCreateResp.setXb( dto.getXb() );
        rktHjslZxbgCreateResp.setMz( dto.getMz() );
        rktHjslZxbgCreateResp.setJggjdq( dto.getJggjdq() );
        rktHjslZxbgCreateResp.setJgssxq( dto.getJgssxq() );
        rktHjslZxbgCreateResp.setJgxz( dto.getJgxz() );
        rktHjslZxbgCreateResp.setCsrq( dto.getCsrq() );
        rktHjslZxbgCreateResp.setCssj( dto.getCssj() );
        rktHjslZxbgCreateResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslZxbgCreateResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslZxbgCreateResp.setCsdxz( dto.getCsdxz() );
        rktHjslZxbgCreateResp.setWhcd( dto.getWhcd() );
        rktHjslZxbgCreateResp.setHyzk( dto.getHyzk() );
        rktHjslZxbgCreateResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslZxbgCreateResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslZxbgCreateResp.setZy( dto.getZy() );
        rktHjslZxbgCreateResp.setZylb( dto.getZylb() );
        rktHjslZxbgCreateResp.setZjxy( dto.getZjxy() );
        rktHjslZxbgCreateResp.setSg( dto.getSg() );
        rktHjslZxbgCreateResp.setXx( dto.getXx() );
        rktHjslZxbgCreateResp.setByzk( dto.getByzk() );
        rktHjslZxbgCreateResp.setXxjb( dto.getXxjb() );
        rktHjslZxbgCreateResp.setLxdh( dto.getLxdh() );
        rktHjslZxbgCreateResp.setFqxm( dto.getFqxm() );
        rktHjslZxbgCreateResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslZxbgCreateResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslZxbgCreateResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslZxbgCreateResp.setFqwwx( dto.getFqwwx() );
        rktHjslZxbgCreateResp.setFqwwm( dto.getFqwwm() );
        rktHjslZxbgCreateResp.setMqxm( dto.getMqxm() );
        rktHjslZxbgCreateResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslZxbgCreateResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslZxbgCreateResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslZxbgCreateResp.setMqwwx( dto.getMqwwx() );
        rktHjslZxbgCreateResp.setMqwwm( dto.getMqwwm() );
        rktHjslZxbgCreateResp.setPoxm( dto.getPoxm() );
        rktHjslZxbgCreateResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslZxbgCreateResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslZxbgCreateResp.setPozjhm( dto.getPozjhm() );
        rktHjslZxbgCreateResp.setPowwx( dto.getPowwx() );
        rktHjslZxbgCreateResp.setPowwm( dto.getPowwm() );
        rktHjslZxbgCreateResp.setJhryxm( dto.getJhryxm() );
        rktHjslZxbgCreateResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslZxbgCreateResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslZxbgCreateResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslZxbgCreateResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslZxbgCreateResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslZxbgCreateResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslZxbgCreateResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslZxbgCreateResp.setJhrexm( dto.getJhrexm() );
        rktHjslZxbgCreateResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslZxbgCreateResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslZxbgCreateResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslZxbgCreateResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslZxbgCreateResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslZxbgCreateResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslZxbgCreateResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslZxbgCreateResp.setLcywlx( dto.getLcywlx() );
        rktHjslZxbgCreateResp.setLcslid( dto.getLcslid() );
        rktHjslZxbgCreateResp.setLcywbt( dto.getLcywbt() );
        rktHjslZxbgCreateResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslZxbgCreateResp.setLczt( dto.getLczt() );
        rktHjslZxbgCreateResp.setBlzt( dto.getBlzt() );
        rktHjslZxbgCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslZxbgCreateResp.setSqrxm( dto.getSqrxm() );
        rktHjslZxbgCreateResp.setSqrxb( dto.getSqrxb() );
        rktHjslZxbgCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslZxbgCreateResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslZxbgCreateResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslZxbgCreateResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslZxbgCreateResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslZxbgCreateResp.setSqrq( dto.getSqrq() );
        rktHjslZxbgCreateResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslZxbgCreateResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslZxbgCreateResp.setSprxm( dto.getSprxm() );
        rktHjslZxbgCreateResp.setSpsj( dto.getSpsj() );
        rktHjslZxbgCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslZxbgCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslZxbgCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslZxbgCreateResp.setSlrxm( dto.getSlrxm() );
        rktHjslZxbgCreateResp.setSlsj( dto.getSlsj() );
        rktHjslZxbgCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslZxbgCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslZxbgCreateResp.setJcwh( dto.getJcwh() );
        rktHjslZxbgCreateResp.setBz( dto.getBz() );
        rktHjslZxbgCreateResp.setBggzqnrmc( dto.getBggzqnrmc() );
        rktHjslZxbgCreateResp.setBggzhnrmc( dto.getBggzhnrmc() );
        rktHjslZxbgCreateResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslZxbgCreateResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslZxbgCreateResp.setQtssxq( dto.getQtssxq() );
        rktHjslZxbgCreateResp.setQtzz( dto.getQtzz() );

        return rktHjslZxbgCreateResp;
    }
}
