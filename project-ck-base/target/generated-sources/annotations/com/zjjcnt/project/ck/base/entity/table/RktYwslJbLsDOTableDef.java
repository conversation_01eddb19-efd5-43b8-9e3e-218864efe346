package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class RktYwslJbLsDOTableDef extends TableDef {

    /**
     * 业务受理情况基本信息DO

 <AUTHOR>
 @date 2025-07-22 13:26:28
 @see com.zjjcnt.project.ck.base.dto.RktYwslJbLsDTO
     */
    public static final RktYwslJbLsDOTableDef RKT_YWSL_JB_LS_DO = new RktYwslJbLsDOTableDef();

    /**
     * 备注
     */
    public final QueryColumn BZ = new QueryColumn(this, "bz");

    /**
     * 姓名
     */
    public final QueryColumn XM = new QueryColumn(this, "xm");

    public final QueryColumn BLZT = new QueryColumn(this, "blzt");

    /**
     * 户主姓名
     */
    public final QueryColumn HZXM = new QueryColumn(this, "hzxm");

    /**
     * 居（村）委会
     */
    public final QueryColumn JCWH = new QueryColumn(this, "jcwh");

    /**
     * 快递单号
     */
    public final QueryColumn KDDH = new QueryColumn(this, "kddh");

    public final QueryColumn LCMC = new QueryColumn(this, "lcmc");

    /**
     * 01数据采集中
            02民警审核中
            10派出所领导审批中
            20区县业务部门审批中
            30区县领导审批中
            40地市业务部门审批中
            50地市领导审批中
            60审批已通过
            70审批不通过
            80业务已办理
            91作废
            99已打印归档
     */
    public final QueryColumn LCZT = new QueryColumn(this, "lczt");

    /**
     * 评价结果
     */
    public final QueryColumn PJJG = new QueryColumn(this, "pjjg");

    /**
     * 评价时间
     */
    public final QueryColumn PJSJ = new QueryColumn(this, "pjsj");

    /**
     * 上报状态10受理中30办结可以上报80上报完成
     */
    public final QueryColumn SBZT = new QueryColumn(this, "sbzt");

    public final QueryColumn SLRS = new QueryColumn(this, "slrs");

    /**
     * 受理时间
     */
    public final QueryColumn SLSJ = new QueryColumn(this, "slsj");

    public final QueryColumn SPSJ = new QueryColumn(this, "spsj");

    public final QueryColumn SPYJ = new QueryColumn(this, "spyj");

    public final QueryColumn SQRQ = new QueryColumn(this, "sqrq");

    /**
     * 其它申请人
     */
    public final QueryColumn QTSQR = new QueryColumn(this, "qtsqr");

    /**
     * 入立户标志
     */
    public final QueryColumn RLHBZ = new QueryColumn(this, "rlhbz");

    /**
     * 收件人姓名
     */
    public final QueryColumn SJRXM = new QueryColumn(this, "sjrxm");

    /**
     * 受理人ID
     */
    public final QueryColumn SLRID = new QueryColumn(this, "slrid");

    /**
     * 受理人姓名
     */
    public final QueryColumn SLRXM = new QueryColumn(this, "slrxm");

    public final QueryColumn SPRXM = new QueryColumn(this, "sprxm");

    public final QueryColumn SQRXB = new QueryColumn(this, "sqrxb");

    /**
     * 申请人_姓名
     */
    public final QueryColumn SQRXM = new QueryColumn(this, "sqrxm");

    public final QueryColumn YWSLH = new QueryColumn(this, "ywslh");

    /**
     * 档案备案标志
     */
    public final QueryColumn DABABZ = new QueryColumn(this, "dababz");

    /**
     * 档案备案时间
     */
    public final QueryColumn DABASJ = new QueryColumn(this, "dabasj");

    /**
     * 打印出件类型
     */
    public final QueryColumn DYCJLX = new QueryColumn(this, "dycjlx");

    /**
     * 公民身份号码
     */
    public final QueryColumn GMSFHM = new QueryColumn(this, "gmsfhm");

    /**
     * 流程定义ID，格式为 流程KEY：版本号：部署号
     */
    public final QueryColumn LCDYID = new QueryColumn(this, "lcdyid");

    public final QueryColumn LCRWJD = new QueryColumn(this, "lcrwjd");

    public final QueryColumn LCSLID = new QueryColumn(this, "lcslid");

    /**
     * 关于**等几人落户的申请报告
     */
    public final QueryColumn LCYWBT = new QueryColumn(this, "lcywbt");

    /**
     * 金铖流程业务类型，扩展自D_ZAGLYWFL治安管理业务分类与代码
            0100	实有人口管理	
            0101	　户籍管理	
            0102	　居民身份证管理
     */
    public final QueryColumn LCYWLX = new QueryColumn(this, "lcywlx");

    /**
     * 跑了几次评价
     */
    public final QueryColumn PJPLJC = new QueryColumn(this, "pjpljc");

    /**
     * 人社局审核人
     */
    public final QueryColumn RSJSHR = new QueryColumn(this, "rsjshr");

    /**
     * 上报办结时间
     */
    public final QueryColumn SBBJSJ = new QueryColumn(this, "sbbjsj");

    /**
     * 上报申报时间
     */
    public final QueryColumn SBSBSJ = new QueryColumn(this, "sbsbsj");

    /**
     * 是否需要上报0否1是
     */
    public final QueryColumn SBSFXY = new QueryColumn(this, "sbsfxy");

    /**
     * 上报审核时间
     */
    public final QueryColumn SBSHSJ = new QueryColumn(this, "sbshsj");

    /**
     * 上报受理时间
     */
    public final QueryColumn SBSLSJ = new QueryColumn(this, "sbslsj");

    /**
     * 上报审批意见
     */
    public final QueryColumn SBSPYJ = new QueryColumn(this, "sbspyj");

    /**
     * 上报业务类型代码
     */
    public final QueryColumn SBYWLX = new QueryColumn(this, "sbywlx");

    /**
     * 上报业务名称
     */
    public final QueryColumn SBYWMC = new QueryColumn(this, "sbywmc");

    public final QueryColumn SPJGDM = new QueryColumn(this, "spjgdm");

    /**
     * 审批信息来源
     */
    public final QueryColumn SPXXLY = new QueryColumn(this, "spxxly");

    /**
     * 申请查询理由
     */
    public final QueryColumn SQCXLY = new QueryColumn(this, "sqcxly");

    /**
     * 互联网业务办理类型
     */
    public final QueryColumn BUSTYPE = new QueryColumn(this, "bustype");

    /**
     * 高技能证书编号
     */
    public final QueryColumn GJNZSBH = new QueryColumn(this, "gjnzsbh");

    /**
     * 互联网申请类型
     */
    public final QueryColumn HLWSQLX = new QueryColumn(this, "hlwsqlx");

    /**
     * 人社局审核结果
     */
    public final QueryColumn RSJSHJG = new QueryColumn(this, "rsjshjg");

    /**
     * 人社局审核理由
     */
    public final QueryColumn RSJSHLY = new QueryColumn(this, "rsjshly");

    /**
     * 人社局审核时间
     */
    public final QueryColumn RSJSHSJ = new QueryColumn(this, "rsjshsj");

    /**
     * 上报审批人姓名
     */
    public final QueryColumn SBSPRXM = new QueryColumn(this, "sbsprxm");

    /**
     * 收件人联系电话
     */
    public final QueryColumn SJRLXDH = new QueryColumn(this, "sjrlxdh");

    /**
     * 受理人联系电话
     */
    public final QueryColumn SLRLXDH = new QueryColumn(this, "slrlxdh");

    /**
     * 审批人联系电话
     */
    public final QueryColumn SPRLXDH = new QueryColumn(this, "sprlxdh");

    /**
     * 申请人工作单位
     */
    public final QueryColumn SQRGZDW = new QueryColumn(this, "sqrgzdw");

    public final QueryColumn SQRLXDH = new QueryColumn(this, "sqrlxdh");

    /**
     * 申请人通讯地址
     */
    public final QueryColumn SQRTXDZ = new QueryColumn(this, "sqrtxdz");

    /**
     * 申请人证件号码
     */
    public final QueryColumn SQRZJHM = new QueryColumn(this, "sqrzjhm");

    /**
     * 申请人证件名称
     */
    public final QueryColumn SQRZJMC = new QueryColumn(this, "sqrzjmc");

    /**
     * 申请人住址详址
     */
    public final QueryColumn SQRZZXZ = new QueryColumn(this, "sqrzzxz");

    /**
     * 预申报信息编号
     */
    public final QueryColumn YSBXXBH = new QueryColumn(this, "ysbxxbh");

    /**
     * 政务网大厅编号
     */
    public final QueryColumn ZWWDTBH = new QueryColumn(this, "zwwdtbh");

    /**
     * 政务网大厅名称
     */
    public final QueryColumn ZWWDTMC = new QueryColumn(this, "zwwdtmc");

    /**
     * 办理户籍业务id
     */
    public final QueryColumn BLHJYWID = new QueryColumn(this, "blhjywid");

    /**
     * 常表是否标准签名
     */
    public final QueryColumn CBSFBZQM = new QueryColumn(this, "cbsfbzqm");

    /**
     * 户籍事项查询类别
     */
    public final QueryColumn HJSXCXLB = new QueryColumn(this, "hjsxcxlb");

    /**
     * 户籍业务办理时间
     */
    public final QueryColumn HJYWBLSJ = new QueryColumn(this, "hjywblsj");

    /**
     * 户主公民身份号码
     */
    public final QueryColumn HZGMSFHM = new QueryColumn(this, "hzgmsfhm");

    /**
     * 户籍地民警首次审核耗时(分)
     */
    public final QueryColumn MJSCSHHS = new QueryColumn(this, "mjscshhs");

    /**
     * 上报审批结果代码
     */
    public final QueryColumn SBSPJGDM = new QueryColumn(this, "sbspjgdm");

    /**
     * 上报审批结束时间
     */
    public final QueryColumn SBSPJSSJ = new QueryColumn(this, "sbspjssj");

    /**
     * 上报审批开始时间
     */
    public final QueryColumn SBSPKSSJ = new QueryColumn(this, "sbspkssj");

    /**
     * 是否打印出生原因
     */
    public final QueryColumn SFDYCSYY = new QueryColumn(this, "sfdycsyy");

    /**
     * 是否打印全户人员
     */
    public final QueryColumn SFDYQHRY = new QueryColumn(this, "sfdyqhry");

    /**
     * 是否跨省协同迁入
     */
    public final QueryColumn SFKSXTQR = new QueryColumn(this, "sfksxtqr");

    /**
     * 是否需要邮寄证件
     */
    public final QueryColumn SFXYYJZJ = new QueryColumn(this, "sfxyyjzj");

    /**
     * 是否已经邮寄证件
     */
    public final QueryColumn SFYJYJZJ = new QueryColumn(this, "sfyjyjzj");

    /**
     * 数据归属单位代码
     */
    public final QueryColumn SJGSDWDM = new QueryColumn(this, "sjgsdwdm");

    /**
     * 数据归属单位名称
     */
    public final QueryColumn SJGSDWMC = new QueryColumn(this, "sjgsdwmc");

    /**
     * 审批信息采集方式
     */
    public final QueryColumn SPXXCJFS = new QueryColumn(this, "spxxcjfs");

    /**
     * 审批信息来源类别
     */
    public final QueryColumn SPXXLYLB = new QueryColumn(this, "spxxlylb");

    /**
     * 互联网申报来源
     */
    public final QueryColumn APPLYFROM = new QueryColumn(this, "applyfrom");

    /**
     * 变更一件事上报标志
     */
    public final QueryColumn BGYJSSBBZ = new QueryColumn(this, "bgyjssbbz");

    /**
     * 变更一件事上报时间
     */
    public final QueryColumn BGYJSSBSJ = new QueryColumn(this, "bgyjssbsj");

    /**
     * 其它申请人联系电话
     */
    public final QueryColumn QTSQRLXDH = new QueryColumn(this, "qtsqrlxdh");

    /**
     * 上报审批单位代码
     */
    public final QueryColumn SBSPRDWDM = new QueryColumn(this, "sbsprdwdm");

    /**
     * 是否打印户口簿首页
     */
    public final QueryColumn SFDYHKBSY = new QueryColumn(this, "sfdyhkbsy");

    /**
     * 是否提取互联网材料0否1是
     */
    public final QueryColumn SFTQHLWCL = new QueryColumn(this, "sftqhlwcl");

    /**
     * 申请表是否标准签名
     */
    public final QueryColumn SQBSFBZQM = new QueryColumn(this, "sqbsfbzqm");

    /**
     * 申请人_公民身份号码
     */
    public final QueryColumn SQRGMSFHM = new QueryColumn(this, "sqrgmsfhm");

    /**
     * 申请人工作单位类别
     */
    public final QueryColumn SQRGZDWLB = new QueryColumn(this, "sqrgzdwlb");

    /**
     * 申请人户口登记机关
     */
    public final QueryColumn SQRHKDJJG = new QueryColumn(this, "sqrhkdjjg");

    /**
     * 申请人人像比对结果
     */
    public final QueryColumn SQRRXBDJG = new QueryColumn(this, "sqrrxbdjg");

    /**
     * 申请人人像比对时间
     */
    public final QueryColumn SQRRXBDSJ = new QueryColumn(this, "sqrrxbdsj");

    public final QueryColumn SQRYBDRGX = new QueryColumn(this, "sqrybdrgx");

    /**
     * 申请人住址省市县（区）
     */
    public final QueryColumn SQRZZSSXQ = new QueryColumn(this, "sqrzzssxq");

    /**
     * 政务大厅用户编号
     */
    public final QueryColumn HALLUSERID = new QueryColumn(this, "halluserid");

    /**
     * 受理单位_公安机关名称
     */
    public final QueryColumn SLDWGAJGMC = new QueryColumn(this, "sldwgajgmc");

    public final QueryColumn SPDWGAJGMC = new QueryColumn(this, "spdwgajgmc");

    /**
     * 申请人人像比对相似度
     */
    public final QueryColumn SQRRXBDXSD = new QueryColumn(this, "sqrrxbdxsd");

    /**
     * 其它申请人公民身份号码
     */
    public final QueryColumn QTSQRGMSFHM = new QueryColumn(this, "qtsqrgmsfhm");

    /**
     * 是否按照集体户方式打印
     */
    public final QueryColumn SFAZJTHFSDY = new QueryColumn(this, "sfazjthfsdy");

    /**
     * 是否生成电子签章户口页
     */
    public final QueryColumn SFSCDZQZHKY = new QueryColumn(this, "sfscdzqzhky");

    /**
     * 原申报系统业务文件类型
     */
    public final QueryColumn YSBXTYWWJLX = new QueryColumn(this, "ysbxtywwjlx");

    /**
     * 原申报系统业务文件名称
     */
    public final QueryColumn YSBXTYWWJMC = new QueryColumn(this, "ysbxtywwjmc");

    /**
     * 政务大厅用户名称
     */
    public final QueryColumn HALLUSERNAME = new QueryColumn(this, "hallusername");

    /**
     * 受理单位_公安机关机构代码
     */
    public final QueryColumn SLDWGAJGJGDM = new QueryColumn(this, "sldwgajgjgdm");

    public final QueryColumn SPDWGAJGJGDM = new QueryColumn(this, "spdwgajgjgdm");

    /**
     * 申请人工作单位统一社会信用代码
     */
    public final QueryColumn SQRGZDWTYSHXYDM = new QueryColumn(this, "sqrgzdwtyshxydm");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{BZ, XM, BLZT, HZXM, JCWH, KDDH, LCMC, LCZT, PJJG, PJSJ, SBZT, SLRS, SLSJ, SPSJ, SPYJ, SQRQ, QTSQR, RLHBZ, SJRXM, SLRID, SLRXM, SPRXM, SQRXB, SQRXM, YWSLH, DABABZ, DABASJ, DYCJLX, GMSFHM, LCDYID, LCRWJD, LCSLID, LCYWBT, LCYWLX, PJPLJC, RSJSHR, SBBJSJ, SBSBSJ, SBSFXY, SBSHSJ, SBSLSJ, SBSPYJ, SBYWLX, SBYWMC, SPJGDM, SPXXLY, SQCXLY, BUSTYPE, GJNZSBH, HLWSQLX, RSJSHJG, RSJSHLY, RSJSHSJ, SBSPRXM, SJRLXDH, SLRLXDH, SPRLXDH, SQRGZDW, SQRLXDH, SQRTXDZ, SQRZJHM, SQRZJMC, SQRZZXZ, YSBXXBH, ZWWDTBH, ZWWDTMC, BLHJYWID, CBSFBZQM, HJSXCXLB, HJYWBLSJ, HZGMSFHM, MJSCSHHS, SBSPJGDM, SBSPJSSJ, SBSPKSSJ, SFDYCSYY, SFDYQHRY, SFKSXTQR, SFXYYJZJ, SFYJYJZJ, SJGSDWDM, SJGSDWMC, SPXXCJFS, SPXXLYLB, APPLYFROM, BGYJSSBBZ, BGYJSSBSJ, QTSQRLXDH, SBSPRDWDM, SFDYHKBSY, SFTQHLWCL, SQBSFBZQM, SQRGMSFHM, SQRGZDWLB, SQRHKDJJG, SQRRXBDJG, SQRRXBDSJ, SQRYBDRGX, SQRZZSSXQ, HALLUSERID, SLDWGAJGMC, SPDWGAJGMC, SQRRXBDXSD, QTSQRGMSFHM, SFAZJTHFSDY, SFSCDZQZHKY, YSBXTYWWJLX, YSBXTYWWJMC, HALLUSERNAME, SLDWGAJGJGDM, SPDWGAJGJGDM, SQRGZDWTYSHXYDM};

    public RktYwslJbLsDOTableDef() {
        super("", "rkt_ywsl_jb_ls");
    }

    private RktYwslJbLsDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public RktYwslJbLsDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new RktYwslJbLsDOTableDef("", "rkt_ywsl_jb_ls", alias));
    }

}
