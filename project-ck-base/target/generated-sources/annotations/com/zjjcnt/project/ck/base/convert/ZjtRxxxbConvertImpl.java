package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtRxxxbDTO;
import com.zjjcnt.project.ck.base.dto.resp.ZjtRxxxbViewResp;
import com.zjjcnt.project.ck.base.entity.ZjtRxxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtRxxxbConvertImpl implements ZjtRxxxbConvert {

    @Override
    public ZjtRxxxbDTO convert(ZjtRxxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtRxxxbDTO zjtRxxxbDTO = new ZjtRxxxbDTO();

        zjtRxxxbDTO.setId( entity.getId() );
        zjtRxxxbDTO.setZpid( entity.getZpid() );
        zjtRxxxbDTO.setRyid( entity.getRyid() );
        zjtRxxxbDTO.setGmsfhm( entity.getGmsfhm() );
        zjtRxxxbDTO.setXm( entity.getXm() );
        zjtRxxxbDTO.setZpwjbh( entity.getZpwjbh() );
        zjtRxxxbDTO.setLrrq( entity.getLrrq() );
        zjtRxxxbDTO.setCyzjdm( entity.getCyzjdm() );
        zjtRxxxbDTO.setZjhm( entity.getZjhm() );
        zjtRxxxbDTO.setWwx( entity.getWwx() );
        zjtRxxxbDTO.setWwm( entity.getWwm() );
        zjtRxxxbDTO.setXplx( entity.getXplx() );
        zjtRxxxbDTO.setYwlb( entity.getYwlb() );
        zjtRxxxbDTO.setYwlsh( entity.getYwlsh() );
        zjtRxxxbDTO.setZxsj( entity.getZxsj() );
        zjtRxxxbDTO.setCjsj( entity.getCjsj() );

        return zjtRxxxbDTO;
    }

    @Override
    public ZjtRxxxbDO convertToDO(ZjtRxxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtRxxxbDO zjtRxxxbDO = new ZjtRxxxbDO();

        zjtRxxxbDO.setId( dto.getId() );
        zjtRxxxbDO.setZpid( dto.getZpid() );
        zjtRxxxbDO.setRyid( dto.getRyid() );
        zjtRxxxbDO.setGmsfhm( dto.getGmsfhm() );
        zjtRxxxbDO.setXm( dto.getXm() );
        zjtRxxxbDO.setZpwjbh( dto.getZpwjbh() );
        zjtRxxxbDO.setLrrq( dto.getLrrq() );
        zjtRxxxbDO.setCyzjdm( dto.getCyzjdm() );
        zjtRxxxbDO.setZjhm( dto.getZjhm() );
        zjtRxxxbDO.setWwx( dto.getWwx() );
        zjtRxxxbDO.setWwm( dto.getWwm() );
        zjtRxxxbDO.setXplx( dto.getXplx() );
        zjtRxxxbDO.setYwlb( dto.getYwlb() );
        zjtRxxxbDO.setYwlsh( dto.getYwlsh() );
        zjtRxxxbDO.setZxsj( dto.getZxsj() );
        zjtRxxxbDO.setCjsj( dto.getCjsj() );

        return zjtRxxxbDO;
    }

    @Override
    public ZjtRxxxbViewResp convertToViewResp(ZjtRxxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtRxxxbViewResp zjtRxxxbViewResp = new ZjtRxxxbViewResp();

        zjtRxxxbViewResp.setZpid( dto.getZpid() );
        zjtRxxxbViewResp.setRyid( dto.getRyid() );
        zjtRxxxbViewResp.setGmsfhm( dto.getGmsfhm() );
        zjtRxxxbViewResp.setXm( dto.getXm() );
        zjtRxxxbViewResp.setZpwjbh( dto.getZpwjbh() );
        zjtRxxxbViewResp.setLrrq( dto.getLrrq() );
        zjtRxxxbViewResp.setCyzjdm( dto.getCyzjdm() );
        zjtRxxxbViewResp.setZjhm( dto.getZjhm() );
        zjtRxxxbViewResp.setWwx( dto.getWwx() );
        zjtRxxxbViewResp.setWwm( dto.getWwm() );
        zjtRxxxbViewResp.setXplx( dto.getXplx() );
        zjtRxxxbViewResp.setYwlb( dto.getYwlb() );
        zjtRxxxbViewResp.setYwlsh( dto.getYwlsh() );
        zjtRxxxbViewResp.setBase64zp( dto.getBase64zp() );

        return zjtRxxxbViewResp;
    }
}
