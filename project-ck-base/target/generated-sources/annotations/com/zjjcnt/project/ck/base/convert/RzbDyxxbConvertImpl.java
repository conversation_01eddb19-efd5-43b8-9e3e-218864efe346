package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RzbDyxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.RzbDyxxbCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RzbDyxxbPageReq;
import com.zjjcnt.project.ck.base.dto.req.RzbDyxxbUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RzbDyxxbCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RzbDyxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RzbDyxxbViewResp;
import com.zjjcnt.project.ck.base.entity.RzbDyxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RzbDyxxbConvertImpl implements RzbDyxxbConvert {

    @Override
    public RzbDyxxbDTO convert(RzbDyxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        RzbDyxxbDTO rzbDyxxbDTO = new RzbDyxxbDTO();

        rzbDyxxbDTO.setId( entity.getId() );
        rzbDyxxbDTO.setDyid( entity.getDyid() );
        rzbDyxxbDTO.setRyid( entity.getRyid() );
        rzbDyxxbDTO.setGmsfhm( entity.getGmsfhm() );
        rzbDyxxbDTO.setXm( entity.getXm() );
        rzbDyxxbDTO.setDylb( entity.getDylb() );
        rzbDyxxbDTO.setZjbh( entity.getZjbh() );
        rzbDyxxbDTO.setYznf( entity.getYznf() );
        rzbDyxxbDTO.setCzsj( entity.getCzsj() );
        rzbDyxxbDTO.setCzrid( entity.getCzrid() );
        rzbDyxxbDTO.setCzyxm( entity.getCzyxm() );
        rzbDyxxbDTO.setCzydwdm( entity.getCzydwdm() );
        rzbDyxxbDTO.setCzydwmc( entity.getCzydwmc() );
        rzbDyxxbDTO.setCzip( entity.getCzip() );
        rzbDyxxbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        rzbDyxxbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        rzbDyxxbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        rzbDyxxbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        rzbDyxxbDTO.setYwslh( entity.getYwslh() );
        rzbDyxxbDTO.setSlclbh( entity.getSlclbh() );
        rzbDyxxbDTO.setClmc( entity.getClmc() );
        rzbDyxxbDTO.setLcywlx( entity.getLcywlx() );

        return rzbDyxxbDTO;
    }

    @Override
    public RzbDyxxbDO convertToDO(RzbDyxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzbDyxxbDO rzbDyxxbDO = new RzbDyxxbDO();

        rzbDyxxbDO.setId( dto.getId() );
        rzbDyxxbDO.setDyid( dto.getDyid() );
        rzbDyxxbDO.setRyid( dto.getRyid() );
        rzbDyxxbDO.setGmsfhm( dto.getGmsfhm() );
        rzbDyxxbDO.setXm( dto.getXm() );
        rzbDyxxbDO.setDylb( dto.getDylb() );
        rzbDyxxbDO.setZjbh( dto.getZjbh() );
        rzbDyxxbDO.setYznf( dto.getYznf() );
        rzbDyxxbDO.setCzsj( dto.getCzsj() );
        rzbDyxxbDO.setCzrid( dto.getCzrid() );
        rzbDyxxbDO.setCzyxm( dto.getCzyxm() );
        rzbDyxxbDO.setCzydwdm( dto.getCzydwdm() );
        rzbDyxxbDO.setCzydwmc( dto.getCzydwmc() );
        rzbDyxxbDO.setCzip( dto.getCzip() );
        rzbDyxxbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        rzbDyxxbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        rzbDyxxbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        rzbDyxxbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        rzbDyxxbDO.setYwslh( dto.getYwslh() );
        rzbDyxxbDO.setSlclbh( dto.getSlclbh() );
        rzbDyxxbDO.setClmc( dto.getClmc() );
        rzbDyxxbDO.setLcywlx( dto.getLcywlx() );

        return rzbDyxxbDO;
    }

    @Override
    public RzbDyxxbDTO convertToDTO(RzbDyxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        RzbDyxxbDTO rzbDyxxbDTO = new RzbDyxxbDTO();

        rzbDyxxbDTO.setYwslh( req.getYwslh() );

        return rzbDyxxbDTO;
    }

    @Override
    public RzbDyxxbDTO convertToDTO(RzbDyxxbCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RzbDyxxbDTO rzbDyxxbDTO = new RzbDyxxbDTO();

        rzbDyxxbDTO.setRyid( req.getRyid() );
        rzbDyxxbDTO.setGmsfhm( req.getGmsfhm() );
        rzbDyxxbDTO.setXm( req.getXm() );
        rzbDyxxbDTO.setDylb( req.getDylb() );
        rzbDyxxbDTO.setZjbh( req.getZjbh() );
        rzbDyxxbDTO.setYznf( req.getYznf() );
        rzbDyxxbDTO.setCzsj( req.getCzsj() );
        rzbDyxxbDTO.setCzrid( req.getCzrid() );
        rzbDyxxbDTO.setCzyxm( req.getCzyxm() );
        rzbDyxxbDTO.setCzydwdm( req.getCzydwdm() );
        rzbDyxxbDTO.setCzydwmc( req.getCzydwmc() );
        rzbDyxxbDTO.setCzip( req.getCzip() );
        rzbDyxxbDTO.setHjdsjgsdwdm( req.getHjdsjgsdwdm() );
        rzbDyxxbDTO.setHjdsjgsdwmc( req.getHjdsjgsdwmc() );
        rzbDyxxbDTO.setSldsjgsdwdm( req.getSldsjgsdwdm() );
        rzbDyxxbDTO.setSldsjgsdwmc( req.getSldsjgsdwmc() );
        rzbDyxxbDTO.setYwslh( req.getYwslh() );
        rzbDyxxbDTO.setSlclbh( req.getSlclbh() );
        rzbDyxxbDTO.setClmc( req.getClmc() );
        rzbDyxxbDTO.setLcywlx( req.getLcywlx() );

        return rzbDyxxbDTO;
    }

    @Override
    public RzbDyxxbDTO convertToDTO(RzbDyxxbUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RzbDyxxbDTO rzbDyxxbDTO = new RzbDyxxbDTO();

        rzbDyxxbDTO.setDyid( req.getDyid() );
        rzbDyxxbDTO.setRyid( req.getRyid() );
        rzbDyxxbDTO.setGmsfhm( req.getGmsfhm() );
        rzbDyxxbDTO.setXm( req.getXm() );
        rzbDyxxbDTO.setDylb( req.getDylb() );
        rzbDyxxbDTO.setZjbh( req.getZjbh() );
        rzbDyxxbDTO.setYznf( req.getYznf() );
        rzbDyxxbDTO.setCzsj( req.getCzsj() );
        rzbDyxxbDTO.setCzrid( req.getCzrid() );
        rzbDyxxbDTO.setCzyxm( req.getCzyxm() );
        rzbDyxxbDTO.setCzydwdm( req.getCzydwdm() );
        rzbDyxxbDTO.setCzydwmc( req.getCzydwmc() );
        rzbDyxxbDTO.setCzip( req.getCzip() );
        rzbDyxxbDTO.setHjdsjgsdwdm( req.getHjdsjgsdwdm() );
        rzbDyxxbDTO.setHjdsjgsdwmc( req.getHjdsjgsdwmc() );
        rzbDyxxbDTO.setSldsjgsdwdm( req.getSldsjgsdwdm() );
        rzbDyxxbDTO.setSldsjgsdwmc( req.getSldsjgsdwmc() );
        rzbDyxxbDTO.setYwslh( req.getYwslh() );
        rzbDyxxbDTO.setSlclbh( req.getSlclbh() );
        rzbDyxxbDTO.setClmc( req.getClmc() );
        rzbDyxxbDTO.setLcywlx( req.getLcywlx() );

        return rzbDyxxbDTO;
    }

    @Override
    public RzbDyxxbPageResp convertToPageResp(RzbDyxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzbDyxxbPageResp rzbDyxxbPageResp = new RzbDyxxbPageResp();

        rzbDyxxbPageResp.setDyid( dto.getDyid() );
        rzbDyxxbPageResp.setRyid( dto.getRyid() );
        rzbDyxxbPageResp.setXm( dto.getXm() );
        rzbDyxxbPageResp.setGmsfhm( dto.getGmsfhm() );
        rzbDyxxbPageResp.setDylb( dto.getDylb() );
        rzbDyxxbPageResp.setZjbh( dto.getZjbh() );
        rzbDyxxbPageResp.setYznf( dto.getYznf() );
        rzbDyxxbPageResp.setClmc( dto.getClmc() );
        rzbDyxxbPageResp.setLcywlx( dto.getLcywlx() );
        rzbDyxxbPageResp.setCzsj( dto.getCzsj() );
        rzbDyxxbPageResp.setCzrid( dto.getCzrid() );
        rzbDyxxbPageResp.setCzyxm( dto.getCzyxm() );
        rzbDyxxbPageResp.setCzydwdm( dto.getCzydwdm() );
        rzbDyxxbPageResp.setCzydwmc( dto.getCzydwmc() );
        rzbDyxxbPageResp.setCzip( dto.getCzip() );
        rzbDyxxbPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        rzbDyxxbPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        rzbDyxxbPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        rzbDyxxbPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        rzbDyxxbPageResp.setYwslh( dto.getYwslh() );
        rzbDyxxbPageResp.setSlclbh( dto.getSlclbh() );

        return rzbDyxxbPageResp;
    }

    @Override
    public RzbDyxxbViewResp convertToViewResp(RzbDyxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzbDyxxbViewResp rzbDyxxbViewResp = new RzbDyxxbViewResp();

        rzbDyxxbViewResp.setDyid( dto.getDyid() );
        rzbDyxxbViewResp.setRyid( dto.getRyid() );
        rzbDyxxbViewResp.setGmsfhm( dto.getGmsfhm() );
        rzbDyxxbViewResp.setXm( dto.getXm() );
        rzbDyxxbViewResp.setDylb( dto.getDylb() );
        rzbDyxxbViewResp.setZjbh( dto.getZjbh() );
        rzbDyxxbViewResp.setYznf( dto.getYznf() );
        rzbDyxxbViewResp.setCzsj( dto.getCzsj() );
        rzbDyxxbViewResp.setCzrid( dto.getCzrid() );
        rzbDyxxbViewResp.setCzyxm( dto.getCzyxm() );
        rzbDyxxbViewResp.setCzydwdm( dto.getCzydwdm() );
        rzbDyxxbViewResp.setCzydwmc( dto.getCzydwmc() );
        rzbDyxxbViewResp.setCzip( dto.getCzip() );
        rzbDyxxbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        rzbDyxxbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        rzbDyxxbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        rzbDyxxbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        rzbDyxxbViewResp.setYwslh( dto.getYwslh() );
        rzbDyxxbViewResp.setSlclbh( dto.getSlclbh() );
        rzbDyxxbViewResp.setClmc( dto.getClmc() );
        rzbDyxxbViewResp.setLcywlx( dto.getLcywlx() );

        return rzbDyxxbViewResp;
    }

    @Override
    public RzbDyxxbCreateResp convertToCreateResp(RzbDyxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzbDyxxbCreateResp rzbDyxxbCreateResp = new RzbDyxxbCreateResp();

        rzbDyxxbCreateResp.setDyid( dto.getDyid() );
        rzbDyxxbCreateResp.setRyid( dto.getRyid() );
        rzbDyxxbCreateResp.setGmsfhm( dto.getGmsfhm() );
        rzbDyxxbCreateResp.setXm( dto.getXm() );
        rzbDyxxbCreateResp.setDylb( dto.getDylb() );
        rzbDyxxbCreateResp.setZjbh( dto.getZjbh() );
        rzbDyxxbCreateResp.setYznf( dto.getYznf() );
        rzbDyxxbCreateResp.setCzsj( dto.getCzsj() );
        rzbDyxxbCreateResp.setCzrid( dto.getCzrid() );
        rzbDyxxbCreateResp.setCzyxm( dto.getCzyxm() );
        rzbDyxxbCreateResp.setCzydwdm( dto.getCzydwdm() );
        rzbDyxxbCreateResp.setCzydwmc( dto.getCzydwmc() );
        rzbDyxxbCreateResp.setCzip( dto.getCzip() );
        rzbDyxxbCreateResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        rzbDyxxbCreateResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        rzbDyxxbCreateResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        rzbDyxxbCreateResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        rzbDyxxbCreateResp.setYwslh( dto.getYwslh() );
        rzbDyxxbCreateResp.setSlclbh( dto.getSlclbh() );
        rzbDyxxbCreateResp.setClmc( dto.getClmc() );
        rzbDyxxbCreateResp.setLcywlx( dto.getLcywlx() );

        return rzbDyxxbCreateResp;
    }
}
