package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.CrjScgjCzrzbDTO;
import com.zjjcnt.project.ck.base.entity.CrjScgjCzrzbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:41+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class CrjScgjCzrzbConvertImpl implements CrjScgjCzrzbConvert {

    @Override
    public CrjScgjCzrzbDTO convert(CrjScgjCzrzbDO entity) {
        if ( entity == null ) {
            return null;
        }

        CrjScgjCzrzbDTO crjScgjCzrzbDTO = new CrjScgjCzrzbDTO();

        crjScgjCzrzbDTO.setId( entity.getId() );
        crjScgjCzrzbDTO.setScgjrzid( entity.getScgjrzid() );
        crjScgjCzrzbDTO.setScgjryid( entity.getScgjryid() );
        crjScgjCzrzbDTO.setClbz( entity.getClbz() );
        crjScgjCzrzbDTO.setCznr( entity.getCznr() );
        crjScgjCzrzbDTO.setCzsj( entity.getCzsj() );
        crjScgjCzrzbDTO.setCzrip( entity.getCzrip() );
        crjScgjCzrzbDTO.setCzrxm( entity.getCzrxm() );
        crjScgjCzrzbDTO.setCzrid( entity.getCzrid() );

        return crjScgjCzrzbDTO;
    }

    @Override
    public CrjScgjCzrzbDO convertToDO(CrjScgjCzrzbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjCzrzbDO crjScgjCzrzbDO = new CrjScgjCzrzbDO();

        crjScgjCzrzbDO.setId( dto.getId() );
        crjScgjCzrzbDO.setScgjrzid( dto.getScgjrzid() );
        crjScgjCzrzbDO.setScgjryid( dto.getScgjryid() );
        crjScgjCzrzbDO.setClbz( dto.getClbz() );
        crjScgjCzrzbDO.setCznr( dto.getCznr() );
        crjScgjCzrzbDO.setCzsj( dto.getCzsj() );
        crjScgjCzrzbDO.setCzrip( dto.getCzrip() );
        crjScgjCzrzbDO.setCzrxm( dto.getCzrxm() );
        crjScgjCzrzbDO.setCzrid( dto.getCzrid() );

        return crjScgjCzrzbDO;
    }
}
