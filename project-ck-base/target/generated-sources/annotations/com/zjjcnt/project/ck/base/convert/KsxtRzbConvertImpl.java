package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsxtRzbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtRzbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtRzbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtRzbViewResp;
import com.zjjcnt.project.ck.base.entity.KsxtRzbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:42+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsxtRzbConvertImpl implements KsxtRzbConvert {

    @Override
    public KsxtRzbDTO convert(KsxtRzbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsxtRzbDTO ksxtRzbDTO = new KsxtRzbDTO();

        ksxtRzbDTO.setId( entity.getId() );
        ksxtRzbDTO.setKsxtid( entity.getKsxtid() );
        ksxtRzbDTO.setZaglywxtbh( entity.getZaglywxtbh() );
        ksxtRzbDTO.setBlzt( entity.getBlzt() );
        ksxtRzbDTO.setCzsj( entity.getCzsj() );
        ksxtRzbDTO.setCzyid( entity.getCzyid() );
        ksxtRzbDTO.setCzyxm( entity.getCzyxm() );
        ksxtRzbDTO.setCzip( entity.getCzip() );
        ksxtRzbDTO.setCzydwdm( entity.getCzydwdm() );
        ksxtRzbDTO.setCzydwmc( entity.getCzydwmc() );
        ksxtRzbDTO.setBz( entity.getBz() );

        return ksxtRzbDTO;
    }

    @Override
    public KsxtRzbDO convertToDO(KsxtRzbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtRzbDO ksxtRzbDO = new KsxtRzbDO();

        ksxtRzbDO.setId( dto.getId() );
        ksxtRzbDO.setKsxtid( dto.getKsxtid() );
        ksxtRzbDO.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtRzbDO.setBlzt( dto.getBlzt() );
        ksxtRzbDO.setCzsj( dto.getCzsj() );
        ksxtRzbDO.setCzyid( dto.getCzyid() );
        ksxtRzbDO.setCzyxm( dto.getCzyxm() );
        ksxtRzbDO.setCzip( dto.getCzip() );
        ksxtRzbDO.setCzydwdm( dto.getCzydwdm() );
        ksxtRzbDO.setCzydwmc( dto.getCzydwmc() );
        ksxtRzbDO.setBz( dto.getBz() );

        return ksxtRzbDO;
    }

    @Override
    public KsxtRzbDTO convertToDTO(KsxtRzbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsxtRzbDTO ksxtRzbDTO = new KsxtRzbDTO();

        ksxtRzbDTO.setKsxtid( req.getKsxtid() );

        return ksxtRzbDTO;
    }

    @Override
    public KsxtRzbPageResp convertToPageResp(KsxtRzbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtRzbPageResp ksxtRzbPageResp = new KsxtRzbPageResp();

        ksxtRzbPageResp.setId( dto.getId() );
        ksxtRzbPageResp.setKsxtid( dto.getKsxtid() );
        ksxtRzbPageResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtRzbPageResp.setBlzt( dto.getBlzt() );
        ksxtRzbPageResp.setCzsj( dto.getCzsj() );
        ksxtRzbPageResp.setCzyid( dto.getCzyid() );
        ksxtRzbPageResp.setCzyxm( dto.getCzyxm() );
        ksxtRzbPageResp.setCzip( dto.getCzip() );
        ksxtRzbPageResp.setCzydwdm( dto.getCzydwdm() );
        ksxtRzbPageResp.setCzydwmc( dto.getCzydwmc() );
        ksxtRzbPageResp.setBz( dto.getBz() );

        return ksxtRzbPageResp;
    }

    @Override
    public KsxtRzbViewResp convertToViewResp(KsxtRzbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtRzbViewResp ksxtRzbViewResp = new KsxtRzbViewResp();

        ksxtRzbViewResp.setId( dto.getId() );
        ksxtRzbViewResp.setKsxtid( dto.getKsxtid() );
        ksxtRzbViewResp.setZaglywxtbh( dto.getZaglywxtbh() );
        ksxtRzbViewResp.setBlzt( dto.getBlzt() );
        ksxtRzbViewResp.setCzsj( dto.getCzsj() );
        ksxtRzbViewResp.setCzyid( dto.getCzyid() );
        ksxtRzbViewResp.setCzyxm( dto.getCzyxm() );
        ksxtRzbViewResp.setCzip( dto.getCzip() );
        ksxtRzbViewResp.setCzydwdm( dto.getCzydwdm() );
        ksxtRzbViewResp.setCzydwmc( dto.getCzydwmc() );
        ksxtRzbViewResp.setBz( dto.getBz() );

        return ksxtRzbViewResp;
    }
}
