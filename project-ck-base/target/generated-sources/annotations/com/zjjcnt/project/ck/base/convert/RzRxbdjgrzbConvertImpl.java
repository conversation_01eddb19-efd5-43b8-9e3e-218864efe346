package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RzRxbdjgrzbDTO;
import com.zjjcnt.project.ck.base.entity.RzRxbdjgrzbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:46+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RzRxbdjgrzbConvertImpl implements RzRxbdjgrzbConvert {

    @Override
    public RzRxbdjgrzbDTO convert(RzRxbdjgrzbDO entity) {
        if ( entity == null ) {
            return null;
        }

        RzRxbdjgrzbDTO rzRxbdjgrzbDTO = new RzRxbdjgrzbDTO();

        rzRxbdjgrzbDTO.setId( entity.getId() );
        rzRxbdjgrzbDTO.setRxbdjgid( entity.getRxbdjgid() );
        rzRxbdjgrzbDTO.setRxbdrzid( entity.getRxbdrzid() );
        rzRxbdjgrzbDTO.setGmsfhm( entity.getGmsfhm() );
        rzRxbdjgrzbDTO.setRxid( entity.getRxid() );
        rzRxbdjgrzbDTO.setBdxsd( entity.getBdxsd() );
        rzRxbdjgrzbDTO.setBdxm( entity.getBdxm() );
        rzRxbdjgrzbDTO.setBdgmsfhm( entity.getBdgmsfhm() );
        rzRxbdjgrzbDTO.setBdmbkbh( entity.getBdmbkbh() );
        rzRxbdjgrzbDTO.setBdjglx( entity.getBdjglx() );
        rzRxbdjgrzbDTO.setCljg( entity.getCljg() );
        rzRxbdjgrzbDTO.setClrid( entity.getClrid() );
        rzRxbdjgrzbDTO.setClrxm( entity.getClrxm() );
        rzRxbdjgrzbDTO.setClrdw( entity.getClrdw() );
        rzRxbdjgrzbDTO.setSfxcl( entity.getSfxcl() );
        rzRxbdjgrzbDTO.setBdsj( entity.getBdsj() );
        rzRxbdjgrzbDTO.setClsj( entity.getClsj() );

        return rzRxbdjgrzbDTO;
    }

    @Override
    public RzRxbdjgrzbDO convertToDO(RzRxbdjgrzbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RzRxbdjgrzbDO rzRxbdjgrzbDO = new RzRxbdjgrzbDO();

        rzRxbdjgrzbDO.setId( dto.getId() );
        rzRxbdjgrzbDO.setRxbdjgid( dto.getRxbdjgid() );
        rzRxbdjgrzbDO.setRxbdrzid( dto.getRxbdrzid() );
        rzRxbdjgrzbDO.setGmsfhm( dto.getGmsfhm() );
        rzRxbdjgrzbDO.setRxid( dto.getRxid() );
        rzRxbdjgrzbDO.setBdxsd( dto.getBdxsd() );
        rzRxbdjgrzbDO.setBdxm( dto.getBdxm() );
        rzRxbdjgrzbDO.setBdgmsfhm( dto.getBdgmsfhm() );
        rzRxbdjgrzbDO.setBdmbkbh( dto.getBdmbkbh() );
        rzRxbdjgrzbDO.setBdjglx( dto.getBdjglx() );
        rzRxbdjgrzbDO.setCljg( dto.getCljg() );
        rzRxbdjgrzbDO.setClrid( dto.getClrid() );
        rzRxbdjgrzbDO.setClrxm( dto.getClrxm() );
        rzRxbdjgrzbDO.setClrdw( dto.getClrdw() );
        rzRxbdjgrzbDO.setSfxcl( dto.getSfxcl() );
        rzRxbdjgrzbDO.setBdsj( dto.getBdsj() );
        rzRxbdjgrzbDO.setClsj( dto.getClsj() );

        return rzRxbdjgrzbDO;
    }
}
