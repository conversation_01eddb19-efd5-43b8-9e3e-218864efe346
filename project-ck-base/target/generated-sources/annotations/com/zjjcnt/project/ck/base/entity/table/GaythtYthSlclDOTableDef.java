package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class GaythtYthSlclDOTableDef extends TableDef {

    /**
     * 互联网办件受理材料DO

 <AUTHOR>
 @date 2025-07-25 16:34:24
 @see com.zjjcnt.project.ck.base.dto.GaythtYthSlclDTO
     */
    public static final GaythtYthSlclDOTableDef GAYTHT_YTH_SLCL_DO = new GaythtYthSlclDOTableDef();

    /**
     * 创建人
     */
    public final QueryColumn CJR = new QueryColumn(this, "cjr");

    /**
     * 修改人
     */
    public final QueryColumn XGR = new QueryColumn(this, "xgr");

    /**
     * 创建时间
     */
    public final QueryColumn CJSJ = new QueryColumn(this, "cjsj");

    /**
     * 备注
     */
    public final QueryColumn MEMO = new QueryColumn(this, "memo");

    /**
     * 唯一标识
     */
    public final QueryColumn UNID = new QueryColumn(this, "unid");

    /**
     * 修改时间
     */
    public final QueryColumn XGSJ = new QueryColumn(this, "xgsj");

    /**
     * 有效标志
     */
    public final QueryColumn YXBZ = new QueryColumn(this, "yxbz");

    /**
     * 创建人IP
     */
    public final QueryColumn CJRIP = new QueryColumn(this, "cjrip");

    /**
     * 修改人IP
     */
    public final QueryColumn XGRIP = new QueryColumn(this, "xgrip");

    public final QueryColumn ATTRID = new QueryColumn(this, "attrid");

    /**
     * 备用字段
     */
    public final QueryColumn EXTEND = new QueryColumn(this, "extend");

    /**
     * 是否收取
     */
    public final QueryColumn ISTAKE = new QueryColumn(this, "istake");

    /**
     * 申报号
     */
    public final QueryColumn PROJID = new QueryColumn(this, "projid");

    /**
     * 材料序号
     */
    public final QueryColumn SORTID = new QueryColumn(this, "sortid");

    /**
     * 备用字段2
     */
    public final QueryColumn EXTEND2 = new QueryColumn(this, "extend2");

    /**
     * 备用字段3
     */
    public final QueryColumn EXTEND3 = new QueryColumn(this, "extend3");

    /**
     * 备用字段4
     */
    public final QueryColumn EXTEND4 = new QueryColumn(this, "extend4");

    /**
     * 备用字段5
     */
    public final QueryColumn EXTEND5 = new QueryColumn(this, "extend5");

    /**
     * 部门行政区划编码
     */
    public final QueryColumn AREACODE = new QueryColumn(this, "areacode");

    /**
     * 材料名称
     */
    public final QueryColumn ATTRNAME = new QueryColumn(this, "attrname");

    /**
     * 收取时间
     */
    public final QueryColumn TAKETIME = new QueryColumn(this, "taketime");

    /**
     * 收取方式
     */
    public final QueryColumn TAKETYPE = new QueryColumn(this, "taketype");

    /**
     * 数据产生时间
     */
    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    /**
     * 同步状态
     */
    public final QueryColumn SYNC_STATUS = new QueryColumn(this, "sync_status");

    /**
     * 版本号
     */
    public final QueryColumn DATAVERSION = new QueryColumn(this, "dataversion");

    /**
     * 所属系统
     */
    public final QueryColumn BELONGSYSTEM = new QueryColumn(this, "belongsystem");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{CJR, XGR, CJSJ, MEMO, UNID, XGSJ, YXBZ, CJRIP, XGRIP, ATTRID, EXTEND, ISTAKE, PROJID, SORTID, EXTEND2, EXTEND3, EXTEND4, EXTEND5, AREACODE, ATTRNAME, TAKETIME, TAKETYPE, CREATE_TIME, SYNC_STATUS, DATAVERSION, BELONGSYSTEM};

    public GaythtYthSlclDOTableDef() {
        super("", "gaytht_yth_slcl");
    }

    private GaythtYthSlclDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public GaythtYthSlclDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new GaythtYthSlclDOTableDef("", "gaytht_yth_slcl", alias));
    }

}
