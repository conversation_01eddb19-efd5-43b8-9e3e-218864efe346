package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class GaythtYthSlclsjDOTableDef extends TableDef {

    /**
     * 一体化审批材料数据DO

 <AUTHOR>
 @date 2025-07-25 16:34:24
 @see com.zjjcnt.project.ck.base.dto.GaythtYthSlclsjDTO
     */
    public static final GaythtYthSlclsjDOTableDef GAYTHT_YTH_SLCLSJ_DO = new GaythtYthSlclsjDOTableDef();

    /**
     * 创建人
     */
    public final QueryColumn CJR = new QueryColumn(this, "cjr");

    /**
     * 修改人
     */
    public final QueryColumn XGR = new QueryColumn(this, "xgr");

    /**
     * 创建时间
     */
    public final QueryColumn CJSJ = new QueryColumn(this, "cjsj");

    /**
     * 哈希值
     */
    public final QueryColumn HASH = new QueryColumn(this, "hash");

    /**
     * 唯一标识
     */
    public final QueryColumn UNID = new QueryColumn(this, "unid");

    /**
     * 文件大小
     */
    public final QueryColumn WJDX = new QueryColumn(this, "wjdx");

    /**
     * 文件类型
     */
    public final QueryColumn WJLX = new QueryColumn(this, "wjlx");

    /**
     * 修改时间
     */
    public final QueryColumn XGSJ = new QueryColumn(this, "xgsj");

    /**
     * 有效标志
     */
    public final QueryColumn YXBZ = new QueryColumn(this, "yxbz");

    /**
     * 创建人IP
     */
    public final QueryColumn CJRIP = new QueryColumn(this, "cjrip");

    /**
     * 修改人IP
     */
    public final QueryColumn XGRIP = new QueryColumn(this, "xgrip");

    /**
     * 备用字段
     */
    public final QueryColumn EXTEND = new QueryColumn(this, "extend");

    /**
     * 文件存放类别
     */
    public final QueryColumn WJCFLB = new QueryColumn(this, "wjcflb");

    /**
     * 文件数据
     */
    public final QueryColumn WJDATA = new QueryColumn(this, "wjdata");

    /**
     * 文件实际大小
     */
    public final QueryColumn WJSJDZ = new QueryColumn(this, "wjsjdz");

    /**
     * 备用字段2
     */
    public final QueryColumn EXTEND2 = new QueryColumn(this, "extend2");

    /**
     * 备用字段3
     */
    public final QueryColumn EXTEND3 = new QueryColumn(this, "extend3");

    /**
     * 备用字段4
     */
    public final QueryColumn EXTEND4 = new QueryColumn(this, "extend4");

    /**
     * 备用字段5
     */
    public final QueryColumn EXTEND5 = new QueryColumn(this, "extend5");

    /**
     * 附件存储路径
     */
    public final QueryColumn FILEURL = new QueryColumn(this, "fileurl");

    /**
     * 附件名称
     */
    public final QueryColumn FILENAME = new QueryColumn(this, "filename");

    /**
     * 材料数据ID
     */
    public final QueryColumn SLCLSJID = new QueryColumn(this, "slclsjid");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{CJR, XGR, CJSJ, HASH, UNID, WJDX, WJLX, XGSJ, YXBZ, CJRIP, XGRIP, EXTEND, WJCFLB, WJDATA, WJSJDZ, EXTEND2, EXTEND3, EXTEND4, EXTEND5, FILEURL, FILENAME, SLCLSJID};

    public GaythtYthSlclsjDOTableDef() {
        super("", "gaytht_yth_slclsj");
    }

    private GaythtYthSlclsjDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public GaythtYthSlclsjDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new GaythtYthSlclsjDOTableDef("", "gaytht_yth_slclsj", alias));
    }

}
