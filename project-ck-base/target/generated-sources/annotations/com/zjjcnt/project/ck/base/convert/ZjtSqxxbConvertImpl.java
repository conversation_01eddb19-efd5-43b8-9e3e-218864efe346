package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtSqxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtSqxxbCreateReq;
import com.zjjcnt.project.ck.base.dto.req.ZjtSqxxbPageReq;
import com.zjjcnt.project.ck.base.dto.req.ZjtSqxxbUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSqxxbCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSqxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSqxxbViewResp;
import com.zjjcnt.project.ck.base.entity.ZjtSqxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtSqxxbConvertImpl implements ZjtSqxxbConvert {

    @Override
    public ZjtSqxxbDTO convert(ZjtSqxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtSqxxbDTO zjtSqxxbDTO = new ZjtSqxxbDTO();

        zjtSqxxbDTO.setId( entity.getId() );
        zjtSqxxbDTO.setYwslh( entity.getYwslh() );
        zjtSqxxbDTO.setRyid( entity.getRyid() );
        zjtSqxxbDTO.setGmsfhm( entity.getGmsfhm() );
        zjtSqxxbDTO.setXm( entity.getXm() );
        zjtSqxxbDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        zjtSqxxbDTO.setSqrxm( entity.getSqrxm() );
        zjtSqxxbDTO.setSqrlxdh( entity.getSqrlxdh() );
        zjtSqxxbDTO.setSqrq( entity.getSqrq() );
        zjtSqxxbDTO.setSqrzpid( entity.getSqrzpid() );
        zjtSqxxbDTO.setSlzt( entity.getSlzt() );
        zjtSqxxbDTO.setSflx( entity.getSflx() );
        zjtSqxxbDTO.setSfje( entity.getSfje() );
        zjtSqxxbDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        zjtSqxxbDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        zjtSqxxbDTO.setSlrxm( entity.getSlrxm() );
        zjtSqxxbDTO.setSlsj( entity.getSlsj() );
        zjtSqxxbDTO.setJcwh( entity.getJcwh() );
        zjtSqxxbDTO.setBz( entity.getBz() );
        zjtSqxxbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        zjtSqxxbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        zjtSqxxbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        zjtSqxxbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        zjtSqxxbDTO.setSldfjsjgsdwdm( entity.getSldfjsjgsdwdm() );
        zjtSqxxbDTO.setSldfjsjgsdwmc( entity.getSldfjsjgsdwmc() );
        zjtSqxxbDTO.setSfdjh( entity.getSfdjh() );
        zjtSqxxbDTO.setBysllx( entity.getBysllx() );
        zjtSqxxbDTO.setByslsm( entity.getByslsm() );
        zjtSqxxbDTO.setZzlx( entity.getZzlx() );
        zjtSqxxbDTO.setFwdx( entity.getFwdx() );
        zjtSqxxbDTO.setHlwsqid( entity.getHlwsqid() );
        zjtSqxxbDTO.setUsername( entity.getUsername() );
        zjtSqxxbDTO.setSqrrxbdsj( entity.getSqrrxbdsj() );
        zjtSqxxbDTO.setSqrrxbdxsd( entity.getSqrrxbdxsd() );
        zjtSqxxbDTO.setSqrrxbdjg( entity.getSqrrxbdjg() );

        return zjtSqxxbDTO;
    }

    @Override
    public ZjtSqxxbDO convertToDO(ZjtSqxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtSqxxbDO zjtSqxxbDO = new ZjtSqxxbDO();

        zjtSqxxbDO.setId( dto.getId() );
        zjtSqxxbDO.setYwslh( dto.getYwslh() );
        zjtSqxxbDO.setRyid( dto.getRyid() );
        zjtSqxxbDO.setGmsfhm( dto.getGmsfhm() );
        zjtSqxxbDO.setXm( dto.getXm() );
        zjtSqxxbDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        zjtSqxxbDO.setSqrxm( dto.getSqrxm() );
        zjtSqxxbDO.setSqrlxdh( dto.getSqrlxdh() );
        zjtSqxxbDO.setSqrq( dto.getSqrq() );
        zjtSqxxbDO.setSqrzpid( dto.getSqrzpid() );
        zjtSqxxbDO.setSlzt( dto.getSlzt() );
        zjtSqxxbDO.setSflx( dto.getSflx() );
        zjtSqxxbDO.setSfje( dto.getSfje() );
        zjtSqxxbDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        zjtSqxxbDO.setSldwgajgmc( dto.getSldwgajgmc() );
        zjtSqxxbDO.setSlrxm( dto.getSlrxm() );
        zjtSqxxbDO.setSlsj( dto.getSlsj() );
        zjtSqxxbDO.setJcwh( dto.getJcwh() );
        zjtSqxxbDO.setBz( dto.getBz() );
        zjtSqxxbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtSqxxbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtSqxxbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtSqxxbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtSqxxbDO.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtSqxxbDO.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtSqxxbDO.setSfdjh( dto.getSfdjh() );
        zjtSqxxbDO.setBysllx( dto.getBysllx() );
        zjtSqxxbDO.setByslsm( dto.getByslsm() );
        zjtSqxxbDO.setZzlx( dto.getZzlx() );
        zjtSqxxbDO.setFwdx( dto.getFwdx() );
        zjtSqxxbDO.setHlwsqid( dto.getHlwsqid() );
        zjtSqxxbDO.setUsername( dto.getUsername() );
        zjtSqxxbDO.setSqrrxbdsj( dto.getSqrrxbdsj() );
        zjtSqxxbDO.setSqrrxbdxsd( dto.getSqrrxbdxsd() );
        zjtSqxxbDO.setSqrrxbdjg( dto.getSqrrxbdjg() );

        return zjtSqxxbDO;
    }

    @Override
    public ZjtSqxxbDTO convertToDTO(ZjtSqxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        ZjtSqxxbDTO zjtSqxxbDTO = new ZjtSqxxbDTO();

        zjtSqxxbDTO.setGmsfhm( req.getGmsfhm() );
        zjtSqxxbDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        zjtSqxxbDTO.setSlzt( req.getSlzt() );
        zjtSqxxbDTO.setSlsjStart( req.getSlsjStart() );
        zjtSqxxbDTO.setSlsjEnd( req.getSlsjEnd() );

        return zjtSqxxbDTO;
    }

    @Override
    public ZjtSqxxbDTO convertToDTO(ZjtSqxxbCreateReq req) {
        if ( req == null ) {
            return null;
        }

        ZjtSqxxbDTO zjtSqxxbDTO = new ZjtSqxxbDTO();

        zjtSqxxbDTO.setRyid( req.getRyid() );
        zjtSqxxbDTO.setGmsfhm( req.getGmsfhm() );
        zjtSqxxbDTO.setXm( req.getXm() );
        zjtSqxxbDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        zjtSqxxbDTO.setSqrxm( req.getSqrxm() );
        zjtSqxxbDTO.setSlzt( req.getSlzt() );
        zjtSqxxbDTO.setHjdsjgsdwdm( req.getHjdsjgsdwdm() );
        zjtSqxxbDTO.setHjdsjgsdwmc( req.getHjdsjgsdwmc() );
        zjtSqxxbDTO.setSldsjgsdwdm( req.getSldsjgsdwdm() );
        zjtSqxxbDTO.setSldsjgsdwmc( req.getSldsjgsdwmc() );
        zjtSqxxbDTO.setSldfjsjgsdwdm( req.getSldfjsjgsdwdm() );
        zjtSqxxbDTO.setSldfjsjgsdwmc( req.getSldfjsjgsdwmc() );
        zjtSqxxbDTO.setBysllx( req.getBysllx() );
        zjtSqxxbDTO.setByslsm( req.getByslsm() );
        zjtSqxxbDTO.setZzlx( req.getZzlx() );
        zjtSqxxbDTO.setFwdx( req.getFwdx() );
        zjtSqxxbDTO.setHlwsqid( req.getHlwsqid() );
        zjtSqxxbDTO.setUsername( req.getUsername() );
        zjtSqxxbDTO.setSqrrxbdsj( req.getSqrrxbdsj() );
        zjtSqxxbDTO.setSqrrxbdxsd( req.getSqrrxbdxsd() );
        zjtSqxxbDTO.setSqrrxbdjg( req.getSqrrxbdjg() );
        zjtSqxxbDTO.setBase64zp( req.getBase64zp() );

        return zjtSqxxbDTO;
    }

    @Override
    public ZjtSqxxbDTO convertToDTO(ZjtSqxxbUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        ZjtSqxxbDTO zjtSqxxbDTO = new ZjtSqxxbDTO();

        zjtSqxxbDTO.setYwslh( req.getYwslh() );
        zjtSqxxbDTO.setRyid( req.getRyid() );
        zjtSqxxbDTO.setGmsfhm( req.getGmsfhm() );
        zjtSqxxbDTO.setXm( req.getXm() );
        zjtSqxxbDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        zjtSqxxbDTO.setSqrxm( req.getSqrxm() );
        zjtSqxxbDTO.setSqrlxdh( req.getSqrlxdh() );
        zjtSqxxbDTO.setSqrq( req.getSqrq() );
        zjtSqxxbDTO.setSqrzpid( req.getSqrzpid() );
        zjtSqxxbDTO.setSlzt( req.getSlzt() );
        zjtSqxxbDTO.setSflx( req.getSflx() );
        zjtSqxxbDTO.setSfje( req.getSfje() );
        zjtSqxxbDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        zjtSqxxbDTO.setSldwgajgmc( req.getSldwgajgmc() );
        zjtSqxxbDTO.setSlrxm( req.getSlrxm() );
        zjtSqxxbDTO.setSlsj( req.getSlsj() );
        zjtSqxxbDTO.setJcwh( req.getJcwh() );
        zjtSqxxbDTO.setBz( req.getBz() );
        zjtSqxxbDTO.setHjdsjgsdwdm( req.getHjdsjgsdwdm() );
        zjtSqxxbDTO.setHjdsjgsdwmc( req.getHjdsjgsdwmc() );
        zjtSqxxbDTO.setSldsjgsdwdm( req.getSldsjgsdwdm() );
        zjtSqxxbDTO.setSldsjgsdwmc( req.getSldsjgsdwmc() );
        zjtSqxxbDTO.setSldfjsjgsdwdm( req.getSldfjsjgsdwdm() );
        zjtSqxxbDTO.setSldfjsjgsdwmc( req.getSldfjsjgsdwmc() );
        zjtSqxxbDTO.setSfdjh( req.getSfdjh() );
        zjtSqxxbDTO.setBysllx( req.getBysllx() );
        zjtSqxxbDTO.setByslsm( req.getByslsm() );
        zjtSqxxbDTO.setZzlx( req.getZzlx() );
        zjtSqxxbDTO.setFwdx( req.getFwdx() );
        zjtSqxxbDTO.setHlwsqid( req.getHlwsqid() );
        zjtSqxxbDTO.setUsername( req.getUsername() );
        zjtSqxxbDTO.setSqrrxbdsj( req.getSqrrxbdsj() );
        zjtSqxxbDTO.setSqrrxbdxsd( req.getSqrrxbdxsd() );
        zjtSqxxbDTO.setSqrrxbdjg( req.getSqrrxbdjg() );

        return zjtSqxxbDTO;
    }

    @Override
    public ZjtSqxxbPageResp convertToPageResp(ZjtSqxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtSqxxbPageResp zjtSqxxbPageResp = new ZjtSqxxbPageResp();

        zjtSqxxbPageResp.setYwslh( dto.getYwslh() );
        zjtSqxxbPageResp.setRyid( dto.getRyid() );
        zjtSqxxbPageResp.setGmsfhm( dto.getGmsfhm() );
        zjtSqxxbPageResp.setXm( dto.getXm() );
        zjtSqxxbPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        zjtSqxxbPageResp.setSqrxm( dto.getSqrxm() );
        zjtSqxxbPageResp.setSqrlxdh( dto.getSqrlxdh() );
        zjtSqxxbPageResp.setSqrq( dto.getSqrq() );
        zjtSqxxbPageResp.setSqrzpid( dto.getSqrzpid() );
        zjtSqxxbPageResp.setSlzt( dto.getSlzt() );
        zjtSqxxbPageResp.setSflx( dto.getSflx() );
        zjtSqxxbPageResp.setSfje( dto.getSfje() );
        zjtSqxxbPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        zjtSqxxbPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        zjtSqxxbPageResp.setSlrxm( dto.getSlrxm() );
        zjtSqxxbPageResp.setSlsj( dto.getSlsj() );
        zjtSqxxbPageResp.setJcwh( dto.getJcwh() );
        zjtSqxxbPageResp.setBz( dto.getBz() );
        zjtSqxxbPageResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtSqxxbPageResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtSqxxbPageResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtSqxxbPageResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtSqxxbPageResp.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtSqxxbPageResp.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtSqxxbPageResp.setSfdjh( dto.getSfdjh() );
        zjtSqxxbPageResp.setBysllx( dto.getBysllx() );
        zjtSqxxbPageResp.setByslsm( dto.getByslsm() );
        zjtSqxxbPageResp.setZzlx( dto.getZzlx() );
        zjtSqxxbPageResp.setFwdx( dto.getFwdx() );
        zjtSqxxbPageResp.setHlwsqid( dto.getHlwsqid() );
        zjtSqxxbPageResp.setUsername( dto.getUsername() );
        zjtSqxxbPageResp.setSqrrxbdsj( dto.getSqrrxbdsj() );
        zjtSqxxbPageResp.setSqrrxbdxsd( dto.getSqrrxbdxsd() );
        zjtSqxxbPageResp.setSqrrxbdjg( dto.getSqrrxbdjg() );

        return zjtSqxxbPageResp;
    }

    @Override
    public ZjtSqxxbViewResp convertToViewResp(ZjtSqxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtSqxxbViewResp zjtSqxxbViewResp = new ZjtSqxxbViewResp();

        zjtSqxxbViewResp.setYwslh( dto.getYwslh() );
        zjtSqxxbViewResp.setRyid( dto.getRyid() );
        zjtSqxxbViewResp.setGmsfhm( dto.getGmsfhm() );
        zjtSqxxbViewResp.setXm( dto.getXm() );
        zjtSqxxbViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        zjtSqxxbViewResp.setSqrxm( dto.getSqrxm() );
        zjtSqxxbViewResp.setSqrlxdh( dto.getSqrlxdh() );
        zjtSqxxbViewResp.setSqrq( dto.getSqrq() );
        zjtSqxxbViewResp.setSqrzpid( dto.getSqrzpid() );
        zjtSqxxbViewResp.setSlzt( dto.getSlzt() );
        zjtSqxxbViewResp.setSflx( dto.getSflx() );
        zjtSqxxbViewResp.setSfje( dto.getSfje() );
        zjtSqxxbViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        zjtSqxxbViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        zjtSqxxbViewResp.setSlrxm( dto.getSlrxm() );
        zjtSqxxbViewResp.setSlsj( dto.getSlsj() );
        zjtSqxxbViewResp.setJcwh( dto.getJcwh() );
        zjtSqxxbViewResp.setBz( dto.getBz() );
        zjtSqxxbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtSqxxbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtSqxxbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtSqxxbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtSqxxbViewResp.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtSqxxbViewResp.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtSqxxbViewResp.setSfdjh( dto.getSfdjh() );
        zjtSqxxbViewResp.setBysllx( dto.getBysllx() );
        zjtSqxxbViewResp.setByslsm( dto.getByslsm() );
        zjtSqxxbViewResp.setZzlx( dto.getZzlx() );
        zjtSqxxbViewResp.setFwdx( dto.getFwdx() );
        zjtSqxxbViewResp.setHlwsqid( dto.getHlwsqid() );
        zjtSqxxbViewResp.setUsername( dto.getUsername() );
        zjtSqxxbViewResp.setSqrrxbdsj( dto.getSqrrxbdsj() );
        zjtSqxxbViewResp.setSqrrxbdxsd( dto.getSqrrxbdxsd() );
        zjtSqxxbViewResp.setSqrrxbdjg( dto.getSqrrxbdjg() );

        return zjtSqxxbViewResp;
    }

    @Override
    public ZjtSqxxbCreateResp convertToCreateResp(ZjtSqxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtSqxxbCreateResp zjtSqxxbCreateResp = new ZjtSqxxbCreateResp();

        zjtSqxxbCreateResp.setYwslh( dto.getYwslh() );
        zjtSqxxbCreateResp.setRyid( dto.getRyid() );
        zjtSqxxbCreateResp.setGmsfhm( dto.getGmsfhm() );
        zjtSqxxbCreateResp.setXm( dto.getXm() );
        zjtSqxxbCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        zjtSqxxbCreateResp.setSqrxm( dto.getSqrxm() );
        zjtSqxxbCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        zjtSqxxbCreateResp.setSqrq( dto.getSqrq() );
        zjtSqxxbCreateResp.setSqrzpid( dto.getSqrzpid() );
        zjtSqxxbCreateResp.setSlzt( dto.getSlzt() );
        zjtSqxxbCreateResp.setSflx( dto.getSflx() );
        zjtSqxxbCreateResp.setSfje( dto.getSfje() );
        zjtSqxxbCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        zjtSqxxbCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        zjtSqxxbCreateResp.setSlrxm( dto.getSlrxm() );
        zjtSqxxbCreateResp.setSlsj( dto.getSlsj() );
        zjtSqxxbCreateResp.setJcwh( dto.getJcwh() );
        zjtSqxxbCreateResp.setBz( dto.getBz() );
        zjtSqxxbCreateResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtSqxxbCreateResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtSqxxbCreateResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtSqxxbCreateResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtSqxxbCreateResp.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtSqxxbCreateResp.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtSqxxbCreateResp.setSfdjh( dto.getSfdjh() );
        zjtSqxxbCreateResp.setBysllx( dto.getBysllx() );
        zjtSqxxbCreateResp.setByslsm( dto.getByslsm() );
        zjtSqxxbCreateResp.setZzlx( dto.getZzlx() );
        zjtSqxxbCreateResp.setFwdx( dto.getFwdx() );
        zjtSqxxbCreateResp.setHlwsqid( dto.getHlwsqid() );
        zjtSqxxbCreateResp.setUsername( dto.getUsername() );
        zjtSqxxbCreateResp.setSqrrxbdsj( dto.getSqrrxbdsj() );
        zjtSqxxbCreateResp.setSqrrxbdxsd( dto.getSqrrxbdxsd() );
        zjtSqxxbCreateResp.setSqrrxbdjg( dto.getSqrrxbdjg() );

        return zjtSqxxbCreateResp;
    }
}
