package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktHjslHldjjrjlhDTO;
import com.zjjcnt.project.ck.base.dto.req.RktHjslHldjjrjlhCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslHldjjrjlhPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktHjslHldjjrjlhUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslHldjjrjlhCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslHldjjrjlhPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktHjslHldjjrjlhViewResp;
import com.zjjcnt.project.ck.base.entity.RktHjslHldjjrjlhDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:43+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktHjslHldjjrjlhConvertImpl implements RktHjslHldjjrjlhConvert {

    @Override
    public RktHjslHldjjrjlhDTO convert(RktHjslHldjjrjlhDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktHjslHldjjrjlhDTO rktHjslHldjjrjlhDTO = new RktHjslHldjjrjlhDTO();

        rktHjslHldjjrjlhDTO.setId( entity.getId() );
        rktHjslHldjjrjlhDTO.setYwslnbbh( entity.getYwslnbbh() );
        rktHjslHldjjrjlhDTO.setHhnbid( entity.getHhnbid() );
        rktHjslHldjjrjlhDTO.setYwslh( entity.getYwslh() );
        rktHjslHldjjrjlhDTO.setHh( entity.getHh() );
        rktHjslHldjjrjlhDTO.setHhid( entity.getHhid() );
        rktHjslHldjjrjlhDTO.setHlx( entity.getHlx() );
        rktHjslHldjjrjlhDTO.setHmc( entity.getHmc() );
        rktHjslHldjjrjlhDTO.setHjddzbm( entity.getHjddzbm() );
        rktHjslHldjjrjlhDTO.setHjdssxq( entity.getHjdssxq() );
        rktHjslHldjjrjlhDTO.setHjdxxdz( entity.getHjdxxdz() );
        rktHjslHldjjrjlhDTO.setHjdrhyzbs( entity.getHjdrhyzbs() );
        rktHjslHldjjrjlhDTO.setJlx( entity.getJlx() );
        rktHjslHldjjrjlhDTO.setMlph( entity.getMlph() );
        rktHjslHldjjrjlhDTO.setMlxz( entity.getMlxz() );
        rktHjslHldjjrjlhDTO.setLcywlx( entity.getLcywlx() );
        rktHjslHldjjrjlhDTO.setLcdyid( entity.getLcdyid() );
        rktHjslHldjjrjlhDTO.setLcmc( entity.getLcmc() );
        rktHjslHldjjrjlhDTO.setLcslid( entity.getLcslid() );
        rktHjslHldjjrjlhDTO.setLcywbt( entity.getLcywbt() );
        rktHjslHldjjrjlhDTO.setLcrwjd( entity.getLcrwjd() );
        rktHjslHldjjrjlhDTO.setLczt( entity.getLczt() );
        rktHjslHldjjrjlhDTO.setBlzt( entity.getBlzt() );
        rktHjslHldjjrjlhDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        rktHjslHldjjrjlhDTO.setSqrxm( entity.getSqrxm() );
        rktHjslHldjjrjlhDTO.setSqrxb( entity.getSqrxb() );
        rktHjslHldjjrjlhDTO.setSqrlxdh( entity.getSqrlxdh() );
        rktHjslHldjjrjlhDTO.setSqrybdrgx( entity.getSqrybdrgx() );
        rktHjslHldjjrjlhDTO.setSqrzzssxq( entity.getSqrzzssxq() );
        rktHjslHldjjrjlhDTO.setSqrzzxz( entity.getSqrzzxz() );
        rktHjslHldjjrjlhDTO.setSqrhkdjjg( entity.getSqrhkdjjg() );
        rktHjslHldjjrjlhDTO.setSqrq( entity.getSqrq() );
        rktHjslHldjjrjlhDTO.setSpdwgajgjgdm( entity.getSpdwgajgjgdm() );
        rktHjslHldjjrjlhDTO.setSpdwgajgmc( entity.getSpdwgajgmc() );
        rktHjslHldjjrjlhDTO.setSprxm( entity.getSprxm() );
        rktHjslHldjjrjlhDTO.setSpsj( entity.getSpsj() );
        rktHjslHldjjrjlhDTO.setSpjgdm( entity.getSpjgdm() );
        rktHjslHldjjrjlhDTO.setSpyj( entity.getSpyj() );
        rktHjslHldjjrjlhDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        rktHjslHldjjrjlhDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        rktHjslHldjjrjlhDTO.setSlrxm( entity.getSlrxm() );
        rktHjslHldjjrjlhDTO.setSlsj( entity.getSlsj() );
        rktHjslHldjjrjlhDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        rktHjslHldjjrjlhDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        rktHjslHldjjrjlhDTO.setJcwh( entity.getJcwh() );
        rktHjslHldjjrjlhDTO.setBz( entity.getBz() );
        rktHjslHldjjrjlhDTO.setXh( entity.getXh() );
        rktHjslHldjjrjlhDTO.setHkxz( entity.getHkxz() );
        rktHjslHldjjrjlhDTO.setHb( entity.getHb() );
        rktHjslHldjjrjlhDTO.setYhzgx( entity.getYhzgx() );
        rktHjslHldjjrjlhDTO.setCxsx( entity.getCxsx() );
        rktHjslHldjjrjlhDTO.setGmsfhm( entity.getGmsfhm() );
        rktHjslHldjjrjlhDTO.setXm( entity.getXm() );
        rktHjslHldjjrjlhDTO.setX( entity.getX() );
        rktHjslHldjjrjlhDTO.setM( entity.getM() );
        rktHjslHldjjrjlhDTO.setCym( entity.getCym() );
        rktHjslHldjjrjlhDTO.setXmpy( entity.getXmpy() );
        rktHjslHldjjrjlhDTO.setCympy( entity.getCympy() );
        rktHjslHldjjrjlhDTO.setXb( entity.getXb() );
        rktHjslHldjjrjlhDTO.setMz( entity.getMz() );
        rktHjslHldjjrjlhDTO.setJggjdq( entity.getJggjdq() );
        rktHjslHldjjrjlhDTO.setJgssxq( entity.getJgssxq() );
        rktHjslHldjjrjlhDTO.setJgxz( entity.getJgxz() );
        rktHjslHldjjrjlhDTO.setCsrq( entity.getCsrq() );
        rktHjslHldjjrjlhDTO.setCssj( entity.getCssj() );
        rktHjslHldjjrjlhDTO.setCsdgjdq( entity.getCsdgjdq() );
        rktHjslHldjjrjlhDTO.setCsdssxq( entity.getCsdssxq() );
        rktHjslHldjjrjlhDTO.setCsdxz( entity.getCsdxz() );
        rktHjslHldjjrjlhDTO.setWhcd( entity.getWhcd() );
        rktHjslHldjjrjlhDTO.setHyzk( entity.getHyzk() );
        rktHjslHldjjrjlhDTO.setCyzkdwbm( entity.getCyzkdwbm() );
        rktHjslHldjjrjlhDTO.setCyzkdwmc( entity.getCyzkdwmc() );
        rktHjslHldjjrjlhDTO.setZy( entity.getZy() );
        rktHjslHldjjrjlhDTO.setZylb( entity.getZylb() );
        rktHjslHldjjrjlhDTO.setZjxy( entity.getZjxy() );
        rktHjslHldjjrjlhDTO.setSg( entity.getSg() );
        rktHjslHldjjrjlhDTO.setXx( entity.getXx() );
        rktHjslHldjjrjlhDTO.setByzk( entity.getByzk() );
        rktHjslHldjjrjlhDTO.setXxjb( entity.getXxjb() );
        rktHjslHldjjrjlhDTO.setLxdh( entity.getLxdh() );
        rktHjslHldjjrjlhDTO.setFqxm( entity.getFqxm() );
        rktHjslHldjjrjlhDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        rktHjslHldjjrjlhDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        rktHjslHldjjrjlhDTO.setFqzjhm( entity.getFqzjhm() );
        rktHjslHldjjrjlhDTO.setFqwwx( entity.getFqwwx() );
        rktHjslHldjjrjlhDTO.setFqwwm( entity.getFqwwm() );
        rktHjslHldjjrjlhDTO.setMqxm( entity.getMqxm() );
        rktHjslHldjjrjlhDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        rktHjslHldjjrjlhDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        rktHjslHldjjrjlhDTO.setMqzjhm( entity.getMqzjhm() );
        rktHjslHldjjrjlhDTO.setMqwwx( entity.getMqwwx() );
        rktHjslHldjjrjlhDTO.setMqwwm( entity.getMqwwm() );
        rktHjslHldjjrjlhDTO.setPoxm( entity.getPoxm() );
        rktHjslHldjjrjlhDTO.setPogmsfhm( entity.getPogmsfhm() );
        rktHjslHldjjrjlhDTO.setPocyzjdm( entity.getPocyzjdm() );
        rktHjslHldjjrjlhDTO.setPozjhm( entity.getPozjhm() );
        rktHjslHldjjrjlhDTO.setPowwx( entity.getPowwx() );
        rktHjslHldjjrjlhDTO.setPowwm( entity.getPowwm() );
        rktHjslHldjjrjlhDTO.setJhryxm( entity.getJhryxm() );
        rktHjslHldjjrjlhDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        rktHjslHldjjrjlhDTO.setJhryjhgx( entity.getJhryjhgx() );
        rktHjslHldjjrjlhDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        rktHjslHldjjrjlhDTO.setJhryzjhm( entity.getJhryzjhm() );
        rktHjslHldjjrjlhDTO.setJhrywwx( entity.getJhrywwx() );
        rktHjslHldjjrjlhDTO.setJhrywwm( entity.getJhrywwm() );
        rktHjslHldjjrjlhDTO.setJhrylxdh( entity.getJhrylxdh() );
        rktHjslHldjjrjlhDTO.setJhrexm( entity.getJhrexm() );
        rktHjslHldjjrjlhDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        rktHjslHldjjrjlhDTO.setJhrejhgx( entity.getJhrejhgx() );
        rktHjslHldjjrjlhDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        rktHjslHldjjrjlhDTO.setJhrezjhm( entity.getJhrezjhm() );
        rktHjslHldjjrjlhDTO.setJhrewwx( entity.getJhrewwx() );
        rktHjslHldjjrjlhDTO.setJhrewwm( entity.getJhrewwm() );
        rktHjslHldjjrjlhDTO.setJhrelxdh( entity.getJhrelxdh() );
        rktHjslHldjjrjlhDTO.setZjlb( entity.getZjlb() );
        rktHjslHldjjrjlhDTO.setQfjg( entity.getQfjg() );
        rktHjslHldjjrjlhDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        rktHjslHldjjrjlhDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        rktHjslHldjjrjlhDTO.setHgdjjrjrylhlb( entity.getHgdjjrjrylhlb() );
        rktHjslHldjjrjlhDTO.setHgdjjrjrylhlbdm( entity.getHgdjjrjrylhlbdm() );
        rktHjslHldjjrjlhDTO.setLzdgjdq( entity.getLzdgjdq() );
        rktHjslHldjjrjlhDTO.setLzdxz( entity.getLzdxz() );
        rktHjslHldjjrjlhDTO.setBdfw( entity.getBdfw() );
        rktHjslHldjjrjlhDTO.setYcyzjzl( entity.getYcyzjzl() );
        rktHjslHldjjrjlhDTO.setYcyzjhm( entity.getYcyzjhm() );
        rktHjslHldjjrjlhDTO.setBlhjywid( entity.getBlhjywid() );
        rktHjslHldjjrjlhDTO.setHjywblsj( entity.getHjywblsj() );
        rktHjslHldjjrjlhDTO.setQtssxq( entity.getQtssxq() );
        rktHjslHldjjrjlhDTO.setQtzz( entity.getQtzz() );
        rktHjslHldjjrjlhDTO.setCgqxm( entity.getCgqxm() );
        rktHjslHldjjrjlhDTO.setCgqgmsfhm( entity.getCgqgmsfhm() );
        rktHjslHldjjrjlhDTO.setCgqcsrq( entity.getCgqcsrq() );
        rktHjslHldjjrjlhDTO.setCgqxb( entity.getCgqxb() );
        rktHjslHldjjrjlhDTO.setCgqmz( entity.getCgqmz() );
        rktHjslHldjjrjlhDTO.setCgqhksx( entity.getCgqhksx() );
        rktHjslHldjjrjlhDTO.setCgqhkxz( entity.getCgqhkxz() );
        rktHjslHldjjrjlhDTO.setCgqzjqfjg( entity.getCgqzjqfjg() );
        rktHjslHldjjrjlhDTO.setCgqzjqfrq( entity.getCgqzjqfrq() );
        rktHjslHldjjrjlhDTO.setCgqbzxx( entity.getCgqbzxx() );

        return rktHjslHldjjrjlhDTO;
    }

    @Override
    public RktHjslHldjjrjlhDO convertToDO(RktHjslHldjjrjlhDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslHldjjrjlhDO rktHjslHldjjrjlhDO = new RktHjslHldjjrjlhDO();

        rktHjslHldjjrjlhDO.setId( dto.getId() );
        rktHjslHldjjrjlhDO.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslHldjjrjlhDO.setHhnbid( dto.getHhnbid() );
        rktHjslHldjjrjlhDO.setYwslh( dto.getYwslh() );
        rktHjslHldjjrjlhDO.setHh( dto.getHh() );
        rktHjslHldjjrjlhDO.setHhid( dto.getHhid() );
        rktHjslHldjjrjlhDO.setHlx( dto.getHlx() );
        rktHjslHldjjrjlhDO.setHmc( dto.getHmc() );
        rktHjslHldjjrjlhDO.setHjddzbm( dto.getHjddzbm() );
        rktHjslHldjjrjlhDO.setHjdssxq( dto.getHjdssxq() );
        rktHjslHldjjrjlhDO.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslHldjjrjlhDO.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslHldjjrjlhDO.setJlx( dto.getJlx() );
        rktHjslHldjjrjlhDO.setMlph( dto.getMlph() );
        rktHjslHldjjrjlhDO.setMlxz( dto.getMlxz() );
        rktHjslHldjjrjlhDO.setLcywlx( dto.getLcywlx() );
        rktHjslHldjjrjlhDO.setLcdyid( dto.getLcdyid() );
        rktHjslHldjjrjlhDO.setLcmc( dto.getLcmc() );
        rktHjslHldjjrjlhDO.setLcslid( dto.getLcslid() );
        rktHjslHldjjrjlhDO.setLcywbt( dto.getLcywbt() );
        rktHjslHldjjrjlhDO.setLcrwjd( dto.getLcrwjd() );
        rktHjslHldjjrjlhDO.setLczt( dto.getLczt() );
        rktHjslHldjjrjlhDO.setBlzt( dto.getBlzt() );
        rktHjslHldjjrjlhDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslHldjjrjlhDO.setSqrxm( dto.getSqrxm() );
        rktHjslHldjjrjlhDO.setSqrxb( dto.getSqrxb() );
        rktHjslHldjjrjlhDO.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslHldjjrjlhDO.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslHldjjrjlhDO.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslHldjjrjlhDO.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslHldjjrjlhDO.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslHldjjrjlhDO.setSqrq( dto.getSqrq() );
        rktHjslHldjjrjlhDO.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslHldjjrjlhDO.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslHldjjrjlhDO.setSprxm( dto.getSprxm() );
        rktHjslHldjjrjlhDO.setSpsj( dto.getSpsj() );
        rktHjslHldjjrjlhDO.setSpjgdm( dto.getSpjgdm() );
        rktHjslHldjjrjlhDO.setSpyj( dto.getSpyj() );
        rktHjslHldjjrjlhDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslHldjjrjlhDO.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslHldjjrjlhDO.setSlrxm( dto.getSlrxm() );
        rktHjslHldjjrjlhDO.setSlsj( dto.getSlsj() );
        rktHjslHldjjrjlhDO.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslHldjjrjlhDO.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslHldjjrjlhDO.setJcwh( dto.getJcwh() );
        rktHjslHldjjrjlhDO.setBz( dto.getBz() );
        rktHjslHldjjrjlhDO.setXh( dto.getXh() );
        rktHjslHldjjrjlhDO.setHkxz( dto.getHkxz() );
        rktHjslHldjjrjlhDO.setHb( dto.getHb() );
        rktHjslHldjjrjlhDO.setYhzgx( dto.getYhzgx() );
        rktHjslHldjjrjlhDO.setCxsx( dto.getCxsx() );
        rktHjslHldjjrjlhDO.setGmsfhm( dto.getGmsfhm() );
        rktHjslHldjjrjlhDO.setXm( dto.getXm() );
        rktHjslHldjjrjlhDO.setX( dto.getX() );
        rktHjslHldjjrjlhDO.setM( dto.getM() );
        rktHjslHldjjrjlhDO.setCym( dto.getCym() );
        rktHjslHldjjrjlhDO.setXmpy( dto.getXmpy() );
        rktHjslHldjjrjlhDO.setCympy( dto.getCympy() );
        rktHjslHldjjrjlhDO.setXb( dto.getXb() );
        rktHjslHldjjrjlhDO.setMz( dto.getMz() );
        rktHjslHldjjrjlhDO.setJggjdq( dto.getJggjdq() );
        rktHjslHldjjrjlhDO.setJgssxq( dto.getJgssxq() );
        rktHjslHldjjrjlhDO.setJgxz( dto.getJgxz() );
        rktHjslHldjjrjlhDO.setCsrq( dto.getCsrq() );
        rktHjslHldjjrjlhDO.setCssj( dto.getCssj() );
        rktHjslHldjjrjlhDO.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslHldjjrjlhDO.setCsdssxq( dto.getCsdssxq() );
        rktHjslHldjjrjlhDO.setCsdxz( dto.getCsdxz() );
        rktHjslHldjjrjlhDO.setWhcd( dto.getWhcd() );
        rktHjslHldjjrjlhDO.setHyzk( dto.getHyzk() );
        rktHjslHldjjrjlhDO.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslHldjjrjlhDO.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslHldjjrjlhDO.setZy( dto.getZy() );
        rktHjslHldjjrjlhDO.setZylb( dto.getZylb() );
        rktHjslHldjjrjlhDO.setZjxy( dto.getZjxy() );
        rktHjslHldjjrjlhDO.setSg( dto.getSg() );
        rktHjslHldjjrjlhDO.setXx( dto.getXx() );
        rktHjslHldjjrjlhDO.setByzk( dto.getByzk() );
        rktHjslHldjjrjlhDO.setXxjb( dto.getXxjb() );
        rktHjslHldjjrjlhDO.setLxdh( dto.getLxdh() );
        rktHjslHldjjrjlhDO.setFqxm( dto.getFqxm() );
        rktHjslHldjjrjlhDO.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslHldjjrjlhDO.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslHldjjrjlhDO.setFqzjhm( dto.getFqzjhm() );
        rktHjslHldjjrjlhDO.setFqwwx( dto.getFqwwx() );
        rktHjslHldjjrjlhDO.setFqwwm( dto.getFqwwm() );
        rktHjslHldjjrjlhDO.setMqxm( dto.getMqxm() );
        rktHjslHldjjrjlhDO.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslHldjjrjlhDO.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslHldjjrjlhDO.setMqzjhm( dto.getMqzjhm() );
        rktHjslHldjjrjlhDO.setMqwwx( dto.getMqwwx() );
        rktHjslHldjjrjlhDO.setMqwwm( dto.getMqwwm() );
        rktHjslHldjjrjlhDO.setPoxm( dto.getPoxm() );
        rktHjslHldjjrjlhDO.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslHldjjrjlhDO.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslHldjjrjlhDO.setPozjhm( dto.getPozjhm() );
        rktHjslHldjjrjlhDO.setPowwx( dto.getPowwx() );
        rktHjslHldjjrjlhDO.setPowwm( dto.getPowwm() );
        rktHjslHldjjrjlhDO.setJhryxm( dto.getJhryxm() );
        rktHjslHldjjrjlhDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslHldjjrjlhDO.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslHldjjrjlhDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslHldjjrjlhDO.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslHldjjrjlhDO.setJhrywwx( dto.getJhrywwx() );
        rktHjslHldjjrjlhDO.setJhrywwm( dto.getJhrywwm() );
        rktHjslHldjjrjlhDO.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslHldjjrjlhDO.setJhrexm( dto.getJhrexm() );
        rktHjslHldjjrjlhDO.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslHldjjrjlhDO.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslHldjjrjlhDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslHldjjrjlhDO.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslHldjjrjlhDO.setJhrewwx( dto.getJhrewwx() );
        rktHjslHldjjrjlhDO.setJhrewwm( dto.getJhrewwm() );
        rktHjslHldjjrjlhDO.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslHldjjrjlhDO.setZjlb( dto.getZjlb() );
        rktHjslHldjjrjlhDO.setQfjg( dto.getQfjg() );
        rktHjslHldjjrjlhDO.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslHldjjrjlhDO.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslHldjjrjlhDO.setHgdjjrjrylhlb( dto.getHgdjjrjrylhlb() );
        rktHjslHldjjrjlhDO.setHgdjjrjrylhlbdm( dto.getHgdjjrjrylhlbdm() );
        rktHjslHldjjrjlhDO.setLzdgjdq( dto.getLzdgjdq() );
        rktHjslHldjjrjlhDO.setLzdxz( dto.getLzdxz() );
        rktHjslHldjjrjlhDO.setBdfw( dto.getBdfw() );
        rktHjslHldjjrjlhDO.setYcyzjzl( dto.getYcyzjzl() );
        rktHjslHldjjrjlhDO.setYcyzjhm( dto.getYcyzjhm() );
        rktHjslHldjjrjlhDO.setBlhjywid( dto.getBlhjywid() );
        rktHjslHldjjrjlhDO.setHjywblsj( dto.getHjywblsj() );
        rktHjslHldjjrjlhDO.setQtssxq( dto.getQtssxq() );
        rktHjslHldjjrjlhDO.setQtzz( dto.getQtzz() );
        rktHjslHldjjrjlhDO.setCgqxm( dto.getCgqxm() );
        rktHjslHldjjrjlhDO.setCgqgmsfhm( dto.getCgqgmsfhm() );
        rktHjslHldjjrjlhDO.setCgqcsrq( dto.getCgqcsrq() );
        rktHjslHldjjrjlhDO.setCgqxb( dto.getCgqxb() );
        rktHjslHldjjrjlhDO.setCgqmz( dto.getCgqmz() );
        rktHjslHldjjrjlhDO.setCgqhksx( dto.getCgqhksx() );
        rktHjslHldjjrjlhDO.setCgqhkxz( dto.getCgqhkxz() );
        rktHjslHldjjrjlhDO.setCgqzjqfjg( dto.getCgqzjqfjg() );
        rktHjslHldjjrjlhDO.setCgqzjqfrq( dto.getCgqzjqfrq() );
        rktHjslHldjjrjlhDO.setCgqbzxx( dto.getCgqbzxx() );

        return rktHjslHldjjrjlhDO;
    }

    @Override
    public RktHjslHldjjrjlhDTO convertToDTO(RktHjslHldjjrjlhPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslHldjjrjlhDTO rktHjslHldjjrjlhDTO = new RktHjslHldjjrjlhDTO();

        rktHjslHldjjrjlhDTO.setHhnbid( req.getHhnbid() );
        rktHjslHldjjrjlhDTO.setYwslh( req.getYwslh() );
        rktHjslHldjjrjlhDTO.setHh( req.getHh() );
        rktHjslHldjjrjlhDTO.setHhid( req.getHhid() );
        rktHjslHldjjrjlhDTO.setHlx( req.getHlx() );
        rktHjslHldjjrjlhDTO.setHmc( req.getHmc() );
        rktHjslHldjjrjlhDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslHldjjrjlhDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslHldjjrjlhDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslHldjjrjlhDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslHldjjrjlhDTO.setJlx( req.getJlx() );
        rktHjslHldjjrjlhDTO.setMlph( req.getMlph() );
        rktHjslHldjjrjlhDTO.setMlxz( req.getMlxz() );
        rktHjslHldjjrjlhDTO.setLcywlx( req.getLcywlx() );
        rktHjslHldjjrjlhDTO.setLcdyid( req.getLcdyid() );
        rktHjslHldjjrjlhDTO.setLcmc( req.getLcmc() );
        rktHjslHldjjrjlhDTO.setLcslid( req.getLcslid() );
        rktHjslHldjjrjlhDTO.setLcywbt( req.getLcywbt() );
        rktHjslHldjjrjlhDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslHldjjrjlhDTO.setLczt( req.getLczt() );
        rktHjslHldjjrjlhDTO.setBlzt( req.getBlzt() );
        rktHjslHldjjrjlhDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslHldjjrjlhDTO.setSqrxm( req.getSqrxm() );
        rktHjslHldjjrjlhDTO.setSqrxb( req.getSqrxb() );
        rktHjslHldjjrjlhDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslHldjjrjlhDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslHldjjrjlhDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslHldjjrjlhDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslHldjjrjlhDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslHldjjrjlhDTO.setSqrq( req.getSqrq() );
        rktHjslHldjjrjlhDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslHldjjrjlhDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslHldjjrjlhDTO.setSprxm( req.getSprxm() );
        rktHjslHldjjrjlhDTO.setSpsj( req.getSpsj() );
        rktHjslHldjjrjlhDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslHldjjrjlhDTO.setSpyj( req.getSpyj() );
        rktHjslHldjjrjlhDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslHldjjrjlhDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslHldjjrjlhDTO.setSlrxm( req.getSlrxm() );
        rktHjslHldjjrjlhDTO.setSlsj( req.getSlsj() );
        rktHjslHldjjrjlhDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslHldjjrjlhDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslHldjjrjlhDTO.setJcwh( req.getJcwh() );
        rktHjslHldjjrjlhDTO.setBz( req.getBz() );
        rktHjslHldjjrjlhDTO.setXh( req.getXh() );
        rktHjslHldjjrjlhDTO.setHkxz( req.getHkxz() );
        rktHjslHldjjrjlhDTO.setHb( req.getHb() );
        rktHjslHldjjrjlhDTO.setYhzgx( req.getYhzgx() );
        rktHjslHldjjrjlhDTO.setCxsx( req.getCxsx() );
        rktHjslHldjjrjlhDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslHldjjrjlhDTO.setXm( req.getXm() );
        rktHjslHldjjrjlhDTO.setX( req.getX() );
        rktHjslHldjjrjlhDTO.setM( req.getM() );
        rktHjslHldjjrjlhDTO.setCym( req.getCym() );
        rktHjslHldjjrjlhDTO.setXmpy( req.getXmpy() );
        rktHjslHldjjrjlhDTO.setCympy( req.getCympy() );
        rktHjslHldjjrjlhDTO.setXb( req.getXb() );
        rktHjslHldjjrjlhDTO.setMz( req.getMz() );
        rktHjslHldjjrjlhDTO.setJggjdq( req.getJggjdq() );
        rktHjslHldjjrjlhDTO.setJgssxq( req.getJgssxq() );
        rktHjslHldjjrjlhDTO.setJgxz( req.getJgxz() );
        rktHjslHldjjrjlhDTO.setCsrq( req.getCsrq() );
        rktHjslHldjjrjlhDTO.setCssj( req.getCssj() );
        rktHjslHldjjrjlhDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslHldjjrjlhDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslHldjjrjlhDTO.setCsdxz( req.getCsdxz() );
        rktHjslHldjjrjlhDTO.setWhcd( req.getWhcd() );
        rktHjslHldjjrjlhDTO.setHyzk( req.getHyzk() );
        rktHjslHldjjrjlhDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslHldjjrjlhDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslHldjjrjlhDTO.setZy( req.getZy() );
        rktHjslHldjjrjlhDTO.setZylb( req.getZylb() );
        rktHjslHldjjrjlhDTO.setZjxy( req.getZjxy() );
        rktHjslHldjjrjlhDTO.setSg( req.getSg() );
        rktHjslHldjjrjlhDTO.setXx( req.getXx() );
        rktHjslHldjjrjlhDTO.setByzk( req.getByzk() );
        rktHjslHldjjrjlhDTO.setXxjb( req.getXxjb() );
        rktHjslHldjjrjlhDTO.setLxdh( req.getLxdh() );
        rktHjslHldjjrjlhDTO.setFqxm( req.getFqxm() );
        rktHjslHldjjrjlhDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslHldjjrjlhDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslHldjjrjlhDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslHldjjrjlhDTO.setFqwwx( req.getFqwwx() );
        rktHjslHldjjrjlhDTO.setFqwwm( req.getFqwwm() );
        rktHjslHldjjrjlhDTO.setMqxm( req.getMqxm() );
        rktHjslHldjjrjlhDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslHldjjrjlhDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslHldjjrjlhDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslHldjjrjlhDTO.setMqwwx( req.getMqwwx() );
        rktHjslHldjjrjlhDTO.setMqwwm( req.getMqwwm() );
        rktHjslHldjjrjlhDTO.setPoxm( req.getPoxm() );
        rktHjslHldjjrjlhDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslHldjjrjlhDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslHldjjrjlhDTO.setPozjhm( req.getPozjhm() );
        rktHjslHldjjrjlhDTO.setPowwx( req.getPowwx() );
        rktHjslHldjjrjlhDTO.setPowwm( req.getPowwm() );
        rktHjslHldjjrjlhDTO.setJhryxm( req.getJhryxm() );
        rktHjslHldjjrjlhDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslHldjjrjlhDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslHldjjrjlhDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslHldjjrjlhDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslHldjjrjlhDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslHldjjrjlhDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslHldjjrjlhDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslHldjjrjlhDTO.setJhrexm( req.getJhrexm() );
        rktHjslHldjjrjlhDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslHldjjrjlhDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslHldjjrjlhDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslHldjjrjlhDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslHldjjrjlhDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslHldjjrjlhDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslHldjjrjlhDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslHldjjrjlhDTO.setZjlb( req.getZjlb() );
        rktHjslHldjjrjlhDTO.setQfjg( req.getQfjg() );
        rktHjslHldjjrjlhDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslHldjjrjlhDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslHldjjrjlhDTO.setHgdjjrjrylhlb( req.getHgdjjrjrylhlb() );
        rktHjslHldjjrjlhDTO.setHgdjjrjrylhlbdm( req.getHgdjjrjrylhlbdm() );
        rktHjslHldjjrjlhDTO.setLzdgjdq( req.getLzdgjdq() );
        rktHjslHldjjrjlhDTO.setLzdxz( req.getLzdxz() );
        rktHjslHldjjrjlhDTO.setBdfw( req.getBdfw() );
        rktHjslHldjjrjlhDTO.setYcyzjzl( req.getYcyzjzl() );
        rktHjslHldjjrjlhDTO.setYcyzjhm( req.getYcyzjhm() );
        rktHjslHldjjrjlhDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslHldjjrjlhDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslHldjjrjlhDTO.setQtssxq( req.getQtssxq() );
        rktHjslHldjjrjlhDTO.setQtzz( req.getQtzz() );
        rktHjslHldjjrjlhDTO.setCgqxm( req.getCgqxm() );
        rktHjslHldjjrjlhDTO.setCgqgmsfhm( req.getCgqgmsfhm() );
        rktHjslHldjjrjlhDTO.setCgqcsrq( req.getCgqcsrq() );
        rktHjslHldjjrjlhDTO.setCgqxb( req.getCgqxb() );
        rktHjslHldjjrjlhDTO.setCgqmz( req.getCgqmz() );
        rktHjslHldjjrjlhDTO.setCgqhksx( req.getCgqhksx() );
        rktHjslHldjjrjlhDTO.setCgqhkxz( req.getCgqhkxz() );
        rktHjslHldjjrjlhDTO.setCgqzjqfjg( req.getCgqzjqfjg() );
        rktHjslHldjjrjlhDTO.setCgqzjqfrq( req.getCgqzjqfrq() );
        rktHjslHldjjrjlhDTO.setCgqbzxx( req.getCgqbzxx() );

        return rktHjslHldjjrjlhDTO;
    }

    @Override
    public RktHjslHldjjrjlhDTO convertToDTO(RktHjslHldjjrjlhCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslHldjjrjlhDTO rktHjslHldjjrjlhDTO = new RktHjslHldjjrjlhDTO();

        rktHjslHldjjrjlhDTO.setHhnbid( req.getHhnbid() );
        rktHjslHldjjrjlhDTO.setYwslh( req.getYwslh() );
        rktHjslHldjjrjlhDTO.setHh( req.getHh() );
        rktHjslHldjjrjlhDTO.setHhid( req.getHhid() );
        rktHjslHldjjrjlhDTO.setHlx( req.getHlx() );
        rktHjslHldjjrjlhDTO.setHmc( req.getHmc() );
        rktHjslHldjjrjlhDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslHldjjrjlhDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslHldjjrjlhDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslHldjjrjlhDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslHldjjrjlhDTO.setJlx( req.getJlx() );
        rktHjslHldjjrjlhDTO.setMlph( req.getMlph() );
        rktHjslHldjjrjlhDTO.setMlxz( req.getMlxz() );
        rktHjslHldjjrjlhDTO.setLcywlx( req.getLcywlx() );
        rktHjslHldjjrjlhDTO.setLcdyid( req.getLcdyid() );
        rktHjslHldjjrjlhDTO.setLcmc( req.getLcmc() );
        rktHjslHldjjrjlhDTO.setLcslid( req.getLcslid() );
        rktHjslHldjjrjlhDTO.setLcywbt( req.getLcywbt() );
        rktHjslHldjjrjlhDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslHldjjrjlhDTO.setLczt( req.getLczt() );
        rktHjslHldjjrjlhDTO.setBlzt( req.getBlzt() );
        rktHjslHldjjrjlhDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslHldjjrjlhDTO.setSqrxm( req.getSqrxm() );
        rktHjslHldjjrjlhDTO.setSqrxb( req.getSqrxb() );
        rktHjslHldjjrjlhDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslHldjjrjlhDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslHldjjrjlhDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslHldjjrjlhDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslHldjjrjlhDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslHldjjrjlhDTO.setSqrq( req.getSqrq() );
        rktHjslHldjjrjlhDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslHldjjrjlhDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslHldjjrjlhDTO.setSprxm( req.getSprxm() );
        rktHjslHldjjrjlhDTO.setSpsj( req.getSpsj() );
        rktHjslHldjjrjlhDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslHldjjrjlhDTO.setSpyj( req.getSpyj() );
        rktHjslHldjjrjlhDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslHldjjrjlhDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslHldjjrjlhDTO.setSlrxm( req.getSlrxm() );
        rktHjslHldjjrjlhDTO.setSlsj( req.getSlsj() );
        rktHjslHldjjrjlhDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslHldjjrjlhDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslHldjjrjlhDTO.setJcwh( req.getJcwh() );
        rktHjslHldjjrjlhDTO.setBz( req.getBz() );
        rktHjslHldjjrjlhDTO.setXh( req.getXh() );
        rktHjslHldjjrjlhDTO.setHkxz( req.getHkxz() );
        rktHjslHldjjrjlhDTO.setHb( req.getHb() );
        rktHjslHldjjrjlhDTO.setYhzgx( req.getYhzgx() );
        rktHjslHldjjrjlhDTO.setCxsx( req.getCxsx() );
        rktHjslHldjjrjlhDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslHldjjrjlhDTO.setXm( req.getXm() );
        rktHjslHldjjrjlhDTO.setX( req.getX() );
        rktHjslHldjjrjlhDTO.setM( req.getM() );
        rktHjslHldjjrjlhDTO.setCym( req.getCym() );
        rktHjslHldjjrjlhDTO.setXmpy( req.getXmpy() );
        rktHjslHldjjrjlhDTO.setCympy( req.getCympy() );
        rktHjslHldjjrjlhDTO.setXb( req.getXb() );
        rktHjslHldjjrjlhDTO.setMz( req.getMz() );
        rktHjslHldjjrjlhDTO.setJggjdq( req.getJggjdq() );
        rktHjslHldjjrjlhDTO.setJgssxq( req.getJgssxq() );
        rktHjslHldjjrjlhDTO.setJgxz( req.getJgxz() );
        rktHjslHldjjrjlhDTO.setCsrq( req.getCsrq() );
        rktHjslHldjjrjlhDTO.setCssj( req.getCssj() );
        rktHjslHldjjrjlhDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslHldjjrjlhDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslHldjjrjlhDTO.setCsdxz( req.getCsdxz() );
        rktHjslHldjjrjlhDTO.setWhcd( req.getWhcd() );
        rktHjslHldjjrjlhDTO.setHyzk( req.getHyzk() );
        rktHjslHldjjrjlhDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslHldjjrjlhDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslHldjjrjlhDTO.setZy( req.getZy() );
        rktHjslHldjjrjlhDTO.setZylb( req.getZylb() );
        rktHjslHldjjrjlhDTO.setZjxy( req.getZjxy() );
        rktHjslHldjjrjlhDTO.setSg( req.getSg() );
        rktHjslHldjjrjlhDTO.setXx( req.getXx() );
        rktHjslHldjjrjlhDTO.setByzk( req.getByzk() );
        rktHjslHldjjrjlhDTO.setXxjb( req.getXxjb() );
        rktHjslHldjjrjlhDTO.setLxdh( req.getLxdh() );
        rktHjslHldjjrjlhDTO.setFqxm( req.getFqxm() );
        rktHjslHldjjrjlhDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslHldjjrjlhDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslHldjjrjlhDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslHldjjrjlhDTO.setFqwwx( req.getFqwwx() );
        rktHjslHldjjrjlhDTO.setFqwwm( req.getFqwwm() );
        rktHjslHldjjrjlhDTO.setMqxm( req.getMqxm() );
        rktHjslHldjjrjlhDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslHldjjrjlhDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslHldjjrjlhDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslHldjjrjlhDTO.setMqwwx( req.getMqwwx() );
        rktHjslHldjjrjlhDTO.setMqwwm( req.getMqwwm() );
        rktHjslHldjjrjlhDTO.setPoxm( req.getPoxm() );
        rktHjslHldjjrjlhDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslHldjjrjlhDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslHldjjrjlhDTO.setPozjhm( req.getPozjhm() );
        rktHjslHldjjrjlhDTO.setPowwx( req.getPowwx() );
        rktHjslHldjjrjlhDTO.setPowwm( req.getPowwm() );
        rktHjslHldjjrjlhDTO.setJhryxm( req.getJhryxm() );
        rktHjslHldjjrjlhDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslHldjjrjlhDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslHldjjrjlhDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslHldjjrjlhDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslHldjjrjlhDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslHldjjrjlhDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslHldjjrjlhDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslHldjjrjlhDTO.setJhrexm( req.getJhrexm() );
        rktHjslHldjjrjlhDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslHldjjrjlhDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslHldjjrjlhDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslHldjjrjlhDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslHldjjrjlhDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslHldjjrjlhDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslHldjjrjlhDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslHldjjrjlhDTO.setZjlb( req.getZjlb() );
        rktHjslHldjjrjlhDTO.setQfjg( req.getQfjg() );
        rktHjslHldjjrjlhDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslHldjjrjlhDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslHldjjrjlhDTO.setHgdjjrjrylhlb( req.getHgdjjrjrylhlb() );
        rktHjslHldjjrjlhDTO.setHgdjjrjrylhlbdm( req.getHgdjjrjrylhlbdm() );
        rktHjslHldjjrjlhDTO.setLzdgjdq( req.getLzdgjdq() );
        rktHjslHldjjrjlhDTO.setLzdxz( req.getLzdxz() );
        rktHjslHldjjrjlhDTO.setBdfw( req.getBdfw() );
        rktHjslHldjjrjlhDTO.setYcyzjzl( req.getYcyzjzl() );
        rktHjslHldjjrjlhDTO.setYcyzjhm( req.getYcyzjhm() );
        rktHjslHldjjrjlhDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslHldjjrjlhDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslHldjjrjlhDTO.setQtssxq( req.getQtssxq() );
        rktHjslHldjjrjlhDTO.setQtzz( req.getQtzz() );
        rktHjslHldjjrjlhDTO.setCgqxm( req.getCgqxm() );
        rktHjslHldjjrjlhDTO.setCgqgmsfhm( req.getCgqgmsfhm() );
        rktHjslHldjjrjlhDTO.setCgqcsrq( req.getCgqcsrq() );
        rktHjslHldjjrjlhDTO.setCgqxb( req.getCgqxb() );
        rktHjslHldjjrjlhDTO.setCgqmz( req.getCgqmz() );
        rktHjslHldjjrjlhDTO.setCgqhksx( req.getCgqhksx() );
        rktHjslHldjjrjlhDTO.setCgqhkxz( req.getCgqhkxz() );
        rktHjslHldjjrjlhDTO.setCgqzjqfjg( req.getCgqzjqfjg() );
        rktHjslHldjjrjlhDTO.setCgqzjqfrq( req.getCgqzjqfrq() );
        rktHjslHldjjrjlhDTO.setCgqbzxx( req.getCgqbzxx() );

        return rktHjslHldjjrjlhDTO;
    }

    @Override
    public RktHjslHldjjrjlhDTO convertToDTO(RktHjslHldjjrjlhUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktHjslHldjjrjlhDTO rktHjslHldjjrjlhDTO = new RktHjslHldjjrjlhDTO();

        rktHjslHldjjrjlhDTO.setYwslnbbh( req.getYwslnbbh() );
        rktHjslHldjjrjlhDTO.setHhnbid( req.getHhnbid() );
        rktHjslHldjjrjlhDTO.setYwslh( req.getYwslh() );
        rktHjslHldjjrjlhDTO.setHh( req.getHh() );
        rktHjslHldjjrjlhDTO.setHhid( req.getHhid() );
        rktHjslHldjjrjlhDTO.setHlx( req.getHlx() );
        rktHjslHldjjrjlhDTO.setHmc( req.getHmc() );
        rktHjslHldjjrjlhDTO.setHjddzbm( req.getHjddzbm() );
        rktHjslHldjjrjlhDTO.setHjdssxq( req.getHjdssxq() );
        rktHjslHldjjrjlhDTO.setHjdxxdz( req.getHjdxxdz() );
        rktHjslHldjjrjlhDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        rktHjslHldjjrjlhDTO.setJlx( req.getJlx() );
        rktHjslHldjjrjlhDTO.setMlph( req.getMlph() );
        rktHjslHldjjrjlhDTO.setMlxz( req.getMlxz() );
        rktHjslHldjjrjlhDTO.setLcywlx( req.getLcywlx() );
        rktHjslHldjjrjlhDTO.setLcdyid( req.getLcdyid() );
        rktHjslHldjjrjlhDTO.setLcmc( req.getLcmc() );
        rktHjslHldjjrjlhDTO.setLcslid( req.getLcslid() );
        rktHjslHldjjrjlhDTO.setLcywbt( req.getLcywbt() );
        rktHjslHldjjrjlhDTO.setLcrwjd( req.getLcrwjd() );
        rktHjslHldjjrjlhDTO.setLczt( req.getLczt() );
        rktHjslHldjjrjlhDTO.setBlzt( req.getBlzt() );
        rktHjslHldjjrjlhDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktHjslHldjjrjlhDTO.setSqrxm( req.getSqrxm() );
        rktHjslHldjjrjlhDTO.setSqrxb( req.getSqrxb() );
        rktHjslHldjjrjlhDTO.setSqrlxdh( req.getSqrlxdh() );
        rktHjslHldjjrjlhDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktHjslHldjjrjlhDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktHjslHldjjrjlhDTO.setSqrzzxz( req.getSqrzzxz() );
        rktHjslHldjjrjlhDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktHjslHldjjrjlhDTO.setSqrq( req.getSqrq() );
        rktHjslHldjjrjlhDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktHjslHldjjrjlhDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktHjslHldjjrjlhDTO.setSprxm( req.getSprxm() );
        rktHjslHldjjrjlhDTO.setSpsj( req.getSpsj() );
        rktHjslHldjjrjlhDTO.setSpjgdm( req.getSpjgdm() );
        rktHjslHldjjrjlhDTO.setSpyj( req.getSpyj() );
        rktHjslHldjjrjlhDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktHjslHldjjrjlhDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktHjslHldjjrjlhDTO.setSlrxm( req.getSlrxm() );
        rktHjslHldjjrjlhDTO.setSlsj( req.getSlsj() );
        rktHjslHldjjrjlhDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktHjslHldjjrjlhDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktHjslHldjjrjlhDTO.setJcwh( req.getJcwh() );
        rktHjslHldjjrjlhDTO.setBz( req.getBz() );
        rktHjslHldjjrjlhDTO.setXh( req.getXh() );
        rktHjslHldjjrjlhDTO.setHkxz( req.getHkxz() );
        rktHjslHldjjrjlhDTO.setHb( req.getHb() );
        rktHjslHldjjrjlhDTO.setYhzgx( req.getYhzgx() );
        rktHjslHldjjrjlhDTO.setCxsx( req.getCxsx() );
        rktHjslHldjjrjlhDTO.setGmsfhm( req.getGmsfhm() );
        rktHjslHldjjrjlhDTO.setXm( req.getXm() );
        rktHjslHldjjrjlhDTO.setX( req.getX() );
        rktHjslHldjjrjlhDTO.setM( req.getM() );
        rktHjslHldjjrjlhDTO.setCym( req.getCym() );
        rktHjslHldjjrjlhDTO.setXmpy( req.getXmpy() );
        rktHjslHldjjrjlhDTO.setCympy( req.getCympy() );
        rktHjslHldjjrjlhDTO.setXb( req.getXb() );
        rktHjslHldjjrjlhDTO.setMz( req.getMz() );
        rktHjslHldjjrjlhDTO.setJggjdq( req.getJggjdq() );
        rktHjslHldjjrjlhDTO.setJgssxq( req.getJgssxq() );
        rktHjslHldjjrjlhDTO.setJgxz( req.getJgxz() );
        rktHjslHldjjrjlhDTO.setCsrq( req.getCsrq() );
        rktHjslHldjjrjlhDTO.setCssj( req.getCssj() );
        rktHjslHldjjrjlhDTO.setCsdgjdq( req.getCsdgjdq() );
        rktHjslHldjjrjlhDTO.setCsdssxq( req.getCsdssxq() );
        rktHjslHldjjrjlhDTO.setCsdxz( req.getCsdxz() );
        rktHjslHldjjrjlhDTO.setWhcd( req.getWhcd() );
        rktHjslHldjjrjlhDTO.setHyzk( req.getHyzk() );
        rktHjslHldjjrjlhDTO.setCyzkdwbm( req.getCyzkdwbm() );
        rktHjslHldjjrjlhDTO.setCyzkdwmc( req.getCyzkdwmc() );
        rktHjslHldjjrjlhDTO.setZy( req.getZy() );
        rktHjslHldjjrjlhDTO.setZylb( req.getZylb() );
        rktHjslHldjjrjlhDTO.setZjxy( req.getZjxy() );
        rktHjslHldjjrjlhDTO.setSg( req.getSg() );
        rktHjslHldjjrjlhDTO.setXx( req.getXx() );
        rktHjslHldjjrjlhDTO.setByzk( req.getByzk() );
        rktHjslHldjjrjlhDTO.setXxjb( req.getXxjb() );
        rktHjslHldjjrjlhDTO.setLxdh( req.getLxdh() );
        rktHjslHldjjrjlhDTO.setFqxm( req.getFqxm() );
        rktHjslHldjjrjlhDTO.setFqgmsfhm( req.getFqgmsfhm() );
        rktHjslHldjjrjlhDTO.setFqcyzjdm( req.getFqcyzjdm() );
        rktHjslHldjjrjlhDTO.setFqzjhm( req.getFqzjhm() );
        rktHjslHldjjrjlhDTO.setFqwwx( req.getFqwwx() );
        rktHjslHldjjrjlhDTO.setFqwwm( req.getFqwwm() );
        rktHjslHldjjrjlhDTO.setMqxm( req.getMqxm() );
        rktHjslHldjjrjlhDTO.setMqgmsfhm( req.getMqgmsfhm() );
        rktHjslHldjjrjlhDTO.setMqcyzjdm( req.getMqcyzjdm() );
        rktHjslHldjjrjlhDTO.setMqzjhm( req.getMqzjhm() );
        rktHjslHldjjrjlhDTO.setMqwwx( req.getMqwwx() );
        rktHjslHldjjrjlhDTO.setMqwwm( req.getMqwwm() );
        rktHjslHldjjrjlhDTO.setPoxm( req.getPoxm() );
        rktHjslHldjjrjlhDTO.setPogmsfhm( req.getPogmsfhm() );
        rktHjslHldjjrjlhDTO.setPocyzjdm( req.getPocyzjdm() );
        rktHjslHldjjrjlhDTO.setPozjhm( req.getPozjhm() );
        rktHjslHldjjrjlhDTO.setPowwx( req.getPowwx() );
        rktHjslHldjjrjlhDTO.setPowwm( req.getPowwm() );
        rktHjslHldjjrjlhDTO.setJhryxm( req.getJhryxm() );
        rktHjslHldjjrjlhDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        rktHjslHldjjrjlhDTO.setJhryjhgx( req.getJhryjhgx() );
        rktHjslHldjjrjlhDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        rktHjslHldjjrjlhDTO.setJhryzjhm( req.getJhryzjhm() );
        rktHjslHldjjrjlhDTO.setJhrywwx( req.getJhrywwx() );
        rktHjslHldjjrjlhDTO.setJhrywwm( req.getJhrywwm() );
        rktHjslHldjjrjlhDTO.setJhrylxdh( req.getJhrylxdh() );
        rktHjslHldjjrjlhDTO.setJhrexm( req.getJhrexm() );
        rktHjslHldjjrjlhDTO.setJhregmsfhm( req.getJhregmsfhm() );
        rktHjslHldjjrjlhDTO.setJhrejhgx( req.getJhrejhgx() );
        rktHjslHldjjrjlhDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        rktHjslHldjjrjlhDTO.setJhrezjhm( req.getJhrezjhm() );
        rktHjslHldjjrjlhDTO.setJhrewwx( req.getJhrewwx() );
        rktHjslHldjjrjlhDTO.setJhrewwm( req.getJhrewwm() );
        rktHjslHldjjrjlhDTO.setJhrelxdh( req.getJhrelxdh() );
        rktHjslHldjjrjlhDTO.setZjlb( req.getZjlb() );
        rktHjslHldjjrjlhDTO.setQfjg( req.getQfjg() );
        rktHjslHldjjrjlhDTO.setYxqxqsrq( req.getYxqxqsrq() );
        rktHjslHldjjrjlhDTO.setYxqxjzrq( req.getYxqxjzrq() );
        rktHjslHldjjrjlhDTO.setHgdjjrjrylhlb( req.getHgdjjrjrylhlb() );
        rktHjslHldjjrjlhDTO.setHgdjjrjrylhlbdm( req.getHgdjjrjrylhlbdm() );
        rktHjslHldjjrjlhDTO.setLzdgjdq( req.getLzdgjdq() );
        rktHjslHldjjrjlhDTO.setLzdxz( req.getLzdxz() );
        rktHjslHldjjrjlhDTO.setBdfw( req.getBdfw() );
        rktHjslHldjjrjlhDTO.setYcyzjzl( req.getYcyzjzl() );
        rktHjslHldjjrjlhDTO.setYcyzjhm( req.getYcyzjhm() );
        rktHjslHldjjrjlhDTO.setBlhjywid( req.getBlhjywid() );
        rktHjslHldjjrjlhDTO.setHjywblsj( req.getHjywblsj() );
        rktHjslHldjjrjlhDTO.setQtssxq( req.getQtssxq() );
        rktHjslHldjjrjlhDTO.setQtzz( req.getQtzz() );
        rktHjslHldjjrjlhDTO.setCgqxm( req.getCgqxm() );
        rktHjslHldjjrjlhDTO.setCgqgmsfhm( req.getCgqgmsfhm() );
        rktHjslHldjjrjlhDTO.setCgqcsrq( req.getCgqcsrq() );
        rktHjslHldjjrjlhDTO.setCgqxb( req.getCgqxb() );
        rktHjslHldjjrjlhDTO.setCgqmz( req.getCgqmz() );
        rktHjslHldjjrjlhDTO.setCgqhksx( req.getCgqhksx() );
        rktHjslHldjjrjlhDTO.setCgqhkxz( req.getCgqhkxz() );
        rktHjslHldjjrjlhDTO.setCgqzjqfjg( req.getCgqzjqfjg() );
        rktHjslHldjjrjlhDTO.setCgqzjqfrq( req.getCgqzjqfrq() );
        rktHjslHldjjrjlhDTO.setCgqbzxx( req.getCgqbzxx() );

        return rktHjslHldjjrjlhDTO;
    }

    @Override
    public RktHjslHldjjrjlhPageResp convertToPageResp(RktHjslHldjjrjlhDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslHldjjrjlhPageResp rktHjslHldjjrjlhPageResp = new RktHjslHldjjrjlhPageResp();

        rktHjslHldjjrjlhPageResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslHldjjrjlhPageResp.setHhnbid( dto.getHhnbid() );
        rktHjslHldjjrjlhPageResp.setYwslh( dto.getYwslh() );
        rktHjslHldjjrjlhPageResp.setHh( dto.getHh() );
        rktHjslHldjjrjlhPageResp.setHhid( dto.getHhid() );
        rktHjslHldjjrjlhPageResp.setHlx( dto.getHlx() );
        rktHjslHldjjrjlhPageResp.setHmc( dto.getHmc() );
        rktHjslHldjjrjlhPageResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslHldjjrjlhPageResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslHldjjrjlhPageResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslHldjjrjlhPageResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslHldjjrjlhPageResp.setJlx( dto.getJlx() );
        rktHjslHldjjrjlhPageResp.setMlph( dto.getMlph() );
        rktHjslHldjjrjlhPageResp.setMlxz( dto.getMlxz() );
        rktHjslHldjjrjlhPageResp.setLcywlx( dto.getLcywlx() );
        rktHjslHldjjrjlhPageResp.setLcdyid( dto.getLcdyid() );
        rktHjslHldjjrjlhPageResp.setLcmc( dto.getLcmc() );
        rktHjslHldjjrjlhPageResp.setLcslid( dto.getLcslid() );
        rktHjslHldjjrjlhPageResp.setLcywbt( dto.getLcywbt() );
        rktHjslHldjjrjlhPageResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslHldjjrjlhPageResp.setLczt( dto.getLczt() );
        rktHjslHldjjrjlhPageResp.setBlzt( dto.getBlzt() );
        rktHjslHldjjrjlhPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslHldjjrjlhPageResp.setSqrxm( dto.getSqrxm() );
        rktHjslHldjjrjlhPageResp.setSqrxb( dto.getSqrxb() );
        rktHjslHldjjrjlhPageResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslHldjjrjlhPageResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslHldjjrjlhPageResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslHldjjrjlhPageResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslHldjjrjlhPageResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslHldjjrjlhPageResp.setSqrq( dto.getSqrq() );
        rktHjslHldjjrjlhPageResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslHldjjrjlhPageResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslHldjjrjlhPageResp.setSprxm( dto.getSprxm() );
        rktHjslHldjjrjlhPageResp.setSpsj( dto.getSpsj() );
        rktHjslHldjjrjlhPageResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslHldjjrjlhPageResp.setSpyj( dto.getSpyj() );
        rktHjslHldjjrjlhPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslHldjjrjlhPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslHldjjrjlhPageResp.setSlrxm( dto.getSlrxm() );
        rktHjslHldjjrjlhPageResp.setSlsj( dto.getSlsj() );
        rktHjslHldjjrjlhPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslHldjjrjlhPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslHldjjrjlhPageResp.setJcwh( dto.getJcwh() );
        rktHjslHldjjrjlhPageResp.setBz( dto.getBz() );
        rktHjslHldjjrjlhPageResp.setXh( dto.getXh() );
        rktHjslHldjjrjlhPageResp.setHkxz( dto.getHkxz() );
        rktHjslHldjjrjlhPageResp.setHb( dto.getHb() );
        rktHjslHldjjrjlhPageResp.setYhzgx( dto.getYhzgx() );
        rktHjslHldjjrjlhPageResp.setCxsx( dto.getCxsx() );
        rktHjslHldjjrjlhPageResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslHldjjrjlhPageResp.setXm( dto.getXm() );
        rktHjslHldjjrjlhPageResp.setX( dto.getX() );
        rktHjslHldjjrjlhPageResp.setM( dto.getM() );
        rktHjslHldjjrjlhPageResp.setCym( dto.getCym() );
        rktHjslHldjjrjlhPageResp.setXmpy( dto.getXmpy() );
        rktHjslHldjjrjlhPageResp.setCympy( dto.getCympy() );
        rktHjslHldjjrjlhPageResp.setXb( dto.getXb() );
        rktHjslHldjjrjlhPageResp.setMz( dto.getMz() );
        rktHjslHldjjrjlhPageResp.setJggjdq( dto.getJggjdq() );
        rktHjslHldjjrjlhPageResp.setJgssxq( dto.getJgssxq() );
        rktHjslHldjjrjlhPageResp.setJgxz( dto.getJgxz() );
        rktHjslHldjjrjlhPageResp.setCsrq( dto.getCsrq() );
        rktHjslHldjjrjlhPageResp.setCssj( dto.getCssj() );
        rktHjslHldjjrjlhPageResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslHldjjrjlhPageResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslHldjjrjlhPageResp.setCsdxz( dto.getCsdxz() );
        rktHjslHldjjrjlhPageResp.setWhcd( dto.getWhcd() );
        rktHjslHldjjrjlhPageResp.setHyzk( dto.getHyzk() );
        rktHjslHldjjrjlhPageResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslHldjjrjlhPageResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslHldjjrjlhPageResp.setZy( dto.getZy() );
        rktHjslHldjjrjlhPageResp.setZylb( dto.getZylb() );
        rktHjslHldjjrjlhPageResp.setZjxy( dto.getZjxy() );
        rktHjslHldjjrjlhPageResp.setSg( dto.getSg() );
        rktHjslHldjjrjlhPageResp.setXx( dto.getXx() );
        rktHjslHldjjrjlhPageResp.setByzk( dto.getByzk() );
        rktHjslHldjjrjlhPageResp.setXxjb( dto.getXxjb() );
        rktHjslHldjjrjlhPageResp.setLxdh( dto.getLxdh() );
        rktHjslHldjjrjlhPageResp.setFqxm( dto.getFqxm() );
        rktHjslHldjjrjlhPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslHldjjrjlhPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslHldjjrjlhPageResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslHldjjrjlhPageResp.setFqwwx( dto.getFqwwx() );
        rktHjslHldjjrjlhPageResp.setFqwwm( dto.getFqwwm() );
        rktHjslHldjjrjlhPageResp.setMqxm( dto.getMqxm() );
        rktHjslHldjjrjlhPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslHldjjrjlhPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslHldjjrjlhPageResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslHldjjrjlhPageResp.setMqwwx( dto.getMqwwx() );
        rktHjslHldjjrjlhPageResp.setMqwwm( dto.getMqwwm() );
        rktHjslHldjjrjlhPageResp.setPoxm( dto.getPoxm() );
        rktHjslHldjjrjlhPageResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslHldjjrjlhPageResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslHldjjrjlhPageResp.setPozjhm( dto.getPozjhm() );
        rktHjslHldjjrjlhPageResp.setPowwx( dto.getPowwx() );
        rktHjslHldjjrjlhPageResp.setPowwm( dto.getPowwm() );
        rktHjslHldjjrjlhPageResp.setJhryxm( dto.getJhryxm() );
        rktHjslHldjjrjlhPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslHldjjrjlhPageResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslHldjjrjlhPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslHldjjrjlhPageResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslHldjjrjlhPageResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslHldjjrjlhPageResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslHldjjrjlhPageResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslHldjjrjlhPageResp.setJhrexm( dto.getJhrexm() );
        rktHjslHldjjrjlhPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslHldjjrjlhPageResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslHldjjrjlhPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslHldjjrjlhPageResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslHldjjrjlhPageResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslHldjjrjlhPageResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslHldjjrjlhPageResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslHldjjrjlhPageResp.setZjlb( dto.getZjlb() );
        rktHjslHldjjrjlhPageResp.setQfjg( dto.getQfjg() );
        rktHjslHldjjrjlhPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslHldjjrjlhPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslHldjjrjlhPageResp.setHgdjjrjrylhlb( dto.getHgdjjrjrylhlb() );
        rktHjslHldjjrjlhPageResp.setHgdjjrjrylhlbdm( dto.getHgdjjrjrylhlbdm() );
        rktHjslHldjjrjlhPageResp.setLzdgjdq( dto.getLzdgjdq() );
        rktHjslHldjjrjlhPageResp.setLzdxz( dto.getLzdxz() );
        rktHjslHldjjrjlhPageResp.setBdfw( dto.getBdfw() );
        rktHjslHldjjrjlhPageResp.setYcyzjzl( dto.getYcyzjzl() );
        rktHjslHldjjrjlhPageResp.setYcyzjhm( dto.getYcyzjhm() );
        rktHjslHldjjrjlhPageResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslHldjjrjlhPageResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslHldjjrjlhPageResp.setQtssxq( dto.getQtssxq() );
        rktHjslHldjjrjlhPageResp.setQtzz( dto.getQtzz() );
        rktHjslHldjjrjlhPageResp.setCgqxm( dto.getCgqxm() );
        rktHjslHldjjrjlhPageResp.setCgqgmsfhm( dto.getCgqgmsfhm() );
        rktHjslHldjjrjlhPageResp.setCgqcsrq( dto.getCgqcsrq() );
        rktHjslHldjjrjlhPageResp.setCgqxb( dto.getCgqxb() );
        rktHjslHldjjrjlhPageResp.setCgqmz( dto.getCgqmz() );
        rktHjslHldjjrjlhPageResp.setCgqhksx( dto.getCgqhksx() );
        rktHjslHldjjrjlhPageResp.setCgqhkxz( dto.getCgqhkxz() );
        rktHjslHldjjrjlhPageResp.setCgqzjqfjg( dto.getCgqzjqfjg() );
        rktHjslHldjjrjlhPageResp.setCgqzjqfrq( dto.getCgqzjqfrq() );
        rktHjslHldjjrjlhPageResp.setCgqbzxx( dto.getCgqbzxx() );

        return rktHjslHldjjrjlhPageResp;
    }

    @Override
    public RktHjslHldjjrjlhViewResp convertToViewResp(RktHjslHldjjrjlhDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslHldjjrjlhViewResp rktHjslHldjjrjlhViewResp = new RktHjslHldjjrjlhViewResp();

        rktHjslHldjjrjlhViewResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslHldjjrjlhViewResp.setHhnbid( dto.getHhnbid() );
        rktHjslHldjjrjlhViewResp.setYwslh( dto.getYwslh() );
        rktHjslHldjjrjlhViewResp.setHh( dto.getHh() );
        rktHjslHldjjrjlhViewResp.setHhid( dto.getHhid() );
        rktHjslHldjjrjlhViewResp.setHlx( dto.getHlx() );
        rktHjslHldjjrjlhViewResp.setHmc( dto.getHmc() );
        rktHjslHldjjrjlhViewResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslHldjjrjlhViewResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslHldjjrjlhViewResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslHldjjrjlhViewResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslHldjjrjlhViewResp.setJlx( dto.getJlx() );
        rktHjslHldjjrjlhViewResp.setMlph( dto.getMlph() );
        rktHjslHldjjrjlhViewResp.setMlxz( dto.getMlxz() );
        rktHjslHldjjrjlhViewResp.setLcywlx( dto.getLcywlx() );
        rktHjslHldjjrjlhViewResp.setLcdyid( dto.getLcdyid() );
        rktHjslHldjjrjlhViewResp.setLcmc( dto.getLcmc() );
        rktHjslHldjjrjlhViewResp.setLcslid( dto.getLcslid() );
        rktHjslHldjjrjlhViewResp.setLcywbt( dto.getLcywbt() );
        rktHjslHldjjrjlhViewResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslHldjjrjlhViewResp.setLczt( dto.getLczt() );
        rktHjslHldjjrjlhViewResp.setBlzt( dto.getBlzt() );
        rktHjslHldjjrjlhViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslHldjjrjlhViewResp.setSqrxm( dto.getSqrxm() );
        rktHjslHldjjrjlhViewResp.setSqrxb( dto.getSqrxb() );
        rktHjslHldjjrjlhViewResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslHldjjrjlhViewResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslHldjjrjlhViewResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslHldjjrjlhViewResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslHldjjrjlhViewResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslHldjjrjlhViewResp.setSqrq( dto.getSqrq() );
        rktHjslHldjjrjlhViewResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslHldjjrjlhViewResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslHldjjrjlhViewResp.setSprxm( dto.getSprxm() );
        rktHjslHldjjrjlhViewResp.setSpsj( dto.getSpsj() );
        rktHjslHldjjrjlhViewResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslHldjjrjlhViewResp.setSpyj( dto.getSpyj() );
        rktHjslHldjjrjlhViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslHldjjrjlhViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslHldjjrjlhViewResp.setSlrxm( dto.getSlrxm() );
        rktHjslHldjjrjlhViewResp.setSlsj( dto.getSlsj() );
        rktHjslHldjjrjlhViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslHldjjrjlhViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslHldjjrjlhViewResp.setJcwh( dto.getJcwh() );
        rktHjslHldjjrjlhViewResp.setBz( dto.getBz() );
        rktHjslHldjjrjlhViewResp.setXh( dto.getXh() );
        rktHjslHldjjrjlhViewResp.setHkxz( dto.getHkxz() );
        rktHjslHldjjrjlhViewResp.setHb( dto.getHb() );
        rktHjslHldjjrjlhViewResp.setYhzgx( dto.getYhzgx() );
        rktHjslHldjjrjlhViewResp.setCxsx( dto.getCxsx() );
        rktHjslHldjjrjlhViewResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslHldjjrjlhViewResp.setXm( dto.getXm() );
        rktHjslHldjjrjlhViewResp.setX( dto.getX() );
        rktHjslHldjjrjlhViewResp.setM( dto.getM() );
        rktHjslHldjjrjlhViewResp.setCym( dto.getCym() );
        rktHjslHldjjrjlhViewResp.setXmpy( dto.getXmpy() );
        rktHjslHldjjrjlhViewResp.setCympy( dto.getCympy() );
        rktHjslHldjjrjlhViewResp.setXb( dto.getXb() );
        rktHjslHldjjrjlhViewResp.setMz( dto.getMz() );
        rktHjslHldjjrjlhViewResp.setJggjdq( dto.getJggjdq() );
        rktHjslHldjjrjlhViewResp.setJgssxq( dto.getJgssxq() );
        rktHjslHldjjrjlhViewResp.setJgxz( dto.getJgxz() );
        rktHjslHldjjrjlhViewResp.setCsrq( dto.getCsrq() );
        rktHjslHldjjrjlhViewResp.setCssj( dto.getCssj() );
        rktHjslHldjjrjlhViewResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslHldjjrjlhViewResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslHldjjrjlhViewResp.setCsdxz( dto.getCsdxz() );
        rktHjslHldjjrjlhViewResp.setWhcd( dto.getWhcd() );
        rktHjslHldjjrjlhViewResp.setHyzk( dto.getHyzk() );
        rktHjslHldjjrjlhViewResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslHldjjrjlhViewResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslHldjjrjlhViewResp.setZy( dto.getZy() );
        rktHjslHldjjrjlhViewResp.setZylb( dto.getZylb() );
        rktHjslHldjjrjlhViewResp.setZjxy( dto.getZjxy() );
        rktHjslHldjjrjlhViewResp.setSg( dto.getSg() );
        rktHjslHldjjrjlhViewResp.setXx( dto.getXx() );
        rktHjslHldjjrjlhViewResp.setByzk( dto.getByzk() );
        rktHjslHldjjrjlhViewResp.setXxjb( dto.getXxjb() );
        rktHjslHldjjrjlhViewResp.setLxdh( dto.getLxdh() );
        rktHjslHldjjrjlhViewResp.setFqxm( dto.getFqxm() );
        rktHjslHldjjrjlhViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslHldjjrjlhViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslHldjjrjlhViewResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslHldjjrjlhViewResp.setFqwwx( dto.getFqwwx() );
        rktHjslHldjjrjlhViewResp.setFqwwm( dto.getFqwwm() );
        rktHjslHldjjrjlhViewResp.setMqxm( dto.getMqxm() );
        rktHjslHldjjrjlhViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslHldjjrjlhViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslHldjjrjlhViewResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslHldjjrjlhViewResp.setMqwwx( dto.getMqwwx() );
        rktHjslHldjjrjlhViewResp.setMqwwm( dto.getMqwwm() );
        rktHjslHldjjrjlhViewResp.setPoxm( dto.getPoxm() );
        rktHjslHldjjrjlhViewResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslHldjjrjlhViewResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslHldjjrjlhViewResp.setPozjhm( dto.getPozjhm() );
        rktHjslHldjjrjlhViewResp.setPowwx( dto.getPowwx() );
        rktHjslHldjjrjlhViewResp.setPowwm( dto.getPowwm() );
        rktHjslHldjjrjlhViewResp.setJhryxm( dto.getJhryxm() );
        rktHjslHldjjrjlhViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslHldjjrjlhViewResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslHldjjrjlhViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslHldjjrjlhViewResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslHldjjrjlhViewResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslHldjjrjlhViewResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslHldjjrjlhViewResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslHldjjrjlhViewResp.setJhrexm( dto.getJhrexm() );
        rktHjslHldjjrjlhViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslHldjjrjlhViewResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslHldjjrjlhViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslHldjjrjlhViewResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslHldjjrjlhViewResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslHldjjrjlhViewResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslHldjjrjlhViewResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslHldjjrjlhViewResp.setZjlb( dto.getZjlb() );
        rktHjslHldjjrjlhViewResp.setQfjg( dto.getQfjg() );
        rktHjslHldjjrjlhViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslHldjjrjlhViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslHldjjrjlhViewResp.setHgdjjrjrylhlb( dto.getHgdjjrjrylhlb() );
        rktHjslHldjjrjlhViewResp.setHgdjjrjrylhlbdm( dto.getHgdjjrjrylhlbdm() );
        rktHjslHldjjrjlhViewResp.setLzdgjdq( dto.getLzdgjdq() );
        rktHjslHldjjrjlhViewResp.setLzdxz( dto.getLzdxz() );
        rktHjslHldjjrjlhViewResp.setBdfw( dto.getBdfw() );
        rktHjslHldjjrjlhViewResp.setYcyzjzl( dto.getYcyzjzl() );
        rktHjslHldjjrjlhViewResp.setYcyzjhm( dto.getYcyzjhm() );
        rktHjslHldjjrjlhViewResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslHldjjrjlhViewResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslHldjjrjlhViewResp.setQtssxq( dto.getQtssxq() );
        rktHjslHldjjrjlhViewResp.setQtzz( dto.getQtzz() );
        rktHjslHldjjrjlhViewResp.setCgqxm( dto.getCgqxm() );
        rktHjslHldjjrjlhViewResp.setCgqgmsfhm( dto.getCgqgmsfhm() );
        rktHjslHldjjrjlhViewResp.setCgqcsrq( dto.getCgqcsrq() );
        rktHjslHldjjrjlhViewResp.setCgqxb( dto.getCgqxb() );
        rktHjslHldjjrjlhViewResp.setCgqmz( dto.getCgqmz() );
        rktHjslHldjjrjlhViewResp.setCgqhksx( dto.getCgqhksx() );
        rktHjslHldjjrjlhViewResp.setCgqhkxz( dto.getCgqhkxz() );
        rktHjslHldjjrjlhViewResp.setCgqzjqfjg( dto.getCgqzjqfjg() );
        rktHjslHldjjrjlhViewResp.setCgqzjqfrq( dto.getCgqzjqfrq() );
        rktHjslHldjjrjlhViewResp.setCgqbzxx( dto.getCgqbzxx() );

        return rktHjslHldjjrjlhViewResp;
    }

    @Override
    public RktHjslHldjjrjlhCreateResp convertToCreateResp(RktHjslHldjjrjlhDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktHjslHldjjrjlhCreateResp rktHjslHldjjrjlhCreateResp = new RktHjslHldjjrjlhCreateResp();

        rktHjslHldjjrjlhCreateResp.setYwslnbbh( dto.getYwslnbbh() );
        rktHjslHldjjrjlhCreateResp.setHhnbid( dto.getHhnbid() );
        rktHjslHldjjrjlhCreateResp.setYwslh( dto.getYwslh() );
        rktHjslHldjjrjlhCreateResp.setHh( dto.getHh() );
        rktHjslHldjjrjlhCreateResp.setHhid( dto.getHhid() );
        rktHjslHldjjrjlhCreateResp.setHlx( dto.getHlx() );
        rktHjslHldjjrjlhCreateResp.setHmc( dto.getHmc() );
        rktHjslHldjjrjlhCreateResp.setHjddzbm( dto.getHjddzbm() );
        rktHjslHldjjrjlhCreateResp.setHjdssxq( dto.getHjdssxq() );
        rktHjslHldjjrjlhCreateResp.setHjdxxdz( dto.getHjdxxdz() );
        rktHjslHldjjrjlhCreateResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        rktHjslHldjjrjlhCreateResp.setJlx( dto.getJlx() );
        rktHjslHldjjrjlhCreateResp.setMlph( dto.getMlph() );
        rktHjslHldjjrjlhCreateResp.setMlxz( dto.getMlxz() );
        rktHjslHldjjrjlhCreateResp.setLcywlx( dto.getLcywlx() );
        rktHjslHldjjrjlhCreateResp.setLcdyid( dto.getLcdyid() );
        rktHjslHldjjrjlhCreateResp.setLcmc( dto.getLcmc() );
        rktHjslHldjjrjlhCreateResp.setLcslid( dto.getLcslid() );
        rktHjslHldjjrjlhCreateResp.setLcywbt( dto.getLcywbt() );
        rktHjslHldjjrjlhCreateResp.setLcrwjd( dto.getLcrwjd() );
        rktHjslHldjjrjlhCreateResp.setLczt( dto.getLczt() );
        rktHjslHldjjrjlhCreateResp.setBlzt( dto.getBlzt() );
        rktHjslHldjjrjlhCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktHjslHldjjrjlhCreateResp.setSqrxm( dto.getSqrxm() );
        rktHjslHldjjrjlhCreateResp.setSqrxb( dto.getSqrxb() );
        rktHjslHldjjrjlhCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        rktHjslHldjjrjlhCreateResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktHjslHldjjrjlhCreateResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktHjslHldjjrjlhCreateResp.setSqrzzxz( dto.getSqrzzxz() );
        rktHjslHldjjrjlhCreateResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktHjslHldjjrjlhCreateResp.setSqrq( dto.getSqrq() );
        rktHjslHldjjrjlhCreateResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktHjslHldjjrjlhCreateResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktHjslHldjjrjlhCreateResp.setSprxm( dto.getSprxm() );
        rktHjslHldjjrjlhCreateResp.setSpsj( dto.getSpsj() );
        rktHjslHldjjrjlhCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktHjslHldjjrjlhCreateResp.setSpyj( dto.getSpyj() );
        rktHjslHldjjrjlhCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktHjslHldjjrjlhCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktHjslHldjjrjlhCreateResp.setSlrxm( dto.getSlrxm() );
        rktHjslHldjjrjlhCreateResp.setSlsj( dto.getSlsj() );
        rktHjslHldjjrjlhCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktHjslHldjjrjlhCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktHjslHldjjrjlhCreateResp.setJcwh( dto.getJcwh() );
        rktHjslHldjjrjlhCreateResp.setBz( dto.getBz() );
        rktHjslHldjjrjlhCreateResp.setXh( dto.getXh() );
        rktHjslHldjjrjlhCreateResp.setHkxz( dto.getHkxz() );
        rktHjslHldjjrjlhCreateResp.setHb( dto.getHb() );
        rktHjslHldjjrjlhCreateResp.setYhzgx( dto.getYhzgx() );
        rktHjslHldjjrjlhCreateResp.setCxsx( dto.getCxsx() );
        rktHjslHldjjrjlhCreateResp.setGmsfhm( dto.getGmsfhm() );
        rktHjslHldjjrjlhCreateResp.setXm( dto.getXm() );
        rktHjslHldjjrjlhCreateResp.setX( dto.getX() );
        rktHjslHldjjrjlhCreateResp.setM( dto.getM() );
        rktHjslHldjjrjlhCreateResp.setCym( dto.getCym() );
        rktHjslHldjjrjlhCreateResp.setXmpy( dto.getXmpy() );
        rktHjslHldjjrjlhCreateResp.setCympy( dto.getCympy() );
        rktHjslHldjjrjlhCreateResp.setXb( dto.getXb() );
        rktHjslHldjjrjlhCreateResp.setMz( dto.getMz() );
        rktHjslHldjjrjlhCreateResp.setJggjdq( dto.getJggjdq() );
        rktHjslHldjjrjlhCreateResp.setJgssxq( dto.getJgssxq() );
        rktHjslHldjjrjlhCreateResp.setJgxz( dto.getJgxz() );
        rktHjslHldjjrjlhCreateResp.setCsrq( dto.getCsrq() );
        rktHjslHldjjrjlhCreateResp.setCssj( dto.getCssj() );
        rktHjslHldjjrjlhCreateResp.setCsdgjdq( dto.getCsdgjdq() );
        rktHjslHldjjrjlhCreateResp.setCsdssxq( dto.getCsdssxq() );
        rktHjslHldjjrjlhCreateResp.setCsdxz( dto.getCsdxz() );
        rktHjslHldjjrjlhCreateResp.setWhcd( dto.getWhcd() );
        rktHjslHldjjrjlhCreateResp.setHyzk( dto.getHyzk() );
        rktHjslHldjjrjlhCreateResp.setCyzkdwbm( dto.getCyzkdwbm() );
        rktHjslHldjjrjlhCreateResp.setCyzkdwmc( dto.getCyzkdwmc() );
        rktHjslHldjjrjlhCreateResp.setZy( dto.getZy() );
        rktHjslHldjjrjlhCreateResp.setZylb( dto.getZylb() );
        rktHjslHldjjrjlhCreateResp.setZjxy( dto.getZjxy() );
        rktHjslHldjjrjlhCreateResp.setSg( dto.getSg() );
        rktHjslHldjjrjlhCreateResp.setXx( dto.getXx() );
        rktHjslHldjjrjlhCreateResp.setByzk( dto.getByzk() );
        rktHjslHldjjrjlhCreateResp.setXxjb( dto.getXxjb() );
        rktHjslHldjjrjlhCreateResp.setLxdh( dto.getLxdh() );
        rktHjslHldjjrjlhCreateResp.setFqxm( dto.getFqxm() );
        rktHjslHldjjrjlhCreateResp.setFqgmsfhm( dto.getFqgmsfhm() );
        rktHjslHldjjrjlhCreateResp.setFqcyzjdm( dto.getFqcyzjdm() );
        rktHjslHldjjrjlhCreateResp.setFqzjhm( dto.getFqzjhm() );
        rktHjslHldjjrjlhCreateResp.setFqwwx( dto.getFqwwx() );
        rktHjslHldjjrjlhCreateResp.setFqwwm( dto.getFqwwm() );
        rktHjslHldjjrjlhCreateResp.setMqxm( dto.getMqxm() );
        rktHjslHldjjrjlhCreateResp.setMqgmsfhm( dto.getMqgmsfhm() );
        rktHjslHldjjrjlhCreateResp.setMqcyzjdm( dto.getMqcyzjdm() );
        rktHjslHldjjrjlhCreateResp.setMqzjhm( dto.getMqzjhm() );
        rktHjslHldjjrjlhCreateResp.setMqwwx( dto.getMqwwx() );
        rktHjslHldjjrjlhCreateResp.setMqwwm( dto.getMqwwm() );
        rktHjslHldjjrjlhCreateResp.setPoxm( dto.getPoxm() );
        rktHjslHldjjrjlhCreateResp.setPogmsfhm( dto.getPogmsfhm() );
        rktHjslHldjjrjlhCreateResp.setPocyzjdm( dto.getPocyzjdm() );
        rktHjslHldjjrjlhCreateResp.setPozjhm( dto.getPozjhm() );
        rktHjslHldjjrjlhCreateResp.setPowwx( dto.getPowwx() );
        rktHjslHldjjrjlhCreateResp.setPowwm( dto.getPowwm() );
        rktHjslHldjjrjlhCreateResp.setJhryxm( dto.getJhryxm() );
        rktHjslHldjjrjlhCreateResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        rktHjslHldjjrjlhCreateResp.setJhryjhgx( dto.getJhryjhgx() );
        rktHjslHldjjrjlhCreateResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        rktHjslHldjjrjlhCreateResp.setJhryzjhm( dto.getJhryzjhm() );
        rktHjslHldjjrjlhCreateResp.setJhrywwx( dto.getJhrywwx() );
        rktHjslHldjjrjlhCreateResp.setJhrywwm( dto.getJhrywwm() );
        rktHjslHldjjrjlhCreateResp.setJhrylxdh( dto.getJhrylxdh() );
        rktHjslHldjjrjlhCreateResp.setJhrexm( dto.getJhrexm() );
        rktHjslHldjjrjlhCreateResp.setJhregmsfhm( dto.getJhregmsfhm() );
        rktHjslHldjjrjlhCreateResp.setJhrejhgx( dto.getJhrejhgx() );
        rktHjslHldjjrjlhCreateResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        rktHjslHldjjrjlhCreateResp.setJhrezjhm( dto.getJhrezjhm() );
        rktHjslHldjjrjlhCreateResp.setJhrewwx( dto.getJhrewwx() );
        rktHjslHldjjrjlhCreateResp.setJhrewwm( dto.getJhrewwm() );
        rktHjslHldjjrjlhCreateResp.setJhrelxdh( dto.getJhrelxdh() );
        rktHjslHldjjrjlhCreateResp.setZjlb( dto.getZjlb() );
        rktHjslHldjjrjlhCreateResp.setQfjg( dto.getQfjg() );
        rktHjslHldjjrjlhCreateResp.setYxqxqsrq( dto.getYxqxqsrq() );
        rktHjslHldjjrjlhCreateResp.setYxqxjzrq( dto.getYxqxjzrq() );
        rktHjslHldjjrjlhCreateResp.setHgdjjrjrylhlb( dto.getHgdjjrjrylhlb() );
        rktHjslHldjjrjlhCreateResp.setHgdjjrjrylhlbdm( dto.getHgdjjrjrylhlbdm() );
        rktHjslHldjjrjlhCreateResp.setLzdgjdq( dto.getLzdgjdq() );
        rktHjslHldjjrjlhCreateResp.setLzdxz( dto.getLzdxz() );
        rktHjslHldjjrjlhCreateResp.setBdfw( dto.getBdfw() );
        rktHjslHldjjrjlhCreateResp.setYcyzjzl( dto.getYcyzjzl() );
        rktHjslHldjjrjlhCreateResp.setYcyzjhm( dto.getYcyzjhm() );
        rktHjslHldjjrjlhCreateResp.setBlhjywid( dto.getBlhjywid() );
        rktHjslHldjjrjlhCreateResp.setHjywblsj( dto.getHjywblsj() );
        rktHjslHldjjrjlhCreateResp.setQtssxq( dto.getQtssxq() );
        rktHjslHldjjrjlhCreateResp.setQtzz( dto.getQtzz() );
        rktHjslHldjjrjlhCreateResp.setCgqxm( dto.getCgqxm() );
        rktHjslHldjjrjlhCreateResp.setCgqgmsfhm( dto.getCgqgmsfhm() );
        rktHjslHldjjrjlhCreateResp.setCgqcsrq( dto.getCgqcsrq() );
        rktHjslHldjjrjlhCreateResp.setCgqxb( dto.getCgqxb() );
        rktHjslHldjjrjlhCreateResp.setCgqmz( dto.getCgqmz() );
        rktHjslHldjjrjlhCreateResp.setCgqhksx( dto.getCgqhksx() );
        rktHjslHldjjrjlhCreateResp.setCgqhkxz( dto.getCgqhkxz() );
        rktHjslHldjjrjlhCreateResp.setCgqzjqfjg( dto.getCgqzjqfjg() );
        rktHjslHldjjrjlhCreateResp.setCgqzjqfrq( dto.getCgqzjqfrq() );
        rktHjslHldjjrjlhCreateResp.setCgqbzxx( dto.getCgqbzxx() );

        return rktHjslHldjjrjlhCreateResp;
    }
}
