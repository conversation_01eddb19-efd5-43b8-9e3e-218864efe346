package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class KsxtQcfkxxbDOTableDef extends TableDef {

    /**
     * 跨省协同迁出反馈信息表DO

 <AUTHOR>
 @date 2025-08-05 13:59:15
 @see com.zjjcnt.project.ck.base.dto.KsxtQcfkxxbDTO
     */
    public static final KsxtQcfkxxbDOTableDef KSXT_QCFKXXB_DO = new KsxtQcfkxxbDOTableDef();

    /**
     * 备注
     */
    public final QueryColumn BZ = new QueryColumn(this, "bz");

    /**
     * 姓名
     */
    public final QueryColumn XM = new QueryColumn(this, "xm");

    /**
     * 职业
     */
    public final QueryColumn ZY = new QueryColumn(this, "zy");

    /**
     * 曾用名
     */
    public final QueryColumn CYM = new QueryColumn(this, "cym");

    /**
     * 办理时间
     */
    public final QueryColumn BLSJ = new QueryColumn(this, "blsj");

    /**
     * 出生日期
     */
    public final QueryColumn CSRQ = new QueryColumn(this, "csrq");

    /**
     * 民族
     */
    public final QueryColumn MZDM = new QueryColumn(this, "mzdm");

    /**
     * 入库时间
     */
    public final QueryColumn RKSJ = new QueryColumn(this, "rksj");

    /**
     * 性别
     */
    public final QueryColumn XBDM = new QueryColumn(this, "xbdm");

    /**
     * 文化程度
     */
    public final QueryColumn XLDM = new QueryColumn(this, "xldm");

    /**
     * 办理人姓名
     */
    public final QueryColumn BLRXM = new QueryColumn(this, "blrxm");

    /**
     * 持证人姓名
     */
    public final QueryColumn CZRXM = new QueryColumn(this, "czrxm");

    /**
     * 迁移证编号
     */
    public final QueryColumn QYZBH = new QueryColumn(this, "qyzbh");

    /**
     * 准迁证编号
     */
    public final QueryColumn ZQZBH = new QueryColumn(this, "zqzbh");

    /**
     * 公民身份号码
     */
    public final QueryColumn GMSFHM = new QueryColumn(this, "gmsfhm");

    /**
     * 婚姻状况
     */
    public final QueryColumn HYZKDM = new QueryColumn(this, "hyzkdm");

    /**
     * 跨省协同ID
     */
    public final QueryColumn KSXTID = new QueryColumn(this, "ksxtid");

    /**
     * 区域范围代码
     */
    public final QueryColumn QYFWDM = new QueryColumn(this, "qyfwdm");

    /**
     * 户籍地_联系电话
     */
    public final QueryColumn HJDLXDH = new QueryColumn(this, "hjdlxdh");

    /**
     * 迁移证_签发日期
     */
    public final QueryColumn QYZQFRQ = new QueryColumn(this, "qyzqfrq");

    /**
     * 籍贯_省市县区
     */
    public final QueryColumn JGSSXQDM = new QueryColumn(this, "jgssxqdm");

    /**
     * 迁移（流动）原因
     */
    public final QueryColumn QYLDYYDM = new QueryColumn(this, "qyldyydm");

    /**
     * 出生地_省市县区
     */
    public final QueryColumn CSDSSXQDM = new QueryColumn(this, "csdssxqdm");

    /**
     * 持证人公民身份号码
     */
    public final QueryColumn CZRGMSFHM = new QueryColumn(this, "czrgmsfhm");

    /**
     * 户籍地_公安机关名称
     */
    public final QueryColumn HJDGAJGMC = new QueryColumn(this, "hjdgajgmc");

    /**
     * 籍贯_国家（地区）
     */
    public final QueryColumn JGGJHDQDM = new QueryColumn(this, "jggjhdqdm");

    /**
     * 籍贯_区划内详细地址
     */
    public final QueryColumn JGQHNXXDZ = new QueryColumn(this, "jgqhnxxdz");

    /**
     * 去往地_省市县（区）
     */
    public final QueryColumn QWDSSXQDM = new QueryColumn(this, "qwdssxqdm");

    /**
     * 原住址城乡分类代码
     */
    public final QueryColumn YZZCXFLDM = new QueryColumn(this, "yzzcxfldm");

    /**
     * 原地址住址省市县（区）
     */
    public final QueryColumn YZZSSXQDM = new QueryColumn(this, "yzzssxqdm");

    /**
     * 出生地_国家（地区）
     */
    public final QueryColumn CSDGJHDQDM = new QueryColumn(this, "csdgjhdqdm");

    /**
     * 出生地_区划内详细地址
     */
    public final QueryColumn CSDQHNXXDZ = new QueryColumn(this, "csdqhnxxdz");

    /**
     * 电子迁移_证照标识
     */
    public final QueryColumn DZQYDZZZBZ = new QueryColumn(this, "dzqydzzzbz");

    /**
     * 电子准迁_证照标识
     */
    public final QueryColumn DZZQDZZZBZ = new QueryColumn(this, "dzzqdzzzbz");

    /**
     * 跨省协同迁出反馈ID
     */
    public final QueryColumn KSXTQCFKID = new QueryColumn(this, "ksxtqcfkid");

    /**
     * 去往地_区划内详细地址
     */
    public final QueryColumn QWDQHNXXDZ = new QueryColumn(this, "qwdqhnxxdz");

    /**
     * 迁移证_有效期截止日期
     */
    public final QueryColumn QYZYXQJZRQ = new QueryColumn(this, "qyzyxqjzrq");

    /**
     * 原住址住址区划内详细地址
     */
    public final QueryColumn YZZQHNXXDZ = new QueryColumn(this, "yzzqhnxxdz");

    /**
     * 治安管理业务协同编号
     */
    public final QueryColumn ZAGLYWXTBH = new QueryColumn(this, "zaglywxtbh");

    /**
     * 户籍地_公安机关机构代码
     */
    public final QueryColumn HJDGAJGJGDM = new QueryColumn(this, "hjdgajgjgdm");

    /**
     * 受理地数据归属单位代码
     */
    public final QueryColumn HJDSJGSDWDM = new QueryColumn(this, "hjdsjgsdwdm");

    /**
     * 受理地数据归属单位名称
     */
    public final QueryColumn HJDSJGSDWMC = new QueryColumn(this, "hjdsjgsdwmc");

    /**
     * 发送单位数据归属单位代码
     */
    public final QueryColumn FSDWSJGSDWDM = new QueryColumn(this, "fsdwsjgsdwdm");

    /**
     * 发送单位数据归属单位名称
     */
    public final QueryColumn FSDWSJGSDWMC = new QueryColumn(this, "fsdwsjgsdwmc");

    /**
     * 接收单位数据归属单位代码
     */
    public final QueryColumn JSDWSJGSDWDM = new QueryColumn(this, "jsdwsjgsdwdm");

    /**
     * 接收单位数据归属单位名称
     */
    public final QueryColumn JSDWSJGSDWMC = new QueryColumn(this, "jsdwsjgsdwmc");

    /**
     * 与持证人关系家庭关系
     */
    public final QueryColumn YCZRGXJTGXDM = new QueryColumn(this, "yczrgxjtgxdm");

    /**
     * 迁移证_签发机关_公安机关名称
     */
    public final QueryColumn QYZQFJGGAJGMC = new QueryColumn(this, "qyzqfjggajgmc");

    /**
     * 电子迁移_治安管理电子证照编号
     */
    public final QueryColumn DZQYZAGLDZZZBH = new QueryColumn(this, "dzqyzagldzzzbh");

    /**
     * 电子准迁_治安管理电子证照编号
     */
    public final QueryColumn DZZQZAGLDZZZBH = new QueryColumn(this, "dzzqzagldzzzbh");

    /**
     * 去往地_户口登记机关公安机关名称
     */
    public final QueryColumn QWDHKDJJGGAJGMC = new QueryColumn(this, "qwdhkdjjggajgmc");

    /**
     * 迁移证_签发机关_公安机关机构代码
     */
    public final QueryColumn QYZQFJGGAJGJGDM = new QueryColumn(this, "qyzqfjggajgjgdm");

    /**
     * 去往地_户口登记机关公安机关机构代码
     */
    public final QueryColumn QWDHKDJJGGAJGJGDM = new QueryColumn(this, "qwdhkdjjggajgjgdm");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{BZ, XM, ZY, CYM, BLSJ, CSRQ, MZDM, RKSJ, XBDM, XLDM, BLRXM, CZRXM, QYZBH, ZQZBH, GMSFHM, HYZKDM, KSXTID, QYFWDM, HJDLXDH, QYZQFRQ, JGSSXQDM, QYLDYYDM, CSDSSXQDM, CZRGMSFHM, HJDGAJGMC, JGGJHDQDM, JGQHNXXDZ, QWDSSXQDM, YZZCXFLDM, YZZSSXQDM, CSDGJHDQDM, CSDQHNXXDZ, DZQYDZZZBZ, DZZQDZZZBZ, KSXTQCFKID, QWDQHNXXDZ, QYZYXQJZRQ, YZZQHNXXDZ, ZAGLYWXTBH, HJDGAJGJGDM, HJDSJGSDWDM, HJDSJGSDWMC, FSDWSJGSDWDM, FSDWSJGSDWMC, JSDWSJGSDWDM, JSDWSJGSDWMC, YCZRGXJTGXDM, QYZQFJGGAJGMC, DZQYZAGLDZZZBH, DZZQZAGLDZZZBH, QWDHKDJJGGAJGMC, QYZQFJGGAJGJGDM, QWDHKDJJGGAJGJGDM};

    public KsxtQcfkxxbDOTableDef() {
        super("", "ksxt_qcfkxxb");
    }

    private KsxtQcfkxxbDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public KsxtQcfkxxbDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new KsxtQcfkxxbDOTableDef("", "ksxt_qcfkxxb", alias));
    }

}
