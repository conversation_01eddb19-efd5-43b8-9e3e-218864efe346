package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.RktYwslJbLsDTO;
import com.zjjcnt.project.ck.base.dto.req.RktYwslJbLsCreateReq;
import com.zjjcnt.project.ck.base.dto.req.RktYwslJbLsPageReq;
import com.zjjcnt.project.ck.base.dto.req.RktYwslJbLsUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslJbLsCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslJbLsPageResp;
import com.zjjcnt.project.ck.base.dto.resp.RktYwslJbLsViewResp;
import com.zjjcnt.project.ck.base.entity.RktYwslJbLsDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:40+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class RktYwslJbLsConvertImpl implements RktYwslJbLsConvert {

    @Override
    public RktYwslJbLsDTO convert(RktYwslJbLsDO entity) {
        if ( entity == null ) {
            return null;
        }

        RktYwslJbLsDTO rktYwslJbLsDTO = new RktYwslJbLsDTO();

        rktYwslJbLsDTO.setId( entity.getId() );
        rktYwslJbLsDTO.setYwslh( entity.getYwslh() );
        rktYwslJbLsDTO.setLcdyid( entity.getLcdyid() );
        rktYwslJbLsDTO.setLcmc( entity.getLcmc() );
        rktYwslJbLsDTO.setSpyj( entity.getSpyj() );
        rktYwslJbLsDTO.setSlrs( entity.getSlrs() );
        rktYwslJbLsDTO.setGmsfhm( entity.getGmsfhm() );
        rktYwslJbLsDTO.setXm( entity.getXm() );
        rktYwslJbLsDTO.setLcywlx( entity.getLcywlx() );
        rktYwslJbLsDTO.setLcslid( entity.getLcslid() );
        rktYwslJbLsDTO.setLcywbt( entity.getLcywbt() );
        rktYwslJbLsDTO.setLcrwjd( entity.getLcrwjd() );
        rktYwslJbLsDTO.setLczt( entity.getLczt() );
        rktYwslJbLsDTO.setBlzt( entity.getBlzt() );
        rktYwslJbLsDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        rktYwslJbLsDTO.setSqrxm( entity.getSqrxm() );
        rktYwslJbLsDTO.setSqrxb( entity.getSqrxb() );
        rktYwslJbLsDTO.setSqrlxdh( entity.getSqrlxdh() );
        rktYwslJbLsDTO.setSqrybdrgx( entity.getSqrybdrgx() );
        rktYwslJbLsDTO.setSqrzzssxq( entity.getSqrzzssxq() );
        rktYwslJbLsDTO.setSqrzzxz( entity.getSqrzzxz() );
        rktYwslJbLsDTO.setSqrhkdjjg( entity.getSqrhkdjjg() );
        rktYwslJbLsDTO.setSjrxm( entity.getSjrxm() );
        rktYwslJbLsDTO.setSjrlxdh( entity.getSjrlxdh() );
        rktYwslJbLsDTO.setSqrtxdz( entity.getSqrtxdz() );
        rktYwslJbLsDTO.setSqrzjmc( entity.getSqrzjmc() );
        rktYwslJbLsDTO.setSqrzjhm( entity.getSqrzjhm() );
        rktYwslJbLsDTO.setSqrgzdw( entity.getSqrgzdw() );
        rktYwslJbLsDTO.setHjsxcxlb( entity.getHjsxcxlb() );
        rktYwslJbLsDTO.setSqcxly( entity.getSqcxly() );
        rktYwslJbLsDTO.setSfscdzqzhky( entity.getSfscdzqzhky() );
        rktYwslJbLsDTO.setSfdyhkbsy( entity.getSfdyhkbsy() );
        rktYwslJbLsDTO.setSfazjthfsdy( entity.getSfazjthfsdy() );
        rktYwslJbLsDTO.setSfdyqhry( entity.getSfdyqhry() );
        rktYwslJbLsDTO.setSfdycsyy( entity.getSfdycsyy() );
        rktYwslJbLsDTO.setSlrlxdh( entity.getSlrlxdh() );
        rktYwslJbLsDTO.setSprlxdh( entity.getSprlxdh() );
        rktYwslJbLsDTO.setSfxyyjzj( entity.getSfxyyjzj() );
        rktYwslJbLsDTO.setSfyjyjzj( entity.getSfyjyjzj() );
        rktYwslJbLsDTO.setKddh( entity.getKddh() );
        rktYwslJbLsDTO.setSpxxlylb( entity.getSpxxlylb() );
        rktYwslJbLsDTO.setSpxxly( entity.getSpxxly() );
        rktYwslJbLsDTO.setYsbxxbh( entity.getYsbxxbh() );
        rktYwslJbLsDTO.setYsbxtywwjmc( entity.getYsbxtywwjmc() );
        rktYwslJbLsDTO.setYsbxtywwjlx( entity.getYsbxtywwjlx() );
        rktYwslJbLsDTO.setSqrq( entity.getSqrq() );
        rktYwslJbLsDTO.setSpdwgajgjgdm( entity.getSpdwgajgjgdm() );
        rktYwslJbLsDTO.setSpdwgajgmc( entity.getSpdwgajgmc() );
        rktYwslJbLsDTO.setSprxm( entity.getSprxm() );
        rktYwslJbLsDTO.setSpsj( entity.getSpsj() );
        rktYwslJbLsDTO.setSpjgdm( entity.getSpjgdm() );
        rktYwslJbLsDTO.setSldwgajgjgdm( entity.getSldwgajgjgdm() );
        rktYwslJbLsDTO.setSldwgajgmc( entity.getSldwgajgmc() );
        rktYwslJbLsDTO.setSlrxm( entity.getSlrxm() );
        rktYwslJbLsDTO.setSlsj( entity.getSlsj() );
        rktYwslJbLsDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        rktYwslJbLsDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        rktYwslJbLsDTO.setJcwh( entity.getJcwh() );
        rktYwslJbLsDTO.setBz( entity.getBz() );
        rktYwslJbLsDTO.setBlhjywid( entity.getBlhjywid() );
        rktYwslJbLsDTO.setHjywblsj( entity.getHjywblsj() );
        rktYwslJbLsDTO.setSlrid( entity.getSlrid() );
        rktYwslJbLsDTO.setPjjg( entity.getPjjg() );
        rktYwslJbLsDTO.setPjpljc( entity.getPjpljc() );
        rktYwslJbLsDTO.setPjsj( entity.getPjsj() );
        rktYwslJbLsDTO.setSbsfxy( entity.getSbsfxy() );
        rktYwslJbLsDTO.setSbzt( entity.getSbzt() );
        rktYwslJbLsDTO.setSbywlx( entity.getSbywlx() );
        rktYwslJbLsDTO.setSbywmc( entity.getSbywmc() );
        rktYwslJbLsDTO.setSbsbsj( entity.getSbsbsj() );
        rktYwslJbLsDTO.setSbslsj( entity.getSbslsj() );
        rktYwslJbLsDTO.setSbshsj( entity.getSbshsj() );
        rktYwslJbLsDTO.setSbbjsj( entity.getSbbjsj() );
        rktYwslJbLsDTO.setSbspkssj( entity.getSbspkssj() );
        rktYwslJbLsDTO.setSbspjssj( entity.getSbspjssj() );
        rktYwslJbLsDTO.setSbsprxm( entity.getSbsprxm() );
        rktYwslJbLsDTO.setSbsprdwdm( entity.getSbsprdwdm() );
        rktYwslJbLsDTO.setSbspjgdm( entity.getSbspjgdm() );
        rktYwslJbLsDTO.setSbspyj( entity.getSbspyj() );
        rktYwslJbLsDTO.setMjscshhs( entity.getMjscshhs() );
        rktYwslJbLsDTO.setSpxxcjfs( entity.getSpxxcjfs() );
        rktYwslJbLsDTO.setHzxm( entity.getHzxm() );
        rktYwslJbLsDTO.setHzgmsfhm( entity.getHzgmsfhm() );
        rktYwslJbLsDTO.setDababz( entity.getDababz() );
        rktYwslJbLsDTO.setDabasj( entity.getDabasj() );
        rktYwslJbLsDTO.setRlhbz( entity.getRlhbz() );
        rktYwslJbLsDTO.setRsjshjg( entity.getRsjshjg() );
        rktYwslJbLsDTO.setRsjshly( entity.getRsjshly() );
        rktYwslJbLsDTO.setRsjshsj( entity.getRsjshsj() );
        rktYwslJbLsDTO.setRsjshr( entity.getRsjshr() );
        rktYwslJbLsDTO.setGjnzsbh( entity.getGjnzsbh() );
        rktYwslJbLsDTO.setZwwdtbh( entity.getZwwdtbh() );
        rktYwslJbLsDTO.setZwwdtmc( entity.getZwwdtmc() );
        rktYwslJbLsDTO.setHlwsqlx( entity.getHlwsqlx() );
        rktYwslJbLsDTO.setHalluserid( entity.getHalluserid() );
        rktYwslJbLsDTO.setHallusername( entity.getHallusername() );
        rktYwslJbLsDTO.setApplyfrom( entity.getApplyfrom() );
        rktYwslJbLsDTO.setBustype( entity.getBustype() );
        rktYwslJbLsDTO.setSftqhlwcl( entity.getSftqhlwcl() );
        rktYwslJbLsDTO.setDycjlx( entity.getDycjlx() );
        rktYwslJbLsDTO.setBgyjssbbz( entity.getBgyjssbbz() );
        rktYwslJbLsDTO.setBgyjssbsj( entity.getBgyjssbsj() );
        rktYwslJbLsDTO.setSqbsfbzqm( entity.getSqbsfbzqm() );
        rktYwslJbLsDTO.setCbsfbzqm( entity.getCbsfbzqm() );
        rktYwslJbLsDTO.setSfksxtqr( entity.getSfksxtqr() );
        rktYwslJbLsDTO.setQtsqr( entity.getQtsqr() );
        rktYwslJbLsDTO.setSqrrxbdsj( entity.getSqrrxbdsj() );
        rktYwslJbLsDTO.setSqrrxbdxsd( entity.getSqrrxbdxsd() );
        rktYwslJbLsDTO.setSqrrxbdjg( entity.getSqrrxbdjg() );
        rktYwslJbLsDTO.setSqrgzdwlb( entity.getSqrgzdwlb() );
        rktYwslJbLsDTO.setSqrgzdwtyshxydm( entity.getSqrgzdwtyshxydm() );
        rktYwslJbLsDTO.setQtsqrgmsfhm( entity.getQtsqrgmsfhm() );
        rktYwslJbLsDTO.setQtsqrlxdh( entity.getQtsqrlxdh() );

        return rktYwslJbLsDTO;
    }

    @Override
    public RktYwslJbLsDO convertToDO(RktYwslJbLsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslJbLsDO rktYwslJbLsDO = new RktYwslJbLsDO();

        rktYwslJbLsDO.setId( dto.getId() );
        rktYwslJbLsDO.setYwslh( dto.getYwslh() );
        rktYwslJbLsDO.setLcdyid( dto.getLcdyid() );
        rktYwslJbLsDO.setLcmc( dto.getLcmc() );
        rktYwslJbLsDO.setSpyj( dto.getSpyj() );
        rktYwslJbLsDO.setSlrs( dto.getSlrs() );
        rktYwslJbLsDO.setGmsfhm( dto.getGmsfhm() );
        rktYwslJbLsDO.setXm( dto.getXm() );
        rktYwslJbLsDO.setLcywlx( dto.getLcywlx() );
        rktYwslJbLsDO.setLcslid( dto.getLcslid() );
        rktYwslJbLsDO.setLcywbt( dto.getLcywbt() );
        rktYwslJbLsDO.setLcrwjd( dto.getLcrwjd() );
        rktYwslJbLsDO.setLczt( dto.getLczt() );
        rktYwslJbLsDO.setBlzt( dto.getBlzt() );
        rktYwslJbLsDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktYwslJbLsDO.setSqrxm( dto.getSqrxm() );
        rktYwslJbLsDO.setSqrxb( dto.getSqrxb() );
        rktYwslJbLsDO.setSqrlxdh( dto.getSqrlxdh() );
        rktYwslJbLsDO.setSqrybdrgx( dto.getSqrybdrgx() );
        rktYwslJbLsDO.setSqrzzssxq( dto.getSqrzzssxq() );
        rktYwslJbLsDO.setSqrzzxz( dto.getSqrzzxz() );
        rktYwslJbLsDO.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktYwslJbLsDO.setSjrxm( dto.getSjrxm() );
        rktYwslJbLsDO.setSjrlxdh( dto.getSjrlxdh() );
        rktYwslJbLsDO.setSqrtxdz( dto.getSqrtxdz() );
        rktYwslJbLsDO.setSqrzjmc( dto.getSqrzjmc() );
        rktYwslJbLsDO.setSqrzjhm( dto.getSqrzjhm() );
        rktYwslJbLsDO.setSqrgzdw( dto.getSqrgzdw() );
        rktYwslJbLsDO.setHjsxcxlb( dto.getHjsxcxlb() );
        rktYwslJbLsDO.setSqcxly( dto.getSqcxly() );
        rktYwslJbLsDO.setSfscdzqzhky( dto.getSfscdzqzhky() );
        rktYwslJbLsDO.setSfdyhkbsy( dto.getSfdyhkbsy() );
        rktYwslJbLsDO.setSfazjthfsdy( dto.getSfazjthfsdy() );
        rktYwslJbLsDO.setSfdyqhry( dto.getSfdyqhry() );
        rktYwslJbLsDO.setSfdycsyy( dto.getSfdycsyy() );
        rktYwslJbLsDO.setSlrlxdh( dto.getSlrlxdh() );
        rktYwslJbLsDO.setSprlxdh( dto.getSprlxdh() );
        rktYwslJbLsDO.setSfxyyjzj( dto.getSfxyyjzj() );
        rktYwslJbLsDO.setSfyjyjzj( dto.getSfyjyjzj() );
        rktYwslJbLsDO.setKddh( dto.getKddh() );
        rktYwslJbLsDO.setSpxxlylb( dto.getSpxxlylb() );
        rktYwslJbLsDO.setSpxxly( dto.getSpxxly() );
        rktYwslJbLsDO.setYsbxxbh( dto.getYsbxxbh() );
        rktYwslJbLsDO.setYsbxtywwjmc( dto.getYsbxtywwjmc() );
        rktYwslJbLsDO.setYsbxtywwjlx( dto.getYsbxtywwjlx() );
        rktYwslJbLsDO.setSqrq( dto.getSqrq() );
        rktYwslJbLsDO.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktYwslJbLsDO.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktYwslJbLsDO.setSprxm( dto.getSprxm() );
        rktYwslJbLsDO.setSpsj( dto.getSpsj() );
        rktYwslJbLsDO.setSpjgdm( dto.getSpjgdm() );
        rktYwslJbLsDO.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktYwslJbLsDO.setSldwgajgmc( dto.getSldwgajgmc() );
        rktYwslJbLsDO.setSlrxm( dto.getSlrxm() );
        rktYwslJbLsDO.setSlsj( dto.getSlsj() );
        rktYwslJbLsDO.setSjgsdwdm( dto.getSjgsdwdm() );
        rktYwslJbLsDO.setSjgsdwmc( dto.getSjgsdwmc() );
        rktYwslJbLsDO.setJcwh( dto.getJcwh() );
        rktYwslJbLsDO.setBz( dto.getBz() );
        rktYwslJbLsDO.setBlhjywid( dto.getBlhjywid() );
        rktYwslJbLsDO.setHjywblsj( dto.getHjywblsj() );
        rktYwslJbLsDO.setSlrid( dto.getSlrid() );
        rktYwslJbLsDO.setPjjg( dto.getPjjg() );
        rktYwslJbLsDO.setPjpljc( dto.getPjpljc() );
        rktYwslJbLsDO.setPjsj( dto.getPjsj() );
        rktYwslJbLsDO.setSbsfxy( dto.getSbsfxy() );
        rktYwslJbLsDO.setSbzt( dto.getSbzt() );
        rktYwslJbLsDO.setSbywlx( dto.getSbywlx() );
        rktYwslJbLsDO.setSbywmc( dto.getSbywmc() );
        rktYwslJbLsDO.setSbsbsj( dto.getSbsbsj() );
        rktYwslJbLsDO.setSbslsj( dto.getSbslsj() );
        rktYwslJbLsDO.setSbshsj( dto.getSbshsj() );
        rktYwslJbLsDO.setSbbjsj( dto.getSbbjsj() );
        rktYwslJbLsDO.setSbspkssj( dto.getSbspkssj() );
        rktYwslJbLsDO.setSbspjssj( dto.getSbspjssj() );
        rktYwslJbLsDO.setSbsprxm( dto.getSbsprxm() );
        rktYwslJbLsDO.setSbsprdwdm( dto.getSbsprdwdm() );
        rktYwslJbLsDO.setSbspjgdm( dto.getSbspjgdm() );
        rktYwslJbLsDO.setSbspyj( dto.getSbspyj() );
        rktYwslJbLsDO.setMjscshhs( dto.getMjscshhs() );
        rktYwslJbLsDO.setSpxxcjfs( dto.getSpxxcjfs() );
        rktYwslJbLsDO.setHzxm( dto.getHzxm() );
        rktYwslJbLsDO.setHzgmsfhm( dto.getHzgmsfhm() );
        rktYwslJbLsDO.setDababz( dto.getDababz() );
        rktYwslJbLsDO.setDabasj( dto.getDabasj() );
        rktYwslJbLsDO.setRlhbz( dto.getRlhbz() );
        rktYwslJbLsDO.setRsjshjg( dto.getRsjshjg() );
        rktYwslJbLsDO.setRsjshly( dto.getRsjshly() );
        rktYwslJbLsDO.setRsjshsj( dto.getRsjshsj() );
        rktYwslJbLsDO.setRsjshr( dto.getRsjshr() );
        rktYwslJbLsDO.setGjnzsbh( dto.getGjnzsbh() );
        rktYwslJbLsDO.setZwwdtbh( dto.getZwwdtbh() );
        rktYwslJbLsDO.setZwwdtmc( dto.getZwwdtmc() );
        rktYwslJbLsDO.setHlwsqlx( dto.getHlwsqlx() );
        rktYwslJbLsDO.setHalluserid( dto.getHalluserid() );
        rktYwslJbLsDO.setHallusername( dto.getHallusername() );
        rktYwslJbLsDO.setApplyfrom( dto.getApplyfrom() );
        rktYwslJbLsDO.setBustype( dto.getBustype() );
        rktYwslJbLsDO.setSftqhlwcl( dto.getSftqhlwcl() );
        rktYwslJbLsDO.setDycjlx( dto.getDycjlx() );
        rktYwslJbLsDO.setBgyjssbbz( dto.getBgyjssbbz() );
        rktYwslJbLsDO.setBgyjssbsj( dto.getBgyjssbsj() );
        rktYwslJbLsDO.setSqbsfbzqm( dto.getSqbsfbzqm() );
        rktYwslJbLsDO.setCbsfbzqm( dto.getCbsfbzqm() );
        rktYwslJbLsDO.setSfksxtqr( dto.getSfksxtqr() );
        rktYwslJbLsDO.setQtsqr( dto.getQtsqr() );
        rktYwslJbLsDO.setSqrrxbdsj( dto.getSqrrxbdsj() );
        rktYwslJbLsDO.setSqrrxbdxsd( dto.getSqrrxbdxsd() );
        rktYwslJbLsDO.setSqrrxbdjg( dto.getSqrrxbdjg() );
        rktYwslJbLsDO.setSqrgzdwlb( dto.getSqrgzdwlb() );
        rktYwslJbLsDO.setSqrgzdwtyshxydm( dto.getSqrgzdwtyshxydm() );
        rktYwslJbLsDO.setQtsqrgmsfhm( dto.getQtsqrgmsfhm() );
        rktYwslJbLsDO.setQtsqrlxdh( dto.getQtsqrlxdh() );

        return rktYwslJbLsDO;
    }

    @Override
    public RktYwslJbLsDTO convertToDTO(RktYwslJbLsPageReq req) {
        if ( req == null ) {
            return null;
        }

        RktYwslJbLsDTO rktYwslJbLsDTO = new RktYwslJbLsDTO();

        rktYwslJbLsDTO.setLcdyid( req.getLcdyid() );
        rktYwslJbLsDTO.setLcmc( req.getLcmc() );
        rktYwslJbLsDTO.setSpyj( req.getSpyj() );
        rktYwslJbLsDTO.setSlrs( req.getSlrs() );
        rktYwslJbLsDTO.setGmsfhm( req.getGmsfhm() );
        rktYwslJbLsDTO.setXm( req.getXm() );
        rktYwslJbLsDTO.setLcywlx( req.getLcywlx() );
        rktYwslJbLsDTO.setLcslid( req.getLcslid() );
        rktYwslJbLsDTO.setLcywbt( req.getLcywbt() );
        rktYwslJbLsDTO.setLcrwjd( req.getLcrwjd() );
        rktYwslJbLsDTO.setLczt( req.getLczt() );
        rktYwslJbLsDTO.setBlzt( req.getBlzt() );
        rktYwslJbLsDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktYwslJbLsDTO.setSqrxm( req.getSqrxm() );
        rktYwslJbLsDTO.setSqrxb( req.getSqrxb() );
        rktYwslJbLsDTO.setSqrlxdh( req.getSqrlxdh() );
        rktYwslJbLsDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktYwslJbLsDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktYwslJbLsDTO.setSqrzzxz( req.getSqrzzxz() );
        rktYwslJbLsDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktYwslJbLsDTO.setSjrxm( req.getSjrxm() );
        rktYwslJbLsDTO.setSjrlxdh( req.getSjrlxdh() );
        rktYwslJbLsDTO.setSqrtxdz( req.getSqrtxdz() );
        rktYwslJbLsDTO.setSqrzjmc( req.getSqrzjmc() );
        rktYwslJbLsDTO.setSqrzjhm( req.getSqrzjhm() );
        rktYwslJbLsDTO.setSqrgzdw( req.getSqrgzdw() );
        rktYwslJbLsDTO.setHjsxcxlb( req.getHjsxcxlb() );
        rktYwslJbLsDTO.setSqcxly( req.getSqcxly() );
        rktYwslJbLsDTO.setSfscdzqzhky( req.getSfscdzqzhky() );
        rktYwslJbLsDTO.setSfdyhkbsy( req.getSfdyhkbsy() );
        rktYwslJbLsDTO.setSfazjthfsdy( req.getSfazjthfsdy() );
        rktYwslJbLsDTO.setSfdyqhry( req.getSfdyqhry() );
        rktYwslJbLsDTO.setSfdycsyy( req.getSfdycsyy() );
        rktYwslJbLsDTO.setSlrlxdh( req.getSlrlxdh() );
        rktYwslJbLsDTO.setSprlxdh( req.getSprlxdh() );
        rktYwslJbLsDTO.setSfxyyjzj( req.getSfxyyjzj() );
        rktYwslJbLsDTO.setSfyjyjzj( req.getSfyjyjzj() );
        rktYwslJbLsDTO.setKddh( req.getKddh() );
        rktYwslJbLsDTO.setSpxxlylb( req.getSpxxlylb() );
        rktYwslJbLsDTO.setSpxxly( req.getSpxxly() );
        rktYwslJbLsDTO.setYsbxxbh( req.getYsbxxbh() );
        rktYwslJbLsDTO.setYsbxtywwjmc( req.getYsbxtywwjmc() );
        rktYwslJbLsDTO.setYsbxtywwjlx( req.getYsbxtywwjlx() );
        rktYwslJbLsDTO.setSqrq( req.getSqrq() );
        rktYwslJbLsDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktYwslJbLsDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktYwslJbLsDTO.setSprxm( req.getSprxm() );
        rktYwslJbLsDTO.setSpsj( req.getSpsj() );
        rktYwslJbLsDTO.setSpjgdm( req.getSpjgdm() );
        rktYwslJbLsDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktYwslJbLsDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktYwslJbLsDTO.setSlrxm( req.getSlrxm() );
        rktYwslJbLsDTO.setSlsj( req.getSlsj() );
        rktYwslJbLsDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktYwslJbLsDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktYwslJbLsDTO.setJcwh( req.getJcwh() );
        rktYwslJbLsDTO.setBz( req.getBz() );
        rktYwslJbLsDTO.setBlhjywid( req.getBlhjywid() );
        rktYwslJbLsDTO.setHjywblsj( req.getHjywblsj() );
        rktYwslJbLsDTO.setSlrid( req.getSlrid() );
        rktYwslJbLsDTO.setPjjg( req.getPjjg() );
        rktYwslJbLsDTO.setPjpljc( req.getPjpljc() );
        rktYwslJbLsDTO.setPjsj( req.getPjsj() );
        rktYwslJbLsDTO.setSbsfxy( req.getSbsfxy() );
        rktYwslJbLsDTO.setSbzt( req.getSbzt() );
        rktYwslJbLsDTO.setSbywlx( req.getSbywlx() );
        rktYwslJbLsDTO.setSbywmc( req.getSbywmc() );
        rktYwslJbLsDTO.setSbsbsj( req.getSbsbsj() );
        rktYwslJbLsDTO.setSbslsj( req.getSbslsj() );
        rktYwslJbLsDTO.setSbshsj( req.getSbshsj() );
        rktYwslJbLsDTO.setSbbjsj( req.getSbbjsj() );
        rktYwslJbLsDTO.setSbspkssj( req.getSbspkssj() );
        rktYwslJbLsDTO.setSbspjssj( req.getSbspjssj() );
        rktYwslJbLsDTO.setSbsprxm( req.getSbsprxm() );
        rktYwslJbLsDTO.setSbsprdwdm( req.getSbsprdwdm() );
        rktYwslJbLsDTO.setSbspjgdm( req.getSbspjgdm() );
        rktYwslJbLsDTO.setSbspyj( req.getSbspyj() );
        rktYwslJbLsDTO.setMjscshhs( req.getMjscshhs() );
        rktYwslJbLsDTO.setSpxxcjfs( req.getSpxxcjfs() );
        rktYwslJbLsDTO.setHzxm( req.getHzxm() );
        rktYwslJbLsDTO.setHzgmsfhm( req.getHzgmsfhm() );
        rktYwslJbLsDTO.setDababz( req.getDababz() );
        rktYwslJbLsDTO.setDabasj( req.getDabasj() );
        rktYwslJbLsDTO.setRlhbz( req.getRlhbz() );
        rktYwslJbLsDTO.setRsjshjg( req.getRsjshjg() );
        rktYwslJbLsDTO.setRsjshly( req.getRsjshly() );
        rktYwslJbLsDTO.setRsjshsj( req.getRsjshsj() );
        rktYwslJbLsDTO.setRsjshr( req.getRsjshr() );
        rktYwslJbLsDTO.setGjnzsbh( req.getGjnzsbh() );
        rktYwslJbLsDTO.setZwwdtbh( req.getZwwdtbh() );
        rktYwslJbLsDTO.setZwwdtmc( req.getZwwdtmc() );
        rktYwslJbLsDTO.setHlwsqlx( req.getHlwsqlx() );
        rktYwslJbLsDTO.setHalluserid( req.getHalluserid() );
        rktYwslJbLsDTO.setHallusername( req.getHallusername() );
        rktYwslJbLsDTO.setApplyfrom( req.getApplyfrom() );
        rktYwslJbLsDTO.setBustype( req.getBustype() );
        rktYwslJbLsDTO.setSftqhlwcl( req.getSftqhlwcl() );
        rktYwslJbLsDTO.setDycjlx( req.getDycjlx() );
        rktYwslJbLsDTO.setBgyjssbbz( req.getBgyjssbbz() );
        rktYwslJbLsDTO.setBgyjssbsj( req.getBgyjssbsj() );
        rktYwslJbLsDTO.setSqbsfbzqm( req.getSqbsfbzqm() );
        rktYwslJbLsDTO.setCbsfbzqm( req.getCbsfbzqm() );
        rktYwslJbLsDTO.setSfksxtqr( req.getSfksxtqr() );
        rktYwslJbLsDTO.setQtsqr( req.getQtsqr() );
        rktYwslJbLsDTO.setSqrrxbdsj( req.getSqrrxbdsj() );
        rktYwslJbLsDTO.setSqrrxbdxsd( req.getSqrrxbdxsd() );
        rktYwslJbLsDTO.setSqrrxbdjg( req.getSqrrxbdjg() );
        rktYwslJbLsDTO.setSqrgzdwlb( req.getSqrgzdwlb() );
        rktYwslJbLsDTO.setSqrgzdwtyshxydm( req.getSqrgzdwtyshxydm() );
        rktYwslJbLsDTO.setQtsqrgmsfhm( req.getQtsqrgmsfhm() );
        rktYwslJbLsDTO.setQtsqrlxdh( req.getQtsqrlxdh() );

        return rktYwslJbLsDTO;
    }

    @Override
    public RktYwslJbLsDTO convertToDTO(RktYwslJbLsCreateReq req) {
        if ( req == null ) {
            return null;
        }

        RktYwslJbLsDTO rktYwslJbLsDTO = new RktYwslJbLsDTO();

        rktYwslJbLsDTO.setLcdyid( req.getLcdyid() );
        rktYwslJbLsDTO.setLcmc( req.getLcmc() );
        rktYwslJbLsDTO.setSpyj( req.getSpyj() );
        rktYwslJbLsDTO.setSlrs( req.getSlrs() );
        rktYwslJbLsDTO.setGmsfhm( req.getGmsfhm() );
        rktYwslJbLsDTO.setXm( req.getXm() );
        rktYwslJbLsDTO.setLcywlx( req.getLcywlx() );
        rktYwslJbLsDTO.setLcslid( req.getLcslid() );
        rktYwslJbLsDTO.setLcywbt( req.getLcywbt() );
        rktYwslJbLsDTO.setLcrwjd( req.getLcrwjd() );
        rktYwslJbLsDTO.setLczt( req.getLczt() );
        rktYwslJbLsDTO.setBlzt( req.getBlzt() );
        rktYwslJbLsDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktYwslJbLsDTO.setSqrxm( req.getSqrxm() );
        rktYwslJbLsDTO.setSqrxb( req.getSqrxb() );
        rktYwslJbLsDTO.setSqrlxdh( req.getSqrlxdh() );
        rktYwslJbLsDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktYwslJbLsDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktYwslJbLsDTO.setSqrzzxz( req.getSqrzzxz() );
        rktYwslJbLsDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktYwslJbLsDTO.setSjrxm( req.getSjrxm() );
        rktYwslJbLsDTO.setSjrlxdh( req.getSjrlxdh() );
        rktYwslJbLsDTO.setSqrtxdz( req.getSqrtxdz() );
        rktYwslJbLsDTO.setSqrzjmc( req.getSqrzjmc() );
        rktYwslJbLsDTO.setSqrzjhm( req.getSqrzjhm() );
        rktYwslJbLsDTO.setSqrgzdw( req.getSqrgzdw() );
        rktYwslJbLsDTO.setHjsxcxlb( req.getHjsxcxlb() );
        rktYwslJbLsDTO.setSqcxly( req.getSqcxly() );
        rktYwslJbLsDTO.setSfscdzqzhky( req.getSfscdzqzhky() );
        rktYwslJbLsDTO.setSfdyhkbsy( req.getSfdyhkbsy() );
        rktYwslJbLsDTO.setSfazjthfsdy( req.getSfazjthfsdy() );
        rktYwslJbLsDTO.setSfdyqhry( req.getSfdyqhry() );
        rktYwslJbLsDTO.setSfdycsyy( req.getSfdycsyy() );
        rktYwslJbLsDTO.setSlrlxdh( req.getSlrlxdh() );
        rktYwslJbLsDTO.setSprlxdh( req.getSprlxdh() );
        rktYwslJbLsDTO.setSfxyyjzj( req.getSfxyyjzj() );
        rktYwslJbLsDTO.setSfyjyjzj( req.getSfyjyjzj() );
        rktYwslJbLsDTO.setKddh( req.getKddh() );
        rktYwslJbLsDTO.setSpxxlylb( req.getSpxxlylb() );
        rktYwslJbLsDTO.setSpxxly( req.getSpxxly() );
        rktYwslJbLsDTO.setYsbxxbh( req.getYsbxxbh() );
        rktYwslJbLsDTO.setYsbxtywwjmc( req.getYsbxtywwjmc() );
        rktYwslJbLsDTO.setYsbxtywwjlx( req.getYsbxtywwjlx() );
        rktYwslJbLsDTO.setSqrq( req.getSqrq() );
        rktYwslJbLsDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktYwslJbLsDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktYwslJbLsDTO.setSprxm( req.getSprxm() );
        rktYwslJbLsDTO.setSpsj( req.getSpsj() );
        rktYwslJbLsDTO.setSpjgdm( req.getSpjgdm() );
        rktYwslJbLsDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktYwslJbLsDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktYwslJbLsDTO.setSlrxm( req.getSlrxm() );
        rktYwslJbLsDTO.setSlsj( req.getSlsj() );
        rktYwslJbLsDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktYwslJbLsDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktYwslJbLsDTO.setJcwh( req.getJcwh() );
        rktYwslJbLsDTO.setBz( req.getBz() );
        rktYwslJbLsDTO.setBlhjywid( req.getBlhjywid() );
        rktYwslJbLsDTO.setHjywblsj( req.getHjywblsj() );
        rktYwslJbLsDTO.setSlrid( req.getSlrid() );
        rktYwslJbLsDTO.setPjjg( req.getPjjg() );
        rktYwslJbLsDTO.setPjpljc( req.getPjpljc() );
        rktYwslJbLsDTO.setPjsj( req.getPjsj() );
        rktYwslJbLsDTO.setSbsfxy( req.getSbsfxy() );
        rktYwslJbLsDTO.setSbzt( req.getSbzt() );
        rktYwslJbLsDTO.setSbywlx( req.getSbywlx() );
        rktYwslJbLsDTO.setSbywmc( req.getSbywmc() );
        rktYwslJbLsDTO.setSbsbsj( req.getSbsbsj() );
        rktYwslJbLsDTO.setSbslsj( req.getSbslsj() );
        rktYwslJbLsDTO.setSbshsj( req.getSbshsj() );
        rktYwslJbLsDTO.setSbbjsj( req.getSbbjsj() );
        rktYwslJbLsDTO.setSbspkssj( req.getSbspkssj() );
        rktYwslJbLsDTO.setSbspjssj( req.getSbspjssj() );
        rktYwslJbLsDTO.setSbsprxm( req.getSbsprxm() );
        rktYwslJbLsDTO.setSbsprdwdm( req.getSbsprdwdm() );
        rktYwslJbLsDTO.setSbspjgdm( req.getSbspjgdm() );
        rktYwslJbLsDTO.setSbspyj( req.getSbspyj() );
        rktYwslJbLsDTO.setMjscshhs( req.getMjscshhs() );
        rktYwslJbLsDTO.setSpxxcjfs( req.getSpxxcjfs() );
        rktYwslJbLsDTO.setHzxm( req.getHzxm() );
        rktYwslJbLsDTO.setHzgmsfhm( req.getHzgmsfhm() );
        rktYwslJbLsDTO.setDababz( req.getDababz() );
        rktYwslJbLsDTO.setDabasj( req.getDabasj() );
        rktYwslJbLsDTO.setRlhbz( req.getRlhbz() );
        rktYwslJbLsDTO.setRsjshjg( req.getRsjshjg() );
        rktYwslJbLsDTO.setRsjshly( req.getRsjshly() );
        rktYwslJbLsDTO.setRsjshsj( req.getRsjshsj() );
        rktYwslJbLsDTO.setRsjshr( req.getRsjshr() );
        rktYwslJbLsDTO.setGjnzsbh( req.getGjnzsbh() );
        rktYwslJbLsDTO.setZwwdtbh( req.getZwwdtbh() );
        rktYwslJbLsDTO.setZwwdtmc( req.getZwwdtmc() );
        rktYwslJbLsDTO.setHlwsqlx( req.getHlwsqlx() );
        rktYwslJbLsDTO.setHalluserid( req.getHalluserid() );
        rktYwslJbLsDTO.setHallusername( req.getHallusername() );
        rktYwslJbLsDTO.setApplyfrom( req.getApplyfrom() );
        rktYwslJbLsDTO.setBustype( req.getBustype() );
        rktYwslJbLsDTO.setSftqhlwcl( req.getSftqhlwcl() );
        rktYwslJbLsDTO.setDycjlx( req.getDycjlx() );
        rktYwslJbLsDTO.setBgyjssbbz( req.getBgyjssbbz() );
        rktYwslJbLsDTO.setBgyjssbsj( req.getBgyjssbsj() );
        rktYwslJbLsDTO.setSqbsfbzqm( req.getSqbsfbzqm() );
        rktYwslJbLsDTO.setCbsfbzqm( req.getCbsfbzqm() );
        rktYwslJbLsDTO.setSfksxtqr( req.getSfksxtqr() );
        rktYwslJbLsDTO.setQtsqr( req.getQtsqr() );
        rktYwslJbLsDTO.setSqrrxbdsj( req.getSqrrxbdsj() );
        rktYwslJbLsDTO.setSqrrxbdxsd( req.getSqrrxbdxsd() );
        rktYwslJbLsDTO.setSqrrxbdjg( req.getSqrrxbdjg() );
        rktYwslJbLsDTO.setSqrgzdwlb( req.getSqrgzdwlb() );
        rktYwslJbLsDTO.setSqrgzdwtyshxydm( req.getSqrgzdwtyshxydm() );
        rktYwslJbLsDTO.setQtsqrgmsfhm( req.getQtsqrgmsfhm() );
        rktYwslJbLsDTO.setQtsqrlxdh( req.getQtsqrlxdh() );

        return rktYwslJbLsDTO;
    }

    @Override
    public RktYwslJbLsDTO convertToDTO(RktYwslJbLsUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        RktYwslJbLsDTO rktYwslJbLsDTO = new RktYwslJbLsDTO();

        rktYwslJbLsDTO.setYwslh( req.getYwslh() );
        rktYwslJbLsDTO.setLcdyid( req.getLcdyid() );
        rktYwslJbLsDTO.setLcmc( req.getLcmc() );
        rktYwslJbLsDTO.setSpyj( req.getSpyj() );
        rktYwslJbLsDTO.setSlrs( req.getSlrs() );
        rktYwslJbLsDTO.setGmsfhm( req.getGmsfhm() );
        rktYwslJbLsDTO.setXm( req.getXm() );
        rktYwslJbLsDTO.setLcywlx( req.getLcywlx() );
        rktYwslJbLsDTO.setLcslid( req.getLcslid() );
        rktYwslJbLsDTO.setLcywbt( req.getLcywbt() );
        rktYwslJbLsDTO.setLcrwjd( req.getLcrwjd() );
        rktYwslJbLsDTO.setLczt( req.getLczt() );
        rktYwslJbLsDTO.setBlzt( req.getBlzt() );
        rktYwslJbLsDTO.setSqrgmsfhm( req.getSqrgmsfhm() );
        rktYwslJbLsDTO.setSqrxm( req.getSqrxm() );
        rktYwslJbLsDTO.setSqrxb( req.getSqrxb() );
        rktYwslJbLsDTO.setSqrlxdh( req.getSqrlxdh() );
        rktYwslJbLsDTO.setSqrybdrgx( req.getSqrybdrgx() );
        rktYwslJbLsDTO.setSqrzzssxq( req.getSqrzzssxq() );
        rktYwslJbLsDTO.setSqrzzxz( req.getSqrzzxz() );
        rktYwslJbLsDTO.setSqrhkdjjg( req.getSqrhkdjjg() );
        rktYwslJbLsDTO.setSjrxm( req.getSjrxm() );
        rktYwslJbLsDTO.setSjrlxdh( req.getSjrlxdh() );
        rktYwslJbLsDTO.setSqrtxdz( req.getSqrtxdz() );
        rktYwslJbLsDTO.setSqrzjmc( req.getSqrzjmc() );
        rktYwslJbLsDTO.setSqrzjhm( req.getSqrzjhm() );
        rktYwslJbLsDTO.setSqrgzdw( req.getSqrgzdw() );
        rktYwslJbLsDTO.setHjsxcxlb( req.getHjsxcxlb() );
        rktYwslJbLsDTO.setSqcxly( req.getSqcxly() );
        rktYwslJbLsDTO.setSfscdzqzhky( req.getSfscdzqzhky() );
        rktYwslJbLsDTO.setSfdyhkbsy( req.getSfdyhkbsy() );
        rktYwslJbLsDTO.setSfazjthfsdy( req.getSfazjthfsdy() );
        rktYwslJbLsDTO.setSfdyqhry( req.getSfdyqhry() );
        rktYwslJbLsDTO.setSfdycsyy( req.getSfdycsyy() );
        rktYwslJbLsDTO.setSlrlxdh( req.getSlrlxdh() );
        rktYwslJbLsDTO.setSprlxdh( req.getSprlxdh() );
        rktYwslJbLsDTO.setSfxyyjzj( req.getSfxyyjzj() );
        rktYwslJbLsDTO.setSfyjyjzj( req.getSfyjyjzj() );
        rktYwslJbLsDTO.setKddh( req.getKddh() );
        rktYwslJbLsDTO.setSpxxlylb( req.getSpxxlylb() );
        rktYwslJbLsDTO.setSpxxly( req.getSpxxly() );
        rktYwslJbLsDTO.setYsbxxbh( req.getYsbxxbh() );
        rktYwslJbLsDTO.setYsbxtywwjmc( req.getYsbxtywwjmc() );
        rktYwslJbLsDTO.setYsbxtywwjlx( req.getYsbxtywwjlx() );
        rktYwslJbLsDTO.setSqrq( req.getSqrq() );
        rktYwslJbLsDTO.setSpdwgajgjgdm( req.getSpdwgajgjgdm() );
        rktYwslJbLsDTO.setSpdwgajgmc( req.getSpdwgajgmc() );
        rktYwslJbLsDTO.setSprxm( req.getSprxm() );
        rktYwslJbLsDTO.setSpsj( req.getSpsj() );
        rktYwslJbLsDTO.setSpjgdm( req.getSpjgdm() );
        rktYwslJbLsDTO.setSldwgajgjgdm( req.getSldwgajgjgdm() );
        rktYwslJbLsDTO.setSldwgajgmc( req.getSldwgajgmc() );
        rktYwslJbLsDTO.setSlrxm( req.getSlrxm() );
        rktYwslJbLsDTO.setSlsj( req.getSlsj() );
        rktYwslJbLsDTO.setSjgsdwdm( req.getSjgsdwdm() );
        rktYwslJbLsDTO.setSjgsdwmc( req.getSjgsdwmc() );
        rktYwslJbLsDTO.setJcwh( req.getJcwh() );
        rktYwslJbLsDTO.setBz( req.getBz() );
        rktYwslJbLsDTO.setBlhjywid( req.getBlhjywid() );
        rktYwslJbLsDTO.setHjywblsj( req.getHjywblsj() );
        rktYwslJbLsDTO.setSlrid( req.getSlrid() );
        rktYwslJbLsDTO.setPjjg( req.getPjjg() );
        rktYwslJbLsDTO.setPjpljc( req.getPjpljc() );
        rktYwslJbLsDTO.setPjsj( req.getPjsj() );
        rktYwslJbLsDTO.setSbsfxy( req.getSbsfxy() );
        rktYwslJbLsDTO.setSbzt( req.getSbzt() );
        rktYwslJbLsDTO.setSbywlx( req.getSbywlx() );
        rktYwslJbLsDTO.setSbywmc( req.getSbywmc() );
        rktYwslJbLsDTO.setSbsbsj( req.getSbsbsj() );
        rktYwslJbLsDTO.setSbslsj( req.getSbslsj() );
        rktYwslJbLsDTO.setSbshsj( req.getSbshsj() );
        rktYwslJbLsDTO.setSbbjsj( req.getSbbjsj() );
        rktYwslJbLsDTO.setSbspkssj( req.getSbspkssj() );
        rktYwslJbLsDTO.setSbspjssj( req.getSbspjssj() );
        rktYwslJbLsDTO.setSbsprxm( req.getSbsprxm() );
        rktYwslJbLsDTO.setSbsprdwdm( req.getSbsprdwdm() );
        rktYwslJbLsDTO.setSbspjgdm( req.getSbspjgdm() );
        rktYwslJbLsDTO.setSbspyj( req.getSbspyj() );
        rktYwslJbLsDTO.setMjscshhs( req.getMjscshhs() );
        rktYwslJbLsDTO.setSpxxcjfs( req.getSpxxcjfs() );
        rktYwslJbLsDTO.setHzxm( req.getHzxm() );
        rktYwslJbLsDTO.setHzgmsfhm( req.getHzgmsfhm() );
        rktYwslJbLsDTO.setDababz( req.getDababz() );
        rktYwslJbLsDTO.setDabasj( req.getDabasj() );
        rktYwslJbLsDTO.setRlhbz( req.getRlhbz() );
        rktYwslJbLsDTO.setRsjshjg( req.getRsjshjg() );
        rktYwslJbLsDTO.setRsjshly( req.getRsjshly() );
        rktYwslJbLsDTO.setRsjshsj( req.getRsjshsj() );
        rktYwslJbLsDTO.setRsjshr( req.getRsjshr() );
        rktYwslJbLsDTO.setGjnzsbh( req.getGjnzsbh() );
        rktYwslJbLsDTO.setZwwdtbh( req.getZwwdtbh() );
        rktYwslJbLsDTO.setZwwdtmc( req.getZwwdtmc() );
        rktYwslJbLsDTO.setHlwsqlx( req.getHlwsqlx() );
        rktYwslJbLsDTO.setHalluserid( req.getHalluserid() );
        rktYwslJbLsDTO.setHallusername( req.getHallusername() );
        rktYwslJbLsDTO.setApplyfrom( req.getApplyfrom() );
        rktYwslJbLsDTO.setBustype( req.getBustype() );
        rktYwslJbLsDTO.setSftqhlwcl( req.getSftqhlwcl() );
        rktYwslJbLsDTO.setDycjlx( req.getDycjlx() );
        rktYwslJbLsDTO.setBgyjssbbz( req.getBgyjssbbz() );
        rktYwslJbLsDTO.setBgyjssbsj( req.getBgyjssbsj() );
        rktYwslJbLsDTO.setSqbsfbzqm( req.getSqbsfbzqm() );
        rktYwslJbLsDTO.setCbsfbzqm( req.getCbsfbzqm() );
        rktYwslJbLsDTO.setSfksxtqr( req.getSfksxtqr() );
        rktYwslJbLsDTO.setQtsqr( req.getQtsqr() );
        rktYwslJbLsDTO.setSqrrxbdsj( req.getSqrrxbdsj() );
        rktYwslJbLsDTO.setSqrrxbdxsd( req.getSqrrxbdxsd() );
        rktYwslJbLsDTO.setSqrrxbdjg( req.getSqrrxbdjg() );
        rktYwslJbLsDTO.setSqrgzdwlb( req.getSqrgzdwlb() );
        rktYwslJbLsDTO.setSqrgzdwtyshxydm( req.getSqrgzdwtyshxydm() );
        rktYwslJbLsDTO.setQtsqrgmsfhm( req.getQtsqrgmsfhm() );
        rktYwslJbLsDTO.setQtsqrlxdh( req.getQtsqrlxdh() );

        return rktYwslJbLsDTO;
    }

    @Override
    public RktYwslJbLsPageResp convertToPageResp(RktYwslJbLsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslJbLsPageResp rktYwslJbLsPageResp = new RktYwslJbLsPageResp();

        rktYwslJbLsPageResp.setYwslh( dto.getYwslh() );
        rktYwslJbLsPageResp.setLcdyid( dto.getLcdyid() );
        rktYwslJbLsPageResp.setLcmc( dto.getLcmc() );
        rktYwslJbLsPageResp.setSpyj( dto.getSpyj() );
        rktYwslJbLsPageResp.setSlrs( dto.getSlrs() );
        rktYwslJbLsPageResp.setGmsfhm( dto.getGmsfhm() );
        rktYwslJbLsPageResp.setXm( dto.getXm() );
        rktYwslJbLsPageResp.setLcywlx( dto.getLcywlx() );
        rktYwslJbLsPageResp.setLcslid( dto.getLcslid() );
        rktYwslJbLsPageResp.setLcywbt( dto.getLcywbt() );
        rktYwslJbLsPageResp.setLcrwjd( dto.getLcrwjd() );
        rktYwslJbLsPageResp.setLczt( dto.getLczt() );
        rktYwslJbLsPageResp.setBlzt( dto.getBlzt() );
        rktYwslJbLsPageResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktYwslJbLsPageResp.setSqrxm( dto.getSqrxm() );
        rktYwslJbLsPageResp.setSqrxb( dto.getSqrxb() );
        rktYwslJbLsPageResp.setSqrlxdh( dto.getSqrlxdh() );
        rktYwslJbLsPageResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktYwslJbLsPageResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktYwslJbLsPageResp.setSqrzzxz( dto.getSqrzzxz() );
        rktYwslJbLsPageResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktYwslJbLsPageResp.setSjrxm( dto.getSjrxm() );
        rktYwslJbLsPageResp.setSjrlxdh( dto.getSjrlxdh() );
        rktYwslJbLsPageResp.setSqrtxdz( dto.getSqrtxdz() );
        rktYwslJbLsPageResp.setSqrzjmc( dto.getSqrzjmc() );
        rktYwslJbLsPageResp.setSqrzjhm( dto.getSqrzjhm() );
        rktYwslJbLsPageResp.setSqrgzdw( dto.getSqrgzdw() );
        rktYwslJbLsPageResp.setHjsxcxlb( dto.getHjsxcxlb() );
        rktYwslJbLsPageResp.setSqcxly( dto.getSqcxly() );
        rktYwslJbLsPageResp.setSfscdzqzhky( dto.getSfscdzqzhky() );
        rktYwslJbLsPageResp.setSfdyhkbsy( dto.getSfdyhkbsy() );
        rktYwslJbLsPageResp.setSfazjthfsdy( dto.getSfazjthfsdy() );
        rktYwslJbLsPageResp.setSfdyqhry( dto.getSfdyqhry() );
        rktYwslJbLsPageResp.setSfdycsyy( dto.getSfdycsyy() );
        rktYwslJbLsPageResp.setSlrlxdh( dto.getSlrlxdh() );
        rktYwslJbLsPageResp.setSprlxdh( dto.getSprlxdh() );
        rktYwslJbLsPageResp.setSfxyyjzj( dto.getSfxyyjzj() );
        rktYwslJbLsPageResp.setSfyjyjzj( dto.getSfyjyjzj() );
        rktYwslJbLsPageResp.setKddh( dto.getKddh() );
        rktYwslJbLsPageResp.setSpxxlylb( dto.getSpxxlylb() );
        rktYwslJbLsPageResp.setSpxxly( dto.getSpxxly() );
        rktYwslJbLsPageResp.setYsbxxbh( dto.getYsbxxbh() );
        rktYwslJbLsPageResp.setYsbxtywwjmc( dto.getYsbxtywwjmc() );
        rktYwslJbLsPageResp.setYsbxtywwjlx( dto.getYsbxtywwjlx() );
        rktYwslJbLsPageResp.setSqrq( dto.getSqrq() );
        rktYwslJbLsPageResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktYwslJbLsPageResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktYwslJbLsPageResp.setSprxm( dto.getSprxm() );
        rktYwslJbLsPageResp.setSpsj( dto.getSpsj() );
        rktYwslJbLsPageResp.setSpjgdm( dto.getSpjgdm() );
        rktYwslJbLsPageResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktYwslJbLsPageResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktYwslJbLsPageResp.setSlrxm( dto.getSlrxm() );
        rktYwslJbLsPageResp.setSlsj( dto.getSlsj() );
        rktYwslJbLsPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktYwslJbLsPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktYwslJbLsPageResp.setJcwh( dto.getJcwh() );
        rktYwslJbLsPageResp.setBz( dto.getBz() );
        rktYwslJbLsPageResp.setBlhjywid( dto.getBlhjywid() );
        rktYwslJbLsPageResp.setHjywblsj( dto.getHjywblsj() );
        rktYwslJbLsPageResp.setSlrid( dto.getSlrid() );
        rktYwslJbLsPageResp.setPjjg( dto.getPjjg() );
        rktYwslJbLsPageResp.setPjpljc( dto.getPjpljc() );
        rktYwslJbLsPageResp.setPjsj( dto.getPjsj() );
        rktYwslJbLsPageResp.setSbsfxy( dto.getSbsfxy() );
        rktYwslJbLsPageResp.setSbzt( dto.getSbzt() );
        rktYwslJbLsPageResp.setSbywlx( dto.getSbywlx() );
        rktYwslJbLsPageResp.setSbywmc( dto.getSbywmc() );
        rktYwslJbLsPageResp.setSbsbsj( dto.getSbsbsj() );
        rktYwslJbLsPageResp.setSbslsj( dto.getSbslsj() );
        rktYwslJbLsPageResp.setSbshsj( dto.getSbshsj() );
        rktYwslJbLsPageResp.setSbbjsj( dto.getSbbjsj() );
        rktYwslJbLsPageResp.setSbspkssj( dto.getSbspkssj() );
        rktYwslJbLsPageResp.setSbspjssj( dto.getSbspjssj() );
        rktYwslJbLsPageResp.setSbsprxm( dto.getSbsprxm() );
        rktYwslJbLsPageResp.setSbsprdwdm( dto.getSbsprdwdm() );
        rktYwslJbLsPageResp.setSbspjgdm( dto.getSbspjgdm() );
        rktYwslJbLsPageResp.setSbspyj( dto.getSbspyj() );
        rktYwslJbLsPageResp.setMjscshhs( dto.getMjscshhs() );
        rktYwslJbLsPageResp.setSpxxcjfs( dto.getSpxxcjfs() );
        rktYwslJbLsPageResp.setHzxm( dto.getHzxm() );
        rktYwslJbLsPageResp.setHzgmsfhm( dto.getHzgmsfhm() );
        rktYwslJbLsPageResp.setDababz( dto.getDababz() );
        rktYwslJbLsPageResp.setDabasj( dto.getDabasj() );
        rktYwslJbLsPageResp.setRlhbz( dto.getRlhbz() );
        rktYwslJbLsPageResp.setRsjshjg( dto.getRsjshjg() );
        rktYwslJbLsPageResp.setRsjshly( dto.getRsjshly() );
        rktYwslJbLsPageResp.setRsjshsj( dto.getRsjshsj() );
        rktYwslJbLsPageResp.setRsjshr( dto.getRsjshr() );
        rktYwslJbLsPageResp.setGjnzsbh( dto.getGjnzsbh() );
        rktYwslJbLsPageResp.setZwwdtbh( dto.getZwwdtbh() );
        rktYwslJbLsPageResp.setZwwdtmc( dto.getZwwdtmc() );
        rktYwslJbLsPageResp.setHlwsqlx( dto.getHlwsqlx() );
        rktYwslJbLsPageResp.setHalluserid( dto.getHalluserid() );
        rktYwslJbLsPageResp.setHallusername( dto.getHallusername() );
        rktYwslJbLsPageResp.setApplyfrom( dto.getApplyfrom() );
        rktYwslJbLsPageResp.setBustype( dto.getBustype() );
        rktYwslJbLsPageResp.setSftqhlwcl( dto.getSftqhlwcl() );
        rktYwslJbLsPageResp.setDycjlx( dto.getDycjlx() );
        rktYwslJbLsPageResp.setBgyjssbbz( dto.getBgyjssbbz() );
        rktYwslJbLsPageResp.setBgyjssbsj( dto.getBgyjssbsj() );
        rktYwslJbLsPageResp.setSqbsfbzqm( dto.getSqbsfbzqm() );
        rktYwslJbLsPageResp.setCbsfbzqm( dto.getCbsfbzqm() );
        rktYwslJbLsPageResp.setSfksxtqr( dto.getSfksxtqr() );
        rktYwslJbLsPageResp.setQtsqr( dto.getQtsqr() );
        rktYwslJbLsPageResp.setSqrrxbdsj( dto.getSqrrxbdsj() );
        rktYwslJbLsPageResp.setSqrrxbdxsd( dto.getSqrrxbdxsd() );
        rktYwslJbLsPageResp.setSqrrxbdjg( dto.getSqrrxbdjg() );
        rktYwslJbLsPageResp.setSqrgzdwlb( dto.getSqrgzdwlb() );
        rktYwslJbLsPageResp.setSqrgzdwtyshxydm( dto.getSqrgzdwtyshxydm() );
        rktYwslJbLsPageResp.setQtsqrgmsfhm( dto.getQtsqrgmsfhm() );
        rktYwslJbLsPageResp.setQtsqrlxdh( dto.getQtsqrlxdh() );

        return rktYwslJbLsPageResp;
    }

    @Override
    public RktYwslJbLsViewResp convertToViewResp(RktYwslJbLsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslJbLsViewResp rktYwslJbLsViewResp = new RktYwslJbLsViewResp();

        rktYwslJbLsViewResp.setYwslh( dto.getYwslh() );
        rktYwslJbLsViewResp.setLcdyid( dto.getLcdyid() );
        rktYwslJbLsViewResp.setLcmc( dto.getLcmc() );
        rktYwslJbLsViewResp.setSpyj( dto.getSpyj() );
        rktYwslJbLsViewResp.setSlrs( dto.getSlrs() );
        rktYwslJbLsViewResp.setGmsfhm( dto.getGmsfhm() );
        rktYwslJbLsViewResp.setXm( dto.getXm() );
        rktYwslJbLsViewResp.setLcywlx( dto.getLcywlx() );
        rktYwslJbLsViewResp.setLcslid( dto.getLcslid() );
        rktYwslJbLsViewResp.setLcywbt( dto.getLcywbt() );
        rktYwslJbLsViewResp.setLcrwjd( dto.getLcrwjd() );
        rktYwslJbLsViewResp.setLczt( dto.getLczt() );
        rktYwslJbLsViewResp.setBlzt( dto.getBlzt() );
        rktYwslJbLsViewResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktYwslJbLsViewResp.setSqrxm( dto.getSqrxm() );
        rktYwslJbLsViewResp.setSqrxb( dto.getSqrxb() );
        rktYwslJbLsViewResp.setSqrlxdh( dto.getSqrlxdh() );
        rktYwslJbLsViewResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktYwslJbLsViewResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktYwslJbLsViewResp.setSqrzzxz( dto.getSqrzzxz() );
        rktYwslJbLsViewResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktYwslJbLsViewResp.setSjrxm( dto.getSjrxm() );
        rktYwslJbLsViewResp.setSjrlxdh( dto.getSjrlxdh() );
        rktYwslJbLsViewResp.setSqrtxdz( dto.getSqrtxdz() );
        rktYwslJbLsViewResp.setSqrzjmc( dto.getSqrzjmc() );
        rktYwslJbLsViewResp.setSqrzjhm( dto.getSqrzjhm() );
        rktYwslJbLsViewResp.setSqrgzdw( dto.getSqrgzdw() );
        rktYwslJbLsViewResp.setHjsxcxlb( dto.getHjsxcxlb() );
        rktYwslJbLsViewResp.setSqcxly( dto.getSqcxly() );
        rktYwslJbLsViewResp.setSfscdzqzhky( dto.getSfscdzqzhky() );
        rktYwslJbLsViewResp.setSfdyhkbsy( dto.getSfdyhkbsy() );
        rktYwslJbLsViewResp.setSfazjthfsdy( dto.getSfazjthfsdy() );
        rktYwslJbLsViewResp.setSfdyqhry( dto.getSfdyqhry() );
        rktYwslJbLsViewResp.setSfdycsyy( dto.getSfdycsyy() );
        rktYwslJbLsViewResp.setSlrlxdh( dto.getSlrlxdh() );
        rktYwslJbLsViewResp.setSprlxdh( dto.getSprlxdh() );
        rktYwslJbLsViewResp.setSfxyyjzj( dto.getSfxyyjzj() );
        rktYwslJbLsViewResp.setSfyjyjzj( dto.getSfyjyjzj() );
        rktYwslJbLsViewResp.setKddh( dto.getKddh() );
        rktYwslJbLsViewResp.setSpxxlylb( dto.getSpxxlylb() );
        rktYwslJbLsViewResp.setSpxxly( dto.getSpxxly() );
        rktYwslJbLsViewResp.setYsbxxbh( dto.getYsbxxbh() );
        rktYwslJbLsViewResp.setYsbxtywwjmc( dto.getYsbxtywwjmc() );
        rktYwslJbLsViewResp.setYsbxtywwjlx( dto.getYsbxtywwjlx() );
        rktYwslJbLsViewResp.setSqrq( dto.getSqrq() );
        rktYwslJbLsViewResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktYwslJbLsViewResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktYwslJbLsViewResp.setSprxm( dto.getSprxm() );
        rktYwslJbLsViewResp.setSpsj( dto.getSpsj() );
        rktYwslJbLsViewResp.setSpjgdm( dto.getSpjgdm() );
        rktYwslJbLsViewResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktYwslJbLsViewResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktYwslJbLsViewResp.setSlrxm( dto.getSlrxm() );
        rktYwslJbLsViewResp.setSlsj( dto.getSlsj() );
        rktYwslJbLsViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktYwslJbLsViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktYwslJbLsViewResp.setJcwh( dto.getJcwh() );
        rktYwslJbLsViewResp.setBz( dto.getBz() );
        rktYwslJbLsViewResp.setBlhjywid( dto.getBlhjywid() );
        rktYwslJbLsViewResp.setHjywblsj( dto.getHjywblsj() );
        rktYwslJbLsViewResp.setSlrid( dto.getSlrid() );
        rktYwslJbLsViewResp.setPjjg( dto.getPjjg() );
        rktYwslJbLsViewResp.setPjpljc( dto.getPjpljc() );
        rktYwslJbLsViewResp.setPjsj( dto.getPjsj() );
        rktYwslJbLsViewResp.setSbsfxy( dto.getSbsfxy() );
        rktYwslJbLsViewResp.setSbzt( dto.getSbzt() );
        rktYwslJbLsViewResp.setSbywlx( dto.getSbywlx() );
        rktYwslJbLsViewResp.setSbywmc( dto.getSbywmc() );
        rktYwslJbLsViewResp.setSbsbsj( dto.getSbsbsj() );
        rktYwslJbLsViewResp.setSbslsj( dto.getSbslsj() );
        rktYwslJbLsViewResp.setSbshsj( dto.getSbshsj() );
        rktYwslJbLsViewResp.setSbbjsj( dto.getSbbjsj() );
        rktYwslJbLsViewResp.setSbspkssj( dto.getSbspkssj() );
        rktYwslJbLsViewResp.setSbspjssj( dto.getSbspjssj() );
        rktYwslJbLsViewResp.setSbsprxm( dto.getSbsprxm() );
        rktYwslJbLsViewResp.setSbsprdwdm( dto.getSbsprdwdm() );
        rktYwslJbLsViewResp.setSbspjgdm( dto.getSbspjgdm() );
        rktYwslJbLsViewResp.setSbspyj( dto.getSbspyj() );
        rktYwslJbLsViewResp.setMjscshhs( dto.getMjscshhs() );
        rktYwslJbLsViewResp.setSpxxcjfs( dto.getSpxxcjfs() );
        rktYwslJbLsViewResp.setHzxm( dto.getHzxm() );
        rktYwslJbLsViewResp.setHzgmsfhm( dto.getHzgmsfhm() );
        rktYwslJbLsViewResp.setDababz( dto.getDababz() );
        rktYwslJbLsViewResp.setDabasj( dto.getDabasj() );
        rktYwslJbLsViewResp.setRlhbz( dto.getRlhbz() );
        rktYwslJbLsViewResp.setRsjshjg( dto.getRsjshjg() );
        rktYwslJbLsViewResp.setRsjshly( dto.getRsjshly() );
        rktYwslJbLsViewResp.setRsjshsj( dto.getRsjshsj() );
        rktYwslJbLsViewResp.setRsjshr( dto.getRsjshr() );
        rktYwslJbLsViewResp.setGjnzsbh( dto.getGjnzsbh() );
        rktYwslJbLsViewResp.setZwwdtbh( dto.getZwwdtbh() );
        rktYwslJbLsViewResp.setZwwdtmc( dto.getZwwdtmc() );
        rktYwslJbLsViewResp.setHlwsqlx( dto.getHlwsqlx() );
        rktYwslJbLsViewResp.setHalluserid( dto.getHalluserid() );
        rktYwslJbLsViewResp.setHallusername( dto.getHallusername() );
        rktYwslJbLsViewResp.setApplyfrom( dto.getApplyfrom() );
        rktYwslJbLsViewResp.setBustype( dto.getBustype() );
        rktYwslJbLsViewResp.setSftqhlwcl( dto.getSftqhlwcl() );
        rktYwslJbLsViewResp.setDycjlx( dto.getDycjlx() );
        rktYwslJbLsViewResp.setBgyjssbbz( dto.getBgyjssbbz() );
        rktYwslJbLsViewResp.setBgyjssbsj( dto.getBgyjssbsj() );
        rktYwslJbLsViewResp.setSqbsfbzqm( dto.getSqbsfbzqm() );
        rktYwslJbLsViewResp.setCbsfbzqm( dto.getCbsfbzqm() );
        rktYwslJbLsViewResp.setSfksxtqr( dto.getSfksxtqr() );
        rktYwslJbLsViewResp.setQtsqr( dto.getQtsqr() );
        rktYwslJbLsViewResp.setSqrrxbdsj( dto.getSqrrxbdsj() );
        rktYwslJbLsViewResp.setSqrrxbdxsd( dto.getSqrrxbdxsd() );
        rktYwslJbLsViewResp.setSqrrxbdjg( dto.getSqrrxbdjg() );
        rktYwslJbLsViewResp.setSqrgzdwlb( dto.getSqrgzdwlb() );
        rktYwslJbLsViewResp.setSqrgzdwtyshxydm( dto.getSqrgzdwtyshxydm() );
        rktYwslJbLsViewResp.setQtsqrgmsfhm( dto.getQtsqrgmsfhm() );
        rktYwslJbLsViewResp.setQtsqrlxdh( dto.getQtsqrlxdh() );

        return rktYwslJbLsViewResp;
    }

    @Override
    public RktYwslJbLsCreateResp convertToCreateResp(RktYwslJbLsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RktYwslJbLsCreateResp rktYwslJbLsCreateResp = new RktYwslJbLsCreateResp();

        rktYwslJbLsCreateResp.setYwslh( dto.getYwslh() );
        rktYwslJbLsCreateResp.setLcdyid( dto.getLcdyid() );
        rktYwslJbLsCreateResp.setLcmc( dto.getLcmc() );
        rktYwslJbLsCreateResp.setSpyj( dto.getSpyj() );
        rktYwslJbLsCreateResp.setSlrs( dto.getSlrs() );
        rktYwslJbLsCreateResp.setGmsfhm( dto.getGmsfhm() );
        rktYwslJbLsCreateResp.setXm( dto.getXm() );
        rktYwslJbLsCreateResp.setLcywlx( dto.getLcywlx() );
        rktYwslJbLsCreateResp.setLcslid( dto.getLcslid() );
        rktYwslJbLsCreateResp.setLcywbt( dto.getLcywbt() );
        rktYwslJbLsCreateResp.setLcrwjd( dto.getLcrwjd() );
        rktYwslJbLsCreateResp.setLczt( dto.getLczt() );
        rktYwslJbLsCreateResp.setBlzt( dto.getBlzt() );
        rktYwslJbLsCreateResp.setSqrgmsfhm( dto.getSqrgmsfhm() );
        rktYwslJbLsCreateResp.setSqrxm( dto.getSqrxm() );
        rktYwslJbLsCreateResp.setSqrxb( dto.getSqrxb() );
        rktYwslJbLsCreateResp.setSqrlxdh( dto.getSqrlxdh() );
        rktYwslJbLsCreateResp.setSqrybdrgx( dto.getSqrybdrgx() );
        rktYwslJbLsCreateResp.setSqrzzssxq( dto.getSqrzzssxq() );
        rktYwslJbLsCreateResp.setSqrzzxz( dto.getSqrzzxz() );
        rktYwslJbLsCreateResp.setSqrhkdjjg( dto.getSqrhkdjjg() );
        rktYwslJbLsCreateResp.setSjrxm( dto.getSjrxm() );
        rktYwslJbLsCreateResp.setSjrlxdh( dto.getSjrlxdh() );
        rktYwslJbLsCreateResp.setSqrtxdz( dto.getSqrtxdz() );
        rktYwslJbLsCreateResp.setSqrzjmc( dto.getSqrzjmc() );
        rktYwslJbLsCreateResp.setSqrzjhm( dto.getSqrzjhm() );
        rktYwslJbLsCreateResp.setSqrgzdw( dto.getSqrgzdw() );
        rktYwslJbLsCreateResp.setHjsxcxlb( dto.getHjsxcxlb() );
        rktYwslJbLsCreateResp.setSqcxly( dto.getSqcxly() );
        rktYwslJbLsCreateResp.setSfscdzqzhky( dto.getSfscdzqzhky() );
        rktYwslJbLsCreateResp.setSfdyhkbsy( dto.getSfdyhkbsy() );
        rktYwslJbLsCreateResp.setSfazjthfsdy( dto.getSfazjthfsdy() );
        rktYwslJbLsCreateResp.setSfdyqhry( dto.getSfdyqhry() );
        rktYwslJbLsCreateResp.setSfdycsyy( dto.getSfdycsyy() );
        rktYwslJbLsCreateResp.setSlrlxdh( dto.getSlrlxdh() );
        rktYwslJbLsCreateResp.setSprlxdh( dto.getSprlxdh() );
        rktYwslJbLsCreateResp.setSfxyyjzj( dto.getSfxyyjzj() );
        rktYwslJbLsCreateResp.setSfyjyjzj( dto.getSfyjyjzj() );
        rktYwslJbLsCreateResp.setKddh( dto.getKddh() );
        rktYwslJbLsCreateResp.setSpxxlylb( dto.getSpxxlylb() );
        rktYwslJbLsCreateResp.setSpxxly( dto.getSpxxly() );
        rktYwslJbLsCreateResp.setYsbxxbh( dto.getYsbxxbh() );
        rktYwslJbLsCreateResp.setYsbxtywwjmc( dto.getYsbxtywwjmc() );
        rktYwslJbLsCreateResp.setYsbxtywwjlx( dto.getYsbxtywwjlx() );
        rktYwslJbLsCreateResp.setSqrq( dto.getSqrq() );
        rktYwslJbLsCreateResp.setSpdwgajgjgdm( dto.getSpdwgajgjgdm() );
        rktYwslJbLsCreateResp.setSpdwgajgmc( dto.getSpdwgajgmc() );
        rktYwslJbLsCreateResp.setSprxm( dto.getSprxm() );
        rktYwslJbLsCreateResp.setSpsj( dto.getSpsj() );
        rktYwslJbLsCreateResp.setSpjgdm( dto.getSpjgdm() );
        rktYwslJbLsCreateResp.setSldwgajgjgdm( dto.getSldwgajgjgdm() );
        rktYwslJbLsCreateResp.setSldwgajgmc( dto.getSldwgajgmc() );
        rktYwslJbLsCreateResp.setSlrxm( dto.getSlrxm() );
        rktYwslJbLsCreateResp.setSlsj( dto.getSlsj() );
        rktYwslJbLsCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        rktYwslJbLsCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        rktYwslJbLsCreateResp.setJcwh( dto.getJcwh() );
        rktYwslJbLsCreateResp.setBz( dto.getBz() );
        rktYwslJbLsCreateResp.setBlhjywid( dto.getBlhjywid() );
        rktYwslJbLsCreateResp.setHjywblsj( dto.getHjywblsj() );
        rktYwslJbLsCreateResp.setSlrid( dto.getSlrid() );
        rktYwslJbLsCreateResp.setPjjg( dto.getPjjg() );
        rktYwslJbLsCreateResp.setPjpljc( dto.getPjpljc() );
        rktYwslJbLsCreateResp.setPjsj( dto.getPjsj() );
        rktYwslJbLsCreateResp.setSbsfxy( dto.getSbsfxy() );
        rktYwslJbLsCreateResp.setSbzt( dto.getSbzt() );
        rktYwslJbLsCreateResp.setSbywlx( dto.getSbywlx() );
        rktYwslJbLsCreateResp.setSbywmc( dto.getSbywmc() );
        rktYwslJbLsCreateResp.setSbsbsj( dto.getSbsbsj() );
        rktYwslJbLsCreateResp.setSbslsj( dto.getSbslsj() );
        rktYwslJbLsCreateResp.setSbshsj( dto.getSbshsj() );
        rktYwslJbLsCreateResp.setSbbjsj( dto.getSbbjsj() );
        rktYwslJbLsCreateResp.setSbspkssj( dto.getSbspkssj() );
        rktYwslJbLsCreateResp.setSbspjssj( dto.getSbspjssj() );
        rktYwslJbLsCreateResp.setSbsprxm( dto.getSbsprxm() );
        rktYwslJbLsCreateResp.setSbsprdwdm( dto.getSbsprdwdm() );
        rktYwslJbLsCreateResp.setSbspjgdm( dto.getSbspjgdm() );
        rktYwslJbLsCreateResp.setSbspyj( dto.getSbspyj() );
        rktYwslJbLsCreateResp.setMjscshhs( dto.getMjscshhs() );
        rktYwslJbLsCreateResp.setSpxxcjfs( dto.getSpxxcjfs() );
        rktYwslJbLsCreateResp.setHzxm( dto.getHzxm() );
        rktYwslJbLsCreateResp.setHzgmsfhm( dto.getHzgmsfhm() );
        rktYwslJbLsCreateResp.setDababz( dto.getDababz() );
        rktYwslJbLsCreateResp.setDabasj( dto.getDabasj() );
        rktYwslJbLsCreateResp.setRlhbz( dto.getRlhbz() );
        rktYwslJbLsCreateResp.setRsjshjg( dto.getRsjshjg() );
        rktYwslJbLsCreateResp.setRsjshly( dto.getRsjshly() );
        rktYwslJbLsCreateResp.setRsjshsj( dto.getRsjshsj() );
        rktYwslJbLsCreateResp.setRsjshr( dto.getRsjshr() );
        rktYwslJbLsCreateResp.setGjnzsbh( dto.getGjnzsbh() );
        rktYwslJbLsCreateResp.setZwwdtbh( dto.getZwwdtbh() );
        rktYwslJbLsCreateResp.setZwwdtmc( dto.getZwwdtmc() );
        rktYwslJbLsCreateResp.setHlwsqlx( dto.getHlwsqlx() );
        rktYwslJbLsCreateResp.setHalluserid( dto.getHalluserid() );
        rktYwslJbLsCreateResp.setHallusername( dto.getHallusername() );
        rktYwslJbLsCreateResp.setApplyfrom( dto.getApplyfrom() );
        rktYwslJbLsCreateResp.setBustype( dto.getBustype() );
        rktYwslJbLsCreateResp.setSftqhlwcl( dto.getSftqhlwcl() );
        rktYwslJbLsCreateResp.setDycjlx( dto.getDycjlx() );
        rktYwslJbLsCreateResp.setBgyjssbbz( dto.getBgyjssbbz() );
        rktYwslJbLsCreateResp.setBgyjssbsj( dto.getBgyjssbsj() );
        rktYwslJbLsCreateResp.setSqbsfbzqm( dto.getSqbsfbzqm() );
        rktYwslJbLsCreateResp.setCbsfbzqm( dto.getCbsfbzqm() );
        rktYwslJbLsCreateResp.setSfksxtqr( dto.getSfksxtqr() );
        rktYwslJbLsCreateResp.setQtsqr( dto.getQtsqr() );
        rktYwslJbLsCreateResp.setSqrrxbdsj( dto.getSqrrxbdsj() );
        rktYwslJbLsCreateResp.setSqrrxbdxsd( dto.getSqrrxbdxsd() );
        rktYwslJbLsCreateResp.setSqrrxbdjg( dto.getSqrrxbdjg() );
        rktYwslJbLsCreateResp.setSqrgzdwlb( dto.getSqrgzdwlb() );
        rktYwslJbLsCreateResp.setSqrgzdwtyshxydm( dto.getSqrgzdwtyshxydm() );
        rktYwslJbLsCreateResp.setQtsqrgmsfhm( dto.getQtsqrgmsfhm() );
        rktYwslJbLsCreateResp.setQtsqrlxdh( dto.getQtsqrlxdh() );

        return rktYwslJbLsCreateResp;
    }
}
