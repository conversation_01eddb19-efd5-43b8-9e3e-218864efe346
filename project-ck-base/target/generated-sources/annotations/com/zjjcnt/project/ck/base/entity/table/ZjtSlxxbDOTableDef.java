package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class ZjtSlxxbDOTableDef extends TableDef {

    /**
     * 居民身份证受理信息表DO

 <AUTHOR>
 @date 2024-05-09 11:08:52
 @see com.zjjcnt.project.ck.base.dto.ZjtSlxxbDTO
     */
    public static final ZjtSlxxbDOTableDef ZJT_SLXXB_DO = new ZjtSlxxbDOTableDef();

    /**
     * 备注
     */
    public final QueryColumn BZ = new QueryColumn(this, "bz");

    /**
     * 民族
     */
    public final QueryColumn MZ = new QueryColumn(this, "mz");

    /**
     * 性别
     */
    public final QueryColumn XB = new QueryColumn(this, "xb");

    /**
     * 姓名
     */
    public final QueryColumn XM = new QueryColumn(this, "xm");

    /**
     * 住址
     */
    public final QueryColumn ZZ = new QueryColumn(this, "zz");

    /**
     * 街路巷
     */
    public final QueryColumn JLX = new QueryColumn(this, "jlx");

    /**
     * 派出所
     */
    public final QueryColumn PCS = new QueryColumn(this, "pcs");

    /**
     * 排序号
     */
    public final QueryColumn PXH = new QueryColumn(this, "pxh");

    /**
     * 受理号
     */
    public final QueryColumn SLH = new QueryColumn(this, "slh");

    /**
     * 责任区
     */
    public final QueryColumn ZRQ = new QueryColumn(this, "zrq");

    /**
     * 处理单位
     */
    public final QueryColumn CLDW = new QueryColumn(this, "cldw");

    /**
     * 处理情况
     */
    public final QueryColumn CLQK = new QueryColumn(this, "clqk");

    /**
     * 处理日期
     */
    public final QueryColumn CLRQ = new QueryColumn(this, "clrq");

    /**
     * 出生日期
     */
    public final QueryColumn CSRQ = new QueryColumn(this, "csrq");

    /**
     * 错误描述
     */
    public final QueryColumn CWMS = new QueryColumn(this, "cwms");

    public final QueryColumn CZSJ = new QueryColumn(this, "czsj");

    /**
     * 单位代码
     */
    public final QueryColumn DWDM = new QueryColumn(this, "dwdm");

    /**
     * 服务对象
     */
    public final QueryColumn FWDX = new QueryColumn(this, "fwdx");

    /**
     * 回馈时间
     */
    public final QueryColumn HKSJ = new QueryColumn(this, "hksj");

    /**
     * 居（村）委会
     */
    public final QueryColumn JCWH = new QueryColumn(this, "jcwh");

    /**
     * 检验单位
     */
    public final QueryColumn JYDW = new QueryColumn(this, "jydw");

    /**
     * 检验日期
     */
    public final QueryColumn JYRQ = new QueryColumn(this, "jyrq");

    /**
     * 领证方式
     */
    public final QueryColumn LQFS = new QueryColumn(this, "lqfs");

    /**
     * 领取日期
     */
    public final QueryColumn LQRQ = new QueryColumn(this, "lqrq");

    /**
     * 门（楼）牌号
     */
    public final QueryColumn MLPH = new QueryColumn(this, "mlph");

    /**
     * 门（楼）详址
     */
    public final QueryColumn MLXZ = new QueryColumn(this, "mlxz");

    /**
     * 评价结果
     */
    public final QueryColumn PJJG = new QueryColumn(this, "pjjg");

    /**
     * 评价时间
     */
    public final QueryColumn PJSJ = new QueryColumn(this, "pjsj");

    /**
     * 签发单位
     */
    public final QueryColumn QFDW = new QueryColumn(this, "qfdw");

    /**
     * 签发机关
     */
    public final QueryColumn QFJG = new QueryColumn(this, "qfjg");

    /**
     * 签发日期
     */
    public final QueryColumn QFRQ = new QueryColumn(this, "qfrq");

    public final QueryColumn RWID = new QueryColumn(this, "rwid");

    /**
     * 人员ID
     */
    public final QueryColumn RYID = new QueryColumn(this, "ryid");

    /**
     * 收费金额
     */
    public final QueryColumn SFJE = new QueryColumn(this, "sfje");

    /**
     * 收费类型
     */
    public final QueryColumn SFLX = new QueryColumn(this, "sflx");

    /**
     * 审核单位
     */
    public final QueryColumn SHDW = new QueryColumn(this, "shdw");

    /**
     * 审核情况
     */
    public final QueryColumn SHQK = new QueryColumn(this, "shqk");

    /**
     * 审核日期
     */
    public final QueryColumn SHRQ = new QueryColumn(this, "shrq");

    public final QueryColumn SLFS = new QueryColumn(this, "slfs");

    /**
     * 申领原因
     */
    public final QueryColumn SLYY = new QueryColumn(this, "slyy");

    /**
     * 受理状态
     */
    public final QueryColumn SLZT = new QueryColumn(this, "slzt");

    /**
     * 省市县（区）
     */
    public final QueryColumn SSXQ = new QueryColumn(this, "ssxq");

    /**
     * 同步标志
     */
    public final QueryColumn TBBZ = new QueryColumn(this, "tbbz");

    /**
     * 乡镇（街道）
     */
    public final QueryColumn XZJD = new QueryColumn(this, "xzjd");

    /**
     * 业务标志
     */
    public final QueryColumn YWBZ = new QueryColumn(this, "ywbz");

    /**
     * 照片ID
     */
    public final QueryColumn ZPID = new QueryColumn(this, "zpid");

    /**
     * 制证类型
     */
    public final QueryColumn ZZLX = new QueryColumn(this, "zzlx");

    public final QueryColumn BWBHA = new QueryColumn(this, "bwbha");

    public final QueryColumn BWBHB = new QueryColumn(this, "bwbhb");

    public final QueryColumn BWBHC = new QueryColumn(this, "bwbhc");

    /**
     * 操作员ID
     */
    public final QueryColumn CZYID = new QueryColumn(this, "czyid");

    /**
     * 操作员ip
     */
    public final QueryColumn CZYIP = new QueryColumn(this, "czyip");

    /**
     * 操作员姓名
     */
    public final QueryColumn CZYXM = new QueryColumn(this, "czyxm");

    /**
     * 分拣批次号
     */
    public final QueryColumn FJPCH = new QueryColumn(this, "fjpch");

    /**
     * 检验人姓名
     */
    public final QueryColumn JYRXM = new QueryColumn(this, "jyrxm");

    /**
     * 领取人姓名
     */
    public final QueryColumn LQRXM = new QueryColumn(this, "lqrxm");

    /**
     * 签发人ID
     */
    public final QueryColumn QFRID = new QueryColumn(this, "qfrid");

    /**
     * 审核人姓名
     */
    public final QueryColumn QFRXM = new QueryColumn(this, "qfrxm");

    /**
     * 收费单据号
     */
    public final QueryColumn SFDJH = new QueryColumn(this, "sfdjh");

    public final QueryColumn SHRID = new QueryColumn(this, "shrid");

    /**
     * 审核人姓名
     */
    public final QueryColumn SHRXM = new QueryColumn(this, "shrxm");

    /**
     * 收件人姓名
     */
    public final QueryColumn SJRXM = new QueryColumn(this, "sjrxm");

    public final QueryColumn SJRXZ = new QueryColumn(this, "sjrxz");

    /**
     * 收件人邮编
     */
    public final QueryColumn SJRYB = new QueryColumn(this, "sjryb");

    /**
     * 受理方式新
     */
    public final QueryColumn SLFSX = new QueryColumn(this, "slfsx");

    public final QueryColumn SPDZ1 = new QueryColumn(this, "spdz1");

    /**
     * 申请人_姓名
     */
    public final QueryColumn SQRXM = new QueryColumn(this, "sqrxm");

    /**
     * 双语证标志
     */
    public final QueryColumn SYZBZ = new QueryColumn(this, "syzbz");

    public final QueryColumn YWSLH = new QueryColumn(this, "ywslh");

    /**
     * 总分成金额
     */
    public final QueryColumn ZFCJE = new QueryColumn(this, "zfcje");

    /**
     * 指纹二指位
     */
    public final QueryColumn ZWEZW = new QueryColumn(this, "zwezw");

    /**
     * 指纹一指位
     */
    public final QueryColumn ZWYZW = new QueryColumn(this, "zwyzw");

    /**
     * 办证设备编号
     */
    public final QueryColumn BZSBBH = new QueryColumn(this, "bzsbbh");

    /**
     * 办证设备名称
     */
    public final QueryColumn BZSBMC = new QueryColumn(this, "bzsbmc");

    public final QueryColumn CLDWDM = new QueryColumn(this, "cldwdm");

    /**
     * 地市分成金额
     */
    public final QueryColumn DSFCJE = new QueryColumn(this, "dsfcje");

    /**
     * 审核单位
     */
    public final QueryColumn DSSHDW = new QueryColumn(this, "dsshdw");

    /**
     * 审核情况
     */
    public final QueryColumn DSSHQK = new QueryColumn(this, "dsshqk");

    /**
     * 审核日期
     */
    public final QueryColumn DSSHRQ = new QueryColumn(this, "dsshrq");

    public final QueryColumn DZTBBZ = new QueryColumn(this, "dztbbz");

    /**
     * 对账同步标志
     */
    public final QueryColumn DZTBSJ = new QueryColumn(this, "dztbsj");

    /**
     * 电子整档标志
     */
    public final QueryColumn DZZDBZ = new QueryColumn(this, "dzzdbz");

    /**
     * 公民身份号码
     */
    public final QueryColumn GMSFHM = new QueryColumn(this, "gmsfhm");

    /**
     * 旧证起始日期
     */
    public final QueryColumn JZQSRQ = new QueryColumn(this, "jzqsrq");

    public final QueryColumn KSFSBZ = new QueryColumn(this, "ksfsbz");

    public final QueryColumn KSFSSJ = new QueryColumn(this, "ksfssj");

    /**
     * 离线采集时间
     */
    public final QueryColumn LXCJSJ = new QueryColumn(this, "lxcjsj");

    /**
     * 离线受理单位
     */
    public final QueryColumn LXSLDW = new QueryColumn(this, "lxsldw");

    /**
     * 民族（民族文字）
     */
    public final QueryColumn MZMZWZ = new QueryColumn(this, "mzmzwz");

    /**
     * 内部受理ID
     */
    public final QueryColumn NBSLID = new QueryColumn(this, "nbslid");

    /**
     * 跑了几次评价
     */
    public final QueryColumn PJPLJC = new QueryColumn(this, "pjpljc");

    /**
     * 区县分成金额
     */
    public final QueryColumn QXFCJE = new QueryColumn(this, "qxfcje");

    /**
     * 区域范围代码
     */
    public final QueryColumn QYFWDM = new QueryColumn(this, "qyfwdm");

    /**
     * 人脸比对ID
     */
    public final QueryColumn RLBDBZ = new QueryColumn(this, "rlbdbz");

    public final QueryColumn RLBDID = new QueryColumn(this, "rlbdid");

    /**
     * 人脸比对标志
     */
    public final QueryColumn RLBDSJ = new QueryColumn(this, "rlbdsj");

    /**
     * 任务调度时间
     */
    public final QueryColumn RWDDSJ = new QueryColumn(this, "rwddsj");

    public final QueryColumn RXBDHS = new QueryColumn(this, "rxbdhs");

    public final QueryColumn RXBDJG = new QueryColumn(this, "rxbdjg");

    public final QueryColumn RYNBID = new QueryColumn(this, "rynbid");

    /**
     * 是否指纹证件
     */
    public final QueryColumn SFZWZJ = new QueryColumn(this, "sfzwzj");

    public final QueryColumn SHDWDM = new QueryColumn(this, "shdwdm");

    /**
     * 数据包流水号
     */
    public final QueryColumn SJBLSH = new QueryColumn(this, "sjblsh");

    /**
     * 省厅接收时间
     */
    public final QueryColumn STJSSJ = new QueryColumn(this, "stjssj");

    /**
     * 性别（民族文字）
     */
    public final QueryColumn XBMZWZ = new QueryColumn(this, "xbmzwz");

    /**
     * 姓名（民族文字）
     */
    public final QueryColumn XMMZWZ = new QueryColumn(this, "xmmzwz");

    /**
     * 邮政快递单号
     */
    public final QueryColumn YZKDDH = new QueryColumn(this, "yzkddh");

    /**
     * 证件到达日期
     */
    public final QueryColumn ZJDDRQ = new QueryColumn(this, "zjddrq");

    public final QueryColumn ZJZDXZ = new QueryColumn(this, "zjzdxz");

    /**
     * 质量回馈状态
     */
    public final QueryColumn ZLHKZT = new QueryColumn(this, "zlhkzt");

    /**
     * 照片采集类型
     */
    public final QueryColumn ZPCJLX = new QueryColumn(this, "zpcjlx");

    /**
     * 临时照片表ID
     */
    public final QueryColumn ZPLSID = new QueryColumn(this, "zplsid");

    /**
     * 照片设备编号
     */
    public final QueryColumn ZPSBBH = new QueryColumn(this, "zpsbbh");

    /**
     * 指纹图像临时ID
     */
    public final QueryColumn ZWTXID = new QueryColumn(this, "zwtxid");

    /**
     * 中心分成金额
     */
    public final QueryColumn ZXFCJE = new QueryColumn(this, "zxfcje");

    /**
     * 出生地省市县（区）
     */
    public final QueryColumn CSDSSXQ = new QueryColumn(this, "csdssxq");

    public final QueryColumn CZYDWMC = new QueryColumn(this, "czydwmc");

    /**
     * 操作员联系电话
     */
    public final QueryColumn CZYLXDH = new QueryColumn(this, "czylxdh");

    public final QueryColumn DSSHRID = new QueryColumn(this, "dsshrid");

    /**
     * 审核人姓名
     */
    public final QueryColumn DSSHRXM = new QueryColumn(this, "dsshrxm");

    /**
     * 对账同步时间
     */
    public final QueryColumn DZSJBBH = new QueryColumn(this, "dzsjbbh");

    public final QueryColumn HJDDZBM = new QueryColumn(this, "hjddzbm");

    /**
     * 互联网申请id
     */
    public final QueryColumn HLWSQID = new QueryColumn(this, "hlwsqid");

    /**
     * 紧急联系人电话
     */
    public final QueryColumn JJLXRDH = new QueryColumn(this, "jjlxrdh");

    /**
     * 紧急联系人姓名
     */
    public final QueryColumn JJLXRXM = new QueryColumn(this, "jjlxrxm");

    public final QueryColumn KSYWLSH = new QueryColumn(this, "ksywlsh");

    /**
     * 领取人身份号码
     */
    public final QueryColumn LQRSFHM = new QueryColumn(this, "lqrsfhm");

    /**
     * 领取人照片ID
     */
    public final QueryColumn LQRZPID = new QueryColumn(this, "lqrzpid");

    /**
     * 绿通证审核结果
     */
    public final QueryColumn LTZSHJG = new QueryColumn(this, "ltzshjg");

    /**
     * 绿通证审批时间
     */
    public final QueryColumn LTZSPSJ = new QueryColumn(this, "ltzspsj");

    /**
     * 绿通证申请时间
     */
    public final QueryColumn LTZSQSJ = new QueryColumn(this, "ltzsqsj");

    /**
     * 绿通证申请事由
     */
    public final QueryColumn LTZSQSY = new QueryColumn(this, "ltzsqsy");

    /**
     * 绿通证申请原因
     */
    public final QueryColumn LTZSQYY = new QueryColumn(this, "ltzsqyy");

    /**
     * 离线操作员名称
     */
    public final QueryColumn LXCZYMC = new QueryColumn(this, "lxczymc");

    public final QueryColumn LZCZRID = new QueryColumn(this, "lzczrid");

    public final QueryColumn LZCZRXM = new QueryColumn(this, "lzczrxm");

    public final QueryColumn MLPNBID = new QueryColumn(this, "mlpnbid");

    /**
     * 民族附加项代码
     */
    public final QueryColumn MZFJXDM = new QueryColumn(this, "mzfjxdm");

    /**
     * 内部身份证ID
     */
    public final QueryColumn NBSFZID = new QueryColumn(this, "nbsfzid");

    public final QueryColumn RXBDKBH = new QueryColumn(this, "rxbdkbh");

    public final QueryColumn RXBDXSD = new QueryColumn(this, "rxbdxsd");

    /**
     * 是否待挂失证件0否1是
     */
    public final QueryColumn SFDGSZJ = new QueryColumn(this, "sfdgszj");

    /**
     * 是否申请绿通证
     */
    public final QueryColumn SFSQLTZ = new QueryColumn(this, "sfsqltz");

    /**
     * 区县审核联系电话
     */
    public final QueryColumn SHRLXDH = new QueryColumn(this, "shrlxdh");

    /**
     * 收件人联系电话
     */
    public final QueryColumn SJRLXDH = new QueryColumn(this, "sjrlxdh");

    public final QueryColumn SJRSSXQ = new QueryColumn(this, "sjrssxq");

    /**
     * 收件人通讯地址
     */
    public final QueryColumn SJRTXDZ = new QueryColumn(this, "sjrtxdz");

    public final QueryColumn SLSHJDZ = new QueryColumn(this, "slshjdz");

    public final QueryColumn SQRLXDH = new QueryColumn(this, "sqrlxdh");

    /**
     * 照片设备标识号
     */
    public final QueryColumn ZPSBBSH = new QueryColumn(this, "zpsbbsh");

    public final QueryColumn ZPYTBID = new QueryColumn(this, "zpytbid");

    /**
     * 指纹采集器编号
     */
    public final QueryColumn ZWCJQID = new QueryColumn(this, "zwcjqid");

    public final QueryColumn ZWEBDJG = new QueryColumn(this, "zwebdjg");

    /**
     * 指纹二注册结果
     */
    public final QueryColumn ZWEZCJG = new QueryColumn(this, "zwezcjg");

    public final QueryColumn ZWYBDJG = new QueryColumn(this, "zwybdjg");

    /**
     * 指纹一注册结果
     */
    public final QueryColumn ZWYZCJG = new QueryColumn(this, "zwyzcjg");

    /**
     * 办证设备品牌型号
     */
    public final QueryColumn BZSBPPXH = new QueryColumn(this, "bzsbppxh");

    /**
     * 办证设备生产公司
     */
    public final QueryColumn BZSBSCGS = new QueryColumn(this, "bzsbscgs");

    /**
     * 办证设备销售公司
     */
    public final QueryColumn BZSBXSGS = new QueryColumn(this, "bzsbxsgs");

    /**
     * 办证设备注册单位
     */
    public final QueryColumn BZSBZCDW = new QueryColumn(this, "bzsbzcdw");

    public final QueryColumn DSSHDWDM = new QueryColumn(this, "dsshdwdm");

    /**
     * 绿通证审批人编号
     */
    public final QueryColumn LTZSPRID = new QueryColumn(this, "ltzsprid");

    /**
     * 绿通证审批人姓名
     */
    public final QueryColumn LTZSPRXM = new QueryColumn(this, "ltzsprxm");

    /**
     * 签发单位机构代码
     */
    public final QueryColumn QFDWJGDM = new QueryColumn(this, "qfdwjgdm");

    /**
     * 签发机关（民族文字）
     */
    public final QueryColumn QFJGMZWZ = new QueryColumn(this, "qfjgmzwz");

    /**
     * 任务执行日志编号
     */
    public final QueryColumn RWZXRZBH = new QueryColumn(this, "rwzxrzbh");

    public final QueryColumn RXBDKSSJ = new QueryColumn(this, "rxbdkssj");

    /**
     * 是否保存照片原图
     */
    public final QueryColumn SFBCZPYT = new QueryColumn(this, "sfbczpyt");

    /**
     * 手指异常状况代码
     */
    public final QueryColumn SZYCZKDM = new QueryColumn(this, "szyczkdm");

    /**
     * 第三方用户名称
     */
    public final QueryColumn USERNAME = new QueryColumn(this, "username");

    /**
     * 有效期限截止日期
     */
    public final QueryColumn YXQXJZRQ = new QueryColumn(this, "yxqxjzrq");

    /**
     * 有效期限起始日期
     */
    public final QueryColumn YXQXQSRQ = new QueryColumn(this, "yxqxqsrq");

    public final QueryColumn ZJZDSSXQ = new QueryColumn(this, "zjzdssxq");

    /**
     * 照片设备品牌型号
     */
    public final QueryColumn ZPSBPPXH = new QueryColumn(this, "zpsbppxh");

    /**
     * 照片设备生产公司
     */
    public final QueryColumn ZPSBSCGS = new QueryColumn(this, "zpsbscgs");

    /**
     * 照片设备销售公司
     */
    public final QueryColumn ZPSBXSGS = new QueryColumn(this, "zpsbxsgs");

    /**
     * 照片设备注册单位
     */
    public final QueryColumn ZPSBZCDW = new QueryColumn(this, "zpsbzcdw");

    /**
     * 指纹采集结果代码
     */
    public final QueryColumn ZWCJJGDM = new QueryColumn(this, "zwcjjgdm");

    public final QueryColumn ZWEBDXSD = new QueryColumn(this, "zwebdxsd");

    public final QueryColumn ZWYBDXSD = new QueryColumn(this, "zwybdxsd");

    /**
     * 住址行二（民族文字）
     */
    public final QueryColumn ZZHEMZWZ = new QueryColumn(this, "zzhemzwz");

    /**
     * 住址行三（民族文字）
     */
    public final QueryColumn ZZHSMZWZ = new QueryColumn(this, "zzhsmzwz");

    /**
     * 住址行一（民族文字）
     */
    public final QueryColumn ZZHYMZWZ = new QueryColumn(this, "zzhymzwz");

    /**
     * 制证信息错误类别
     */
    public final QueryColumn ZZXXCWLB = new QueryColumn(this, "zzxxcwlb");

    /**
     * 地市审核联系电话
     */
    public final QueryColumn DSSHRLXDH = new QueryColumn(this, "dsshrlxdh");

    public final QueryColumn LSSFZSLBZ = new QueryColumn(this, "lssfzslbz");

    /**
     * 领证操作人单位代码
     */
    public final QueryColumn LZCZRDWDM = new QueryColumn(this, "lzczrdwdm");

    /**
     * 领证操作人单位名称
     */
    public final QueryColumn LZCZRDWMC = new QueryColumn(this, "lzczrdwmc");

    public final QueryColumn LZSSFJHJZ = new QueryColumn(this, "lzssfjhjz");

    public final QueryColumn LZSZWBDSM = new QueryColumn(this, "lzszwbdsm");

    public final QueryColumn LZSZWSFHY = new QueryColumn(this, "lzszwsfhy");

    /**
     * 民族附加项（民族文字）
     */
    public final QueryColumn MZFJXMZWZ = new QueryColumn(this, "mzfjxmzwz");

    /**
     * 是否满十八岁无照片
     */
    public final QueryColumn SFMSBSWZP = new QueryColumn(this, "sfmsbswzp");

    /**
     * 申请人_公民身份号码
     */
    public final QueryColumn SQRGMSFHM = new QueryColumn(this, "sqrgmsfhm");

    /**
     * 办证设备品牌型号编码
     */
    public final QueryColumn BZSBPPXHDM = new QueryColumn(this, "bzsbppxhdm");

    public final QueryColumn LZSZWEHYJG = new QueryColumn(this, "lzszwehyjg");

    public final QueryColumn LZSZWYHYJG = new QueryColumn(this, "lzszwyhyjg");

    /**
     * 治安管理业务类别代码
     */
    public final QueryColumn ZAGLYWLBDM = new QueryColumn(this, "zaglywlbdm");

    /**
     * 治安管理业务协同编号
     */
    public final QueryColumn ZAGLYWXTBH = new QueryColumn(this, "zaglywxtbh");

    /**
     * 照片设备品牌型号编码
     */
    public final QueryColumn ZPSBPPXHDM = new QueryColumn(this, "zpsbppxhdm");

    public final QueryColumn HJDSJGSDWDM = new QueryColumn(this, "hjdsjgsdwdm");

    public final QueryColumn HJDSJGSDWMC = new QueryColumn(this, "hjdsjgsdwmc");

    /**
     * 户籍地址区划内详细地址
     */
    public final QueryColumn HJDZQHNXXDZ = new QueryColumn(this, "hjdzqhnxxdz");

    /**
     * 紧急联系人与受理人关系
     */
    public final QueryColumn JJLXRYSLRGX = new QueryColumn(this, "jjlxryslrgx");

    public final QueryColumn LZSZWEHYXSD = new QueryColumn(this, "lzszwehyxsd");

    public final QueryColumn LZSZWYHYXSD = new QueryColumn(this, "lzszwyhyxsd");

    public final QueryColumn SLDSJGSDWDM = new QueryColumn(this, "sldsjgsdwdm");

    public final QueryColumn SLDSJGSDWMC = new QueryColumn(this, "sldsjgsdwmc");

    public final QueryColumn SLYCKRXSFBD = new QueryColumn(this, "slyckrxsfbd");

    public final QueryColumn SLYLSZWBDSM = new QueryColumn(this, "slylszwbdsm");

    public final QueryColumn SLYLSZWSFBD = new QueryColumn(this, "slylszwsfbd");

    /**
     * 治安管理政务服务事项编码
     */
    public final QueryColumn ZAGLZWFWSXBM = new QueryColumn(this, "zaglzwfwsxbm");

    public final QueryColumn SLDFJSJGSDWDM = new QueryColumn(this, "sldfjsjgsdwdm");

    public final QueryColumn SLDFJSJGSDWMC = new QueryColumn(this, "sldfjsjgsdwmc");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{BZ, MZ, XB, XM, ZZ, JLX, PCS, PXH, SLH, ZRQ, CLDW, CLQK, CLRQ, CSRQ, CWMS, CZSJ, DWDM, FWDX, HKSJ, JCWH, JYDW, JYRQ, LQFS, LQRQ, MLPH, MLXZ, PJJG, PJSJ, QFDW, QFJG, QFRQ, RWID, RYID, SFJE, SFLX, SHDW, SHQK, SHRQ, SLFS, SLYY, SLZT, SSXQ, TBBZ, XZJD, YWBZ, ZPID, ZZLX, BWBHA, BWBHB, BWBHC, CZYID, CZYIP, CZYXM, FJPCH, JYRXM, LQRXM, QFRID, QFRXM, SFDJH, SHRID, SHRXM, SJRXM, SJRXZ, SJRYB, SLFSX, SPDZ1, SQRXM, SYZBZ, YWSLH, ZFCJE, ZWEZW, ZWYZW, BZSBBH, BZSBMC, CLDWDM, DSFCJE, DSSHDW, DSSHQK, DSSHRQ, DZTBBZ, DZTBSJ, DZZDBZ, GMSFHM, JZQSRQ, KSFSBZ, KSFSSJ, LXCJSJ, LXSLDW, MZMZWZ, NBSLID, PJPLJC, QXFCJE, QYFWDM, RLBDBZ, RLBDID, RLBDSJ, RWDDSJ, RXBDHS, RXBDJG, RYNBID, SFZWZJ, SHDWDM, SJBLSH, STJSSJ, XBMZWZ, XMMZWZ, YZKDDH, ZJDDRQ, ZJZDXZ, ZLHKZT, ZPCJLX, ZPLSID, ZPSBBH, ZWTXID, ZXFCJE, CSDSSXQ, CZYDWMC, CZYLXDH, DSSHRID, DSSHRXM, DZSJBBH, HJDDZBM, HLWSQID, JJLXRDH, JJLXRXM, KSYWLSH, LQRSFHM, LQRZPID, LTZSHJG, LTZSPSJ, LTZSQSJ, LTZSQSY, LTZSQYY, LXCZYMC, LZCZRID, LZCZRXM, MLPNBID, MZFJXDM, NBSFZID, RXBDKBH, RXBDXSD, SFDGSZJ, SFSQLTZ, SHRLXDH, SJRLXDH, SJRSSXQ, SJRTXDZ, SLSHJDZ, SQRLXDH, ZPSBBSH, ZPYTBID, ZWCJQID, ZWEBDJG, ZWEZCJG, ZWYBDJG, ZWYZCJG, BZSBPPXH, BZSBSCGS, BZSBXSGS, BZSBZCDW, DSSHDWDM, LTZSPRID, LTZSPRXM, QFDWJGDM, QFJGMZWZ, RWZXRZBH, RXBDKSSJ, SFBCZPYT, SZYCZKDM, USERNAME, YXQXJZRQ, YXQXQSRQ, ZJZDSSXQ, ZPSBPPXH, ZPSBSCGS, ZPSBXSGS, ZPSBZCDW, ZWCJJGDM, ZWEBDXSD, ZWYBDXSD, ZZHEMZWZ, ZZHSMZWZ, ZZHYMZWZ, ZZXXCWLB, DSSHRLXDH, LSSFZSLBZ, LZCZRDWDM, LZCZRDWMC, LZSSFJHJZ, LZSZWBDSM, LZSZWSFHY, MZFJXMZWZ, SFMSBSWZP, SQRGMSFHM, BZSBPPXHDM, LZSZWEHYJG, LZSZWYHYJG, ZAGLYWLBDM, ZAGLYWXTBH, ZPSBPPXHDM, HJDSJGSDWDM, HJDSJGSDWMC, HJDZQHNXXDZ, JJLXRYSLRGX, LZSZWEHYXSD, LZSZWYHYXSD, SLDSJGSDWDM, SLDSJGSDWMC, SLYCKRXSFBD, SLYLSZWBDSM, SLYLSZWSFBD, ZAGLZWFWSXBM, SLDFJSJGSDWDM, SLDFJSJGSDWMC};

    public ZjtSlxxbDOTableDef() {
        super("", "zjt_slxxb");
    }

    private ZjtSlxxbDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public ZjtSlxxbDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new ZjtSlxxbDOTableDef("", "zjt_slxxb", alias));
    }

}
