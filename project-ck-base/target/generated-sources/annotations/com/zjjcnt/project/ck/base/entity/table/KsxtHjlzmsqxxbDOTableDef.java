package com.zjjcnt.project.ck.base.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class KsxtHjlzmsqxxbDOTableDef extends TableDef {

    /**
     * 跨省协同户籍类证明申请信息表DO

 <AUTHOR>
 @date 2025-08-05 13:59:15
 @see com.zjjcnt.project.ck.base.dto.KsxtHjlzmsqxxbDTO
     */
    public static final KsxtHjlzmsqxxbDOTableDef KSXT_HJLZMSQXXB_DO = new KsxtHjlzmsqxxbDOTableDef();

    /**
     * 备注
     */
    public final QueryColumn BZ = new QueryColumn(this, "bz");

    /**
     * 姓名
     */
    public final QueryColumn XM = new QueryColumn(this, "xm");

    /**
     * 备注1
     */
    public final QueryColumn BZ1 = new QueryColumn(this, "bz1");

    /**
     * 反馈日期
     */
    public final QueryColumn FKRQ = new QueryColumn(this, "fkrq");

    /**
     * 联系电话
     */
    public final QueryColumn LXDH = new QueryColumn(this, "lxdh");

    /**
     * 入库时间
     */
    public final QueryColumn RKSJ = new QueryColumn(this, "rksj");

    /**
     * 申请日期
     */
    public final QueryColumn SQRQ = new QueryColumn(this, "sqrq");

    /**
     * 协查人_姓名
     */
    public final QueryColumn XCRXM = new QueryColumn(this, "xcrxm");

    /**
     * 公民身份号码
     */
    public final QueryColumn GMSFHM = new QueryColumn(this, "gmsfhm");

    /**
     * 跨省协同ID
     */
    public final QueryColumn KSXTID = new QueryColumn(this, "ksxtid");

    /**
     * 区域范围代码
     */
    public final QueryColumn QYFWDM = new QueryColumn(this, "qyfwdm");

    /**
     * 协查_日期时间
     */
    public final QueryColumn XCRQSJ = new QueryColumn(this, "xcrqsj");

    /**
     * 申请地_联系电话
     */
    public final QueryColumn SQDLXDH = new QueryColumn(this, "sqdlxdh");

    /**
     * 协查地_联系电话
     */
    public final QueryColumn XCDLXDH = new QueryColumn(this, "xcdlxdh");

    /**
     * 申请地_公安机关名称
     */
    public final QueryColumn SQDGAJGMC = new QueryColumn(this, "sqdgajgmc");

    /**
     * 协查地_公安机关名称
     */
    public final QueryColumn XCDGAJGMC = new QueryColumn(this, "xcdgajgmc");

    /**
     * 查证结果描述_简要情况
     */
    public final QueryColumn CZJGMSJYQK = new QueryColumn(this, "czjgmsjyqk");

    /**
     * 户籍地址_省市县（区）
     */
    public final QueryColumn HJDZSSXQDM = new QueryColumn(this, "hjdzssxqdm");

    /**
     * 户籍协查结果类型代码
     */
    public final QueryColumn HJXCJGLXDM = new QueryColumn(this, "hjxcjglxdm");

    /**
     * 治安管理业务协同编号
     */
    public final QueryColumn ZAGLYWXTBH = new QueryColumn(this, "zaglywxtbh");

    /**
     * 户籍地_数据归属单位代码
     */
    public final QueryColumn HJDSJGSDWDM = new QueryColumn(this, "hjdsjgsdwdm");

    /**
     * 户籍地_数据归属单位名称
     */
    public final QueryColumn HJDSJGSDWMC = new QueryColumn(this, "hjdsjgsdwmc");

    /**
     * 户籍地址_区划内详细地址
     */
    public final QueryColumn HJDZQHNXXDZ = new QueryColumn(this, "hjdzqhnxxdz");

    /**
     * 户籍证明类型及事项代码
     */
    public final QueryColumn HJZMLXJSXDM = new QueryColumn(this, "hjzmlxjsxdm");

    /**
     * 跨省协同户籍类证明ID
     */
    public final QueryColumn KSXTHJLZMID = new QueryColumn(this, "ksxthjlzmid");

    /**
     * 受理地_数据归属单位代码
     */
    public final QueryColumn SLDSJGSDWDM = new QueryColumn(this, "sldsjgsdwdm");

    /**
     * 受理地_数据归属单位名称
     */
    public final QueryColumn SLDSJGSDWMC = new QueryColumn(this, "sldsjgsdwmc");

    /**
     * 申请地_公安机关机构代码
     */
    public final QueryColumn SQDGAJGJGDM = new QueryColumn(this, "sqdgajgjgdm");

    /**
     * 协查地_公安机关机构代码
     */
    public final QueryColumn XCDGAJGJGDM = new QueryColumn(this, "xcdgajgjgdm");

    /**
     * 开具户籍类证明_业务流水号
     */
    public final QueryColumn KJHJLZMYWLSH = new QueryColumn(this, "kjhjlzmywlsh");

    /**
     * 需要证明事项内容_简要情况
     */
    public final QueryColumn XYZMSXNRJYQK = new QueryColumn(this, "xyzmsxnrjyqk");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{BZ, XM, BZ1, FKRQ, LXDH, RKSJ, SQRQ, XCRXM, GMSFHM, KSXTID, QYFWDM, XCRQSJ, SQDLXDH, XCDLXDH, SQDGAJGMC, XCDGAJGMC, CZJGMSJYQK, HJDZSSXQDM, HJXCJGLXDM, ZAGLYWXTBH, HJDSJGSDWDM, HJDSJGSDWMC, HJDZQHNXXDZ, HJZMLXJSXDM, KSXTHJLZMID, SLDSJGSDWDM, SLDSJGSDWMC, SQDGAJGJGDM, XCDGAJGJGDM, KJHJLZMYWLSH, XYZMSXNRJYQK};

    public KsxtHjlzmsqxxbDOTableDef() {
        super("", "ksxt_hjlzmsqxxb");
    }

    private KsxtHjlzmsqxxbDOTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public KsxtHjlzmsqxxbDOTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new KsxtHjlzmsqxxbDOTableDef("", "ksxt_hjlzmsqxxb", alias));
    }

}
