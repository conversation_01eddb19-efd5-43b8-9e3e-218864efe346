package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxDhhmxxbErrorDTO;
import com.zjjcnt.project.ck.base.entity.HjxxDhhmxxbErrorDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-06T16:49:43+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjxxDhhmxxbErrorConvertImpl implements HjxxDhhmxxbErrorConvert {

    @Override
    public HjxxDhhmxxbErrorDTO convert(HjxxDhhmxxbErrorDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjxxDhhmxxbErrorDTO hjxxDhhmxxbErrorDTO = new HjxxDhhmxxbErrorDTO();

        hjxxDhhmxxbErrorDTO.setId( entity.getId() );
        hjxxDhhmxxbErrorDTO.setDhhmid( entity.getDhhmid() );
        hjxxDhhmxxbErrorDTO.setRynbid( entity.getRynbid() );
        hjxxDhhmxxbErrorDTO.setRyid( entity.getRyid() );
        hjxxDhhmxxbErrorDTO.setGmsfhm( entity.getGmsfhm() );
        hjxxDhhmxxbErrorDTO.setXm( entity.getXm() );
        hjxxDhhmxxbErrorDTO.setDhhm( entity.getDhhm() );
        hjxxDhhmxxbErrorDTO.setRksj( entity.getRksj() );
        hjxxDhhmxxbErrorDTO.setSjly( entity.getSjly() );
        hjxxDhhmxxbErrorDTO.setYxbz( entity.getYxbz() );

        return hjxxDhhmxxbErrorDTO;
    }

    @Override
    public HjxxDhhmxxbErrorDO convertToDO(HjxxDhhmxxbErrorDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxDhhmxxbErrorDO hjxxDhhmxxbErrorDO = new HjxxDhhmxxbErrorDO();

        hjxxDhhmxxbErrorDO.setId( dto.getId() );
        hjxxDhhmxxbErrorDO.setDhhmid( dto.getDhhmid() );
        hjxxDhhmxxbErrorDO.setRynbid( dto.getRynbid() );
        hjxxDhhmxxbErrorDO.setRyid( dto.getRyid() );
        hjxxDhhmxxbErrorDO.setGmsfhm( dto.getGmsfhm() );
        hjxxDhhmxxbErrorDO.setXm( dto.getXm() );
        hjxxDhhmxxbErrorDO.setDhhm( dto.getDhhm() );
        hjxxDhhmxxbErrorDO.setRksj( dto.getRksj() );
        hjxxDhhmxxbErrorDO.setSjly( dto.getSjly() );
        hjxxDhhmxxbErrorDO.setYxbz( dto.getYxbz() );

        return hjxxDhhmxxbErrorDO;
    }
}
