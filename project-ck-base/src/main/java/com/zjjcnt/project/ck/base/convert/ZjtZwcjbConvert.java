package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtZwcjbDTO;
import com.zjjcnt.project.ck.base.dto.ZjtZwcjlsbDTO;
import com.zjjcnt.project.ck.base.dto.resp.ZjtZwcjbViewResp;
import com.zjjcnt.project.ck.base.entity.ZjtZwcjbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
* 指纹采集表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtZwcjbConvert {

    ZjtZwcjbConvert INSTANCE = Mappers.getMapper(ZjtZwcjbConvert.class);

    ZjtZwcjbDTO convert(ZjtZwcjbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtZwcjbDO convertToDO(ZjtZwcjbDTO dto);

    ZjtZwcjbViewResp convertToViewResp(ZjtZwcjbDTO dto);

    @Mapping(ignore = true, target = "zwtxid")
    ZjtZwcjbDTO convertToDTO(ZjtZwcjlsbDTO dto);

}
