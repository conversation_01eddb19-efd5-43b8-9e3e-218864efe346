package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtBzsflsbDTO;
import com.zjjcnt.project.ck.base.dto.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtBzsflsbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtBzsflsbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtBzsflsbViewResp;
import com.zjjcnt.project.ck.base.entity.ZjtBzsflsbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
* 办证收费流水表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtBzsflsbConvert {

    ZjtBzsflsbConvert INSTANCE = Mappers.getMapper(ZjtBzsflsbConvert.class);

    ZjtBzsflsbDTO convert(ZjtBzsflsbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtBzsflsbDO convertToDO(ZjtBzsflsbDTO dto);

    ZjtBzsflsbDTO convertToDTO(ZjtBzsflsbPageReq req);

    @Mapping(ignore = true, target = "id")
    ZjtBzsflsbDTO convertToDTO(ZjtSlxxbDTO zjtSlxxb);

    ZjtBzsflsbPageResp convertToPageResp(ZjtBzsflsbDTO dto);

    ZjtBzsflsbViewResp convertToViewResp(ZjtBzsflsbDTO dto);

}
