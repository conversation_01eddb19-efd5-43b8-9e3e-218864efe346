package com.zjjcnt.project.ck.base.dto;

import com.zjjcnt.common.core.dto.BaseDTO;
import lombok.Data;

import java.io.Serial;

/**
 * 跨省协同户籍类证明申请信息表DTO
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 * @see com.zjjcnt.project.ck.base.entity.KsxtHjlzmsqxxbDO
 */
@Data
public class KsxtHjlzmsqxxbDTO implements BaseDTO<String> {
    @Serial
    private static final long serialVersionUID = 5458635986781132681L;

    /**
     * 跨省协同户籍类证明ID
     */
    private String ksxthjlzmid;

    /**
     * 跨省协同ID
     */
    private String ksxtid;

    /**
     * 治安管理业务协同编号
     */
    private String zaglywxtbh;

    /**
     * 公民身份号码
     */
    private String gmsfhm;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 户籍地址_省市县（区）
     */
    private String hjdzssxqdm;

    /**
     * 户籍地址_区划内详细地址
     */
    private String hjdzqhnxxdz;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 户籍证明类型及事项代码
     */
    private String hjzmlxjsxdm;

    /**
     * 需要证明事项内容_简要情况
     */
    private String xyzmsxnrjyqk;

    /**
     * 开具户籍类证明_业务流水号
     */
    private String kjhjlzmywlsh;

    /**
     * 备注
     */
    private String bz;

    /**
     * 户籍协查结果类型代码
     */
    private String hjxcjglxdm;

    /**
     * 查证结果描述_简要情况
     */
    private String czjgmsjyqk;

    /**
     * 备注1
     */
    private String bz1;

    /**
     * 申请日期
     */
    private String sqrq;

    /**
     * 申请地_公安机关机构代码
     */
    private String sqdgajgjgdm;

    /**
     * 申请地_公安机关名称
     */
    private String sqdgajgmc;

    /**
     * 申请地_联系电话
     */
    private String sqdlxdh;

    /**
     * 反馈日期
     */
    private String fkrq;

    /**
     * 协查地_公安机关机构代码
     */
    private String xcdgajgjgdm;

    /**
     * 协查地_公安机关名称
     */
    private String xcdgajgmc;

    /**
     * 协查地_联系电话
     */
    private String xcdlxdh;

    /**
     * 协查人_姓名
     */
    private String xcrxm;

    /**
     * 协查_日期时间
     */
    private String xcrqsj;

    /**
     * 区域范围代码
     */
    private String qyfwdm;

    /**
     * 受理地_数据归属单位代码
     */
    private String sldsjgsdwdm;

    /**
     * 受理地_数据归属单位名称
     */
    private String sldsjgsdwmc;

    /**
     * 户籍地_数据归属单位代码
     */
    private String hjdsjgsdwdm;

    /**
     * 户籍地_数据归属单位名称
     */
    private String hjdsjgsdwmc;

    /**
     * 入库时间
     */
    private String rksj;

    // ------ 非数据库表字段 -------
    /**
     * 大于等于申请日期
     */
    private String sqrqStart;

    /**
     * 小于等于申请日期
     */
    private String sqrqEnd;

    /**
     * 大于等于反馈日期
     */
    private String fkrqStart;

    /**
     * 小于等于反馈日期
     */
    private String fkrqEnd;

    /**
     * 大于等于协查_日期时间
     */
    private String xcrqsjStart;

    /**
     * 小于等于协查_日期时间
     */
    private String xcrqsjEnd;

    /**
     * 大于等于入库时间
     */
    private String rksjStart;

    /**
     * 小于等于入库时间
     */
    private String rksjEnd;


    @Override
    public String getId() {
        return this.ksxthjlzmid;
    }

    @Override
    public void setId(String id) {
        this.ksxthjlzmid = id;
    }
}
