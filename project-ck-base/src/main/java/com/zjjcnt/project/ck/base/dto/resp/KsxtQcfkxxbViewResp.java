package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.DictSsxq;
import com.zjjcnt.common.core.annotation.StringDateFormat;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 跨省协同迁出反馈信息表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 */
@Data
@Schema(description = "跨省协同迁出反馈信息表响应对象")
public class KsxtQcfkxxbViewResp {

    @Schema(description = "跨省协同迁出反馈ID")
    private String ksxtqcfkid;

    @Schema(description = "跨省协同ID")
    private String ksxtid;

    @Schema(description = "治安管理业务协同编号")
    private String zaglywxtbh;

    @Schema(description = "持证人公民身份号码")
    private String czrgmsfhm;

    @Schema(description = "持证人姓名")
    private String czrxm;

    @DictSsxq
    @Schema(description = "原地址住址省市县（区）")
    private String yzzssxqdm;

    @Schema(description = "原住址住址区划内详细地址")
    private String yzzqhnxxdz;

    @Dict(CkDictTypeConstants.DM_CXSX)
    @Schema(description = "原住址城乡分类代码")
    private String yzzcxfldm;

    @DictSsxq
    @Schema(description = "去往地_省市县（区）")
    private String qwdssxqdm;

    @Schema(description = "去往地_区划内详细地址")
    private String qwdqhnxxdz;

    @Schema(description = "去往地_户口登记机关公安机关机构代码")
    private String qwdhkdjjggajgjgdm;

    @Schema(description = "去往地_户口登记机关公安机关名称")
    private String qwdhkdjjggajgmc;

    @Schema(description = "迁移证编号")
    private String qyzbh;

    @Schema(description = "迁移证_签发机关_公安机关机构代码")
    private String qyzqfjggajgjgdm;

    @Schema(description = "迁移证_签发机关_公安机关名称")
    private String qyzqfjggajgmc;

    @StringDateFormat
    @Schema(description = "迁移证_签发日期")
    private String qyzqfrq;

    @StringDateFormat
    @Schema(description = "迁移证_有效期截止日期")
    private String qyzyxqjzrq;

    @Schema(description = "电子迁移_证照标识")
    private String dzqydzzzbz;

    @Schema(description = "电子迁移_治安管理电子证照编号")
    private String dzqyzagldzzzbh;

    @Schema(description = "准迁证编号")
    private String zqzbh;

    @Schema(description = "电子准迁_证照标识")
    private String dzzqdzzzbz;

    @Schema(description = "电子准迁_治安管理电子证照编号")
    private String dzzqzagldzzzbh;

    @Schema(description = "备注")
    private String bz;

    @Dict(CkDictTypeConstants.DM_JTGX)
    @Schema(description = "与持证人关系家庭关系")
    private String yczrgxjtgxdm;

    @Schema(description = "公民身份号码")
    private String gmsfhm;

    @Schema(description = "姓名")
    private String xm;

    @Schema(description = "曾用名")
    private String cym;

    @Dict(CkDictTypeConstants.DM_XB)
    @Schema(description = "性别")
    private String xbdm;

    @Dict(CkDictTypeConstants.DM_MZ)
    @Schema(description = "民族")
    private String mzdm;

    @StringDateFormat
    @Schema(description = "出生日期")
    private String csrq;

    @Dict(CkDictTypeConstants.GJ)
    @Schema(description = "出生地_国家（地区）")
    private String csdgjhdqdm;

    @DictSsxq
    @Schema(description = "出生地_省市县区")
    private String csdssxqdm;

    @Schema(description = "出生地_区划内详细地址")
    private String csdqhnxxdz;

    @Dict(CkDictTypeConstants.GJ)
    @Schema(description = "籍贯_国家（地区）")
    private String jggjhdqdm;

    @DictSsxq
    @Schema(description = "籍贯_省市县区")
    private String jgssxqdm;

    @Schema(description = "籍贯_区划内详细地址")
    private String jgqhnxxdz;

    @Dict(CkDictTypeConstants.DM_WHCD)
    @Schema(description = "文化程度")
    private String xldm;

    @Dict(CkDictTypeConstants.DM_HYZK)
    @Schema(description = "婚姻状况")
    private String hyzkdm;

    @Schema(description = "职业")
    private String zy;

    @Dict(CkDictTypeConstants.DM_QYLDYY)
    @Schema(description = "迁移（流动）原因")
    private String qyldyydm;

    @Schema(description = "户籍地_公安机关机构代码")
    private String hjdgajgjgdm;

    @Schema(description = "户籍地_公安机关名称")
    private String hjdgajgmc;

    @Schema(description = "户籍地_联系电话")
    private String hjdlxdh;

    @Schema(description = "受理地数据归属单位代码")
    private String hjdsjgsdwdm;

    @Schema(description = "受理地数据归属单位名称")
    private String hjdsjgsdwmc;

    @Schema(description = "办理人姓名")
    private String blrxm;

    @StringDateTimeFormat
    @Schema(description = "办理时间")
    private String blsj;

    @Dict(CkDictTypeConstants.DM_QYFW)
    @Schema(description = "区域范围代码")
    private String qyfwdm;

    @Schema(description = "发送单位数据归属单位代码")
    private String fsdwsjgsdwdm;

    @Schema(description = "发送单位数据归属单位名称")
    private String fsdwsjgsdwmc;

    @Schema(description = "接收单位数据归属单位代码")
    private String jsdwsjgsdwdm;

    @Schema(description = "接收单位数据归属单位名称")
    private String jsdwsjgsdwmc;

    @StringDateTimeFormat
    @Schema(description = "入库时间")
    private String rksj;
}
