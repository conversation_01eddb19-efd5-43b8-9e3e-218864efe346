package com.zjjcnt.project.ck.base.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 办证收费流水表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.base.dto.ZjtBzsflsbDTO
 */
@Crypto
@Data
@Table("zjt_bzsflsb")
public class ZjtBzsflsbDO implements IdEntity<String> {

    @Serial
    private static final long serialVersionUID = 4390392135918299859L;

    /**
     *
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String sflsid;

    /**
     * 内部受理ID
     */
    @Column(value = "nbslid")
    private String nbslid;

    /**
     * 业务受理号
     */
    @Column(value = "ywslh")
    private String ywslh;

    /**
     * 身份证受理号
     */
    @Column(value = "slh")
    private String slh;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 人员内部id
     */
    @Column(value = "rynbid")
    private String rynbid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 申领原因
     */
    @Column(value = "slyy")
    private String slyy;

    /**
     * 受理状态
     */
    @Column(value = "slzt")
    private String slzt;

    /**
     * 收费金额
     */
    @Column(value = "sfje")
    private BigDecimal sfje;

    /**
     * 签发机关
     */
    @Column(value = "qfjg")
    private String qfjg;

    /**
     * 有效期限起始日期
     */
    @Column(value = "yxqxqsrq")
    private String yxqxqsrq;

    /**
     * 有效期限截止日期
     */
    @Column(value = "yxqxjzrq")
    private String yxqxjzrq;

    /**
     *
     */
    @Column(value = "sflx")
    private String sflx;

    /**
     * 受理方式
     */
    @Column(value = "slfs")
    private String slfs;

    /**
     * 数据归属单位代码
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     * 省市县（区）
     */
    @Column(value = "ssxq")
    private String ssxq;

    /**
     * 数据归属单位名称
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     * 数据归属单位代码
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;

    /**
     *
     */
    @Column(value = "sldfjsjgsdwdm")
    private String sldfjsjgsdwdm;

    /**
     *
     */
    @Column(value = "sldfjsjgsdwmc")
    private String sldfjsjgsdwmc;

    /**
     * 操作时间
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private String czyid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     * 单位代码
     */
    @Column(value = "dwdm")
    private String dwdm;

    /**
     * 操作员单位名称
     */
    @Column(value = "czydwmc")
    private String czydwmc;

    /**
     *
     */
    @Column(value = "sfdjh")
    private String sfdjh;

    /**
     *
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 总分成金额
     */
    @Column(value = "zfcje")
    private BigDecimal zfcje;

    /**
     * 区县分成金额
     */
    @Column(value = "qxfcje")
    private BigDecimal qxfcje;

    /**
     * 地市分成金额
     */
    @Column(value = "dsfcje")
    private BigDecimal dsfcje;

    /**
     * 中心分成金额
     */
    @Column(value = "zxfcje")
    private BigDecimal zxfcje;


    @Override
    public String getId() {
        return this.sflsid;
    }

    @Override
    public void setId(String id) {
        this.sflsid = id;
    }
}
