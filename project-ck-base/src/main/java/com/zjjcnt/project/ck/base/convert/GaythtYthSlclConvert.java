package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.GaythtYthSlclDTO;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclCreateReq;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclPageReq;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclPageResp;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclViewResp;
import com.zjjcnt.project.ck.base.entity.GaythtYthSlclDO;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclItemResp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* Convert
*
* <AUTHOR>
* @date 2025-07-25 16:34:24
*/
@Mapper
public interface GaythtYthSlclConvert {

    GaythtYthSlclConvert INSTANCE = Mappers.getMapper(GaythtYthSlclConvert.class);

    GaythtYthSlclDTO convert(GaythtYthSlclDO entity);

    @InheritInverseConfiguration(name="convert")
    GaythtYthSlclDO convertToDO(GaythtYthSlclDTO dto);

    GaythtYthSlclDTO convertToDTO(GaythtYthSlclPageReq req);

    GaythtYthSlclDTO convertToDTO(GaythtYthSlclCreateReq req);

    GaythtYthSlclDTO convertToDTO(GaythtYthSlclUpdateReq req);

    GaythtYthSlclPageResp convertToPageResp(GaythtYthSlclDTO dto);

    GaythtYthSlclViewResp convertToViewResp(GaythtYthSlclDTO dto);

    GaythtYthSlclCreateResp convertToCreateResp(GaythtYthSlclDTO dto);

    List<GaythtYthSlclItemResp> convertToItemResp(List<GaythtYthSlclDTO> list);
}