package com.zjjcnt.project.ck.base.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.Data;

import com.zjjcnt.common.core.entity.IdEntity;
import com.zjjcnt.common.core.annotation.Crypto;

import java.math.BigDecimal;
import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import java.lang.String;

/**
 * 一体化审批材料数据DO
 *
 * <AUTHOR>
 * @date 2025-07-25 16:34:24
 * @see com.zjjcnt.project.ck.base.dto.GaythtYthSlclsjDTO
 */
@Data
@Table("gaytht_yth_slclsj")
public class GaythtYthSlclsjDO implements IdEntity<String> {
    private static final long serialVersionUID = -276878200824901687L;

    /**
     * 材料数据ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String slclsjid;

    /**
     * 唯一标识
     */
    @Column(value = "unid")
    private String unid;

    /**
     * 附件名称
     */
    @Column(value = "filename")
    private String filename;

    /**
     * 附件存储路径
     */
    @Column(value = "fileurl")
    private String fileurl;

    /**
     * 文件存放类别
     */
    @Column(value = "wjcflb")
    private String wjcflb;

    /**
     * 文件大小
     */
    @Column(value = "wjdx")
    private BigDecimal wjdx;

    /**
     * 文件类型
     */
    @Column(value = "wjlx")
    private String wjlx;

    /**
     * 文件实际大小
     */
    @Column(value = "wjsjdz")
    private String wjsjdz;

    /**
     * 哈希值
     */
    @Column(value = "hash")
    private String hash;

    /**
     * 文件数据
     */
    @Column(value = "wjdata")
    private String wjdata;

    /**
     * 创建人
     */
    @Column(value = "cjr")
    private String cjr;

    /**
     * 创建人IP
     */
    @Column(value = "cjrip")
    private String cjrip;

    /**
     * 创建时间
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     * 修改人
     */
    @Column(value = "xgr")
    private String xgr;

    /**
     * 修改人IP
     */
    @Column(value = "xgrip")
    private String xgrip;

    /**
     * 修改时间
     */
    @Column(value = "xgsj")
    private String xgsj;

    /**
     * 有效标志
     */
    @Column(value = "yxbz")
    private String yxbz;

    /**
     * 备用字段
     */
    @Column(value = "extend")
    private String extend;

    /**
     * 备用字段2
     */
    @Column(value = "extend2")
    private String extend2;

    /**
     * 备用字段3
     */
    @Column(value = "extend3")
    private String extend3;

    /**
     * 备用字段4
     */
    @Column(value = "extend4")
    private String extend4;

    /**
     * 备用字段5
     */
    @Column(value = "extend5")
    private String extend5;


    @Override
    public String getId() {
        return this.slclsjid;
    }

    @Override
    public void setId(String id) {
        this.slclsjid = id;
    }
}
