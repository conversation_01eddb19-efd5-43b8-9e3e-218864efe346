package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 身份证业务操作表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-06 10:19:05
 */
@Data
@Schema(description = "身份证业务操作表响应对象")
public class ZjtSfzywczbViewResp {

    @Schema(description = "证件业务ID")
    private String zjywid;

    @Schema(description = "业务受理号")
    private String ywslh;

    @Schema(description = "受理号")
    private String slh;

    @Schema(description = "业务标志")
    private String ywbz;

    @Dict(CkDictTypeConstants.DM_EDZSLZT)
    @Schema(description = "受理状态")
    private String slzt;

    @Schema(description = "操作员ID")
    private String czyid;

    @Schema(description = "操作员姓名")
    private String czyxm;

    @StringDateTimeFormat
    @Schema(description = "受理时间")
    private String czsj;

    @Schema(description = "操作IP")
    private String czip;

    @Schema(description = "操作员单位代码")
    private String czydwdm;

    @Schema(description = "操作员单位名称")
    private String czydwmc;

    @Schema(description = "备注")
    private String bz;
}
