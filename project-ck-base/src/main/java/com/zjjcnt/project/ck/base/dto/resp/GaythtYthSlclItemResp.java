package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.project.ck.base.dto.GaythtYthSlclsjDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * GaythtYthSlclItemResp
 *
 * <AUTHOR>
 * @date 2025-07-31 08:36:35
 */
@Data
@Schema(description = "响应对象")
public class GaythtYthSlclItemResp {

    @Schema(description = "唯一标识")
    private String unid;

    @Schema(description = "申报号")
    private String projid;

    @Schema(description = "材料名称")
    private String attrname;

    @Schema(description = "材料序号")
    private BigDecimal sortid;

    @Schema(description = "收取方式")
    private String taketype;

    @Dict("istake")
    @Schema(description = "是否收取")
    private String istake;

    @Schema(description = "收取时间")
    private String taketime;

    @Schema(description = "备注")
    private String memo;

    @Schema(description = "所属系统")
    private String belongsystem;

    @Schema(description = "部门行政区划编码")
    private String areacode;

    @Schema(description = "版本号")
    private Integer dataversion;

    @Dict("syncStatus")
    @Schema(description = "同步状态")
    private String syncStatus;

    @Schema(description = "数据产生时间")
    private String createTime;

    @Schema(description = "")
    private String attrid;

    @Schema(description = "备用字段")
    private String extend;

    @Schema(description = "备用字段2")
    private String extend2;

    @Schema(description = "备用字段3")
    private String extend3;

    @Schema(description = "备用字段4")
    private String extend4;

    @Schema(description = "备用字段5")
    private String extend5;

    @Schema(description = "材料数据列表")
    private List<GaythtYthSlclsjDTO> gaythtYthSlclsjList;

    @Schema(description = "受理材料数量")
    private Integer slclsjsl;
}
