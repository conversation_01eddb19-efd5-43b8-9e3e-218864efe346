package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.ZjtZwcjlsbConvert;
import com.zjjcnt.project.ck.base.dto.ZjtZwcjlsbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtZwcjlsbCreateReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtZwcjlsbCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtZwcjlsbViewResp;
import com.zjjcnt.project.ck.base.service.ZjtZwcjlsbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;

/**
* 指纹采集临时表前端控制器
*
* <AUTHOR>
* @date 2024-05-09 11:39:12
*/
@RequiredArgsConstructor
@Tag(name = "指纹采集临时表")
@RestController
@RequestMapping("/zjtZwcjlsb")
public class ZjtZwcjlsbController extends AbstractCrudController<ZjtZwcjlsbDTO> {

    private final ZjtZwcjlsbService zjtZwcjlsbService;

    @Override
    protected IBaseService<ZjtZwcjlsbDTO> getService() {
        return zjtZwcjlsbService;
    }

    @PostMapping("create")
    @Operation(summary = "新增 F23920")
    public CommonResult<ZjtZwcjlsbCreateResp> create(@RequestBody ZjtZwcjlsbCreateReq req) {
        if (StringUtils.isNotBlank(req.getBase64zwytxsj())){
            req.setZwytxsj(Base64.getDecoder().decode(req.getBase64zwytxsj()));
        }
        if (StringUtils.isNotBlank(req.getBase64zwyzwtzsj())){
            req.setZwyzwtzsj(Base64.getDecoder().decode(req.getBase64zwyzwtzsj()));
        }
        if (StringUtils.isNotBlank(req.getBase64zwetxsj())){
            req.setZwetxsj(Base64.getDecoder().decode(req.getBase64zwetxsj()));
        }
        if (StringUtils.isNotBlank(req.getBase64zwezwtzsj())){
            req.setZwezwtzsj(Base64.getDecoder().decode(req.getBase64zwezwtzsj()));
        }
        return super.create(req, ZjtZwcjlsbConvert.INSTANCE::convertToDTO, ZjtZwcjlsbConvert.INSTANCE::convertToCreateResp);
    }

    @GetMapping("lastValidZw")
    @Operation(summary = "查询最近的有效指纹")
    public CommonResult<ZjtZwcjlsbViewResp> getLastValidZw(@RequestParam(value = "gmsfhm") String gmsfhm) {
        ZjtZwcjlsbDTO zjtZwcjlsbDTO = zjtZwcjlsbService.getLastValidZw(gmsfhm);
        return CommonResult.success(ZjtZwcjlsbConvert.INSTANCE.convertToViewResp(zjtZwcjlsbDTO));
    }

}
