package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.KsxtQcfkxxbConvert;
import com.zjjcnt.project.ck.base.dto.KsxtQcfkxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtQcfkxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtQcfkxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtQcfkxxbViewResp;
import com.zjjcnt.project.ck.base.service.KsxtQcfkxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 跨省协同迁出反馈信息表前端控制器
*
* <AUTHOR>
* @date 2025-08-05 13:59:15
*/
@RequiredArgsConstructor
@Tag(name = "跨省协同迁出反馈信息表")
@RestController
@RequestMapping("/ksxtQcfkxxb")
public class KsxtQcfkxxbController extends AbstractCrudController<KsxtQcfkxxbDTO> {

    private final KsxtQcfkxxbService ksxtQcfkxxbService;

    @Override
    protected IBaseService<KsxtQcfkxxbDTO> getService() {
        return ksxtQcfkxxbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询跨省协同迁出反馈信息表")
    public CommonResult<PageResult<KsxtQcfkxxbPageResp>> page(KsxtQcfkxxbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, KsxtQcfkxxbConvert.INSTANCE::convertToDTO, KsxtQcfkxxbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看跨省协同迁出反馈信息表详情")
    public CommonResult<KsxtQcfkxxbViewResp> view(String id) {
        return super.view(id, KsxtQcfkxxbConvert.INSTANCE::convertToViewResp);
    }

}
