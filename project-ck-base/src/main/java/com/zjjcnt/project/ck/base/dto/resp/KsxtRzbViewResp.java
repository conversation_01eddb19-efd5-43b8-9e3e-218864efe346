package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 跨省协同日志表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 */
@Data
@Schema(description = "跨省协同日志表响应对象")
public class KsxtRzbViewResp {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "跨省协同号")
    private String ksxtid;

    @Schema(description = "治安管理业务协同编号")
    private String zaglywxtbh;

    @Dict(CkDictTypeConstants.DM_GABKSYWBLZT)
    @Schema(description = "业务类型")
    private String blzt;

    @StringDateTimeFormat
    @Schema(description = "入库时间")
    private String czsj;

    @Schema(description = "操作员ID")
    private String czyid;

    @Schema(description = "操作员姓名")
    private String czyxm;

    @Schema(description = "操作IP")
    private String czip;

    @Schema(description = "操作员单位代码")
    private String czydwdm;

    @Schema(description = "操作员单位名称")
    private String czydwmc;

    @Schema(description = "备注")
    private String bz;
}
