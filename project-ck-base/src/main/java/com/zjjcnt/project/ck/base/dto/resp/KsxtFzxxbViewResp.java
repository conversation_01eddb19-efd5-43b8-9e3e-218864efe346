package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 跨省协同辅助信息表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-05 13:49:11
 */
@Data
@Schema(description = "跨省协同辅助信息表响应对象")
public class KsxtFzxxbViewResp {

    @Schema(description = "跨省协同辅助信息ID")
    private String ksxtfzxxid;

    @Schema(description = "业务表名")
    private String ywbm;

    @Schema(description = "跨省协同ID")
    private String ksxtid;

    @Schema(description = "治安管理业务协同编号")
    private String zaglywxtbh;

    @Schema(description = "治安管理业务类别代码")
    private String zaglywlbdm;

    @Schema(description = "标题")
    private String bt;

    @Dict(CkDictTypeConstants.DM_KSTBXTHJDM)
    @Schema(description = "跨省通办协同环节代码")
    private String kstbxthjdm;

    @Dict(CkDictTypeConstants.DM_KSTBXXLXDM)
    @Schema(description = "跨省通办消息类型代码")
    private String kstbxxlxdm;

    @Schema(description = "简要情况")
    private String jyqk;

    @Dict(CkDictTypeConstants.DM_KSTBXTXXCLBZ)
    @Schema(description = "处理标志")
    private String clbz;

    @Schema(description = "操作员ID")
    private String czyid;

    @Schema(description = "操作员姓名")
    private String czyxm;

    @Schema(description = "操作员IP")
    private String czyip;

    @Dict(CkDictTypeConstants.DM_QYFW)
    @Schema(description = "区域范围代码")
    private String qyfwdm;

    @Schema(description = "发送单位_数据归属单位代码")
    private String fsdwsjgsdwdm;

    @Schema(description = "发送单位_数据归属单位名称")
    private String fsdwsjgsdwmc;

    @Schema(description = "接收_数据归属单位代码")
    private String jsdwsjgsdwdm;

    @Schema(description = "接收单位_数据归属单位名称")
    private String jsdwsjgsdwmc;

    @StringDateTimeFormat
    @Schema(description = "入库时间")
    private String rksj;

    @Schema(description = "阅读人姓名")
    private String ydrxm;

    @StringDateTimeFormat
    @Schema(description = "阅读时间")
    private String ydsj;
}
