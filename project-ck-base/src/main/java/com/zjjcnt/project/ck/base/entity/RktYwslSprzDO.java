package com.zjjcnt.project.ck.base.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.Data;

import com.zjjcnt.common.core.entity.IdEntity;
import com.zjjcnt.common.core.annotation.Crypto;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import java.lang.String;

/**
 * 业务受理审批日志DO
 *
 * <AUTHOR>
 * @date 2025-07-21 09:14:48
 * @see com.zjjcnt.project.ck.base.dto.RktYwslSprzDTO
 */
@Crypto
@Data
@Table("rkt_ywsl_sprz")
public class RktYwslSprzDO implements IdEntity<String> {
    private static final long serialVersionUID = -4051849346557848400L;

    /**
     * 
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String nbbh;

    /**
     * 
     */
    @Column(value = "ywslh")
    private String ywslh;

    /**
     * 
     */
    @Column(value = "blzt")
    private String blzt;

    /**
     * 
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private String czyid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     * 操作IP
     */
    @Column(value = "czip")
    private String czip;

    /**
     * 
     */
    @Column(value = "czydwmc")
    private String czydwmc;

    /**
     * 
     */
    @Column(value = "czydwdm")
    private String czydwdm;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 
     */
    @Column(value = "spjgdm")
    private String spjgdm;

    /**
     * 操作员联系电话
     */
    @Crypto
    @Column(value = "czylxdh")
    private String czylxdh;


    @Override
    public String getId() {
        return this.nbbh;
    }

    @Override
    public void setId(String id) {
        this.nbbh = id;
    }
}
