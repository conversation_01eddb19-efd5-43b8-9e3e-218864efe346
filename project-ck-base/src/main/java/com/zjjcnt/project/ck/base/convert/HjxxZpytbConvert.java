package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxZpytbDTO;
import com.zjjcnt.project.ck.base.dto.resp.HjxxZpytbViewResp;
import com.zjjcnt.project.ck.base.entity.HjxxZpytbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 照片原图表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface HjxxZpytbConvert {

    HjxxZpytbConvert INSTANCE = Mappers.getMapper(HjxxZpytbConvert.class);

    HjxxZpytbDTO convert(HjxxZpytbDO entity);

    @InheritInverseConfiguration(name="convert")
    HjxxZpytbDO convertToDO(HjxxZpytbDTO dto);

    HjxxZpytbViewResp convertToViewResp(HjxxZpytbDTO dto);

}
