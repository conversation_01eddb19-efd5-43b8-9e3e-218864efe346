package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.ZjtZwcjbConvert;
import com.zjjcnt.project.ck.base.dto.ZjtZwcjbDTO;
import com.zjjcnt.project.ck.base.dto.resp.ZjtZwcjbViewResp;
import com.zjjcnt.project.ck.base.service.ZjtZwcjbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 指纹采集表前端控制器
*
* <AUTHOR>
* @date 2025-08-06 10:45:36
*/
@RequiredArgsConstructor
@Tag(name = "指纹采集表")
@RestController
@RequestMapping("/zjtZwcjb")
public class ZjtZwcjbController extends AbstractCrudController<ZjtZwcjbDTO> {

    private final ZjtZwcjbService zjtZwcjbService;

    @Override
    protected IBaseService<ZjtZwcjbDTO> getService() {
        return zjtZwcjbService;
    }

    @GetMapping("view")
    @Operation(summary = "查看指纹采集表详情")
    public CommonResult<ZjtZwcjbViewResp> view(String id) {
        return super.view(id, ZjtZwcjbConvert.INSTANCE::convertToViewResp);
    }

}
