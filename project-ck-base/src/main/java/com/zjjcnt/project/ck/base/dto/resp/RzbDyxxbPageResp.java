package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 打印信息表响应对象
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Data
@Schema(description = "打印信息表响应对象")
public class RzbDyxxbPageResp {

    @Schema(description = "打印ID")
    private String dyid;

    @Schema(description = "人员ID")
    private String ryid;

    @Schema(description = "姓名")
    private String xm;

    @Schema(description = "公民身份号码")
    private String gmsfhm;

    @Dict(CkDictTypeConstants.DM_DYLB)
    @Schema(description = "打印类别")
    private String dylb;

    @Schema(description = "证件编号")
    private String zjbh;

    @Schema(description = "印制年份")
    private String yznf;

    @Schema(description = "材料名称")
    private String clmc;

    @Dict(CkDictTypeConstants.YWBM)
    @Schema(description = "业务类型")
    private String lcywlx;

    @StringDateTimeFormat
    @Schema(description = "操作时间")
    private String czsj;

    @Schema(description = "操作人ID")
    private String czrid;

    @Schema(description = "操作员姓名")
    private String czyxm;

    @Schema(description = "操作员单位代码")
    private String czydwdm;

    @Schema(description = "操作员单位名称")
    private String czydwmc;

    @Schema(description = "操作ip")
    private String czip;

    @Schema(description = "数据归属单位代码")
    private String hjdsjgsdwdm;

    @Schema(description = "数据归属单位名称")
    private String hjdsjgsdwmc;

    @Schema(description = "数据归属单位代码")
    private String sldsjgsdwdm;

    @Schema(description = "数据归属单位名称")
    private String sldsjgsdwmc;

    @Schema(description = "业务受理号")
    private String ywslh;

    @Schema(description = "受理材料编号")
    private String slclbh;
}
