package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtSfzywczbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtSfzywczbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSfzywczbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSfzywczbViewResp;
import com.zjjcnt.project.ck.base.entity.ZjtSfzywczbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 身份证业务操作表Convert
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Mapper
public interface ZjtSfzywczbConvert {

    ZjtSfzywczbConvert INSTANCE = Mappers.getMapper(ZjtSfzywczbConvert.class);

    ZjtSfzywczbDTO convert(ZjtSfzywczbDO entity);

    @InheritInverseConfiguration(name = "convert")
    ZjtSfzywczbDO convertToDO(ZjtSfzywczbDTO dto);

    ZjtSfzywczbDTO convertToDTO(ZjtSfzywczbPageReq req);

    ZjtSfzywczbPageResp convertToPageResp(ZjtSfzywczbDTO dto);

    ZjtSfzywczbViewResp convertToViewResp(ZjtSfzywczbDTO dto);

}
