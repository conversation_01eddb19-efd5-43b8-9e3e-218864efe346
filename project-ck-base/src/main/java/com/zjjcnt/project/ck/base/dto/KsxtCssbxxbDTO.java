package com.zjjcnt.project.ck.base.dto;

import com.zjjcnt.common.core.dto.BaseDTO;
import lombok.Data;

import java.io.Serial;

/**
 * 跨省协同出生申报信息表DTO
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 * @see com.zjjcnt.project.ck.base.entity.KsxtCssbxxbDO
 */
@Data
public class KsxtCssbxxbDTO implements BaseDTO<String> {
    @Serial
    private static final long serialVersionUID = -910584082900686247L;

    /**
     * 跨省出生业务ID
     */
    private String ksxtcsid;

    /**
     * 跨省业务ID
     */
    private String ksxtid;

    /**
     * 治安管理业务协同编号
     */
    private String zaglywxtbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 姓
     */
    private String x;

    /**
     * 名
     */
    private String m;

    /**
     * 性别
     */
    private String xbdm;

    /**
     * 民族
     */
    private String mzdm;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 出生时间
     */
    private String cssj;

    /**
     * 出生地_国家（地区）
     */
    private String csdgjhdqdm;

    /**
     * 出生地_省市县（区）
     */
    private String csdssxqdm;

    /**
     * 出生地_区划内详细地址
     */
    private String csdqhnxxdz;

    /**
     * 籍贯_国家（地区）
     */
    private String jggjhdqdm;

    /**
     * 籍贯_省市县（区）
     */
    private String jgssxqdm;

    /**
     * 籍贯_区划内详细地址
     */
    private String jgqhnxxdz;

    /**
     * 血型
     */
    private String xxdm;

    /**
     * 人口信息级别
     */
    private String rkxxjbdm;

    /**
     * 出生登记类别
     */
    private String csdjlbdm;

    /**
     * 出生证明编号
     */
    private String cszmbh;

    /**
     * 与户主关系
     */
    private String yhzgxdm;

    /**
     * 户主_姓名
     */
    private String hzxm;

    /**
     * 户主_公民身份号码
     */
    private String hzgmsfhm;

    /**
     * 父亲_公民身份号码
     */
    private String fqgmsfhm;

    /**
     * 父亲_姓名
     */
    private String fqxm;

    /**
     * 父亲_证件种类
     */
    private String fqcyzjdm;

    /**
     * 父亲_证件号码
     */
    private String fqzjhm;

    /**
     * 母亲_公民身份号码
     */
    private String mqgmsfhm;

    /**
     * 母亲_姓名
     */
    private String mqxm;

    /**
     * 母亲_证件种类
     */
    private String mqcyzjdm;

    /**
     * 母亲_证件号码
     */
    private String mqzjhm;

    /**
     * 监护人一_公民身份号码
     */
    private String jhrygmsfhm;

    /**
     * 监护人一_姓名
     */
    private String jhryxm;

    /**
     * 监护人一_证件种类
     */
    private String jhrycyzjdm;

    /**
     * 监护人一_证件号码
     */
    private String jhryzjhm;

    /**
     * 监护人一_外文姓
     */
    private String jhrywwx;

    /**
     * 监护人一_外文名
     */
    private String jhrywwm;

    /**
     * 监护人一_监护关系
     */
    private String jhryjhgxdm;

    /**
     * 监护人一_联系电话
     */
    private String jhrylxdh;

    /**
     * 监护人二_公民身份号码
     */
    private String jhregmsfhm;

    /**
     * 监护人二_姓名
     */
    private String jhrexm;

    /**
     * 监护人二_证件种类
     */
    private String jhrecyzjdm;

    /**
     * 监护人二_证件号码
     */
    private String jhrezjhm;

    /**
     * 监护人二_外文姓
     */
    private String jhrewwx;

    /**
     * 监护人二_外文名
     */
    private String jhrewwm;

    /**
     * 监护人二_监护关系
     */
    private String jhrejhgxdm;

    /**
     * 监护人二_联系电话
     */
    private String jhrelxdh;

    /**
     * 申报人_公民身份号码
     */
    private String sbrgmsfhm;

    /**
     * 申报人_姓名
     */
    private String sbrxm;

    /**
     * 申报人_联系电话
     */
    private String sbrlxdh;

    /**
     * 申报人_家庭关系
     */
    private String sbrycsrgxjtgxdm;

    /**
     * 现居住地址_省市县（区）
     */
    private String xzzssxqdm;

    /**
     * 现居住地址_区划内详细地址
     */
    private String xzzqhnxxdz;

    /**
     * 受理地_公安机关机构代码
     */
    private String sldgajgjgdm;

    /**
     * 受理地_公安机关名称
     */
    private String sldgajgmc;

    /**
     * 受理地_联系电话
     */
    private String sldlxdh;

    /**
     * 受理人ID
     */
    private String slrid;

    /**
     * 受理人_姓名
     */
    private String slrxm;

    /**
     * 受理时间
     */
    private String slsj;

    /**
     * 治安管理政务服务事项编码
     */
    private String zaglzwfwsxbm;

    /**
     * 治安管理业务类别代码
     */
    private String zaglywlbdm;

    /**
     * 归档配置版本号
     */
    private String gdpzbbh;

    /**
     * 业务协同区域范围代码
     */
    private String qyfwdm;

    /**
     * 受理地_数据归属单位代码
     */
    private String sldsjgsdwdm;

    /**
     * 受理地_数据归属单位名称
     */
    private String sldsjgsdwmc;

    /**
     * 户籍地_数据归属单位代码
     */
    private String hjdsjgsdwdm;

    /**
     * 户籍地_数据归属单位名称
     */
    private String hjdsjgsdwmc;

    /**
     * 入库时间
     */
    private String rksj;

    /**
     * 判断标志是否落户
     */
    private String pdbzsflh;

    /**
     * 判断标志出生证明黑名单
     */
    private String pdbzcszmhmd;

    /**
     * 比对结果
     */
    private String bdjg;

    /**
     * 比对描述
     */
    private String bdms;

    /**
     * 比对时间
     */
    private String bdsj;

    /**
     * 比对人ID
     */
    private String bdrid;

    /**
     * 比对人姓名
     */
    private String bdrxm;

    /**
     * 比对人IP
     */
    private String bdrip;

    // ------ 非数据库表字段 -------
    /**
     * 大于等于出生日期
     */
    private String csrqStart;

    /**
     * 小于等于出生日期
     */
    private String csrqEnd;

    /**
     * 大于等于受理时间
     */
    private String slsjStart;

    /**
     * 小于等于受理时间
     */
    private String slsjEnd;

    /**
     * 大于等于入库时间
     */
    private String rksjStart;

    /**
     * 小于等于入库时间
     */
    private String rksjEnd;

    /**
     * 大于等于比对时间
     */
    private String bdsjStart;

    /**
     * 小于等于比对时间
     */
    private String bdsjEnd;


    @Override
    public String getId() {
        return this.ksxtcsid;
    }

    @Override
    public void setId(String id) {
        this.ksxtcsid = id;
    }
}
