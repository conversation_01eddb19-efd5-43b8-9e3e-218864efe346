package com.zjjcnt.project.ck.base.dto;

import com.zjjcnt.common.core.dto.BaseDTO;
import lombok.Data;

import java.io.Serial;

/**
 * 跨省协同迁出反馈信息表DTO
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 * @see com.zjjcnt.project.ck.base.entity.KsxtQcfkxxbDO
 */
@Data
public class KsxtQcfkxxbDTO implements BaseDTO<String> {
    @Serial
    private static final long serialVersionUID = -5563241565481587723L;

    /**
     * 跨省协同迁出反馈ID
     */
    private String ksxtqcfkid;

    /**
     * 跨省协同ID
     */
    private String ksxtid;

    /**
     * 治安管理业务协同编号
     */
    private String zaglywxtbh;

    /**
     * 持证人公民身份号码
     */
    private String czrgmsfhm;

    /**
     * 持证人姓名
     */
    private String czrxm;

    /**
     * 原地址住址省市县（区）
     */
    private String yzzssxqdm;

    /**
     * 原住址住址区划内详细地址
     */
    private String yzzqhnxxdz;

    /**
     * 原住址城乡分类代码
     */
    private String yzzcxfldm;

    /**
     * 去往地_省市县（区）
     */
    private String qwdssxqdm;

    /**
     * 去往地_区划内详细地址
     */
    private String qwdqhnxxdz;

    /**
     * 去往地_户口登记机关公安机关机构代码
     */
    private String qwdhkdjjggajgjgdm;

    /**
     * 去往地_户口登记机关公安机关名称
     */
    private String qwdhkdjjggajgmc;

    /**
     * 迁移证编号
     */
    private String qyzbh;

    /**
     * 迁移证_签发机关_公安机关机构代码
     */
    private String qyzqfjggajgjgdm;

    /**
     * 迁移证_签发机关_公安机关名称
     */
    private String qyzqfjggajgmc;

    /**
     * 迁移证_签发日期
     */
    private String qyzqfrq;

    /**
     * 迁移证_有效期截止日期
     */
    private String qyzyxqjzrq;

    /**
     * 电子迁移_证照标识
     */
    private String dzqydzzzbz;

    /**
     * 电子迁移_治安管理电子证照编号
     */
    private String dzqyzagldzzzbh;

    /**
     * 准迁证编号
     */
    private String zqzbh;

    /**
     * 电子准迁_证照标识
     */
    private String dzzqdzzzbz;

    /**
     * 电子准迁_治安管理电子证照编号
     */
    private String dzzqzagldzzzbh;

    /**
     * 备注
     */
    private String bz;

    /**
     * 与持证人关系家庭关系
     */
    private String yczrgxjtgxdm;

    /**
     * 公民身份号码
     */
    private String gmsfhm;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 曾用名
     */
    private String cym;

    /**
     * 性别
     */
    private String xbdm;

    /**
     * 民族
     */
    private String mzdm;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 出生地_国家（地区）
     */
    private String csdgjhdqdm;

    /**
     * 出生地_省市县区
     */
    private String csdssxqdm;

    /**
     * 出生地_区划内详细地址
     */
    private String csdqhnxxdz;

    /**
     * 籍贯_国家（地区）
     */
    private String jggjhdqdm;

    /**
     * 籍贯_省市县区
     */
    private String jgssxqdm;

    /**
     * 籍贯_区划内详细地址
     */
    private String jgqhnxxdz;

    /**
     * 文化程度
     */
    private String xldm;

    /**
     * 婚姻状况
     */
    private String hyzkdm;

    /**
     * 职业
     */
    private String zy;

    /**
     * 迁移（流动）原因
     */
    private String qyldyydm;

    /**
     * 户籍地_公安机关机构代码
     */
    private String hjdgajgjgdm;

    /**
     * 户籍地_公安机关名称
     */
    private String hjdgajgmc;

    /**
     * 户籍地_联系电话
     */
    private String hjdlxdh;

    /**
     * 受理地数据归属单位代码
     */
    private String hjdsjgsdwdm;

    /**
     * 受理地数据归属单位名称
     */
    private String hjdsjgsdwmc;

    /**
     * 办理人姓名
     */
    private String blrxm;

    /**
     * 办理时间
     */
    private String blsj;

    /**
     * 区域范围代码
     */
    private String qyfwdm;

    /**
     * 发送单位数据归属单位代码
     */
    private String fsdwsjgsdwdm;

    /**
     * 发送单位数据归属单位名称
     */
    private String fsdwsjgsdwmc;

    /**
     * 接收单位数据归属单位代码
     */
    private String jsdwsjgsdwdm;

    /**
     * 接收单位数据归属单位名称
     */
    private String jsdwsjgsdwmc;

    /**
     * 入库时间
     */
    private String rksj;

    // ------ 非数据库表字段 -------
    /**
     * 大于等于迁移证_签发日期
     */
    private String qyzqfrqStart;

    /**
     * 小于等于迁移证_签发日期
     */
    private String qyzqfrqEnd;

    /**
     * 大于等于迁移证_有效期截止日期
     */
    private String qyzyxqjzrqStart;

    /**
     * 小于等于迁移证_有效期截止日期
     */
    private String qyzyxqjzrqEnd;

    /**
     * 大于等于出生日期
     */
    private String csrqStart;

    /**
     * 小于等于出生日期
     */
    private String csrqEnd;

    /**
     * 大于等于办理时间
     */
    private String blsjStart;

    /**
     * 小于等于办理时间
     */
    private String blsjEnd;

    /**
     * 大于等于入库时间
     */
    private String rksjStart;

    /**
     * 小于等于入库时间
     */
    private String rksjEnd;


    @Override
    public String getId() {
        return this.ksxtqcfkid;
    }

    @Override
    public void setId(String id) {
        this.ksxtqcfkid = id;
    }
}
