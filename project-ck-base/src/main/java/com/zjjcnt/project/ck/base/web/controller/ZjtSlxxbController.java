package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.base.convert.ZjtSlxxbConvert;
import com.zjjcnt.project.ck.base.domain.ZjtSlxxbAuditDTO;
import com.zjjcnt.project.ck.base.dto.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.*;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSlxxbCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSlxxbLtzfjsylResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSlxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSlxxbViewResp;
import com.zjjcnt.project.ck.base.manager.ZjtSlxxbZjCsManager;
import com.zjjcnt.project.ck.base.manager.ZjtSlxxbZjQfManager;
import com.zjjcnt.project.ck.base.manager.ZjtSlxxbZjshManager;
import com.zjjcnt.project.ck.base.service.ZjtSlxxbService;
import com.zjjcnt.project.ck.core.util.DwUtils;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.sysadmin.constant.CkConstants;
import com.zjjcnt.project.ck.sysadmin.constant.ZjConstant;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 居民身份证受理信息表前端控制器
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@RequiredArgsConstructor
@Tag(name = "居民身份证受理信息表")
@RestController
@RequestMapping("/zjtSlxxb")
public class ZjtSlxxbController extends AbstractCrudController<ZjtSlxxbDTO> {

    private final ZjtSlxxbService zjtSlxxbService;
    private final ZjtSlxxbZjshManager zjtSlxxbZjshManager;
    private final ZjtSlxxbZjQfManager zjtSlxxbZjQfManager;
    private final ZjtSlxxbZjCsManager zjtSlxxbZjCsManager;

    @Override
    protected IBaseService<ZjtSlxxbDTO> getService() {
        return zjtSlxxbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询居民身份证受理信息表 N23001")
    public CommonResult<PageResult<ZjtSlxxbPageResp>> page(ZjtSlxxbPageReq req, PageParam pageParam) {

        ZjtSlxxbDTO zjtSlxxbDTO = ZjtSlxxbConvert.INSTANCE.convertToDTO(req);

        if (Boolean.TRUE.equals(req.getHjdsjfwcx())) {
            List<String> yhsjfw = SecurityUtils.getYhsjfw().stream().map(DwUtils::getMinimumSjfw).toList();
            zjtSlxxbDTO.setHjdsjgsdwdmLikeList(yhsjfw);
        }

        if (Boolean.TRUE.equals(req.getSldsjfwcx())) {
            List<String> yhsjfw = SecurityUtils.getYhsjfw().stream().map(DwUtils::getMinimumSjfw).toList();
            zjtSlxxbDTO.setSldsjgsdwdmLikeList(yhsjfw);
        }

        PageResult<ZjtSlxxbDTO> dtoPageResult = getService().page(zjtSlxxbDTO, pageParam);
        PageResult<ZjtSlxxbPageResp> pageResult = PageResult.convert(dtoPageResult,
                ZjtSlxxbConvert.INSTANCE::convertToPageResp);

        return CommonResult.success(pageResult);
    }

    @GetMapping("view")
    @Operation(summary = "查看居民身份证受理信息表详情 N23001")
    public CommonResult<ZjtSlxxbViewResp> view(String id) {
        return super.view(id, ZjtSlxxbConvert.INSTANCE::convertToViewResp);
    }

    @PostMapping("create")
    @Operation(summary = "新增居民身份证受理信息表 F23301")
    public CommonResult<ZjtSlxxbCreateResp> create(@Validated @RequestBody ZjtSlxxbCreateReq req) {
        return super.create(req, ZjtSlxxbConvert.INSTANCE::convertToDTO, ZjtSlxxbConvert.INSTANCE::convertToCreateResp);
    }

    @PostMapping("approvePcs")
    @Operation(summary = "派出所审核通过 F23306")
    public CommonResult<Boolean> approvePcs(@Validated @RequestBody ZjtSlxxbApproveReq req) {
        zjtSlxxbZjCsManager.audit(new ZjtSlxxbAuditDTO(req.getNbslid(), ZjConstant.ZJ_SHQK_HG, CkConstants.GNBH_ZJT_PCSCS));
        return CommonResult.success(true);
    }

    @PostMapping("rejectPcs")
    @Operation(summary = "派出所审核不通过 F23306")
    public CommonResult<Boolean> rejectPcs(@Validated @RequestBody ZjtSlxxbRejectReq req) {
        ZjtSlxxbAuditDTO zjtSlxxbAuditDTO = new ZjtSlxxbAuditDTO(req.getNbslid(), ZjConstant.ZJ_SHQK_BHG,
                CkConstants.GNBH_ZJT_PCSCS);
        zjtSlxxbAuditDTO.setZzxxcwlb(req.getZzxxcwlb());
        zjtSlxxbAuditDTO.setCwms(req.getCwms());
        zjtSlxxbZjCsManager.audit(zjtSlxxbAuditDTO);
        return CommonResult.success(true);
    }

    @PostMapping("approveQx")
    @Operation(summary = "区县审核通过 F23309")
    public CommonResult<Boolean> approveQx(@Validated @RequestBody ZjtSlxxbApproveReq req) {
        zjtSlxxbZjshManager.audit(new ZjtSlxxbAuditDTO(req.getNbslid(), ZjConstant.ZJ_SHQK_HG, CkConstants.GNBH_ZJT_QXZJSHYW));
        return CommonResult.success(true);
    }

    @PostMapping("rejectQx")
    @Operation(summary = "区县审核不通过 F23309")
    public CommonResult<Boolean> rejectQx(@Validated @RequestBody ZjtSlxxbRejectReq req) {
        ZjtSlxxbAuditDTO zjtSlxxbAuditDTO = new ZjtSlxxbAuditDTO(req.getNbslid(), ZjConstant.ZJ_SHQK_BHG,
                CkConstants.GNBH_ZJT_QXZJSHYW);
        zjtSlxxbAuditDTO.setZzxxcwlb(req.getZzxxcwlb());
        zjtSlxxbAuditDTO.setCwms(req.getCwms());
        zjtSlxxbZjshManager.audit(zjtSlxxbAuditDTO);
        return CommonResult.success(true);
    }

    @PostMapping("issueQx")
    @Operation(summary = "区县签发 F23310")
    public CommonResult<Boolean> issueQx(@Validated @RequestBody ZjtSlxxbIssueReq req) {
        zjtSlxxbZjQfManager.processQxZjqfyw(req.getNbslid());
        return CommonResult.success(true);
    }

    @PostMapping("approveDs")
    @Operation(summary = "地市审核通过 F23311")
    public CommonResult<Boolean> approveDs(@Validated @RequestBody ZjtSlxxbApproveReq req) {
        zjtSlxxbZjshManager.audit(new ZjtSlxxbAuditDTO(req.getNbslid(), ZjConstant.ZJ_SHQK_HG, CkConstants.GNBH_ZJT_DSZJSHYW));
        return CommonResult.success(true);
    }

    @PostMapping("rejectDs")
    @Operation(summary = "地市审核不通过 F23311")
    public CommonResult<Boolean> rejectDs(@Validated @RequestBody ZjtSlxxbRejectReq req) {
        ZjtSlxxbAuditDTO zjtSlxxbAuditDTO = new ZjtSlxxbAuditDTO(req.getNbslid(), ZjConstant.ZJ_SHQK_BHG,
                CkConstants.GNBH_ZJT_DSZJSHYW);
        zjtSlxxbAuditDTO.setZzxxcwlb(req.getZzxxcwlb());
        zjtSlxxbAuditDTO.setCwms(req.getCwms());
        zjtSlxxbZjshManager.audit(zjtSlxxbAuditDTO);
        return CommonResult.success(true);
    }

    @PostMapping("updateYsf")
    @Operation(summary = "受理信息已收费 F23307")
    public CommonResult<Boolean> updateYsf(@Validated @RequestBody ZjtSlxxbZjZjslYsfReq req) {
        zjtSlxxbService.processZjslYsfcl(req.getNbslid());
        return CommonResult.success(true);
    }

    @PostMapping("approveLtzQx")
    @Operation(summary = "绿通证区县审核通过 F23353")
    public CommonResult<Boolean> approveLtzQx(@Validated @RequestBody ZjtSlxxbLtzApproveReq req) {
        zjtSlxxbZjshManager.auditLtz(new ZjtSlxxbAuditDTO(req.getNbslid(), ZjConstant.ZJ_SHQK_HG, CkConstants.GNBH_ZJT_LTZFJSH, ZjConstant.LTZ_SHJG_11_FJSPTG));
        return CommonResult.success(true);
    }

    @PostMapping("rejectLtzQx")
    @Operation(summary = "绿通证区县审核不通过 F23353")
    public CommonResult<Boolean> rejectLtzQx(@Validated @RequestBody ZjtSlxxbLtzRejectReq req) {
        ZjtSlxxbAuditDTO zjtSlxxbAuditDTO = new ZjtSlxxbAuditDTO(req.getNbslid(), ZjConstant.ZJ_SHQK_BHG, CkConstants.GNBH_ZJT_LTZFJSH, ZjConstant.LTZ_SHJG_12_FJSPBTG);
        zjtSlxxbZjshManager.auditLtz(zjtSlxxbAuditDTO);
        return CommonResult.success(true);
    }

    @PostMapping("approveLtzDs")
    @Operation(summary = "绿通证地市审核通过 F23354")
    public CommonResult<Boolean> approveLtzDs(@Validated @RequestBody ZjtSlxxbLtzApproveReq req) {
        zjtSlxxbZjshManager.auditLtz(new ZjtSlxxbAuditDTO(req.getNbslid(), ZjConstant.ZJ_SHQK_HG, CkConstants.GNBH_ZJT_LTZSJSH, ZjConstant.LTZ_SHJG_21_SJSPTG));
        return CommonResult.success(true);
    }

    @PostMapping("rejectLtzDs")
    @Operation(summary = "绿通证地市审核不通过 F23354")
    public CommonResult<Boolean> rejectLtzDs(@Validated @RequestBody ZjtSlxxbLtzRejectReq req) {
        ZjtSlxxbAuditDTO zjtSlxxbAuditDTO = new ZjtSlxxbAuditDTO(req.getNbslid(), ZjConstant.ZJ_SHQK_BHG, CkConstants.GNBH_ZJT_LTZSJSH, ZjConstant.LTZ_SHJG_22_SJSPBTG);
        zjtSlxxbZjshManager.auditLtz(zjtSlxxbAuditDTO);
        return CommonResult.success(true);
    }

    @GetMapping("queryLtzfjsyl")
    @Operation(summary = "分局绿通证每日办理剩余量查询 F23356")
    public CommonResult<ZjtSlxxbLtzfjsylResp> queryLtzfjsyl() {
        int count = zjtSlxxbService.queryLtzfjsyl();
        ZjtSlxxbLtzfjsylResp resp = new ZjtSlxxbLtzfjsylResp();
        resp.setSyl(count);
        return CommonResult.success(resp);
    }

    @PostMapping("applyLtz")
    @Operation(summary = "绿通证申请 F23352")
    public CommonResult<Boolean> applyLtz(@Validated @RequestBody ZjtSlxxbApplyLtzReq req) {
        zjtSlxxbService.saveApplyLtz(req);
        return CommonResult.success(true);
    }

    @GetMapping("countWshLtz")
    @Operation(summary = "未审核绿通证数量")
    public CommonResult<Long> countWshLtz(ZjtSlxxbCountWshltzReq req) {

        ZjtSlxxbDTO zjtSlxxbDTO = new ZjtSlxxbDTO();
        zjtSlxxbDTO.setLtzshjg(StringUtils.defaultIfEmpty(req.getLtzshjg(), ZjConstant.LTZ_SHJG_00_WSP));
        zjtSlxxbDTO.setSfsqltz(Constants.YES);
        zjtSlxxbDTO.setZzlxList(req.getZzlxList());
        zjtSlxxbDTO.setSlzt(req.getSlzt());
        List<String> yhsjfw = SecurityUtils.getYhsjfw().stream().map(DwUtils::getMinimumSjfw).toList();
        zjtSlxxbDTO.setSldsjgsdwdmLikeList(yhsjfw);
        long count = zjtSlxxbService.count(zjtSlxxbDTO);
        return CommonResult.success(count);
    }


}
