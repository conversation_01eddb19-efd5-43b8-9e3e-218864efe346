package com.zjjcnt.project.ck.base.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 未审核绿通证数量
 *
 * <AUTHOR>
 * @date 2025-08-07 11:08:00
 */
@Data
public class ZjtSlxxbCountWshltzReq {

    @Schema(description = "绿通证审核结果")
    private String ltzshjg;

    @Schema(description = "受理状态")
    private String slzt;

    @Schema(description = "制证类型列表")
    private List<String> zzlxList;

}