package com.zjjcnt.project.ck.base.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.Data;

import com.zjjcnt.common.core.entity.IdEntity;
import com.zjjcnt.common.core.annotation.Crypto;

import java.math.BigDecimal;
import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import java.lang.String;
import java.lang.Integer;

/**
 * 互联网办件受理材料DO
 *
 * <AUTHOR>
 * @date 2025-07-25 16:34:24
 * @see com.zjjcnt.project.ck.base.dto.GaythtYthSlclDTO
 */
@Data
@Table("gaytht_yth_slcl")
public class GaythtYthSlclDO implements IdEntity<String> {
    private static final long serialVersionUID = -3480593667891513394L;

    /**
     * 唯一标识
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String unid;

    /**
     * 申报号
     */
    @Column(value = "projid")
    private String projid;

    /**
     * 材料名称
     */
    @Column(value = "attrname")
    private String attrname;

    /**
     * 材料序号
     */
    @Column(value = "sortid")
    private BigDecimal sortid;

    /**
     * 收取方式
     */
    @Column(value = "taketype")
    private String taketype;

    /**
     * 是否收取
     */
    @Column(value = "istake")
    private String istake;

    /**
     * 收取时间
     */
    @Column(value = "taketime")
    private String taketime;

    /**
     * 备注
     */
    @Column(value = "memo")
    private String memo;

    /**
     * 所属系统
     */
    @Column(value = "belongsystem")
    private String belongsystem;

    /**
     * 部门行政区划编码
     */
    @Column(value = "areacode")
    private String areacode;

    /**
     * 版本号
     */
    @Column(value = "dataversion")
    private Integer dataversion;

    /**
     * 同步状态
     */
    @Column(value = "sync_status")
    private String syncStatus;

    /**
     * 数据产生时间
     */
    @Column(value = "create_time")
    private String createTime;

    /**
     * 
     */
    @Column(value = "attrid")
    private String attrid;

    /**
     * 创建人
     */
    @Column(value = "cjr")
    private String cjr;

    /**
     * 创建人IP
     */
    @Column(value = "cjrip")
    private String cjrip;

    /**
     * 创建时间
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     * 修改人
     */
    @Column(value = "xgr")
    private String xgr;

    /**
     * 修改人IP
     */
    @Column(value = "xgrip")
    private String xgrip;

    /**
     * 修改时间
     */
    @Column(value = "xgsj")
    private String xgsj;

    /**
     * 有效标志
     */
    @Column(value = "yxbz")
    private String yxbz;

    /**
     * 备用字段
     */
    @Column(value = "extend")
    private String extend;

    /**
     * 备用字段2
     */
    @Column(value = "extend2")
    private String extend2;

    /**
     * 备用字段3
     */
    @Column(value = "extend3")
    private String extend3;

    /**
     * 备用字段4
     */
    @Column(value = "extend4")
    private String extend4;

    /**
     * 备用字段5
     */
    @Column(value = "extend5")
    private String extend5;


    @Override
    public String getId() {
        return this.unid;
    }

    @Override
    public void setId(String id) {
        this.unid = id;
    }
}
