package com.zjjcnt.project.ck.base.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.base.convert.KsxtCssbxxbConvert;
import com.zjjcnt.project.ck.base.dto.KsxtCssbxxbDTO;
import com.zjjcnt.project.ck.base.entity.KsxtCssbxxbDO;
import com.zjjcnt.project.ck.base.mapper.KsxtCssbxxbMapper;
import com.zjjcnt.project.ck.base.service.KsxtCssbxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 跨省协同出生申报信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 */
@Service
public class KsxtCssbxxbServiceImpl extends AbstractBaseServiceImpl<KsxtCssbxxbMapper, KsxtCssbxxbDO, KsxtCssbxxbDTO> implements KsxtCssbxxbService {

    KsxtCssbxxbConvert convert = KsxtCssbxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(KsxtCssbxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(KsxtCssbxxbDO::getKsxtid, dto.getKsxtid(), StringUtils.isNotEmpty(dto.getKsxtid()));
        query.eq(KsxtCssbxxbDO::getZaglywxtbh, dto.getZaglywxtbh(), StringUtils.isNotEmpty(dto.getZaglywxtbh()));
        query.eq(KsxtCssbxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(KsxtCssbxxbDO::getX, dto.getX(), StringUtils.isNotEmpty(dto.getX()));
        query.eq(KsxtCssbxxbDO::getM, dto.getM(), StringUtils.isNotEmpty(dto.getM()));
        query.eq(KsxtCssbxxbDO::getXbdm, dto.getXbdm(), StringUtils.isNotEmpty(dto.getXbdm()));
        query.eq(KsxtCssbxxbDO::getMzdm, dto.getMzdm(), StringUtils.isNotEmpty(dto.getMzdm()));
        query.ge(KsxtCssbxxbDO::getCsrq, dto.getCsrqStart(), StringUtils.isNotEmpty(dto.getCsrqStart()));
        query.le(KsxtCssbxxbDO::getCsrq, dto.getCsrqEnd(), StringUtils.isNotEmpty(dto.getCsrqEnd()));
        query.eq(KsxtCssbxxbDO::getCssj, dto.getCssj(), StringUtils.isNotEmpty(dto.getCssj()));
        query.eq(KsxtCssbxxbDO::getCsdgjhdqdm, dto.getCsdgjhdqdm(), StringUtils.isNotEmpty(dto.getCsdgjhdqdm()));
        query.eq(KsxtCssbxxbDO::getCsdssxqdm, dto.getCsdssxqdm(), StringUtils.isNotEmpty(dto.getCsdssxqdm()));
        query.eq(KsxtCssbxxbDO::getCsdqhnxxdz, dto.getCsdqhnxxdz(), StringUtils.isNotEmpty(dto.getCsdqhnxxdz()));
        query.eq(KsxtCssbxxbDO::getJggjhdqdm, dto.getJggjhdqdm(), StringUtils.isNotEmpty(dto.getJggjhdqdm()));
        query.eq(KsxtCssbxxbDO::getJgssxqdm, dto.getJgssxqdm(), StringUtils.isNotEmpty(dto.getJgssxqdm()));
        query.eq(KsxtCssbxxbDO::getJgqhnxxdz, dto.getJgqhnxxdz(), StringUtils.isNotEmpty(dto.getJgqhnxxdz()));
        query.eq(KsxtCssbxxbDO::getXxdm, dto.getXxdm(), StringUtils.isNotEmpty(dto.getXxdm()));
        query.eq(KsxtCssbxxbDO::getRkxxjbdm, dto.getRkxxjbdm(), StringUtils.isNotEmpty(dto.getRkxxjbdm()));
        query.eq(KsxtCssbxxbDO::getCsdjlbdm, dto.getCsdjlbdm(), StringUtils.isNotEmpty(dto.getCsdjlbdm()));
        query.eq(KsxtCssbxxbDO::getCszmbh, dto.getCszmbh(), StringUtils.isNotEmpty(dto.getCszmbh()));
        query.eq(KsxtCssbxxbDO::getYhzgxdm, dto.getYhzgxdm(), StringUtils.isNotEmpty(dto.getYhzgxdm()));
        query.eq(KsxtCssbxxbDO::getHzxm, ColumnUtils.encryptColumn(dto.getHzxm()), StringUtils.isNotEmpty(dto.getHzxm()));
        query.eq(KsxtCssbxxbDO::getHzgmsfhm, ColumnUtils.encryptColumn(dto.getHzgmsfhm()), StringUtils.isNotEmpty(dto.getHzgmsfhm()));
        query.eq(KsxtCssbxxbDO::getFqgmsfhm, ColumnUtils.encryptColumn(dto.getFqgmsfhm()), StringUtils.isNotEmpty(dto.getFqgmsfhm()));
        query.eq(KsxtCssbxxbDO::getFqxm, ColumnUtils.encryptColumn(dto.getFqxm()), StringUtils.isNotEmpty(dto.getFqxm()));
        query.eq(KsxtCssbxxbDO::getFqcyzjdm, dto.getFqcyzjdm(), StringUtils.isNotEmpty(dto.getFqcyzjdm()));
        query.eq(KsxtCssbxxbDO::getFqzjhm, dto.getFqzjhm(), StringUtils.isNotEmpty(dto.getFqzjhm()));
        query.eq(KsxtCssbxxbDO::getMqgmsfhm, ColumnUtils.encryptColumn(dto.getMqgmsfhm()), StringUtils.isNotEmpty(dto.getMqgmsfhm()));
        query.eq(KsxtCssbxxbDO::getMqxm, ColumnUtils.encryptColumn(dto.getMqxm()), StringUtils.isNotEmpty(dto.getMqxm()));
        query.eq(KsxtCssbxxbDO::getMqcyzjdm, dto.getMqcyzjdm(), StringUtils.isNotEmpty(dto.getMqcyzjdm()));
        query.eq(KsxtCssbxxbDO::getMqzjhm, dto.getMqzjhm(), StringUtils.isNotEmpty(dto.getMqzjhm()));
        query.eq(KsxtCssbxxbDO::getJhrygmsfhm, ColumnUtils.encryptColumn(dto.getJhrygmsfhm()), StringUtils.isNotEmpty(dto.getJhrygmsfhm()));
        query.eq(KsxtCssbxxbDO::getJhryxm, ColumnUtils.encryptColumn(dto.getJhryxm()), StringUtils.isNotEmpty(dto.getJhryxm()));
        query.eq(KsxtCssbxxbDO::getJhrycyzjdm, dto.getJhrycyzjdm(), StringUtils.isNotEmpty(dto.getJhrycyzjdm()));
        query.eq(KsxtCssbxxbDO::getJhryzjhm, dto.getJhryzjhm(), StringUtils.isNotEmpty(dto.getJhryzjhm()));
        query.eq(KsxtCssbxxbDO::getJhrywwx, dto.getJhrywwx(), StringUtils.isNotEmpty(dto.getJhrywwx()));
        query.eq(KsxtCssbxxbDO::getJhrywwm, dto.getJhrywwm(), StringUtils.isNotEmpty(dto.getJhrywwm()));
        query.eq(KsxtCssbxxbDO::getJhryjhgxdm, dto.getJhryjhgxdm(), StringUtils.isNotEmpty(dto.getJhryjhgxdm()));
        query.eq(KsxtCssbxxbDO::getJhrylxdh, ColumnUtils.encryptColumn(dto.getJhrylxdh()), StringUtils.isNotEmpty(dto.getJhrylxdh()));
        query.eq(KsxtCssbxxbDO::getJhregmsfhm, ColumnUtils.encryptColumn(dto.getJhregmsfhm()), StringUtils.isNotEmpty(dto.getJhregmsfhm()));
        query.eq(KsxtCssbxxbDO::getJhrexm, ColumnUtils.encryptColumn(dto.getJhrexm()), StringUtils.isNotEmpty(dto.getJhrexm()));
        query.eq(KsxtCssbxxbDO::getJhrecyzjdm, dto.getJhrecyzjdm(), StringUtils.isNotEmpty(dto.getJhrecyzjdm()));
        query.eq(KsxtCssbxxbDO::getJhrezjhm, dto.getJhrezjhm(), StringUtils.isNotEmpty(dto.getJhrezjhm()));
        query.eq(KsxtCssbxxbDO::getJhrewwx, dto.getJhrewwx(), StringUtils.isNotEmpty(dto.getJhrewwx()));
        query.eq(KsxtCssbxxbDO::getJhrewwm, dto.getJhrewwm(), StringUtils.isNotEmpty(dto.getJhrewwm()));
        query.eq(KsxtCssbxxbDO::getJhrejhgxdm, dto.getJhrejhgxdm(), StringUtils.isNotEmpty(dto.getJhrejhgxdm()));
        query.eq(KsxtCssbxxbDO::getJhrelxdh, ColumnUtils.encryptColumn(dto.getJhrelxdh()), StringUtils.isNotEmpty(dto.getJhrelxdh()));
        query.eq(KsxtCssbxxbDO::getSbrgmsfhm, ColumnUtils.encryptColumn(dto.getSbrgmsfhm()), StringUtils.isNotEmpty(dto.getSbrgmsfhm()));
        query.eq(KsxtCssbxxbDO::getSbrxm, ColumnUtils.encryptColumn(dto.getSbrxm()), StringUtils.isNotEmpty(dto.getSbrxm()));
        query.eq(KsxtCssbxxbDO::getSbrlxdh, ColumnUtils.encryptColumn(dto.getSbrlxdh()), StringUtils.isNotEmpty(dto.getSbrlxdh()));
        query.eq(KsxtCssbxxbDO::getSbrycsrgxjtgxdm, dto.getSbrycsrgxjtgxdm(), StringUtils.isNotEmpty(dto.getSbrycsrgxjtgxdm()));
        query.eq(KsxtCssbxxbDO::getXzzssxqdm, dto.getXzzssxqdm(), StringUtils.isNotEmpty(dto.getXzzssxqdm()));
        query.eq(KsxtCssbxxbDO::getXzzqhnxxdz, dto.getXzzqhnxxdz(), StringUtils.isNotEmpty(dto.getXzzqhnxxdz()));
        query.eq(KsxtCssbxxbDO::getSldgajgjgdm, dto.getSldgajgjgdm(), StringUtils.isNotEmpty(dto.getSldgajgjgdm()));
        query.eq(KsxtCssbxxbDO::getSldgajgmc, dto.getSldgajgmc(), StringUtils.isNotEmpty(dto.getSldgajgmc()));
        query.eq(KsxtCssbxxbDO::getSldlxdh, ColumnUtils.encryptColumn(dto.getSldlxdh()), StringUtils.isNotEmpty(dto.getSldlxdh()));
        query.eq(KsxtCssbxxbDO::getSlrid, dto.getSlrid(), StringUtils.isNotEmpty(dto.getSlrid()));
        query.eq(KsxtCssbxxbDO::getSlrxm, ColumnUtils.encryptColumn(dto.getSlrxm()), StringUtils.isNotEmpty(dto.getSlrxm()));
        query.ge(KsxtCssbxxbDO::getSlsj, dto.getSlsjStart(), StringUtils.isNotEmpty(dto.getSlsjStart()));
        query.le(KsxtCssbxxbDO::getSlsj, dto.getSlsjEnd(), StringUtils.isNotEmpty(dto.getSlsjEnd()));
        query.eq(KsxtCssbxxbDO::getZaglzwfwsxbm, dto.getZaglzwfwsxbm(), StringUtils.isNotEmpty(dto.getZaglzwfwsxbm()));
        query.eq(KsxtCssbxxbDO::getZaglywlbdm, dto.getZaglywlbdm(), StringUtils.isNotEmpty(dto.getZaglywlbdm()));
        query.eq(KsxtCssbxxbDO::getGdpzbbh, dto.getGdpzbbh(), StringUtils.isNotEmpty(dto.getGdpzbbh()));
        query.eq(KsxtCssbxxbDO::getQyfwdm, dto.getQyfwdm(), StringUtils.isNotEmpty(dto.getQyfwdm()));
        query.eq(KsxtCssbxxbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(KsxtCssbxxbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.eq(KsxtCssbxxbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(KsxtCssbxxbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.ge(KsxtCssbxxbDO::getRksj, dto.getRksjStart(), StringUtils.isNotEmpty(dto.getRksjStart()));
        query.le(KsxtCssbxxbDO::getRksj, dto.getRksjEnd(), StringUtils.isNotEmpty(dto.getRksjEnd()));
        query.eq(KsxtCssbxxbDO::getPdbzsflh, dto.getPdbzsflh(), StringUtils.isNotEmpty(dto.getPdbzsflh()));
        query.eq(KsxtCssbxxbDO::getPdbzcszmhmd, dto.getPdbzcszmhmd(), StringUtils.isNotEmpty(dto.getPdbzcszmhmd()));
        query.eq(KsxtCssbxxbDO::getBdjg, dto.getBdjg(), StringUtils.isNotEmpty(dto.getBdjg()));
        query.eq(KsxtCssbxxbDO::getBdms, dto.getBdms(), StringUtils.isNotEmpty(dto.getBdms()));
        query.ge(KsxtCssbxxbDO::getBdsj, dto.getBdsjStart(), StringUtils.isNotEmpty(dto.getBdsjStart()));
        query.le(KsxtCssbxxbDO::getBdsj, dto.getBdsjEnd(), StringUtils.isNotEmpty(dto.getBdsjEnd()));
        query.eq(KsxtCssbxxbDO::getBdrid, dto.getBdrid(), StringUtils.isNotEmpty(dto.getBdrid()));
        query.eq(KsxtCssbxxbDO::getBdrxm, ColumnUtils.encryptColumn(dto.getBdrxm()), StringUtils.isNotEmpty(dto.getBdrxm()));
        query.eq(KsxtCssbxxbDO::getBdrip, dto.getBdrip(), StringUtils.isNotEmpty(dto.getBdrip()));
        return query;
    }

    @Override
    public KsxtCssbxxbDTO convertToDTO(KsxtCssbxxbDO ksxtCssbxxbDO) {
        return convert.convert(ksxtCssbxxbDO);
    }

    @Override
    public KsxtCssbxxbDO convertToDO(KsxtCssbxxbDTO ksxtCssbxxbDTO) {
        return convert.convertToDO(ksxtCssbxxbDTO);
    }
}
