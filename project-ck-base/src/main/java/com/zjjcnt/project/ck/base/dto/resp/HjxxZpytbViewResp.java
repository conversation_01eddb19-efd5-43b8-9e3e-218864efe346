package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 照片原图表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-06 15:44:07
 */
@Data
@Schema(description = "照片原图表响应对象")
public class HjxxZpytbViewResp {

    @Schema(description = "照片原图ID")
    private Long zpytbid;

    @Schema(description = "公民身份号码")
    private String gmsfhm;

    @StringDateTimeFormat
    @Schema(description = "保存时间")
    private String bcsj;

    @Schema(description = "照片大小")
    private Long zpdx;

    @Schema(description = "照片文件类型")
    private String zpwjlx;

    @Schema(description = "照片数据地址类型")
    private String zpsjdzlx;

    @Schema(description = "照片数据地址")
    private String zpsjdz;

    @Schema(description = "照片HASH")
    private String zphash;

    @Dict(CkDictTypeConstants.DM_ZPCJLX)
    @Schema(description = "照片采集类型")
    private String zpcjlx;

    @Schema(description = "设备标识号")
    private String zpsbbsh;

    @Schema(description = "设备品牌型号")
    private String zpsbppxh;

    @Schema(description = "设备品牌型号代码")
    private String zpsbppxhdm;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "base64照片")
    private String base64zp;
}
