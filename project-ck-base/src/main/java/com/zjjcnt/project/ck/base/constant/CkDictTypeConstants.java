package com.zjjcnt.project.ck.base.constant;

/**
 * 常口字典常量
 *
 * <AUTHOR>
 * @date 2024-04-10 09:40:00
 */
public final class CkDictTypeConstants {

    /**
     * 打印标志
     */
    public static final String DM_DYBZ = "1001";

    /**
     * 户别
     */
    public static final String DM_HB = "1005";

    /**
     * 人员类别
     */
    public static final String DM_RYLB = "1006";

    /**
     * 户类型
     */
    public static final String DM_HLX = "1007";

    /**
     * 人员状态
     */
    public static final String DM_RYZT = "1010";

    /**
     * 迁入登记类别
     */
    public static final String DM_QRLB = "1020";

    /**
     * 死亡注销类别
     */
    public static final String DM_SWZXLB = "1021";

    /**
     * 记录标志
     */
    public static final String DM_JLBZ = "1043";

    /**
     * 血型
     */
    public static final String DM_XX = "1044";

    /**
     * 宗教信仰
     */
    public static final String DM_ZJXY = "1045";

    /**
     * 兵役状况
     */
    public static final String DM_BYZK = "1046";

    /**
     * 信息级别
     */
    public static final String DM_XXJB = "1047";

    /**
     * 人员锁定状态
     */
    public static final String DM_RYSDZT = "1050";

    /**
     * 变动原因
     */
    public static final String DM_BDYY = "1056";

    /**
     * 行业类别
     */
    public static final String DM_HYLB = "1057";

    /**
     * 打印类别
     */
    public static final String DM_DYLB = "1070";

    /**
     * 异地制证回馈状态
     */
    public static final String DM_YDZZHKZT = "1072";

    /**
     * 证件代码
     */
    public static final String DM_CYZJ = "1080";

    /**
     * 迁移流动原因
     */
    public static final String DM_QYLDYY = "1081";

    /**
     * 区域范围
     */
    public static final String DM_QYFW = "1082";

    /**
     * 冻结原因
     */
    public static final String DM_DJYY = "1110";

    /**
     * 冻结状态
     */
    public static final String DM_DJZT = "1111";


    /**
     * 城乡属性
     */
    public static final String DM_CXSX = "1201";

    /**
     * 上报状态
     */
    public static final String DM_SBZT = "1255";


    /**
     * 照片采集类型
     */
    public static final String DM_ZPCJLX = "1258";


    /**
     * 照片设备品牌型号
     */
    public static final String DM_ZPSBPPXH = "1259";

    /**
     * 设备生产销售公司
     */
    public static final String DM_SBXSGS = "1280";

    /**
     * 公安部出生证比对结果代码
     */
    public static final String DM_GABCSZBDJGDM = "1293";

    /**
     * 跨省通办消息类型代码
     */
    public static final String DM_KSTBXXLXDM = "1505";

    /**
     * 跨省通办协同环节代码
     */
    public static final String DM_KSTBXTHJDM = "1506";

    /**
     * 跨省通办协同消息处理标志
     */
    public static final String DM_KSTBXTXXCLBZ = "1507";

    /**
     * 申请人人像比对结果代码
     */
    public static final String DM_SQRRXBDJGDM = "1511";

    /**
     * 双重国籍业务批次状态
     */
    public static final String SCGJ_PCZT = "1521";

    /**
     * 双重国籍业务处理标志
     */
    public static final String SCGJ_CLBZ = "1522";

    /**
     * 双重国籍业务户籍数据来源
     */
    public static final String SCGJ_HJSJLY = "1523";

    /**
     * 双重国籍业务审核标志
     */
    public static final String SCGJ_SHBZ = "1524";

    /**
     * 双重国籍业务办结原因
     */
    public static final String SCGJ_BJYY = "1525";

    /**
     * 二代证制证类型
     */
    public static final String DM_EDZZZLX = "5008";

    /**
     * 二代证领证方式
     */
    public static final String DM_EDZLZFS = "5009";

    /**
     * 二代证受理状态
     */
    public static final String DM_EDZSLZT = "5011";

    /**
     * 证件类别
     */
    public static final String DM_ZJLB = "5012";

    /**
     * 制证信息错误类别
     */
    public static final String DM_ZZXXCWLB = "5017";

    /**
     * 审核情况
     */
    public static final String DM_SHQK = "5022";

    /**
     * 领证签发结果
     */
    public static final String DM_LZQFJG = "5025";

    /**
     * 受理方式
     */
    public static final String DM_SLFS = "5026";

    /**
     * 新的证件受理方式
     */
    public static final String DM_SLFS_NEW = "5027";

    /**
     * 公安部跨省业务类型
     */
    public static final String DM_GABKSYWLX = "5028";

    /**
     * 公安部跨省业务办理状态
     */
    public static final String DM_GABKSYWBLZT = "5029";

    /**
     * 不予受理类别
     */
    public static final String DM_BYSLLB = "5031";

    /**
     * 二代证收费类型(新)
     */
    public static final String DM_EDZSFLX = "5035";


    /**
     * 临时证收费类型
     */
    public static final String DM_LSZSFLX = "5042";

    /**
     * 临时身份证受理标志
     */
    public static final String DM_LSSFZSLBZ = "5053";

    /**
     * 跨省通办户籍证明类型及事项代码
     */
    public static final String DM_HJZMLXJSXDM = "5060";

    /**
     * 跨省通办审核结果代码
     */
    public static final String DM_KSTBSHJGDM = "5061";

    /**
     * 跨省通办户籍协查结果代码
     */
    public static final String DM_KSTBHJXCJG = "5062";

    /**
     * 治安管理业务类别代码
     */
    public static final String DM_ZAGLYWLBDM = "5065";

    /**
     * 绿通证审核结果
     */
    public static final String DM_LTZSHJG = "5081";

    /**
     * 绿通证申请事由
     */
    public static final String DM_LTZSQSY = "5082";


    /**
     * 指位代码
     */
    public static final String DM_ZWDM = "5101";

    /**
     * 指纹注册结果代码
     */
    public static final String DM_ZWZCJGDM = "5103";

    /**
     * 指纹采集结果代码
     */
    public static final String DM_ZWCJJGDM = "5104";

    /**
     * 手指异常状况代码
     */
    public static final String DM_SZYCZKDM = "5105";

    /**
     * 评价次数
     */
    public static final String DM_PJCS = "7097";

    /**
     * 评价结果
     */
    public static final String DM_PJJG = "7100";

    /**
     * 二代证申领原因（部标）
     */
    public static final String DM_EDZSLYY = "7802";

    /**
     * 死亡注销类别（部标）
     */
    public static final String DM_SWZXLB_BB = "7803";

    /**
     * 民族
     */
    public static final String DM_MZ = "8001";

    /**
     * 文化程度
     */
    public static final String DM_WHCD = "8002";

    /**
     * 性别
     */
    public static final String DM_XB = "8003";

    /**
     * 家庭关系关系
     */
    public static final String DM_JTGX = "8006";

    /**
     * 婚姻状况
     */
    public static final String DM_HYZK = "8007";

    /**
     * 国籍
     */
    public static final String GJ = "8008";

    /**
     * 职业类别
     */
    public static final String DM_ZYLB = "8009";

    /**
     * 冲销标志
     */
    public static final String DM_CXBZ = "9001";

    /**
     * 是否标志
     */
    public static final String DM_SFBZ = "9027";


    /**
     * 收费业务类型
     */
    public static final String CKSFYWLX = "cksfywlx";

    /**
     * 参保地区业务代码
     */
    public static final String DM_CBDQYWDM = "cbdqywdm";


    /**
     * 金铖流程业务类型 / 业务编码
     */
    public static final String YWBM = "ywbm";

    /**
     * 流程办理状态
     */
    public static final String LCBLZT = "lcblzt";

    /**
     * 审批信息来源类别
     */
    public static final String SPXXLYLB = "spxxlylb";

    /**
     * 材料来源类型
     */
    public static final String CLLYLX = "cllylx";

    /**
     * 长三角业务办理状态
     */
    public static final String CSJYWBLZT = "csjywblzt";

    /**
     * 长三角业务类型
     */
    public static final String CSJYWLX = "csjywlx";

    /**
     * 长三角业务来源省份
     */
    public static final String CSJYWLYSF = "csjywlysf";

    private CkDictTypeConstants() {
    }
}
