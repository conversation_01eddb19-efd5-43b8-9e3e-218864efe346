package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.*;
import com.zjjcnt.common.core.dict.DictionaryNames;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 常住人口基本信息表响应对象
 *
 * <AUTHOR>
 * @date 2024-04-10 13:56:09
 */
@Data
@Schema(description = "常住人口基本信息表响应对象")
public class HjxxCzrkjbxxbViewResp {

    @Schema(description = "人员内部ID")
    private Long rynbid;

    @Schema(description = "人员ID")
    private Long ryid;

    @Schema(description = "户号内部ID")
    private Long hhnbid;

    @Schema(description = "门（楼）牌内部ID")
    private Long mlpnbid;

    @Schema(description = "照片ID")
    private Long zpid;

    @Schema(description = "内部身份证ID")
    private Long nbsfzid;

    @Schema(description = "公民身份号码")
    private String gmsfhm;

    @Schema(description = "签发机关")
    private String qfjg;

    @StringDateFormat
    @Schema(description = "有效期限起始日期")
    private String yxqxqsrq;

    @StringDateFormat
    @Schema(description = "有效期限截止日期")
    private String yxqxjzrq;

    @Schema(description = "姓名")
    private String xm;

    @Schema(description = "曾用名")
    private String cym;

    @Schema(description = "姓名拼音")
    private String xmpy;

    @Schema(description = "曾用名拼音")
    private String cympy;

    @Dict(CkDictTypeConstants.DM_XB)
    @Schema(description = "性别")
    private String xb;

    @Dict(CkDictTypeConstants.DM_MZ)
    @Schema(description = "民族")
    private String mz;

    @StringDateFormat
    @Schema(description = "出生日期")
    private String csrq;

    @Schema(description = "出生时间")
    private String cssj;

    @Dict(CkDictTypeConstants.GJ)
    @Schema(description = "出生地国家（地区）")
    private String csdgjdq;

    @DictSsxq
    @Schema(description = "出生地省市县（区）")
    private String csdssxq;

    @Schema(description = "出生地详址")
    private String csdxz;

    @Schema(description = "电话号码")
    private String dhhm;

    @Schema(description = "监护人一姓名")
    private String jhryxm;

    @Schema(description = "监护人一公民身份号码")
    private String jhrygmsfhm;

    @Dict(CkDictTypeConstants.DM_JTGX)
    @Schema(description = "监护人一监护关系")
    private String jhryjhgx;

    @Schema(description = "监护人二姓名")
    private String jhrexm;

    @Schema(description = "监护人二公民身份号码")
    private String jhregmsfhm;

    @Dict(CkDictTypeConstants.DM_JTGX)
    @Schema(description = "监护人二监护关系")
    private String jhrejhgx;

    @Schema(description = "父亲姓名")
    private String fqxm;

    @Schema(description = "父亲公民身份号码")
    private String fqgmsfhm;

    @Schema(description = "母亲姓名")
    private String mqxm;

    @Schema(description = "母亲公民身份号码")
    private String mqgmsfhm;

    @Schema(description = "配偶姓名")
    private String poxm;

    @Schema(description = "配偶公民身份号码")
    private String pogmsfhm;

    @Dict(CkDictTypeConstants.GJ)
    @Schema(description = "籍贯国家（地区）")
    private String jggjdq;

    @DictSsxq
    @Schema(description = "籍贯省市县（区）")
    private String jgssxq;

    @Dict(CkDictTypeConstants.DM_ZJXY)
    @Schema(description = "宗教信仰")
    private String zjxy;

    @Dict(CkDictTypeConstants.DM_WHCD)
    @Schema(description = "文化程度")
    private String whcd;

    @Dict(CkDictTypeConstants.DM_HYZK)
    @Schema(description = "婚姻状况")
    private String hyzk;

    @Dict(CkDictTypeConstants.DM_BYZK)
    @Schema(description = "兵役状况")
    private String byzk;

    @Schema(description = "身高")
    private String sg;

    @Dict(CkDictTypeConstants.DM_XX)
    @Schema(description = "血型")
    private String xx;

    @Schema(description = "职业")
    private String zy;

    @Dict(CkDictTypeConstants.DM_ZYLB)
    @Schema(description = "职业类别")
    private String zylb;

    @Schema(description = "服务处所")
    private String fwcs;

    @Dict(CkDictTypeConstants.DM_XXJB)
    @Schema(description = "信息级别")
    private String xxjb;

    @StringDateFormat
    @Schema(description = "何时迁来")
    private String hsql;

    @Dict(value = CkDictTypeConstants.DM_BDYY)
    @Schema(description = "何因迁来")
    private String hyql;

    @Dict(CkDictTypeConstants.GJ)
    @Schema(description = "何国家（地区）迁来")
    private String hgjdqql;

    @DictSsxq
    @Schema(description = "何省市县（区）迁来")
    private String hssxqql;

    @Schema(description = "何详址迁来")
    private String hxzql;

    @StringDateFormat
    @Schema(description = "何时来本址")
    private String hslbz;

    @Dict(value = CkDictTypeConstants.DM_BDYY)
    @Schema(description = "何因来本址")
    private String hylbz;

    @Dict(CkDictTypeConstants.GJ)
    @Schema(description = "何国家（地区）来本址")
    private String hgjdqlbz;

    @DictSsxq
    @Schema(description = "何省市县（区）来本址")
    private String hsssqlbz;

    @Schema(description = "何详址来本址")
    private String hxzlbz;

    @StringDateFormat
    @Schema(description = "死亡日期")
    private String swrq;

    @Dict(CkDictTypeConstants.DM_SWZXLB)
    @Schema(description = "死亡注销类别")
    private String swzxlb;

    @StringDateFormat
    @Schema(description = "死亡注销日期")
    private String swzxrq;

    @StringDateFormat
    @Schema(description = "迁出日期")
    private String qcrq;

    @Schema(description = "迁出注销类别")
    private String qczxlb;

    @Dict(CkDictTypeConstants.GJ)
    @Schema(description = "迁往地国家（地区）")
    private String qwdgjdq;

    @DictSsxq
    @Schema(description = "迁往地省市县（区）")
    private String qwdssxq;

    @Schema(description = "迁往地详址")
    private String qwdxz;

    @Schema(description = "出生证明编号")
    private String cszmbh;

    @StringDateFormat
    @Schema(description = "出生证签发日期")
    private String cszqfrq;

    @Dict(CkDictTypeConstants.DM_HYLB)
    @Schema(description = "行业类别")
    private String hylb;

    @DictSsxq
    @Schema(description = "其他省市县（区）")
    private String qtssxq;

    @Schema(description = "其他住址")
    private String qtzz;

    @Dict(value = CkDictTypeConstants.DM_RYLB)
    @Schema(description = "人员类别")
    private String rylb;

    @Dict(CkDictTypeConstants.DM_HB)
    @Schema(description = "户别")
    private String hb;

    @Dict(value = CkDictTypeConstants.DM_JTGX)
    @Schema(description = "与户主关系")
    private String yhzgx;

    @Dict(value = CkDictTypeConstants.DM_RYZT)
    @Schema(description = "人员状态")
    private String ryzt;

    @Dict(value = CkDictTypeConstants.DM_RYSDZT)
    @Schema(description = "人员锁定状态")
    private String rysdzt;

    @Schema(description = "离线DBID")
    private Long lxdbid;

    @Schema(description = "备注")
    private String bz;

    @Dict(CkDictTypeConstants.DM_JLBZ)
    @Schema(description = "记录标志")
    private String jlbz;

    @Schema(description = "业务内容")
    private String ywnr;

    @Schema(description = "创建户籍业务ID")
    private Long cjhjywid;

    @Schema(description = "撤除户籍业务ID")
    private Long cchjywid;

    @StringDateTimeFormat
    @Schema(description = "起用时间")
    private String qysj;

    @StringDateTimeFormat
    @Schema(description = "结束时间")
    private String jssj;

    @Dict(CkDictTypeConstants.DM_CXBZ)
    @Schema(description = "冲销标志")
    private String cxbz;

    @Dict(CkDictTypeConstants.DM_ZJLB)
    @Schema(description = "证件类别")
    private String zjlb;

    @Dict(value = DictionaryNames.DM_JLX)
    @Schema(description = "街路巷")
    private String jlx;

    @Schema(description = "门（楼）牌号")
    private String mlph;

    @Schema(description = "门（楼）详址")
    private String mlxz;

    @DictSjgsdw
    @Schema(description = "派出所")
    private String pcs;

    @Schema(description = "责任区")
    private String zrq;

    @DictSsxq
    @Schema(description = "乡镇（街道）")
    private String xzjd;

    @DictSsxq
    @Schema(description = "居（村）委会")
    private String jcwh;

    @Schema(description = "排序号")
    private String pxh;

    @Schema(description = "门（楼）牌ID")
    private Long mlpid;

    @DictSsxq
    @Schema(description = "省市县（区）")
    private String ssxq;

    @Schema(description = "户号")
    private String hh;

    @Dict(value = CkDictTypeConstants.DM_HLX)
    @Schema(description = "户类型")
    private String hlx;

    @Schema(description = "户号ID")
    private Long hhid;

    @Dict(CkDictTypeConstants.DM_QYFW)
    @Schema(description = "变动范围")
    private String bdfw;

    @StringDateTimeFormat
    @Schema(description = "信息启用时间")
    private String xxqysj;

    @Schema(description = "电话号码2")
    private String dhhm2;

    @Dict(CkDictTypeConstants.DM_SFBZ)
    @Schema(description = "现住地采集状态")
    private String xzdcjzt;

    @Dict(CkDictTypeConstants.DM_ZWDM)
    @Schema(description = "指纹一指位")
    private String zwyzw;

    @Dict(CkDictTypeConstants.DM_ZWZCJGDM)
    @Schema(description = "指纹一注册结果")
    private String zwyzcjg;

    @Dict(CkDictTypeConstants.DM_ZWDM)
    @Schema(description = "指纹二指位")
    private String zwezw;

    @Dict(CkDictTypeConstants.DM_ZWZCJGDM)
    @Schema(description = "指纹二注册结果")
    private String zwezcjg;

    @Dict(CkDictTypeConstants.DM_ZWCJJGDM)
    @Schema(description = "指纹采集结果代码")
    private String zwcjjgdm;

    @Dict(CkDictTypeConstants.DM_SZYCZKDM)
    @Schema(description = "手指异常状况代码")
    private String szyczkdm;

    @Schema(description = "姓")
    private String x;

    @Schema(description = "名")
    private String m;

    @Schema(description = "籍贯详址")
    private String jgxz;

    @Dict(CkDictTypeConstants.DM_CYZJ)
    @Schema(description = "监护人一证件类别")
    private String jhrycyzjdm;

    @Schema(description = "监护人一证件号码")
    private String jhryzjhm;

    @Schema(description = "监护人一外文姓")
    private String jhrywwx;

    @Schema(description = "监护人一外文名")
    private String jhrywwm;

    @Schema(description = "监护人一联系电话")
    private String jhrylxdh;

    @Dict(CkDictTypeConstants.DM_CYZJ)
    @Schema(description = "监护人二证件类别")
    private String jhrecyzjdm;

    @Schema(description = "监护人二证件号码")
    private String jhrezjhm;

    @Schema(description = "监护人二外文姓")
    private String jhrewwx;

    @Schema(description = "监护人二外文名")
    private String jhrewwm;

    @Schema(description = "监护人二联系电话")
    private String jhrelxdh;

    @Dict(CkDictTypeConstants.DM_CYZJ)
    @Schema(description = "父亲证件类别")
    private String fqcyzjdm;

    @Schema(description = "父亲证件号码")
    private String fqzjhm;

    @Schema(description = "父亲外文姓")
    private String fqwwx;

    @Schema(description = "父亲外文名")
    private String fqwwm;

    @Dict(CkDictTypeConstants.DM_CYZJ)
    @Schema(description = "母亲证件类别")
    private String mqcyzjdm;

    @Schema(description = "母亲证件号码")
    private String mqzjhm;

    @Schema(description = "母亲外文姓")
    private String mqwwx;

    @Schema(description = "母亲外文名")
    private String mqwwm;

    @Dict(CkDictTypeConstants.DM_CYZJ)
    @Schema(description = "配偶证件类别")
    private String pocyzjdm;

    @Schema(description = "配偶证件号码")
    private String pozjhm;

    @Schema(description = "配偶外文姓")
    private String powwx;

    @Schema(description = "配偶外文名")
    private String powwm;

    @Schema(description = "从业状况单位代码")
    private String cyzkdwbm;

    @Schema(description = "从业状况单位名称")
    private String cyzkdwmc;

    @Dict(value = CkDictTypeConstants.DM_BDYY)
    @Schema(description = "何迁移流动原因迁来")
    private String hqyldyy;

    @Dict(CkDictTypeConstants.DM_SWZXLB_BB)
    @Schema(description = "死亡原因")
    private String swyy;

    @Dict(value = CkDictTypeConstants.DM_BDYY)
    @Schema(description = "迁出迁移（流动）原因")
    private String qcqyldyy;

    @StringDateTimeFormat
    @Schema(description = "更新时间")
    private String gxsj;

    @Schema(description = "数据归属单位代码")
    private String sjgsdwdm;

    @Schema(description = "数据归属单位名称")
    private String sjgsdwmc;

    @Schema(description = "户籍地地址编码")
    private String hjddzbm;

    @DictSsxq
    @Schema(description = "户籍地省市县（区）")
    private String hjdssxq;

    @Schema(description = "户籍地详细地址")
    private String hjdxxdz;

    @Schema(description = "户籍地人户一致标识")
    private String hjdrhyzbs;

    @Schema(description = "居住地地址编码")
    private String jzddzbm;

    @DictSsxq
    @Schema(description = "居住地省市县（区）")
    private String jzdssxq;

    @Schema(description = "居住地详细地址")
    private String jzdxxdz;

    @Schema(description = "证件地址")
    private String zjdz;

    @Dict(CkDictTypeConstants.DM_QYLDYY)
    @Schema(description = "何迁移流动原因迁来本址")
    private String hqyldyylbz;

    @StringDateTimeFormat
    @Schema(description = "冻结时间")
    private String djsj;

    @Dict(CkDictTypeConstants.DM_DJYY)
    @Schema(description = "冻结原因")
    private String djyy;

    @StringDateTimeFormat
    @Schema(description = "解除冻结时间")
    private String jcdjsj;

    @Schema(description = "户口冻结ID")
    private Long hkdjid;

    @Dict(value = CkDictTypeConstants.DM_DJZT)
    @Schema(description = "冻结状态")
    private String djzt;

    @Schema(description = "统计用行政区划")
    private String tjyxzqh;

    @Dict(value = CkDictTypeConstants.DM_CXSX)
    @Schema(description = "城乡属性")
    private String cxsx;

    @Schema(description = "户籍地址")
    private String hjdz;
}
