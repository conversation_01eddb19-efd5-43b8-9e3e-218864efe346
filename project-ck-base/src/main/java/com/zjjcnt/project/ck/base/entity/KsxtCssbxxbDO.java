package com.zjjcnt.project.ck.base.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

import java.io.Serial;

/**
 * 跨省协同出生申报信息表DO
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 * @see com.zjjcnt.project.ck.base.dto.KsxtCssbxxbDTO
 */
@Crypto
@Data
@Table("ksxt_cssbxxb")
public class KsxtCssbxxbDO implements IdEntity<String> {

    @Serial
    private static final long serialVersionUID = -8730932682001140738L;

    /**
     * 跨省出生业务ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String ksxtcsid;

    /**
     * 跨省业务ID
     */
    @Column(value = "ksxtid")
    private String ksxtid;

    /**
     * 治安管理业务协同编号
     */
    @Column(value = "zaglywxtbh")
    private String zaglywxtbh;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 姓
     */
    @Column(value = "x")
    private String x;

    /**
     * 名
     */
    @Column(value = "m")
    private String m;

    /**
     * 性别
     */
    @Column(value = "xbdm")
    private String xbdm;

    /**
     * 民族
     */
    @Column(value = "mzdm")
    private String mzdm;

    /**
     * 出生日期
     */
    @Column(value = "csrq")
    private String csrq;

    /**
     * 出生时间
     */
    @Column(value = "cssj")
    private String cssj;

    /**
     * 出生地_国家（地区）
     */
    @Column(value = "csdgjhdqdm")
    private String csdgjhdqdm;

    /**
     * 出生地_省市县（区）
     */
    @Column(value = "csdssxqdm")
    private String csdssxqdm;

    /**
     * 出生地_区划内详细地址
     */
    @Column(value = "csdqhnxxdz")
    private String csdqhnxxdz;

    /**
     * 籍贯_国家（地区）
     */
    @Column(value = "jggjhdqdm")
    private String jggjhdqdm;

    /**
     * 籍贯_省市县（区）
     */
    @Column(value = "jgssxqdm")
    private String jgssxqdm;

    /**
     * 籍贯_区划内详细地址
     */
    @Column(value = "jgqhnxxdz")
    private String jgqhnxxdz;

    /**
     * 血型
     */
    @Column(value = "xxdm")
    private String xxdm;

    /**
     * 人口信息级别
     */
    @Column(value = "rkxxjbdm")
    private String rkxxjbdm;

    /**
     * 出生登记类别
     */
    @Column(value = "csdjlbdm")
    private String csdjlbdm;

    /**
     * 出生证明编号
     */
    @Column(value = "cszmbh")
    private String cszmbh;

    /**
     * 与户主关系
     */
    @Column(value = "yhzgxdm")
    private String yhzgxdm;

    /**
     * 户主_姓名
     */
    @Crypto
    @Column(value = "hzxm")
    private String hzxm;

    /**
     * 户主_公民身份号码
     */
    @Crypto
    @Column(value = "hzgmsfhm")
    private String hzgmsfhm;

    /**
     * 父亲_公民身份号码
     */
    @Crypto
    @Column(value = "fqgmsfhm")
    private String fqgmsfhm;

    /**
     * 父亲_姓名
     */
    @Crypto
    @Column(value = "fqxm")
    private String fqxm;

    /**
     * 父亲_证件种类
     */
    @Column(value = "fqcyzjdm")
    private String fqcyzjdm;

    /**
     * 父亲_证件号码
     */
    @Column(value = "fqzjhm")
    private String fqzjhm;

    /**
     * 母亲_公民身份号码
     */
    @Crypto
    @Column(value = "mqgmsfhm")
    private String mqgmsfhm;

    /**
     * 母亲_姓名
     */
    @Crypto
    @Column(value = "mqxm")
    private String mqxm;

    /**
     * 母亲_证件种类
     */
    @Column(value = "mqcyzjdm")
    private String mqcyzjdm;

    /**
     * 母亲_证件号码
     */
    @Column(value = "mqzjhm")
    private String mqzjhm;

    /**
     * 监护人一_公民身份号码
     */
    @Crypto
    @Column(value = "jhrygmsfhm")
    private String jhrygmsfhm;

    /**
     * 监护人一_姓名
     */
    @Crypto
    @Column(value = "jhryxm")
    private String jhryxm;

    /**
     * 监护人一_证件种类
     */
    @Column(value = "jhrycyzjdm")
    private String jhrycyzjdm;

    /**
     * 监护人一_证件号码
     */
    @Column(value = "jhryzjhm")
    private String jhryzjhm;

    /**
     * 监护人一_外文姓
     */
    @Column(value = "jhrywwx")
    private String jhrywwx;

    /**
     * 监护人一_外文名
     */
    @Column(value = "jhrywwm")
    private String jhrywwm;

    /**
     * 监护人一_监护关系
     */
    @Column(value = "jhryjhgxdm")
    private String jhryjhgxdm;

    /**
     * 监护人一_联系电话
     */
    @Crypto
    @Column(value = "jhrylxdh")
    private String jhrylxdh;

    /**
     * 监护人二_公民身份号码
     */
    @Crypto
    @Column(value = "jhregmsfhm")
    private String jhregmsfhm;

    /**
     * 监护人二_姓名
     */
    @Crypto
    @Column(value = "jhrexm")
    private String jhrexm;

    /**
     * 监护人二_证件种类
     */
    @Column(value = "jhrecyzjdm")
    private String jhrecyzjdm;

    /**
     * 监护人二_证件号码
     */
    @Column(value = "jhrezjhm")
    private String jhrezjhm;

    /**
     * 监护人二_外文姓
     */
    @Column(value = "jhrewwx")
    private String jhrewwx;

    /**
     * 监护人二_外文名
     */
    @Column(value = "jhrewwm")
    private String jhrewwm;

    /**
     * 监护人二_监护关系
     */
    @Column(value = "jhrejhgxdm")
    private String jhrejhgxdm;

    /**
     * 监护人二_联系电话
     */
    @Crypto
    @Column(value = "jhrelxdh")
    private String jhrelxdh;

    /**
     * 申报人_公民身份号码
     */
    @Crypto
    @Column(value = "sbrgmsfhm")
    private String sbrgmsfhm;

    /**
     * 申报人_姓名
     */
    @Crypto
    @Column(value = "sbrxm")
    private String sbrxm;

    /**
     * 申报人_联系电话
     */
    @Crypto
    @Column(value = "sbrlxdh")
    private String sbrlxdh;

    /**
     * 申报人_家庭关系
     */
    @Column(value = "sbrycsrgxjtgxdm")
    private String sbrycsrgxjtgxdm;

    /**
     * 现居住地址_省市县（区）
     */
    @Column(value = "xzzssxqdm")
    private String xzzssxqdm;

    /**
     * 现居住地址_区划内详细地址
     */
    @Column(value = "xzzqhnxxdz")
    private String xzzqhnxxdz;

    /**
     * 受理地_公安机关机构代码
     */
    @Column(value = "sldgajgjgdm")
    private String sldgajgjgdm;

    /**
     * 受理地_公安机关名称
     */
    @Column(value = "sldgajgmc")
    private String sldgajgmc;

    /**
     * 受理地_联系电话
     */
    @Crypto
    @Column(value = "sldlxdh")
    private String sldlxdh;

    /**
     * 受理人ID
     */
    @Column(value = "slrid")
    private String slrid;

    /**
     * 受理人_姓名
     */
    @Crypto
    @Column(value = "slrxm")
    private String slrxm;

    /**
     * 受理时间
     */
    @Column(value = "slsj")
    private String slsj;

    /**
     * 治安管理政务服务事项编码
     */
    @Column(value = "zaglzwfwsxbm")
    private String zaglzwfwsxbm;

    /**
     * 治安管理业务类别代码
     */
    @Column(value = "zaglywlbdm")
    private String zaglywlbdm;

    /**
     * 归档配置版本号
     */
    @Column(value = "gdpzbbh")
    private String gdpzbbh;

    /**
     * 业务协同区域范围代码
     */
    @Column(value = "qyfwdm")
    private String qyfwdm;

    /**
     * 受理地_数据归属单位代码
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     * 受理地_数据归属单位名称
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;

    /**
     * 户籍地_数据归属单位代码
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     * 户籍地_数据归属单位名称
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     * 入库时间
     */
    @Column(value = "rksj")
    private String rksj;

    /**
     * 判断标志是否落户
     */
    @Column(value = "pdbzsflh")
    private String pdbzsflh;

    /**
     * 判断标志出生证明黑名单
     */
    @Column(value = "pdbzcszmhmd")
    private String pdbzcszmhmd;

    /**
     * 比对结果
     */
    @Column(value = "bdjg")
    private String bdjg;

    /**
     * 比对描述
     */
    @Column(value = "bdms")
    private String bdms;

    /**
     * 比对时间
     */
    @Column(value = "bdsj")
    private String bdsj;

    /**
     * 比对人ID
     */
    @Column(value = "bdrid")
    private String bdrid;

    /**
     * 比对人姓名
     */
    @Crypto
    @Column(value = "bdrxm")
    private String bdrxm;

    /**
     * 比对人IP
     */
    @Column(value = "bdrip")
    private String bdrip;

    @Override
    public String getId() {
        return this.ksxtcsid;
    }

    @Override
    public void setId(String id) {
        this.ksxtcsid = id;
    }
}
