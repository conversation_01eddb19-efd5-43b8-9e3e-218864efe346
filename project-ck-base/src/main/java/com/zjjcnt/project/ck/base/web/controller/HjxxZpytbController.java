package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.HjxxZpytbConvert;
import com.zjjcnt.project.ck.base.dto.HjxxZpytbDTO;
import com.zjjcnt.project.ck.base.dto.resp.HjxxZpytbViewResp;
import com.zjjcnt.project.ck.base.service.HjxxZpytbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
* 照片原图表前端控制器
*
* <AUTHOR>
* @date 2025-08-06 15:44:07
*/
@RequiredArgsConstructor
@Tag(name = "照片原图表")
@RestController
@RequestMapping("/hjxxZpytb")
public class HjxxZpytbController extends AbstractCrudController<HjxxZpytbDTO>{

    private final HjxxZpytbService hjxxZpytbService;

    @Override
    protected IBaseService<HjxxZpytbDTO> getService() {
        return hjxxZpytbService;
    }

    @GetMapping("view")
    @Operation(summary = "查看照片原图表详情")
    public CommonResult<HjxxZpytbViewResp> view(@RequestParam Long id) {
        HjxxZpytbViewResp hjxxZpytbViewResp = doView(id, HjxxZpytbConvert.INSTANCE::convertToViewResp);
        if (Objects.nonNull(hjxxZpytbViewResp)) {
            hjxxZpytbViewResp.setBase64zp(hjxxZpytbService.getBase64Zp(hjxxZpytbViewResp.getZpsjdz()));
        }
        return CommonResult.success(hjxxZpytbViewResp);
    }

}