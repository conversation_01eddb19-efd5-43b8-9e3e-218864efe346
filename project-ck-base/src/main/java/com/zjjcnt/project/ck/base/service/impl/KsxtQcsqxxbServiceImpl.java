package com.zjjcnt.project.ck.base.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.base.convert.KsxtQcsqxxbConvert;
import com.zjjcnt.project.ck.base.dto.KsxtQcsqxxbDTO;
import com.zjjcnt.project.ck.base.entity.KsxtQcsqxxbDO;
import com.zjjcnt.project.ck.base.mapper.KsxtQcsqxxbMapper;
import com.zjjcnt.project.ck.base.service.KsxtQcsqxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 跨省协同迁出申请信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 */
@Service
public class KsxtQcsqxxbServiceImpl extends AbstractBaseServiceImpl<KsxtQcsqxxbMapper, KsxtQcsqxxbDO, KsxtQcsqxxbDTO> implements KsxtQcsqxxbService {

    KsxtQcsqxxbConvert convert = KsxtQcsqxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(KsxtQcsqxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(KsxtQcsqxxbDO::getKsxtid, dto.getKsxtid(), StringUtils.isNotEmpty(dto.getKsxtid()));
        query.eq(KsxtQcsqxxbDO::getZaglywxtbh, dto.getZaglywxtbh(), StringUtils.isNotEmpty(dto.getZaglywxtbh()));
        query.eq(KsxtQcsqxxbDO::getZaglywlbdm, dto.getZaglywlbdm(), StringUtils.isNotEmpty(dto.getZaglywlbdm()));
        query.eq(KsxtQcsqxxbDO::getZaglzwfwsxbm, dto.getZaglzwfwsxbm(), StringUtils.isNotEmpty(dto.getZaglzwfwsxbm()));
        query.eq(KsxtQcsqxxbDO::getSqrgmsfhm, ColumnUtils.encryptColumn(dto.getSqrgmsfhm()), StringUtils.isNotEmpty(dto.getSqrgmsfhm()));
        query.eq(KsxtQcsqxxbDO::getSqrxm, ColumnUtils.encryptColumn(dto.getSqrxm()), StringUtils.isNotEmpty(dto.getSqrxm()));
        query.eq(KsxtQcsqxxbDO::getSqrlxdh, ColumnUtils.encryptColumn(dto.getSqrlxdh()), StringUtils.isNotEmpty(dto.getSqrlxdh()));
        query.eq(KsxtQcsqxxbDO::getSqrzzssxqdm, dto.getSqrzzssxqdm(), StringUtils.isNotEmpty(dto.getSqrzzssxqdm()));
        query.eq(KsxtQcsqxxbDO::getSqrzzqhnxxdz, dto.getSqrzzqhnxxdz(), StringUtils.isNotEmpty(dto.getSqrzzqhnxxdz()));
        query.eq(KsxtQcsqxxbDO::getSqrhkdjjggajgjgdm, dto.getSqrhkdjjggajgjgdm(), StringUtils.isNotEmpty(dto.getSqrhkdjjggajgjgdm()));
        query.eq(KsxtQcsqxxbDO::getSqrhkdjjggajgmc, dto.getSqrhkdjjggajgmc(), StringUtils.isNotEmpty(dto.getSqrhkdjjggajgmc()));
        query.eq(KsxtQcsqxxbDO::getQcdssxqdm, dto.getQcdssxqdm(), StringUtils.isNotEmpty(dto.getQcdssxqdm()));
        query.eq(KsxtQcsqxxbDO::getQcdqhnxxdz, dto.getQcdqhnxxdz(), StringUtils.isNotEmpty(dto.getQcdqhnxxdz()));
        query.eq(KsxtQcsqxxbDO::getQcdhkdjjggajgjgdm, dto.getQcdhkdjjggajgjgdm(), StringUtils.isNotEmpty(dto.getQcdhkdjjggajgjgdm()));
        query.eq(KsxtQcsqxxbDO::getQcdhkdjjggajgmc, dto.getQcdhkdjjggajgmc(), StringUtils.isNotEmpty(dto.getQcdhkdjjggajgmc()));
        query.eq(KsxtQcsqxxbDO::getQcdsjgsdwdm, dto.getQcdsjgsdwdm(), StringUtils.isNotEmpty(dto.getQcdsjgsdwdm()));
        query.eq(KsxtQcsqxxbDO::getQcdsjgsdwmc, dto.getQcdsjgsdwmc(), StringUtils.isNotEmpty(dto.getQcdsjgsdwmc()));
        query.eq(KsxtQcsqxxbDO::getQrdssxqdm, dto.getQrdssxqdm(), StringUtils.isNotEmpty(dto.getQrdssxqdm()));
        query.eq(KsxtQcsqxxbDO::getQrdqhnxxdz, dto.getQrdqhnxxdz(), StringUtils.isNotEmpty(dto.getQrdqhnxxdz()));
        query.eq(KsxtQcsqxxbDO::getQrdhkdjjggajgjgdm, dto.getQrdhkdjjggajgjgdm(), StringUtils.isNotEmpty(dto.getQrdhkdjjggajgjgdm()));
        query.eq(KsxtQcsqxxbDO::getQrdhkdjjggajgmc, dto.getQrdhkdjjggajgmc(), StringUtils.isNotEmpty(dto.getQrdhkdjjggajgmc()));
        query.eq(KsxtQcsqxxbDO::getZqzbh, dto.getZqzbh(), StringUtils.isNotEmpty(dto.getZqzbh()));
        query.eq(KsxtQcsqxxbDO::getZqzqfjggajgjgdm, dto.getZqzqfjggajgjgdm(), StringUtils.isNotEmpty(dto.getZqzqfjggajgjgdm()));
        query.eq(KsxtQcsqxxbDO::getZqzqfjggajgmc, dto.getZqzqfjggajgmc(), StringUtils.isNotEmpty(dto.getZqzqfjggajgmc()));
        query.ge(KsxtQcsqxxbDO::getZqzqfrq, dto.getZqzqfrqStart(), StringUtils.isNotEmpty(dto.getZqzqfrqStart()));
        query.le(KsxtQcsqxxbDO::getZqzqfrq, dto.getZqzqfrqEnd(), StringUtils.isNotEmpty(dto.getZqzqfrqEnd()));
        query.ge(KsxtQcsqxxbDO::getZqzyxqjzrq, dto.getZqzyxqjzrqStart(), StringUtils.isNotEmpty(dto.getZqzyxqjzrqStart()));
        query.le(KsxtQcsqxxbDO::getZqzyxqjzrq, dto.getZqzyxqjzrqEnd(), StringUtils.isNotEmpty(dto.getZqzyxqjzrqEnd()));
        query.eq(KsxtQcsqxxbDO::getDzzqdzzzbz, dto.getDzzqdzzzbz(), StringUtils.isNotEmpty(dto.getDzzqdzzzbz()));
        query.eq(KsxtQcsqxxbDO::getDzzqzagldzzzbh, dto.getDzzqzagldzzzbh(), StringUtils.isNotEmpty(dto.getDzzqzagldzzzbh()));
        query.eq(KsxtQcsqxxbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(KsxtQcsqxxbDO::getQyldyydm, dto.getQyldyydm(), StringUtils.isNotEmpty(dto.getQyldyydm()));
        query.eq(KsxtQcsqxxbDO::getYsqrgxjtgxdm, dto.getYsqrgxjtgxdm(), StringUtils.isNotEmpty(dto.getYsqrgxjtgxdm()));
        query.eq(KsxtQcsqxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(KsxtQcsqxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(KsxtQcsqxxbDO::getXbdm, dto.getXbdm(), StringUtils.isNotEmpty(dto.getXbdm()));
        query.ge(KsxtQcsqxxbDO::getCsrq, dto.getCsrqStart(), StringUtils.isNotEmpty(dto.getCsrqStart()));
        query.le(KsxtQcsqxxbDO::getCsrq, dto.getCsrqEnd(), StringUtils.isNotEmpty(dto.getCsrqEnd()));
        query.eq(KsxtQcsqxxbDO::getSldgajgjgdm, dto.getSldgajgjgdm(), StringUtils.isNotEmpty(dto.getSldgajgjgdm()));
        query.eq(KsxtQcsqxxbDO::getSldgajgmc, dto.getSldgajgmc(), StringUtils.isNotEmpty(dto.getSldgajgmc()));
        query.eq(KsxtQcsqxxbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(KsxtQcsqxxbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.eq(KsxtQcsqxxbDO::getSldlxdh, ColumnUtils.encryptColumn(dto.getSldlxdh()), StringUtils.isNotEmpty(dto.getSldlxdh()));
        query.eq(KsxtQcsqxxbDO::getSlrxm, ColumnUtils.encryptColumn(dto.getSlrxm()), StringUtils.isNotEmpty(dto.getSlrxm()));
        query.ge(KsxtQcsqxxbDO::getSlsj, dto.getSlsjStart(), StringUtils.isNotEmpty(dto.getSlsjStart()));
        query.le(KsxtQcsqxxbDO::getSlsj, dto.getSlsjEnd(), StringUtils.isNotEmpty(dto.getSlsjEnd()));
        query.eq(KsxtQcsqxxbDO::getGdpzbbh, dto.getGdpzbbh(), StringUtils.isNotEmpty(dto.getGdpzbbh()));
        query.eq(KsxtQcsqxxbDO::getQyfwdm, dto.getQyfwdm(), StringUtils.isNotEmpty(dto.getQyfwdm()));
        query.eq(KsxtQcsqxxbDO::getFsdwsjgsdwdm, dto.getFsdwsjgsdwdm(), StringUtils.isNotEmpty(dto.getFsdwsjgsdwdm()));
        query.eq(KsxtQcsqxxbDO::getFsdwsjgsdwmc, dto.getFsdwsjgsdwmc(), StringUtils.isNotEmpty(dto.getFsdwsjgsdwmc()));
        query.eq(KsxtQcsqxxbDO::getJsdwsjgsdwdm, dto.getJsdwsjgsdwdm(), StringUtils.isNotEmpty(dto.getJsdwsjgsdwdm()));
        query.eq(KsxtQcsqxxbDO::getJsdwsjgsdwmc, dto.getJsdwsjgsdwmc(), StringUtils.isNotEmpty(dto.getJsdwsjgsdwmc()));
        query.ge(KsxtQcsqxxbDO::getRksj, dto.getRksjStart(), StringUtils.isNotEmpty(dto.getRksjStart()));
        query.le(KsxtQcsqxxbDO::getRksj, dto.getRksjEnd(), StringUtils.isNotEmpty(dto.getRksjEnd()));
        query.eq(KsxtQcsqxxbDO::getHjqcbz, dto.getHjqcbz(), StringUtils.isNotEmpty(dto.getHjqcbz()));
        query.eq(KsxtQcsqxxbDO::getSpywslh, dto.getSpywslh(), StringUtils.isNotEmpty(dto.getSpywslh()));
        query.ge(KsxtQcsqxxbDO::getSpywslsj, dto.getSpywslsjStart(), StringUtils.isNotEmpty(dto.getSpywslsjStart()));
        query.le(KsxtQcsqxxbDO::getSpywslsj, dto.getSpywslsjEnd(), StringUtils.isNotEmpty(dto.getSpywslsjEnd()));
        return query;
    }

    @Override
    public KsxtQcsqxxbDTO convertToDTO(KsxtQcsqxxbDO ksxtQcsqxxbDO) {
        return convert.convert(ksxtQcsqxxbDO);
    }

    @Override
    public KsxtQcsqxxbDO convertToDO(KsxtQcsqxxbDTO ksxtQcsqxxbDTO) {
        return convert.convertToDO(ksxtQcsqxxbDTO);
    }
}
