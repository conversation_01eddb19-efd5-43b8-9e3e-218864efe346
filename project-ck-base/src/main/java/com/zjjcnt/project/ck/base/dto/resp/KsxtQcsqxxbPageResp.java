package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.DictSsxq;
import com.zjjcnt.common.core.annotation.StringDateFormat;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 跨省协同迁出申请信息表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 */
@Data
@Schema(description = "跨省协同迁出申请信息表响应对象")
public class KsxtQcsqxxbPageResp {

    @Schema(description = "跨省协同迁出申请ID")
    private String ksxtqcsqid;

    @Schema(description = "跨省协同ID")
    private String ksxtid;

    @Schema(description = "治安管理业务协同编号")
    private String zaglywxtbh;

    @Schema(description = "治安管理业务类别代码")
    private String zaglywlbdm;

    @Schema(description = "治安管理政务服务事项编码")
    private String zaglzwfwsxbm;

    @Schema(description = "申请人公民身份号码")
    private String sqrgmsfhm;

    @Schema(description = "申请人姓名")
    private String sqrxm;

    @Schema(description = "申请人联系电话")
    private String sqrlxdh;

    @DictSsxq
    @Schema(description = "申请人住址省市县（区）")
    private String sqrzzssxqdm;

    @Schema(description = "申请人住址区划内详细地址")
    private String sqrzzqhnxxdz;

    @Schema(description = "申请人户口登记机关公安机关机构代码")
    private String sqrhkdjjggajgjgdm;

    @Schema(description = "申请人户口登记机关公安机关名称")
    private String sqrhkdjjggajgmc;

    @DictSsxq
    @Schema(description = "迁出地省市县（区）")
    private String qcdssxqdm;

    @Schema(description = "迁出地区划内详细地址")
    private String qcdqhnxxdz;

    @Schema(description = "迁出地户口登记机关公安机关机构代码")
    private String qcdhkdjjggajgjgdm;

    @Schema(description = "迁出地户口登记机关公安机关名称")
    private String qcdhkdjjggajgmc;

    @Schema(description = "迁出地数据归属单位代码")
    private String qcdsjgsdwdm;

    @Schema(description = "迁出地数据归属单位名称")
    private String qcdsjgsdwmc;

    @DictSsxq
    @Schema(description = "迁入地省市县（区）")
    private String qrdssxqdm;

    @Schema(description = "迁入地区划内详细地址")
    private String qrdqhnxxdz;

    @Schema(description = "迁入地户口登记机关公安机关机构代码")
    private String qrdhkdjjggajgjgdm;

    @Schema(description = "迁入地户口登记机关公安机关名称")
    private String qrdhkdjjggajgmc;

    @Schema(description = "准迁证编号")
    private String zqzbh;

    @Schema(description = "准迁证_签发机关公安机关机构代码")
    private String zqzqfjggajgjgdm;

    @Schema(description = "准迁证_签发机关公安机关名称")
    private String zqzqfjggajgmc;

    @StringDateFormat
    @Schema(description = "准迁证_签发日期")
    private String zqzqfrq;

    @StringDateFormat
    @Schema(description = "准迁证_有效期截止日期")
    private String zqzyxqjzrq;

    @Schema(description = "电子准迁_证照标识")
    private String dzzqdzzzbz;

    @Schema(description = "电子准迁_治安管理电子证照编号")
    private String dzzqzagldzzzbh;

    @Schema(description = "备注")
    private String bz;

    @Dict(CkDictTypeConstants.DM_QYLDYY)
    @Schema(description = "迁移（流动）原因")
    private String qyldyydm;

    @Dict(CkDictTypeConstants.DM_JTGX)
    @Schema(description = "与申请人关系_家庭关系")
    private String ysqrgxjtgxdm;

    @Schema(description = "公民身份号码")
    private String gmsfhm;

    @Schema(description = "姓名")
    private String xm;

    @Dict(CkDictTypeConstants.DM_XB)
    @Schema(description = "性别")
    private String xbdm;

    @StringDateFormat
    @Schema(description = "出生日期")
    private String csrq;

    @Schema(description = "受理地公安机关机构代码")
    private String sldgajgjgdm;

    @Schema(description = "受理地公安机关名称")
    private String sldgajgmc;

    @Schema(description = "受理地数据归属单位代码")
    private String sldsjgsdwdm;

    @Schema(description = "受理地数据归属单位名称")
    private String sldsjgsdwmc;

    @Schema(description = "受理地联系电话")
    private String sldlxdh;

    @Schema(description = "受理人姓名")
    private String slrxm;

    @StringDateTimeFormat
    @Schema(description = "受理时间")
    private String slsj;

    @Schema(description = "归档配置版本号")
    private String gdpzbbh;

    @Dict(CkDictTypeConstants.DM_QYFW)
    @Schema(description = "区域范围代码")
    private String qyfwdm;

    @Schema(description = "发送单位数据归属单位代码")
    private String fsdwsjgsdwdm;

    @Schema(description = "发送单位数据归属单位名称")
    private String fsdwsjgsdwmc;

    @Schema(description = "接收单位数据归属单位代码")
    private String jsdwsjgsdwdm;

    @Schema(description = "接收单位数据归属单位名称")
    private String jsdwsjgsdwmc;

    @StringDateTimeFormat
    @Schema(description = "入库时间")
    private String rksj;

    @Dict(CkDictTypeConstants.DM_SFBZ)
    @Schema(description = "户籍迁出标志")
    private String hjqcbz;

    @Schema(description = "审批业务受理号")
    private String spywslh;

    @StringDateTimeFormat
    @Schema(description = "审批业务受理时间")
    private String spywslsj;
}
