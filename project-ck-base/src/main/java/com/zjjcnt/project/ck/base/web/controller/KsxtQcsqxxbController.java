package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.KsxtQcsqxxbConvert;
import com.zjjcnt.project.ck.base.dto.KsxtQcsqxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtQcsqxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtQcsqxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtQcsqxxbViewResp;
import com.zjjcnt.project.ck.base.service.KsxtQcsqxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 跨省协同迁出申请信息表前端控制器
*
* <AUTHOR>
* @date 2025-08-05 13:59:15
*/
@RequiredArgsConstructor
@Tag(name = "跨省协同迁出申请信息表")
@RestController
@RequestMapping("/ksxtQcsqxxb")
public class KsxtQcsqxxbController extends AbstractCrudController<KsxtQcsqxxbDTO> {

    private final KsxtQcsqxxbService ksxtQcsqxxbService;

    @Override
    protected IBaseService<KsxtQcsqxxbDTO> getService() {
        return ksxtQcsqxxbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询跨省协同迁出申请信息表")
    public CommonResult<PageResult<KsxtQcsqxxbPageResp>> page(KsxtQcsqxxbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, KsxtQcsqxxbConvert.INSTANCE::convertToDTO, KsxtQcsqxxbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看跨省协同迁出申请信息表详情")
    public CommonResult<KsxtQcsqxxbViewResp> view(String id) {
        return super.view(id, KsxtQcsqxxbConvert.INSTANCE::convertToViewResp);
    }

}
