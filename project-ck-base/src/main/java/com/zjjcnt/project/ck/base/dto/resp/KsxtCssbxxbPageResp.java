package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.DictSsxq;
import com.zjjcnt.common.core.annotation.StringDateFormat;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 跨省协同出生申报信息表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 */
@Data
@Schema(description = "跨省协同出生申报信息表响应对象")
public class KsxtCssbxxbPageResp {

    @Schema(description = "跨省出生业务ID")
    private String ksxtcsid;

    @Schema(description = "跨省业务ID")
    private String ksxtid;

    @Schema(description = "治安管理业务协同编号")
    private String zaglywxtbh;

    @Schema(description = "姓名")
    private String xm;

    @Schema(description = "姓")
    private String x;

    @Schema(description = "名")
    private String m;

    @Dict(CkDictTypeConstants.DM_XB)
    @Schema(description = "性别")
    private String xbdm;

    @Dict(CkDictTypeConstants.DM_MZ)
    @Schema(description = "民族")
    private String mzdm;

    @StringDateFormat
    @Schema(description = "出生日期")
    private String csrq;

    @Schema(description = "出生时间")
    private String cssj;

    @Dict(CkDictTypeConstants.GJ)
    @Schema(description = "出生地_国家（地区）")
    private String csdgjhdqdm;

    @DictSsxq
    @Schema(description = "出生地_省市县（区）")
    private String csdssxqdm;

    @Schema(description = "出生地_区划内详细地址")
    private String csdqhnxxdz;

    @Dict(CkDictTypeConstants.GJ)
    @Schema(description = "籍贯_国家（地区）")
    private String jggjhdqdm;

    @DictSsxq
    @Schema(description = "籍贯_省市县（区）")
    private String jgssxqdm;

    @Schema(description = "籍贯_区划内详细地址")
    private String jgqhnxxdz;

    @Dict(CkDictTypeConstants.DM_XX)
    @Schema(description = "血型")
    private String xxdm;

    @Schema(description = "人口信息级别")
    private String rkxxjbdm;

    @Schema(description = "出生登记类别")
    private String csdjlbdm;

    @Schema(description = "出生证明编号")
    private String cszmbh;

    @Dict(CkDictTypeConstants.DM_JTGX)
    @Schema(description = "与户主关系")
    private String yhzgxdm;

    @Schema(description = "户主_姓名")
    private String hzxm;

    @Schema(description = "户主_公民身份号码")
    private String hzgmsfhm;

    @Schema(description = "父亲_公民身份号码")
    private String fqgmsfhm;

    @Schema(description = "父亲_姓名")
    private String fqxm;

    @Dict(CkDictTypeConstants.DM_CYZJ)
    @Schema(description = "父亲_证件种类")
    private String fqcyzjdm;

    @Schema(description = "父亲_证件号码")
    private String fqzjhm;

    @Schema(description = "母亲_公民身份号码")
    private String mqgmsfhm;

    @Schema(description = "母亲_姓名")
    private String mqxm;

    @Dict(CkDictTypeConstants.DM_CYZJ)
    @Schema(description = "母亲_证件种类")
    private String mqcyzjdm;

    @Schema(description = "母亲_证件号码")
    private String mqzjhm;

    @Schema(description = "监护人一_公民身份号码")
    private String jhrygmsfhm;

    @Schema(description = "监护人一_姓名")
    private String jhryxm;

    @Dict(CkDictTypeConstants.DM_CYZJ)
    @Schema(description = "监护人一_证件种类")
    private String jhrycyzjdm;

    @Schema(description = "监护人一_证件号码")
    private String jhryzjhm;

    @Schema(description = "监护人一_外文姓")
    private String jhrywwx;

    @Schema(description = "监护人一_外文名")
    private String jhrywwm;

    @Dict(CkDictTypeConstants.DM_JTGX)
    @Schema(description = "监护人一_监护关系")
    private String jhryjhgxdm;

    @Schema(description = "监护人一_联系电话")
    private String jhrylxdh;

    @Schema(description = "监护人二_公民身份号码")
    private String jhregmsfhm;

    @Schema(description = "监护人二_姓名")
    private String jhrexm;

    @Dict(CkDictTypeConstants.DM_CYZJ)
    @Schema(description = "监护人二_证件种类")
    private String jhrecyzjdm;

    @Schema(description = "监护人二_证件号码")
    private String jhrezjhm;

    @Schema(description = "监护人二_外文姓")
    private String jhrewwx;

    @Schema(description = "监护人二_外文名")
    private String jhrewwm;

    @Dict(CkDictTypeConstants.DM_JTGX)
    @Schema(description = "监护人二_监护关系")
    private String jhrejhgxdm;

    @Schema(description = "监护人二_联系电话")
    private String jhrelxdh;

    @Schema(description = "申报人_公民身份号码")
    private String sbrgmsfhm;

    @Schema(description = "申报人_姓名")
    private String sbrxm;

    @Schema(description = "申报人_联系电话")
    private String sbrlxdh;

    @Dict(CkDictTypeConstants.DM_JTGX)
    @Schema(description = "申报人_家庭关系")
    private String sbrycsrgxjtgxdm;

    @DictSsxq
    @Schema(description = "现居住地址_省市县（区）")
    private String xzzssxqdm;

    @Schema(description = "现居住地址_区划内详细地址")
    private String xzzqhnxxdz;

    @Schema(description = "受理地_公安机关机构代码")
    private String sldgajgjgdm;

    @Schema(description = "受理地_公安机关名称")
    private String sldgajgmc;

    @Schema(description = "受理地_联系电话")
    private String sldlxdh;

    @Schema(description = "受理人ID")
    private String slrid;

    @Schema(description = "受理人_姓名")
    private String slrxm;

    @StringDateTimeFormat
    @Schema(description = "受理时间")
    private String slsj;

    @Schema(description = "治安管理政务服务事项编码")
    private String zaglzwfwsxbm;

    @Schema(description = "治安管理业务类别代码")
    private String zaglywlbdm;

    @Schema(description = "归档配置版本号")
    private String gdpzbbh;

    @Schema(description = "业务协同区域范围代码")
    private String qyfwdm;

    @Schema(description = "受理地_数据归属单位代码")
    private String sldsjgsdwdm;

    @Schema(description = "受理地_数据归属单位名称")
    private String sldsjgsdwmc;

    @Schema(description = "户籍地_数据归属单位代码")
    private String hjdsjgsdwdm;

    @Schema(description = "户籍地_数据归属单位名称")
    private String hjdsjgsdwmc;

    @StringDateTimeFormat
    @Schema(description = "入库时间")
    private String rksj;

    @Dict(CkDictTypeConstants.DM_SFBZ)
    @Schema(description = "判断标志是否落户")
    private String pdbzsflh;

    @Dict(CkDictTypeConstants.DM_SFBZ)
    @Schema(description = "判断标志出生证明黑名单")
    private String pdbzcszmhmd;

    @Dict(CkDictTypeConstants.DM_GABCSZBDJGDM)
    @Schema(description = "比对结果")
    private String bdjg;

    @Schema(description = "比对描述")
    private String bdms;

    @StringDateTimeFormat
    @Schema(description = "比对时间")
    private String bdsj;

    @Schema(description = "比对人ID")
    private String bdrid;

    @Schema(description = "比对人姓名")
    private String bdrxm;

    @Schema(description = "比对人IP")
    private String bdrip;

}
