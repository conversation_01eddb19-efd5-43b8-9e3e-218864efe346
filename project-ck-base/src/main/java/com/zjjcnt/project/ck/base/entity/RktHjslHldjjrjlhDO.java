package com.zjjcnt.project.ck.base.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.Data;

import com.zjjcnt.common.core.entity.IdEntity;
import com.zjjcnt.common.core.annotation.Crypto;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import java.lang.String;
import com.zjjcnt.common.core.annotation.DictSsxq;
import java.lang.Integer;
import com.zjjcnt.common.core.annotation.StringDateFormat;

/**
 * 回来定居及入籍落户（国外、港澳台回来定居及加入中国国籍落户申请审批信息）DO
 *
 * <AUTHOR>
 * @date 2025-07-22 14:33:27
 * @see com.zjjcnt.project.ck.base.dto.RktHjslHldjjrjlhDTO
 */
@Crypto
@Data
@Table("rkt_hjsl_hldjjrjlh")
public class RktHjslHldjjrjlhDO implements IdEntity<String> {
    private static final long serialVersionUID = 2116392942723193557L;

    /**
     * 
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String ywslnbbh;

    /**
     * 户号内部ID
     */
    @Column(value = "hhnbid")
    private String hhnbid;

    /**
     * 
     */
    @Column(value = "ywslh")
    private String ywslh;

    /**
     * 户号
     */
    @Column(value = "hh")
    private String hh;

    /**
     * 户号ID
     */
    @Column(value = "hhid")
    private String hhid;

    /**
     * 户类型
     */
    @Column(value = "hlx")
    private String hlx;

    /**
     * 
     */
    @Column(value = "hmc")
    private String hmc;

    /**
     * 户籍地地址编码
     */
    @Column(value = "hjddzbm")
    private String hjddzbm;

    /**
     * 户籍地省市县区
     */
    @Column(value = "hjdssxq")
    private String hjdssxq;

    /**
     * 户籍地详细地址
     */
    @Column(value = "hjdxxdz")
    private String hjdxxdz;

    /**
     * 户籍地人户一致标识
     */
    @Column(value = "hjdrhyzbs")
    private String hjdrhyzbs;

    /**
     * 街路巷
     */
    @Column(value = "jlx")
    private String jlx;

    /**
     * 门（楼）牌号
     */
    @Column(value = "mlph")
    private String mlph;

    /**
     * 门（楼）详址
     */
    @Column(value = "mlxz")
    private String mlxz;

    /**
     * 金铖流程业务类型，扩展自D_ZAGLYWFL治安管理业务分类与代码
            0100	实有人口管理	
            0101	　户籍管理	
            0102	　居民身份证管理	
            
     */
    @Column(value = "lcywlx")
    private String lcywlx;

    /**
     * 流程定义ID，格式为 流程KEY：版本号：部署号
     */
    @Column(value = "lcdyid")
    private String lcdyid;

    /**
     * 
     */
    @Column(value = "lcmc")
    private String lcmc;

    /**
     * 
     */
    @Column(value = "lcslid")
    private String lcslid;

    /**
     * 关于**等几人落户的申请报告
     */
    @Column(value = "lcywbt")
    private String lcywbt;

    /**
     * 
     */
    @Column(value = "lcrwjd")
    private String lcrwjd;

    /**
     * 
            
     */
    @Column(value = "lczt")
    private String lczt;

    /**
     * 
     */
    @Column(value = "blzt")
    private String blzt;

    /**
     * 申请人_公民身份号码
     */
    @Crypto
    @Column(value = "sqrgmsfhm")
    private String sqrgmsfhm;

    /**
     * 申请人_姓名
     */
    @Crypto
    @Column(value = "sqrxm")
    private String sqrxm;

    /**
     * 
     */
    @Column(value = "sqrxb")
    private String sqrxb;

    /**
     * 
     */
    @Crypto
    @Column(value = "sqrlxdh")
    private String sqrlxdh;

    /**
     * 
     */
    @Column(value = "sqrybdrgx")
    private String sqrybdrgx;

    /**
     * 申请人住址省市县（区）
     */
    @Column(value = "sqrzzssxq")
    private String sqrzzssxq;

    /**
     * 申请人住址详址
     */
    @Column(value = "sqrzzxz")
    private String sqrzzxz;

    /**
     * 申请人户口登记机关
     */
    @Column(value = "sqrhkdjjg")
    private String sqrhkdjjg;

    /**
     * 
     */
    @Column(value = "sqrq")
    private String sqrq;

    /**
     * 
     */
    @Column(value = "spdwgajgjgdm")
    private String spdwgajgjgdm;

    /**
     * 
     */
    @Column(value = "spdwgajgmc")
    private String spdwgajgmc;

    /**
     * 
     */
    @Crypto
    @Column(value = "sprxm")
    private String sprxm;

    /**
     * 
     */
    @Column(value = "spsj")
    private String spsj;

    /**
     * 
     */
    @Column(value = "spjgdm")
    private String spjgdm;

    /**
     * 
     */
    @Column(value = "spyj")
    private String spyj;

    /**
     * 受理单位_公安机关机构代码
     */
    @Column(value = "sldwgajgjgdm")
    private String sldwgajgjgdm;

    /**
     * 受理单位_公安机关名称
     */
    @Column(value = "sldwgajgmc")
    private String sldwgajgmc;

    /**
     * 受理人姓名
     */
    @Crypto
    @Column(value = "slrxm")
    private String slrxm;

    /**
     * 受理时间
     */
    @Column(value = "slsj")
    private String slsj;

    /**
     * 数据归属单位代码
     */
    @Column(value = "sjgsdwdm")
    private String sjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "sjgsdwmc")
    private String sjgsdwmc;

    /**
     * 居（村）委会
     */
    @Column(value = "jcwh")
    private String jcwh;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 
     */
    @Column(value = "xh")
    private Integer xh;

    /**
     * 
     */
    @Column(value = "hkxz")
    private String hkxz;

    /**
     * 户别
     */
    @Column(value = "hb")
    private String hb;

    /**
     * 与户主关系
     */
    @Column(value = "yhzgx")
    private String yhzgx;

    /**
     * 城乡属性
     */
    @Column(value = "cxsx")
    private String cxsx;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 姓
     */
    @Column(value = "x")
    private String x;

    /**
     * 名
     */
    @Column(value = "m")
    private String m;

    /**
     * 曾用名
     */
    @Column(value = "cym")
    private String cym;

    /**
     * 姓名拼音
     */
    @Crypto
    @Column(value = "xmpy")
    private String xmpy;

    /**
     * 曾用名拼音
     */
    @Column(value = "cympy")
    private String cympy;

    /**
     * 性别
     */
    @Column(value = "xb")
    private String xb;

    /**
     * 民族
     */
    @Column(value = "mz")
    private String mz;

    /**
     * 籍贯国家（地区）
     */
    @Column(value = "jggjdq")
    private String jggjdq;

    /**
     * 籍贯省市县（区）
     */
    @Column(value = "jgssxq")
    private String jgssxq;

    /**
     * 籍贯详址
     */
    @Column(value = "jgxz")
    private String jgxz;

    /**
     * 出生日期
     */
    @Column(value = "csrq")
    private String csrq;

    /**
     * 出生时间
     */
    @Column(value = "cssj")
    private String cssj;

    /**
     * 出生地国家（地区）
     */
    @Column(value = "csdgjdq")
    private String csdgjdq;

    /**
     * 出生地省市县（区）
     */
    @Column(value = "csdssxq")
    private String csdssxq;

    /**
     * 出生地详址
     */
    @Column(value = "csdxz")
    private String csdxz;

    /**
     * 文化程度
     */
    @Column(value = "whcd")
    private String whcd;

    /**
     * 婚姻状况
     */
    @Column(value = "hyzk")
    private String hyzk;

    /**
     * 从业状况单位代码
     */
    @Column(value = "cyzkdwbm")
    private String cyzkdwbm;

    /**
     * 从业状况单位名称
     */
    @Column(value = "cyzkdwmc")
    private String cyzkdwmc;

    /**
     * 职业
     */
    @Column(value = "zy")
    private String zy;

    /**
     * 职业类别
     */
    @Column(value = "zylb")
    private String zylb;

    /**
     * 宗教信仰
     */
    @Column(value = "zjxy")
    private String zjxy;

    /**
     * 身高
     */
    @Column(value = "sg")
    private String sg;

    /**
     * 血型
     */
    @Column(value = "xx")
    private String xx;

    /**
     * 兵役状况
     */
    @Column(value = "byzk")
    private String byzk;

    /**
     * 信息级别
     */
    @Column(value = "xxjb")
    private String xxjb;

    /**
     * 
     */
    @Crypto
    @Column(value = "lxdh")
    private String lxdh;

    /**
     * 父亲姓名
     */
    @Crypto
    @Column(value = "fqxm")
    private String fqxm;

    /**
     * 父亲公民身份号码
     */
    @Crypto
    @Column(value = "fqgmsfhm")
    private String fqgmsfhm;

    /**
     * 父亲证件类别
     */
    @Column(value = "fqcyzjdm")
    private String fqcyzjdm;

    /**
     * 父亲证件号码
     */
    @Column(value = "fqzjhm")
    private String fqzjhm;

    /**
     * 父亲外文姓
     */
    @Column(value = "fqwwx")
    private String fqwwx;

    /**
     * 父亲外文名
     */
    @Column(value = "fqwwm")
    private String fqwwm;

    /**
     * 母亲姓名
     */
    @Crypto
    @Column(value = "mqxm")
    private String mqxm;

    /**
     * 母亲公民身份号码
     */
    @Crypto
    @Column(value = "mqgmsfhm")
    private String mqgmsfhm;

    /**
     * 母亲证件类别
     */
    @Column(value = "mqcyzjdm")
    private String mqcyzjdm;

    /**
     * 母亲证件号码
     */
    @Column(value = "mqzjhm")
    private String mqzjhm;

    /**
     * 母亲外文姓
     */
    @Column(value = "mqwwx")
    private String mqwwx;

    /**
     * 母亲外文名
     */
    @Column(value = "mqwwm")
    private String mqwwm;

    /**
     * 配偶姓名
     */
    @Crypto
    @Column(value = "poxm")
    private String poxm;

    /**
     * 配偶公民身份号码
     */
    @Crypto
    @Column(value = "pogmsfhm")
    private String pogmsfhm;

    /**
     * 配偶证件类别
     */
    @Column(value = "pocyzjdm")
    private String pocyzjdm;

    /**
     * 配偶证件号码
     */
    @Column(value = "pozjhm")
    private String pozjhm;

    /**
     * 配偶外文姓
     */
    @Column(value = "powwx")
    private String powwx;

    /**
     * 配偶外文名
     */
    @Column(value = "powwm")
    private String powwm;

    /**
     * 监护人一姓名
     */
    @Crypto
    @Column(value = "jhryxm")
    private String jhryxm;

    /**
     * 监护人一公民身份号码
     */
    @Crypto
    @Column(value = "jhrygmsfhm")
    private String jhrygmsfhm;

    /**
     * 监护人一监护关系
     */
    @Column(value = "jhryjhgx")
    private String jhryjhgx;

    /**
     * 监护人一证件类别
     */
    @Column(value = "jhrycyzjdm")
    private String jhrycyzjdm;

    /**
     * 监护人一证件号码
     */
    @Column(value = "jhryzjhm")
    private String jhryzjhm;

    /**
     * 监护人一外文姓
     */
    @Column(value = "jhrywwx")
    private String jhrywwx;

    /**
     * 监护人一外文名
     */
    @Column(value = "jhrywwm")
    private String jhrywwm;

    /**
     * 监护人一联系电话
     */
    @Crypto
    @Column(value = "jhrylxdh")
    private String jhrylxdh;

    /**
     * 监护人二姓名
     */
    @Crypto
    @Column(value = "jhrexm")
    private String jhrexm;

    /**
     * 监护人二公民身份号码
     */
    @Crypto
    @Column(value = "jhregmsfhm")
    private String jhregmsfhm;

    /**
     * 监护人二监护关系
     */
    @Column(value = "jhrejhgx")
    private String jhrejhgx;

    /**
     * 监护人二证件类别
     */
    @Column(value = "jhrecyzjdm")
    private String jhrecyzjdm;

    /**
     * 监护人二证件号码
     */
    @Column(value = "jhrezjhm")
    private String jhrezjhm;

    /**
     * 监护人二外文姓
     */
    @Column(value = "jhrewwx")
    private String jhrewwx;

    /**
     * 监护人二外文名
     */
    @Column(value = "jhrewwm")
    private String jhrewwm;

    /**
     * 监护人二联系电话
     */
    @Crypto
    @Column(value = "jhrelxdh")
    private String jhrelxdh;

    /**
     * 证件类别
     */
    @Column(value = "zjlb")
    private String zjlb;

    /**
     * 签发机关
     */
    @Column(value = "qfjg")
    private String qfjg;

    /**
     * 有效期限起始日期
     */
    @Column(value = "yxqxqsrq")
    private String yxqxqsrq;

    /**
     * 有效期限截止日期
     */
    @Column(value = "yxqxjzrq")
    private String yxqxjzrq;

    /**
     * 内部标识符：DE00559
            　中文名称：回国定居及入籍人员落户类别代码
            　中文全拼：hui-guo-ding-ju-ji-ru-ji-ren-yuan-luo-hu-lei-bie-dai-ma
            　　标识符：HGDJJRJRYLHLBDM
            　　　版本：1.0
            　同义名称：
            　　　说明：国外、港澳台回来定居及加入中国国籍人员来本地落户的类别代码
            　对象类词：人
            　　特性词：落户类别
            　　表示词：代码
            　数据类型：字符型
            　表示格式：c3
            　　　值域：采用GA/T XXXX《人口迁移（流动）原因代码》的172-176、203
            
     */
    @Column(value = "hgdjjrjrylhlb")
    private String hgdjjrjrylhlb;

    /**
     * 内部标识符：DE00559
            　中文名称：回国定居及入籍人员落户类别代码
            　中文全拼：hui-guo-ding-ju-ji-ru-ji-ren-yuan-luo-hu-lei-bie-dai-ma
            　　标识符：HGDJJRJRYLHLBDM
            　　　版本：1.0
            　同义名称：
            　　　说明：国外、港澳台回来定居及加入中国国籍人员来本地落户的类别代码
            　对象类词：人
            　　特性词：落户类别
            　　表示词：代码
            　数据类型：字符型
            　表示格式：c3
            　　　值域：采用GA/T XXXX《人口迁移（流动）原因代码》的172-176、203
            
     */
    @Column(value = "hgdjjrjrylhlbdm")
    private String hgdjjrjrylhlbdm;

    /**
     * 
     */
    @Column(value = "lzdgjdq")
    private String lzdgjdq;

    /**
     * 
     */
    @Column(value = "lzdxz")
    private String lzdxz;

    /**
     * 变动范围
     */
    @Column(value = "bdfw")
    private String bdfw;

    /**
     * 原持有证件种类
     */
    @Column(value = "ycyzjzl")
    private String ycyzjzl;

    /**
     * 原持有证件号码
     */
    @Column(value = "ycyzjhm")
    private String ycyzjhm;

    /**
     * 办理户籍业务id
     */
    @Column(value = "blhjywid")
    private String blhjywid;

    /**
     * 户籍业务办理时间
     */
    @Column(value = "hjywblsj")
    private String hjywblsj;

    /**
     * 其他省市县（区）
     */
    @Column(value = "qtssxq")
    private String qtssxq;

    /**
     * 其他住址
     */
    @Column(value = "qtzz")
    private String qtzz;

    /**
     * 出国前姓名
     */
    @Crypto
    @Column(value = "cgqxm")
    private String cgqxm;

    /**
     * 出国前公民身份号码
     */
    @Crypto
    @Column(value = "cgqgmsfhm")
    private String cgqgmsfhm;

    /**
     * 出国前出生日期
     */
    @Column(value = "cgqcsrq")
    private String cgqcsrq;

    /**
     * 出国前性别
     */
    @Column(value = "cgqxb")
    private String cgqxb;

    /**
     * 出国前民族
     */
    @Column(value = "cgqmz")
    private String cgqmz;

    /**
     * 出国前户口省县
     */
    @Column(value = "cgqhksx")
    private String cgqhksx;

    /**
     * 出国前户口详址
     */
    @Column(value = "cgqhkxz")
    private String cgqhkxz;

    /**
     * 出国前证件签发机关
     */
    @Column(value = "cgqzjqfjg")
    private String cgqzjqfjg;

    /**
     * 出国前证件签发日期
     */
    @Column(value = "cgqzjqfrq")
    private String cgqzjqfrq;

    /**
     * 出国前备注信息
     */
    @Column(value = "cgqbzxx")
    private String cgqbzxx;


    @Override
    public String getId() {
        return this.ywslnbbh;
    }

    @Override
    public void setId(String id) {
        this.ywslnbbh = id;
    }
}
