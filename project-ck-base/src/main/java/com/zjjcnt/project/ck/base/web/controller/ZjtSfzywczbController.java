package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.ZjtSfzywczbConvert;
import com.zjjcnt.project.ck.base.dto.ZjtSfzywczbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtSfzywczbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSfzywczbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtSfzywczbViewResp;
import com.zjjcnt.project.ck.base.service.ZjtSfzywczbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 身份证业务操作表前端控制器
 *
 * <AUTHOR>
 * @date 2025-08-06 10:19:05
 */
@RequiredArgsConstructor
@Tag(name = "身份证业务操作表")
@RestController
@RequestMapping("/zjtSfzywczb")
public class ZjtSfzywczbController extends AbstractCrudController<ZjtSfzywczbDTO> {

    private final ZjtSfzywczbService zjtSfzywczbService;

    @Override
    protected IBaseService<ZjtSfzywczbDTO> getService() {
        return zjtSfzywczbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询身份证业务操作表")
    public CommonResult<PageResult<ZjtSfzywczbPageResp>> page(ZjtSfzywczbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, ZjtSfzywczbConvert.INSTANCE::convertToDTO, ZjtSfzywczbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看身份证业务操作表详情")
    public CommonResult<ZjtSfzywczbViewResp> view(String id) {
        return super.view(id, ZjtSfzywczbConvert.INSTANCE::convertToViewResp);
    }

}
