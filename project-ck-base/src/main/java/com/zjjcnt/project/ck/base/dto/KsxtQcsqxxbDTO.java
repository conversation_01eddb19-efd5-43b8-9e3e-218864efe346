package com.zjjcnt.project.ck.base.dto;

import com.zjjcnt.common.core.dto.BaseDTO;
import lombok.Data;

import java.io.Serial;

/**
 * 跨省协同迁出申请信息表DTO
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 * @see com.zjjcnt.project.ck.base.entity.KsxtQcsqxxbDO
 */
@Data
public class KsxtQcsqxxbDTO implements BaseDTO<String> {
    @Serial
    private static final long serialVersionUID = 6491678295259727134L;

    /**
     * 跨省协同迁出申请ID
     */
    private String ksxtqcsqid;

    /**
     * 跨省协同ID
     */
    private String ksxtid;

    /**
     * 治安管理业务协同编号
     */
    private String zaglywxtbh;

    /**
     * 治安管理业务类别代码
     */
    private String zaglywlbdm;

    /**
     * 治安管理政务服务事项编码
     */
    private String zaglzwfwsxbm;

    /**
     * 申请人公民身份号码
     */
    private String sqrgmsfhm;

    /**
     * 申请人姓名
     */
    private String sqrxm;

    /**
     * 申请人联系电话
     */
    private String sqrlxdh;

    /**
     * 申请人住址省市县（区）
     */
    private String sqrzzssxqdm;

    /**
     * 申请人住址区划内详细地址
     */
    private String sqrzzqhnxxdz;

    /**
     * 申请人户口登记机关公安机关机构代码
     */
    private String sqrhkdjjggajgjgdm;

    /**
     * 申请人户口登记机关公安机关名称
     */
    private String sqrhkdjjggajgmc;

    /**
     * 迁出地省市县（区）
     */
    private String qcdssxqdm;

    /**
     * 迁出地区划内详细地址
     */
    private String qcdqhnxxdz;

    /**
     * 迁出地户口登记机关公安机关机构代码
     */
    private String qcdhkdjjggajgjgdm;

    /**
     * 迁出地户口登记机关公安机关名称
     */
    private String qcdhkdjjggajgmc;

    /**
     * 迁出地数据归属单位代码
     */
    private String qcdsjgsdwdm;

    /**
     * 迁出地数据归属单位名称
     */
    private String qcdsjgsdwmc;

    /**
     * 迁入地省市县（区）
     */
    private String qrdssxqdm;

    /**
     * 迁入地区划内详细地址
     */
    private String qrdqhnxxdz;

    /**
     * 迁入地户口登记机关公安机关机构代码
     */
    private String qrdhkdjjggajgjgdm;

    /**
     * 迁入地户口登记机关公安机关名称
     */
    private String qrdhkdjjggajgmc;

    /**
     * 准迁证编号
     */
    private String zqzbh;

    /**
     * 准迁证_签发机关公安机关机构代码
     */
    private String zqzqfjggajgjgdm;

    /**
     * 准迁证_签发机关公安机关名称
     */
    private String zqzqfjggajgmc;

    /**
     * 准迁证_签发日期
     */
    private String zqzqfrq;

    /**
     * 准迁证_有效期截止日期
     */
    private String zqzyxqjzrq;

    /**
     * 电子准迁_证照标识
     */
    private String dzzqdzzzbz;

    /**
     * 电子准迁_治安管理电子证照编号
     */
    private String dzzqzagldzzzbh;

    /**
     * 备注
     */
    private String bz;

    /**
     * 迁移（流动）原因
     */
    private String qyldyydm;

    /**
     * 与申请人关系_家庭关系
     */
    private String ysqrgxjtgxdm;

    /**
     * 公民身份号码
     */
    private String gmsfhm;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xbdm;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 受理地公安机关机构代码
     */
    private String sldgajgjgdm;

    /**
     * 受理地公安机关名称
     */
    private String sldgajgmc;

    /**
     * 受理地数据归属单位代码
     */
    private String sldsjgsdwdm;

    /**
     * 受理地数据归属单位名称
     */
    private String sldsjgsdwmc;

    /**
     * 受理地联系电话
     */
    private String sldlxdh;

    /**
     * 受理人姓名
     */
    private String slrxm;

    /**
     * 受理时间
     */
    private String slsj;

    /**
     * 归档配置版本号
     */
    private String gdpzbbh;

    /**
     * 区域范围代码
     */
    private String qyfwdm;

    /**
     * 发送单位数据归属单位代码
     */
    private String fsdwsjgsdwdm;

    /**
     * 发送单位数据归属单位名称
     */
    private String fsdwsjgsdwmc;

    /**
     * 接收单位数据归属单位代码
     */
    private String jsdwsjgsdwdm;

    /**
     * 接收单位数据归属单位名称
     */
    private String jsdwsjgsdwmc;

    /**
     * 入库时间
     */
    private String rksj;

    /**
     * 户籍迁出标志
     */
    private String hjqcbz;

    /**
     * 审批业务受理号
     */
    private String spywslh;

    /**
     * 审批业务受理时间
     */
    private String spywslsj;

    // ------ 非数据库表字段 -------
    /**
     * 大于等于准迁证_签发日期
     */
    private String zqzqfrqStart;

    /**
     * 小于等于准迁证_签发日期
     */
    private String zqzqfrqEnd;

    /**
     * 大于等于准迁证_有效期截止日期
     */
    private String zqzyxqjzrqStart;

    /**
     * 小于等于准迁证_有效期截止日期
     */
    private String zqzyxqjzrqEnd;

    /**
     * 大于等于出生日期
     */
    private String csrqStart;

    /**
     * 小于等于出生日期
     */
    private String csrqEnd;

    /**
     * 大于等于受理时间
     */
    private String slsjStart;

    /**
     * 小于等于受理时间
     */
    private String slsjEnd;

    /**
     * 大于等于入库时间
     */
    private String rksjStart;

    /**
     * 小于等于入库时间
     */
    private String rksjEnd;

    /**
     * 大于等于审批业务受理时间
     */
    private String spywslsjStart;

    /**
     * 小于等于审批业务受理时间
     */
    private String spywslsjEnd;


    @Override
    public String getId() {
        return this.ksxtqcsqid;
    }

    @Override
    public void setId(String id) {
        this.ksxtqcsqid = id;
    }
}
