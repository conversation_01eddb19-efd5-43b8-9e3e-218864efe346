package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.DictSsxq;
import com.zjjcnt.common.core.annotation.StringDateFormat;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 办证收费流水表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-06 14:38:16
 */
@Data
@Schema(description = "办证收费流水表响应对象")
public class ZjtBzsflsbPageResp {

    @Schema(description = "收费流水id")
    private String sflsid;

    @Schema(description = "内部受理ID")
    private String nbslid;

    @Schema(description = "业务受理号")
    private String ywslh;

    @Schema(description = "身份证受理号")
    private String slh;

    @Schema(description = "人员ID")
    private String ryid;

    @Schema(description = "人员内部id")
    private String rynbid;

    @Schema(description = "公民身份号码")
    private String gmsfhm;

    @Schema(description = "姓名")
    private String xm;

    @Dict(CkDictTypeConstants.DM_EDZSLYY)
    @Schema(description = "申领原因")
    private String slyy;

    @Dict(CkDictTypeConstants.DM_EDZSLZT)
    @Schema(description = "受理状态")
    private String slzt;

    @Schema(description = "收费金额")
    private BigDecimal sfje;

    @Schema(description = "签发机关")
    private String qfjg;

    @StringDateFormat
    @Schema(description = "有效期限起始日期")
    private String yxqxqsrq;

    @StringDateFormat
    @Schema(description = "有效期限截止日期")
    private String yxqxjzrq;

    @Dict(CkDictTypeConstants.DM_EDZSFLX)
    @Schema(description = "收费类型")
    private String sflx;

    @Dict(CkDictTypeConstants.DM_SLFS)
    @Schema(description = "受理方式")
    private String slfs;

    @Schema(description = "数据归属单位代码")
    private String hjdsjgsdwdm;

    @DictSsxq
    @Schema(description = "省市县（区）")
    private String ssxq;

    @Schema(description = "数据归属单位名称")
    private String hjdsjgsdwmc;

    @Schema(description = "数据归属单位代码")
    private String sldsjgsdwdm;

    @Schema(description = "数据归属单位名称")
    private String sldsjgsdwmc;

    @Schema(description = "受理地分局数据归属单位代码")
    private String sldfjsjgsdwdm;

    @Schema(description = "受理地分局数据归属单位名称")
    private String sldfjsjgsdwmc;

    @StringDateTimeFormat
    @Schema(description = "操作时间")
    private String czsj;

    @Schema(description = "操作员ID")
    private String czyid;

    @Schema(description = "操作员姓名")
    private String czyxm;

    @Schema(description = "单位代码")
    private String dwdm;

    @Schema(description = "操作员单位名称")
    private String czydwmc;

    @Schema(description = "收费单据号")
    private String sfdjh;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "总分成金额")
    private BigDecimal zfcje;

    @Schema(description = "区县分成金额")
    private BigDecimal qxfcje;

    @Schema(description = "地市分成金额")
    private BigDecimal dsfcje;

    @Schema(description = "中心分成金额")
    private BigDecimal zxfcje;
}
