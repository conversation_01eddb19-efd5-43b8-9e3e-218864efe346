package com.zjjcnt.project.ck.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.base.constant.CkBaseConstants;
import com.zjjcnt.project.ck.base.constant.ZjtBzsflsbType;
import com.zjjcnt.project.ck.base.convert.RktYwslClysjConvert;
import com.zjjcnt.project.ck.base.convert.ZjtSlxxbConvert;
import com.zjjcnt.project.ck.base.convert.ZjtZwcjbConvert;
import com.zjjcnt.project.ck.base.dto.*;
import com.zjjcnt.project.ck.base.dto.req.ZjtSlxxbApplyLtzReq;
import com.zjjcnt.project.ck.base.entity.ZjtSlxxbDO;
import com.zjjcnt.project.ck.base.exception.CkBaseErrorCode;
import com.zjjcnt.project.ck.base.manager.CkhlwGaythShManager;
import com.zjjcnt.project.ck.base.mapper.ZjtSlxxbMapper;
import com.zjjcnt.project.ck.base.service.*;
import com.zjjcnt.project.ck.core.util.DwUtils;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.constant.CkConstants;
import com.zjjcnt.project.ck.sysadmin.constant.SysadminConstants;
import com.zjjcnt.project.ck.sysadmin.constant.ZjConstant;
import com.zjjcnt.project.ck.sysadmin.dto.RktYwslClysjDTO;
import com.zjjcnt.project.ck.sysadmin.dto.XtDwxxbDTO;
import com.zjjcnt.project.ck.sysadmin.dto.XtYhsjfwbDTO;
import com.zjjcnt.project.ck.sysadmin.file.RktYwslClysjFileManager;
import com.zjjcnt.project.ck.sysadmin.manager.XtywqxManager;
import com.zjjcnt.project.ck.sysadmin.manager.ZjSlhGenerator;
import com.zjjcnt.project.ck.sysadmin.service.XtDwxxbService;
import com.zjjcnt.project.ck.sysadmin.service.XtYhsjfwbService;
import com.zjjcnt.project.ck.sysadmin.utils.CkCommonUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

/**
 * 居民身份证受理信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@RequiredArgsConstructor
@Service
public class ZjtSlxxbServiceImpl extends AbstractBaseServiceImpl<ZjtSlxxbMapper, ZjtSlxxbDO, ZjtSlxxbDTO> implements ZjtSlxxbService {

    ZjtSlxxbConvert convert = ZjtSlxxbConvert.INSTANCE;

    private final ZjtSqxxbService zjtSqxxbService;
    private final ZjSlhGenerator zjSlhGenerator;
    private final CkhlwHjsqJbService ckhlwHjsqJbService;
    private final HjxxZpytbService hjxxZpytbService;
    private final HjxxZplsbService hjxxZplsbService;
    private final ZjtRxxxbService zjtRxxxbService;
    private final ZjtSlxxbLsService zjtSlxxbLsService;
    private final ZjywSlxxbService zjywSlxxbService;
    private final ZjtZwcjlsbService zwcjlsbService;
    private final ZjtZwcjbService zjtZwcjbService;
    private final ZjtLxSlxxbService zjtLxSlxxbService;
    private final ZjtBzsflsbService zjtBzsflsbService;
    private final ZjtSfzywczbService zjtSfzywczbService;
    private final ZjtFyzflsbService zjtFyzflsbService;
    private final XtYhsjfwbService xtYhsjfwbService;
    private final XtywqxManager xtywqxManager;
    private final HjxxDhhmxxbService hjxxDhhmxxbService;
    private final CkhlwGaythShManager ckhlwGaythShManager;
    private final XtDwxxbService xtDwxxbService;
    private final RktYwslClysjFileManager rktYwslClysjFileManager;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtSlxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtSlxxbDO::getYwslh, dto.getYwslh(), StringUtils.isNotEmpty(dto.getYwslh()));
        query.eq(ZjtSlxxbDO::getNbsfzid, dto.getNbsfzid(), StringUtils.isNotEmpty(dto.getNbsfzid()));
        query.eq(ZjtSlxxbDO::getZpid, dto.getZpid(), StringUtils.isNotEmpty(dto.getZpid()));
        query.eq(ZjtSlxxbDO::getSlh, dto.getSlh(), StringUtils.isNotEmpty(dto.getSlh()));
        query.eq(ZjtSlxxbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtSlxxbDO::getRynbid, dto.getRynbid(), StringUtils.isNotEmpty(dto.getRynbid()));
        query.eq(ZjtSlxxbDO::getQfjg, dto.getQfjg(), StringUtils.isNotEmpty(dto.getQfjg()));
        query.ge(ZjtSlxxbDO::getYxqxqsrq, dto.getYxqxqsrqStart(), StringUtils.isNotEmpty(dto.getYxqxqsrqStart()));
        query.le(ZjtSlxxbDO::getYxqxqsrq, dto.getYxqxqsrqEnd(), StringUtils.isNotEmpty(dto.getYxqxqsrqEnd()));
        query.ge(ZjtSlxxbDO::getYxqxjzrq, dto.getYxqxjzrqStart(), StringUtils.isNotEmpty(dto.getYxqxjzrqStart()));
        query.le(ZjtSlxxbDO::getYxqxjzrq, dto.getYxqxjzrqEnd(), StringUtils.isNotEmpty(dto.getYxqxjzrqEnd()));
        query.eq(ZjtSlxxbDO::getZz, dto.getZz(), StringUtils.isNotEmpty(dto.getZz()));
        query.eq(ZjtSlxxbDO::getHjdzqhnxxdz, dto.getHjdzqhnxxdz(), StringUtils.isNotEmpty(dto.getHjdzqhnxxdz()));
        query.eq(ZjtSlxxbDO::getSlyy, dto.getSlyy(), StringUtils.isNotEmpty(dto.getSlyy()));
        query.eq(ZjtSlxxbDO::getZzlx, dto.getZzlx(), StringUtils.isNotEmpty(dto.getZzlx()));
        query.eq(ZjtSlxxbDO::getLqfs, dto.getLqfs(), StringUtils.isNotEmpty(dto.getLqfs()));
        query.eq(ZjtSlxxbDO::getSflx, dto.getSflx(), StringUtils.isNotEmpty(dto.getSflx()));
        query.eq(ZjtSlxxbDO::getSfje, dto.getSfje(), Objects.nonNull(dto.getSfje()));
        query.eq(ZjtSlxxbDO::getSjblsh, dto.getSjblsh(), StringUtils.isNotEmpty(dto.getSjblsh()));
        query.eq(ZjtSlxxbDO::getSlzt, dto.getSlzt(), StringUtils.isNotEmpty(dto.getSlzt()));
        query.eq(ZjtSlxxbDO::getTbbz, dto.getTbbz(), Objects.nonNull(dto.getTbbz()));
        query.eq(ZjtSlxxbDO::getQyfwdm, dto.getQyfwdm(), StringUtils.isNotEmpty(dto.getQyfwdm()));
        query.eq(ZjtSlxxbDO::getSyzbz, dto.getSyzbz(), StringUtils.isNotEmpty(dto.getSyzbz()));
        query.eq(ZjtSlxxbDO::getDzzdbz, dto.getDzzdbz(), StringUtils.isNotEmpty(dto.getDzzdbz()));
        query.eq(ZjtSlxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtSlxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtSlxxbDO::getXb, dto.getXb(), StringUtils.isNotEmpty(dto.getXb()));
        query.eq(ZjtSlxxbDO::getMz, dto.getMz(), StringUtils.isNotEmpty(dto.getMz()));
        query.eq(ZjtSlxxbDO::getMzfjxdm, dto.getMzfjxdm(), StringUtils.isNotEmpty(dto.getMzfjxdm()));
        query.ge(ZjtSlxxbDO::getCsrq, dto.getCsrqStart(), StringUtils.isNotEmpty(dto.getCsrqStart()));
        query.le(ZjtSlxxbDO::getCsrq, dto.getCsrqEnd(), StringUtils.isNotEmpty(dto.getCsrqEnd()));
        query.likeLeft(ZjtSlxxbDO::getCsdssxq, dto.getCsdssxq(), StringUtils.isNotEmpty(dto.getCsdssxq()));
        query.eq(ZjtSlxxbDO::getMlpnbid, dto.getMlpnbid(), StringUtils.isNotEmpty(dto.getMlpnbid()));
        query.likeLeft(ZjtSlxxbDO::getSsxq, dto.getSsxq(), StringUtils.isNotEmpty(dto.getSsxq()));
        query.eq(ZjtSlxxbDO::getJlx, dto.getJlx(), StringUtils.isNotEmpty(dto.getJlx()));
        query.eq(ZjtSlxxbDO::getMlph, dto.getMlph(), StringUtils.isNotEmpty(dto.getMlph()));
        query.eq(ZjtSlxxbDO::getMlxz, dto.getMlxz(), StringUtils.isNotEmpty(dto.getMlxz()));
        query.likeLeft(ZjtSlxxbDO::getPcs, dto.getPcs(), StringUtils.isNotEmpty(dto.getPcs()));
        query.eq(ZjtSlxxbDO::getZrq, dto.getZrq(), StringUtils.isNotEmpty(dto.getZrq()));
        query.eq(ZjtSlxxbDO::getXzjd, dto.getXzjd(), StringUtils.isNotEmpty(dto.getXzjd()));
        query.eq(ZjtSlxxbDO::getJcwh, dto.getJcwh(), StringUtils.isNotEmpty(dto.getJcwh()));
        query.eq(ZjtSlxxbDO::getPxh, dto.getPxh(), StringUtils.isNotEmpty(dto.getPxh()));
        query.eq(ZjtSlxxbDO::getYwbz, dto.getYwbz(), StringUtils.isNotEmpty(dto.getYwbz()));
        query.eq(ZjtSlxxbDO::getCzyid, dto.getCzyid(), StringUtils.isNotEmpty(dto.getCzyid()));
        query.ge(ZjtSlxxbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(ZjtSlxxbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(ZjtSlxxbDO::getDwdm, dto.getDwdm(), StringUtils.isNotEmpty(dto.getDwdm()));
        query.eq(ZjtSlxxbDO::getSjrxm, ColumnUtils.encryptColumn(dto.getSjrxm()), StringUtils.isNotEmpty(dto.getSjrxm()));
        query.eq(ZjtSlxxbDO::getSjrlxdh, ColumnUtils.encryptColumn(dto.getSjrlxdh()), StringUtils.isNotEmpty(dto.getSjrlxdh()));
        query.eq(ZjtSlxxbDO::getSjryb, dto.getSjryb(), StringUtils.isNotEmpty(dto.getSjryb()));
        query.likeLeft(ZjtSlxxbDO::getSjrssxq, dto.getSjrssxq(), StringUtils.isNotEmpty(dto.getSjrssxq()));
        query.eq(ZjtSlxxbDO::getSjrxz, dto.getSjrxz(), StringUtils.isNotEmpty(dto.getSjrxz()));
        query.eq(ZjtSlxxbDO::getSjrtxdz, dto.getSjrtxdz(), StringUtils.isNotEmpty(dto.getSjrtxdz()));
        query.eq(ZjtSlxxbDO::getZzxxcwlb, dto.getZzxxcwlb(), StringUtils.isNotEmpty(dto.getZzxxcwlb()));
        query.eq(ZjtSlxxbDO::getCwms, dto.getCwms(), StringUtils.isNotEmpty(dto.getCwms()));
        query.eq(ZjtSlxxbDO::getJydw, dto.getJydw(), StringUtils.isNotEmpty(dto.getJydw()));
        query.eq(ZjtSlxxbDO::getJyrxm, ColumnUtils.encryptColumn(dto.getJyrxm()), StringUtils.isNotEmpty(dto.getJyrxm()));
        query.ge(ZjtSlxxbDO::getJyrq, dto.getJyrqStart(), StringUtils.isNotEmpty(dto.getJyrqStart()));
        query.le(ZjtSlxxbDO::getJyrq, dto.getJyrqEnd(), StringUtils.isNotEmpty(dto.getJyrqEnd()));
        query.eq(ZjtSlxxbDO::getCldw, dto.getCldw(), StringUtils.isNotEmpty(dto.getCldw()));
        query.eq(ZjtSlxxbDO::getClqk, dto.getClqk(), StringUtils.isNotEmpty(dto.getClqk()));
        query.ge(ZjtSlxxbDO::getClrq, dto.getClrqStart(), StringUtils.isNotEmpty(dto.getClrqStart()));
        query.le(ZjtSlxxbDO::getClrq, dto.getClrqEnd(), StringUtils.isNotEmpty(dto.getClrqEnd()));
        query.eq(ZjtSlxxbDO::getZlhkzt, dto.getZlhkzt(), StringUtils.isNotEmpty(dto.getZlhkzt()));
        query.ge(ZjtSlxxbDO::getHksj, dto.getHksjStart(), StringUtils.isNotEmpty(dto.getHksjStart()));
        query.le(ZjtSlxxbDO::getHksj, dto.getHksjEnd(), StringUtils.isNotEmpty(dto.getHksjEnd()));
        query.eq(ZjtSlxxbDO::getBwbha, dto.getBwbha(), StringUtils.isNotEmpty(dto.getBwbha()));
        query.eq(ZjtSlxxbDO::getBwbhb, dto.getBwbhb(), StringUtils.isNotEmpty(dto.getBwbhb()));
        query.ge(ZjtSlxxbDO::getShrq, dto.getShrqStart(), StringUtils.isNotEmpty(dto.getShrqStart()));
        query.le(ZjtSlxxbDO::getShrq, dto.getShrqEnd(), StringUtils.isNotEmpty(dto.getShrqEnd()));
        query.ge(ZjtSlxxbDO::getStjssj, dto.getStjssjStart(), StringUtils.isNotEmpty(dto.getStjssjStart()));
        query.le(ZjtSlxxbDO::getStjssj, dto.getStjssjEnd(), StringUtils.isNotEmpty(dto.getStjssjEnd()));
        query.eq(ZjtSlxxbDO::getBwbhc, dto.getBwbhc(), StringUtils.isNotEmpty(dto.getBwbhc()));
        query.eq(ZjtSlxxbDO::getFjpch, dto.getFjpch(), StringUtils.isNotEmpty(dto.getFjpch()));
        query.eq(ZjtSlxxbDO::getRlbdid, dto.getRlbdid(), StringUtils.isNotEmpty(dto.getRlbdid()));
        query.eq(ZjtSlxxbDO::getRlbdbz, dto.getRlbdbz(), StringUtils.isNotEmpty(dto.getRlbdbz()));
        query.ge(ZjtSlxxbDO::getRlbdsj, dto.getRlbdsjStart(), StringUtils.isNotEmpty(dto.getRlbdsjStart()));
        query.le(ZjtSlxxbDO::getRlbdsj, dto.getRlbdsjEnd(), StringUtils.isNotEmpty(dto.getRlbdsjEnd()));
        query.eq(ZjtSlxxbDO::getZwyzw, dto.getZwyzw(), StringUtils.isNotEmpty(dto.getZwyzw()));
        query.eq(ZjtSlxxbDO::getZwyzcjg, dto.getZwyzcjg(), StringUtils.isNotEmpty(dto.getZwyzcjg()));
        query.eq(ZjtSlxxbDO::getZwezw, dto.getZwezw(), StringUtils.isNotEmpty(dto.getZwezw()));
        query.eq(ZjtSlxxbDO::getZwezcjg, dto.getZwezcjg(), StringUtils.isNotEmpty(dto.getZwezcjg()));
        query.eq(ZjtSlxxbDO::getZwcjjgdm, dto.getZwcjjgdm(), StringUtils.isNotEmpty(dto.getZwcjjgdm()));
        query.eq(ZjtSlxxbDO::getSzyczkdm, dto.getSzyczkdm(), StringUtils.isNotEmpty(dto.getSzyczkdm()));
        query.eq(ZjtSlxxbDO::getSfzwzj, dto.getSfzwzj(), StringUtils.isNotEmpty(dto.getSfzwzj()));
        query.eq(ZjtSlxxbDO::getDztbbz, dto.getDztbbz(), Objects.nonNull(dto.getDztbbz()));
        query.ge(ZjtSlxxbDO::getDztbsj, dto.getDztbsjStart(), StringUtils.isNotEmpty(dto.getDztbsjStart()));
        query.le(ZjtSlxxbDO::getDztbsj, dto.getDztbsjEnd(), StringUtils.isNotEmpty(dto.getDztbsjEnd()));
        query.eq(ZjtSlxxbDO::getDzsjbbh, dto.getDzsjbbh(), StringUtils.isNotEmpty(dto.getDzsjbbh()));
        query.eq(ZjtSlxxbDO::getSlfs, dto.getSlfs(), StringUtils.isNotEmpty(dto.getSlfs()));
        query.eq(ZjtSlxxbDO::getSlfsx, dto.getSlfsx(), StringUtils.isNotEmpty(dto.getSlfsx()));
        query.eq(ZjtSlxxbDO::getZwtxid, dto.getZwtxid(), StringUtils.isNotEmpty(dto.getZwtxid()));
        query.eq(ZjtSlxxbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(ZjtSlxxbDO::getHjddzbm, dto.getHjddzbm(), StringUtils.isNotEmpty(dto.getHjddzbm()));
        query.eq(ZjtSlxxbDO::getLssfzslbz, dto.getLssfzslbz(), StringUtils.isNotEmpty(dto.getLssfzslbz()));
        query.eq(ZjtSlxxbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(ZjtSlxxbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(ZjtSlxxbDO::getCzydwmc, dto.getCzydwmc(), StringUtils.isNotEmpty(dto.getCzydwmc()));
        query.likeLeft(ZjtSlxxbDO::getZjzdssxq, dto.getZjzdssxq(), StringUtils.isNotEmpty(dto.getZjzdssxq()));
        query.eq(ZjtSlxxbDO::getZjzdxz, dto.getZjzdxz(), StringUtils.isNotEmpty(dto.getZjzdxz()));
        query.ge(ZjtSlxxbDO::getLqrq, dto.getLqrqStart(), StringUtils.isNotEmpty(dto.getLqrqStart()));
        query.le(ZjtSlxxbDO::getLqrq, dto.getLqrqEnd(), StringUtils.isNotEmpty(dto.getLqrqEnd()));
        query.eq(ZjtSlxxbDO::getLqrxm, ColumnUtils.encryptColumn(dto.getLqrxm()), StringUtils.isNotEmpty(dto.getLqrxm()));
        query.eq(ZjtSlxxbDO::getLqrsfhm, dto.getLqrsfhm(), StringUtils.isNotEmpty(dto.getLqrsfhm()));
        query.eq(ZjtSlxxbDO::getLqrzpid, dto.getLqrzpid(), StringUtils.isNotEmpty(dto.getLqrzpid()));
        query.ge(ZjtSlxxbDO::getZjddrq, dto.getZjddrqStart(), StringUtils.isNotEmpty(dto.getZjddrqStart()));
        query.le(ZjtSlxxbDO::getZjddrq, dto.getZjddrqEnd(), StringUtils.isNotEmpty(dto.getZjddrqEnd()));
        query.eq(ZjtSlxxbDO::getLzczrid, dto.getLzczrid(), StringUtils.isNotEmpty(dto.getLzczrid()));
        query.eq(ZjtSlxxbDO::getLzczrxm, ColumnUtils.encryptColumn(dto.getLzczrxm()), StringUtils.isNotEmpty(dto.getLzczrxm()));
        query.eq(ZjtSlxxbDO::getLzczrdwdm, dto.getLzczrdwdm(), StringUtils.isNotEmpty(dto.getLzczrdwdm()));
        query.eq(ZjtSlxxbDO::getLzczrdwmc, dto.getLzczrdwmc(), StringUtils.isNotEmpty(dto.getLzczrdwmc()));
        query.eq(ZjtSlxxbDO::getShrxm, ColumnUtils.encryptColumn(dto.getShrxm()), StringUtils.isNotEmpty(dto.getShrxm()));
        query.eq(ZjtSlxxbDO::getShdw, dto.getShdw(), StringUtils.isNotEmpty(dto.getShdw()));
        query.eq(ZjtSlxxbDO::getShqk, dto.getShqk(), StringUtils.isNotEmpty(dto.getShqk()));
        query.ge(ZjtSlxxbDO::getQfrq, dto.getQfrqStart(), StringUtils.isNotEmpty(dto.getQfrqStart()));
        query.le(ZjtSlxxbDO::getQfrq, dto.getQfrqEnd(), StringUtils.isNotEmpty(dto.getQfrqEnd()));
        query.eq(ZjtSlxxbDO::getQfrid, dto.getQfrid(), StringUtils.isNotEmpty(dto.getQfrid()));
        query.eq(ZjtSlxxbDO::getQfrxm, ColumnUtils.encryptColumn(dto.getQfrxm()), StringUtils.isNotEmpty(dto.getQfrxm()));
        query.eq(ZjtSlxxbDO::getQfdwjgdm, dto.getQfdwjgdm(), StringUtils.isNotEmpty(dto.getQfdwjgdm()));
        query.eq(ZjtSlxxbDO::getQfdw, dto.getQfdw(), StringUtils.isNotEmpty(dto.getQfdw()));
        query.ge(ZjtSlxxbDO::getDsshrq, dto.getDsshrqStart(), StringUtils.isNotEmpty(dto.getDsshrqStart()));
        query.le(ZjtSlxxbDO::getDsshrq, dto.getDsshrqEnd(), StringUtils.isNotEmpty(dto.getDsshrqEnd()));
        query.eq(ZjtSlxxbDO::getDsshrxm, ColumnUtils.encryptColumn(dto.getDsshrxm()), StringUtils.isNotEmpty(dto.getDsshrxm()));
        query.eq(ZjtSlxxbDO::getDsshdw, dto.getDsshdw(), StringUtils.isNotEmpty(dto.getDsshdw()));
        query.eq(ZjtSlxxbDO::getDsshqk, dto.getDsshqk(), StringUtils.isNotEmpty(dto.getDsshqk()));
        query.eq(ZjtSlxxbDO::getRwid, dto.getRwid(), StringUtils.isNotEmpty(dto.getRwid()));
        query.eq(ZjtSlxxbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(ZjtSlxxbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.eq(ZjtSlxxbDO::getSldfjsjgsdwdm, dto.getSldfjsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwdm()));
        query.eq(ZjtSlxxbDO::getSldfjsjgsdwmc, dto.getSldfjsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwmc()));
        query.eq(ZjtSlxxbDO::getShdwdm, dto.getShdwdm(), StringUtils.isNotEmpty(dto.getShdwdm()));
        query.eq(ZjtSlxxbDO::getCldwdm, dto.getCldwdm(), StringUtils.isNotEmpty(dto.getCldwdm()));
        query.eq(ZjtSlxxbDO::getDsshdwdm, dto.getDsshdwdm(), StringUtils.isNotEmpty(dto.getDsshdwdm()));
        query.eq(ZjtSlxxbDO::getShrid, dto.getShrid(), StringUtils.isNotEmpty(dto.getShrid()));
        query.eq(ZjtSlxxbDO::getDsshrid, dto.getDsshrid(), StringUtils.isNotEmpty(dto.getDsshrid()));
        query.eq(ZjtSlxxbDO::getSfdjh, dto.getSfdjh(), StringUtils.isNotEmpty(dto.getSfdjh()));
        query.eq(ZjtSlxxbDO::getRwzxrzbh, dto.getRwzxrzbh(), StringUtils.isNotEmpty(dto.getRwzxrzbh()));
        query.ge(ZjtSlxxbDO::getRwddsj, dto.getRwddsjStart(), StringUtils.isNotEmpty(dto.getRwddsjStart()));
        query.le(ZjtSlxxbDO::getRwddsj, dto.getRwddsjEnd(), StringUtils.isNotEmpty(dto.getRwddsjEnd()));
        query.eq(ZjtSlxxbDO::getSlyckrxsfbd, dto.getSlyckrxsfbd(), StringUtils.isNotEmpty(dto.getSlyckrxsfbd()));
        query.ge(ZjtSlxxbDO::getRxbdkssj, dto.getRxbdkssjStart(), StringUtils.isNotEmpty(dto.getRxbdkssjStart()));
        query.le(ZjtSlxxbDO::getRxbdkssj, dto.getRxbdkssjEnd(), StringUtils.isNotEmpty(dto.getRxbdkssjEnd()));
        query.eq(ZjtSlxxbDO::getRxbdhs, dto.getRxbdhs(), StringUtils.isNotEmpty(dto.getRxbdhs()));
        query.eq(ZjtSlxxbDO::getRxbdxsd, dto.getRxbdxsd(), StringUtils.isNotEmpty(dto.getRxbdxsd()));
        query.eq(ZjtSlxxbDO::getRxbdkbh, dto.getRxbdkbh(), StringUtils.isNotEmpty(dto.getRxbdkbh()));
        query.eq(ZjtSlxxbDO::getRxbdjg, dto.getRxbdjg(), StringUtils.isNotEmpty(dto.getRxbdjg()));
        query.eq(ZjtSlxxbDO::getSlylszwsfbd, dto.getSlylszwsfbd(), StringUtils.isNotEmpty(dto.getSlylszwsfbd()));
        query.eq(ZjtSlxxbDO::getZwybdjg, dto.getZwybdjg(), StringUtils.isNotEmpty(dto.getZwybdjg()));
        query.eq(ZjtSlxxbDO::getZwybdxsd, dto.getZwybdxsd(), StringUtils.isNotEmpty(dto.getZwybdxsd()));
        query.eq(ZjtSlxxbDO::getZwebdjg, dto.getZwebdjg(), StringUtils.isNotEmpty(dto.getZwebdjg()));
        query.eq(ZjtSlxxbDO::getZwebdxsd, dto.getZwebdxsd(), StringUtils.isNotEmpty(dto.getZwebdxsd()));
        query.eq(ZjtSlxxbDO::getLzszwsfhy, dto.getLzszwsfhy(), StringUtils.isNotEmpty(dto.getLzszwsfhy()));
        query.eq(ZjtSlxxbDO::getLzszwyhyjg, dto.getLzszwyhyjg(), StringUtils.isNotEmpty(dto.getLzszwyhyjg()));
        query.eq(ZjtSlxxbDO::getLzszwyhyxsd, dto.getLzszwyhyxsd(), StringUtils.isNotEmpty(dto.getLzszwyhyxsd()));
        query.eq(ZjtSlxxbDO::getLzszwehyjg, dto.getLzszwehyjg(), StringUtils.isNotEmpty(dto.getLzszwehyjg()));
        query.eq(ZjtSlxxbDO::getLzszwehyxsd, dto.getLzszwehyxsd(), StringUtils.isNotEmpty(dto.getLzszwehyxsd()));
        query.eq(ZjtSlxxbDO::getLzssfjhjz, dto.getLzssfjhjz(), StringUtils.isNotEmpty(dto.getLzssfjhjz()));
        query.eq(ZjtSlxxbDO::getSlylszwbdsm, dto.getSlylszwbdsm(), StringUtils.isNotEmpty(dto.getSlylszwbdsm()));
        query.eq(ZjtSlxxbDO::getLzszwbdsm, dto.getLzszwbdsm(), StringUtils.isNotEmpty(dto.getLzszwbdsm()));
        query.eq(ZjtSlxxbDO::getSqrgmsfhm, ColumnUtils.encryptColumn(dto.getSqrgmsfhm()), StringUtils.isNotEmpty(dto.getSqrgmsfhm()));
        query.eq(ZjtSlxxbDO::getSqrxm, ColumnUtils.encryptColumn(dto.getSqrxm()), StringUtils.isNotEmpty(dto.getSqrxm()));
        query.eq(ZjtSlxxbDO::getSqrlxdh, ColumnUtils.encryptColumn(dto.getSqrlxdh()), StringUtils.isNotEmpty(dto.getSqrlxdh()));
        query.ge(ZjtSlxxbDO::getJzqsrq, dto.getJzqsrqStart(), StringUtils.isNotEmpty(dto.getJzqsrqStart()));
        query.le(ZjtSlxxbDO::getJzqsrq, dto.getJzqsrqEnd(), StringUtils.isNotEmpty(dto.getJzqsrqEnd()));
        query.eq(ZjtSlxxbDO::getCzylxdh, ColumnUtils.encryptColumn(dto.getCzylxdh()), StringUtils.isNotEmpty(dto.getCzylxdh()));
        query.eq(ZjtSlxxbDO::getShrlxdh, ColumnUtils.encryptColumn(dto.getShrlxdh()), StringUtils.isNotEmpty(dto.getShrlxdh()));
        query.eq(ZjtSlxxbDO::getDsshrlxdh, ColumnUtils.encryptColumn(dto.getDsshrlxdh()), StringUtils.isNotEmpty(dto.getDsshrlxdh()));
        query.eq(ZjtSlxxbDO::getYzkddh, dto.getYzkddh(), StringUtils.isNotEmpty(dto.getYzkddh()));
        query.eq(ZjtSlxxbDO::getSpdz1, dto.getSpdz1(), StringUtils.isNotEmpty(dto.getSpdz1()));
        query.eq(ZjtSlxxbDO::getSfmsbswzp, dto.getSfmsbswzp(), StringUtils.isNotEmpty(dto.getSfmsbswzp()));
        query.eq(ZjtSlxxbDO::getHlwsqid, dto.getHlwsqid(), StringUtils.isNotEmpty(dto.getHlwsqid()));
        query.eq(ZjtSlxxbDO::getUsername, dto.getUsername(), StringUtils.isNotEmpty(dto.getUsername()));
        query.eq(ZjtSlxxbDO::getPjjg, dto.getPjjg(), StringUtils.isNotEmpty(dto.getPjjg()));
        query.eq(ZjtSlxxbDO::getPjpljc, dto.getPjpljc(), StringUtils.isNotEmpty(dto.getPjpljc()));
        query.ge(ZjtSlxxbDO::getPjsj, dto.getPjsjStart(), StringUtils.isNotEmpty(dto.getPjsjStart()));
        query.le(ZjtSlxxbDO::getPjsj, dto.getPjsjEnd(), StringUtils.isNotEmpty(dto.getPjsjEnd()));
        query.eq(ZjtSlxxbDO::getFwdx, dto.getFwdx(), StringUtils.isNotEmpty(dto.getFwdx()));
        query.eq(ZjtSlxxbDO::getSfdgszj, dto.getSfdgszj(), StringUtils.isNotEmpty(dto.getSfdgszj()));
        query.eq(ZjtSlxxbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(ZjtSlxxbDO::getSlshjdz, dto.getSlshjdz(), StringUtils.isNotEmpty(dto.getSlshjdz()));
        query.eq(ZjtSlxxbDO::getKsywlsh, dto.getKsywlsh(), StringUtils.isNotEmpty(dto.getKsywlsh()));
        query.eq(ZjtSlxxbDO::getKsfsbz, dto.getKsfsbz(), StringUtils.isNotEmpty(dto.getKsfsbz()));
        query.ge(ZjtSlxxbDO::getKsfssj, dto.getKsfssjStart(), StringUtils.isNotEmpty(dto.getKsfssjStart()));
        query.le(ZjtSlxxbDO::getKsfssj, dto.getKsfssjEnd(), StringUtils.isNotEmpty(dto.getKsfssjEnd()));
        query.eq(ZjtSlxxbDO::getZplsid, dto.getZplsid(), StringUtils.isNotEmpty(dto.getZplsid()));
        query.eq(ZjtSlxxbDO::getZpcjlx, dto.getZpcjlx(), StringUtils.isNotEmpty(dto.getZpcjlx()));
        query.eq(ZjtSlxxbDO::getZpsbbsh, dto.getZpsbbsh(), StringUtils.isNotEmpty(dto.getZpsbbsh()));
        query.eq(ZjtSlxxbDO::getZpsbppxhdm, dto.getZpsbppxhdm(), StringUtils.isNotEmpty(dto.getZpsbppxhdm()));
        query.eq(ZjtSlxxbDO::getZpsbppxh, dto.getZpsbppxh(), StringUtils.isNotEmpty(dto.getZpsbppxh()));
        query.eq(ZjtSlxxbDO::getZpytbid, dto.getZpytbid(), Objects.nonNull(dto.getZpytbid()));
        query.eq(ZjtSlxxbDO::getSfbczpyt, dto.getSfbczpyt(), StringUtils.isNotEmpty(dto.getSfbczpyt()));
        query.ge(ZjtSlxxbDO::getLxcjsj, dto.getLxcjsjStart(), StringUtils.isNotEmpty(dto.getLxcjsjStart()));
        query.le(ZjtSlxxbDO::getLxcjsj, dto.getLxcjsjEnd(), StringUtils.isNotEmpty(dto.getLxcjsjEnd()));
        query.eq(ZjtSlxxbDO::getLxczymc, dto.getLxczymc(), StringUtils.isNotEmpty(dto.getLxczymc()));
        query.eq(ZjtSlxxbDO::getLxsldw, dto.getLxsldw(), StringUtils.isNotEmpty(dto.getLxsldw()));
        query.eq(ZjtSlxxbDO::getZpsbbh, dto.getZpsbbh(), StringUtils.isNotEmpty(dto.getZpsbbh()));
        query.likeLeft(ZjtSlxxbDO::getZpsbzcdw, ColumnUtils.removeZeroSuffix(dto.getZpsbzcdw()), StringUtils.isNotEmpty(dto.getZpsbzcdw()));
        query.eq(ZjtSlxxbDO::getZpsbscgs, dto.getZpsbscgs(), StringUtils.isNotEmpty(dto.getZpsbscgs()));
        query.eq(ZjtSlxxbDO::getZpsbxsgs, dto.getZpsbxsgs(), StringUtils.isNotEmpty(dto.getZpsbxsgs()));
        query.eq(ZjtSlxxbDO::getBzsbbh, dto.getBzsbbh(), StringUtils.isNotEmpty(dto.getBzsbbh()));
        query.likeLeft(ZjtSlxxbDO::getBzsbzcdw, ColumnUtils.removeZeroSuffix(dto.getBzsbzcdw()), StringUtils.isNotEmpty(dto.getBzsbzcdw()));
        query.eq(ZjtSlxxbDO::getBzsbppxhdm, dto.getBzsbppxhdm(), StringUtils.isNotEmpty(dto.getBzsbppxhdm()));
        query.eq(ZjtSlxxbDO::getBzsbppxh, dto.getBzsbppxh(), StringUtils.isNotEmpty(dto.getBzsbppxh()));
        query.eq(ZjtSlxxbDO::getBzsbscgs, dto.getBzsbscgs(), StringUtils.isNotEmpty(dto.getBzsbscgs()));
        query.eq(ZjtSlxxbDO::getBzsbxsgs, dto.getBzsbxsgs(), StringUtils.isNotEmpty(dto.getBzsbxsgs()));
        query.eq(ZjtSlxxbDO::getJjlxrxm, ColumnUtils.encryptColumn(dto.getJjlxrxm()), StringUtils.isNotEmpty(dto.getJjlxrxm()));
        query.eq(ZjtSlxxbDO::getJjlxrdh, dto.getJjlxrdh(), StringUtils.isNotEmpty(dto.getJjlxrdh()));
        query.eq(ZjtSlxxbDO::getJjlxryslrgx, dto.getJjlxryslrgx(), StringUtils.isNotEmpty(dto.getJjlxryslrgx()));
        query.eq(ZjtSlxxbDO::getZaglywxtbh, dto.getZaglywxtbh(), StringUtils.isNotEmpty(dto.getZaglywxtbh()));
        query.eq(ZjtSlxxbDO::getZaglzwfwsxbm, dto.getZaglzwfwsxbm(), StringUtils.isNotEmpty(dto.getZaglzwfwsxbm()));
        query.eq(ZjtSlxxbDO::getZaglywlbdm, dto.getZaglywlbdm(), StringUtils.isNotEmpty(dto.getZaglywlbdm()));
        query.eq(ZjtSlxxbDO::getCzyip, dto.getCzyip(), StringUtils.isNotEmpty(dto.getCzyip()));
        query.eq(ZjtSlxxbDO::getZwcjqid, dto.getZwcjqid(), StringUtils.isNotEmpty(dto.getZwcjqid()));
        query.eq(ZjtSlxxbDO::getSfsqltz, dto.getSfsqltz(), StringUtils.isNotEmpty(dto.getSfsqltz()));
        query.eq(ZjtSlxxbDO::getLtzshjg, dto.getLtzshjg(), StringUtils.isNotEmpty(dto.getLtzshjg()));
        query.ge(ZjtSlxxbDO::getLtzspsj, dto.getLtzspsjStart(), StringUtils.isNotEmpty(dto.getLtzspsjStart()));
        query.le(ZjtSlxxbDO::getLtzspsj, dto.getLtzspsjEnd(), StringUtils.isNotEmpty(dto.getLtzspsjEnd()));
        query.eq(ZjtSlxxbDO::getLtzsprid, dto.getLtzsprid(), StringUtils.isNotEmpty(dto.getLtzsprid()));
        query.eq(ZjtSlxxbDO::getLtzsprxm, ColumnUtils.encryptColumn(dto.getLtzsprxm()), StringUtils.isNotEmpty(dto.getLtzsprxm()));
        query.eq(ZjtSlxxbDO::getLtzsqsy, dto.getLtzsqsy(), StringUtils.isNotEmpty(dto.getLtzsqsy()));
        query.in(ZjtSlxxbDO::getSlzt, dto.getSlztList(), CollUtil.isNotEmpty(dto.getSlztList()));

        if (Boolean.TRUE.equals(dto.getSlztWwc())) {
            query.lt(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YLZ);
            query.ne(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_ZF);
        }

        List<String> slztList = getSlztList(dto);

        if (StringUtils.isNotBlank(dto.getCscxzt())) {
            query.in(ZjtSlxxbDO::getSlfs, ZjConstant.ZJ_SLFS_ZZSBSL, ZjConstant.ZJ_SLFS_YDCJYJBZ,
                    ZjConstant.ZJ_SLFS_ZWWSL, ZjConstant.ZJ_SLFS_LXCJYJBZ);
        }

        if (!slztList.isEmpty()) {
            query.in(ZjtSlxxbDO::getSlzt, slztList);
        }

        if (!CollectionUtils.isEmpty(dto.getHjdsjgsdwdmLikeList())) {
            query.and(queryWrapper -> {
                for (String sjfw : dto.getHjdsjgsdwdmLikeList()) {
                    if (DwUtils.isSt(sjfw)) {
                        queryWrapper.or(ZjtSlxxbDO::getHjdsjgsdwdm).likeLeft(sjfw.substring(0, 4));
                    } else {
                        queryWrapper.or(ZjtSlxxbDO::getHjdsjgsdwdm).likeLeft(DwUtils.removeZeroSuffix(sjfw));
                    }
                }
            });
        }

        if (!CollectionUtils.isEmpty(dto.getSldsjgsdwdmLikeList())) {
            query.and(queryWrapper -> {
                for (String sjfw : dto.getSldsjgsdwdmLikeList()) {
                    if (DwUtils.isSt(sjfw)) {
                        queryWrapper.or(ZjtSlxxbDO::getSldsjgsdwdm).likeLeft(sjfw.substring(0, 4));
                    } else {
                        queryWrapper.or(ZjtSlxxbDO::getSldsjgsdwdm).likeLeft(DwUtils.removeZeroSuffix(sjfw));
                    }
                }
            });
        }

        query.in(ZjtSlxxbDO::getZzlx, dto.getZzlxList(), CollUtil.isNotEmpty(dto.getZzlxList()));
        query.in(ZjtSlxxbDO::getLtzshjg, dto.getLtzshjgList(), CollUtil.isNotEmpty(dto.getLtzshjgList()));

        return query;
    }

    @Override
    public ZjtSlxxbDTO convertToDTO(ZjtSlxxbDO zjtSlxxbDO) {
        return convert.convert(zjtSlxxbDO);
    }

    @Override
    public ZjtSlxxbDO convertToDO(ZjtSlxxbDTO zjtSlxxbDTO) {
        return convert.convertToDO(zjtSlxxbDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZjtSlxxbDTO insert(ZjtSlxxbDTO zjtSlxxbDTO) throws ServiceException {
        zjtSlxxbDTO.setFwdx(StringUtils.defaultIfEmpty(zjtSlxxbDTO.getFwdx(), ZjConstant.ZJ_FWDX_10_CK));
        zjtSlxxbDTO.setSfdgszj(StringUtils.defaultIfEmpty(zjtSlxxbDTO.getSfdgszj(), Constants.NO));


        //修改申请信息获取，增加申请信息记录锁定，将判断从方法中提取出来。 20200309 hubin
        ZjtSqxxbDTO zjtSqxxb = validateZjtSqxxb(zjtSlxxbDTO.getYwslh(), zjtSlxxbDTO.getRyid());

        // 首次受理申请信息的slzt必须为初步审查通过(00).
        if (!ZjConstant.ZJ_BLBZ_2ID_CBSCTG.equals(zjtSqxxb.getSlzt())) {
            throw new ServiceException(CkBaseErrorCode.SLXX_CANNOT_COMMIT,
                    "证件受理业务无法完成: 首次受理时, 申请信息的受理状态必须等于“初步审查通过”。");
        }

        //申领原因为首次申领，判断是否存在正常的证件，从受理表，受理历史表，PPAS受理表去检索判断
        if (zjtSlxxbDTO.getSlyy().startsWith("1")
                && validateSfzScslpd(zjtSlxxbDTO.getGmsfhm(), zjtSlxxbDTO.getSlyy()) > 0) {
                throw new ServiceException(CkBaseErrorCode.SLXX_CANNOT_COMMIT,
                        "证件受理业务无法完成: 申领原因是首次申领，但已存在有效证件，无法使用该申领原因。");
            }

        //add hubin 20210121 审核时检查是否存在流程中的证件受理数据
        long zjnum = countByGmsfhmIsblz(zjtSlxxbDTO.getGmsfhm());
        if (zjnum > 0) {
            throw new ServiceException(CkBaseErrorCode.SLXX_CANNOT_COMMIT,
                    "公民身份号码[" + zjtSlxxbDTO.getGmsfhm() + "]存在办理过程中的证件，无法重复受理，请窗口处理");
        }

        //如果前端没有传区域范围代码，则自行根据受理地数据归属单位代码和户籍地数据归属单位代码判断
        if (StringUtils.isEmpty(zjtSlxxbDTO.getQyfwdm())) {
            zjtSlxxbDTO.setQyfwdm(CkCommonUtils.getQyfwdm(zjtSlxxbDTO.getSldsjgsdwdm(), zjtSlxxbDTO.getHjdsjgsdwdm()));
        }

        //查询互联网业务，根据数据来源分类填充信息
        if (zjtSlxxbDTO.getHlwsqid() != null) {
            CkhlwHjsqJbDTO pockhlwjb = ckhlwHjsqJbService.findById(zjtSlxxbDTO.getHlwsqid());
            if (pockhlwjb != null) {
                if ("jcss".equals(pockhlwjb.getSjly())) {
                    zjtSlxxbDTO.setSlfsx(ZjConstant.ZJ_SLFSX_64_JCSSSL);
                    zjtSlxxbDTO.setFwdx(ZjConstant.ZJ_FWDX_52_JCSS);
                } else if ("zw2sp".equals(pockhlwjb.getSjly())) {
                    zjtSlxxbDTO.setSlfsx(ZjConstant.ZJ_SLFSX_61_ZW2SL);
                    zjtSlxxbDTO.setFwdx(ZjConstant.ZJ_FWDX_51_ZWW);
                } else {
                    throw new ServiceException(CkBaseErrorCode.SLXX_CANNOT_COMMIT, "对应互利网业务无数据来源信息。hlwsqid:["
                            + pockhlwjb.getHlwsqid() + "]");
                }
            }
        }
        if (StringUtils.isBlank(zjtSlxxbDTO.getSlfs())) {
            zjtSlxxbDTO.setSlfs(ZjConstant.ZJ_SLFS_PTSL);
        }

        // 首次受理slh等于ywslh.但是最后第5位根据zzlx和lqfs修改
        zjtSlxxbDTO.setSlh(zjSlhGenerator.generateNewSlh(zjtSlxxbDTO.getYwslh(), zjtSlxxbDTO.getZzlx(), zjtSlxxbDTO.getLqfs()));
        zjtSlxxbDTO.setNbslid(zjtSlxxbDTO.getSlh());

        // 初始化受理信息关键字段 默认设置了受理状态
        initBeforeSave(zjtSlxxbDTO);

        // 保存人像信息
        // 若有临时照片id，则使用临时照片内容
        String zpid = insertZjtRxxxb(zjtSlxxbDTO);

        // 保存指纹信息
        String zwtxid = insertZjtZwcjb(zjtSlxxbDTO);

        // todo 目前前端没有上传材料, 暂时注释
//        insertRktYwslClqds(slxxbReq);

        // 保存受理信息
        zjtSlxxbDTO.setZpid(zpid);
        zjtSlxxbDTO.setZwtxid(zwtxid);

//        //如果是自助申领机申请的证件先改01，等收费确认后，改为06
//        if ("8".equals(zjtSlxxbDTO.getSlfs())) {
//            zjtSlxxbDTO.setSlzt(ZjConstant.ZJ_BLBZ_2ID_SJWZ);
//        }

        if ("[51][52]".contains(zjtSlxxbDTO.getFwdx())) {
            zjtSlxxbDTO.setSlzt(ZjConstant.ZJ_BLBZ_2ID_SJWZ);
        }
        //20210617 服务对象是常口离线采集端的数据，受理状态改05待初审
        else if (ZjConstant.ZJ_FWDX_10_CK.equals(zjtSlxxbDTO.getFwdx())) {
            zjtSlxxbDTO.setSlzt(ZjConstant.ZJ_BLBZ_2ID_PCSDSH);

            if (StringUtils.isNotBlank(zjtSlxxbDTO.getLxnbslid())) {
                ZjtLxSlxxbDTO zjtLxSlxxb = zjtLxSlxxbService.findById(zjtSlxxbDTO.getLxnbslid());
                if (Objects.nonNull(zjtLxSlxxb)) {
                    zjtLxSlxxb.setSlzt(ZjConstant.ZJ_BLBZ_2ID_ZWYCJ);
                    zjtLxSlxxbService.update(zjtLxSlxxb);
                }
            }
        }

        //从申请表拷贝相关字段到受理表
        syncZjtSlxxbFromZjtSqxxb(zjtSlxxbDTO, zjtSqxxb);

        //20230110 新增区划内详细地址，如果入参为空，则填入住址
        if (StringUtils.isEmpty(zjtSlxxbDTO.getHjdzqhnxxdz())) {
            zjtSlxxbDTO.setHjdzqhnxxdz(zjtSlxxbDTO.getZz());
        }

        super.insert(zjtSlxxbDTO);

        //保存照片原图材料信息 有传入照片原图ID
        if (zjtSlxxbDTO.getZpytbid() != null && zjtSlxxbDTO.getZpytbid() > 0) {
            hjxxZpytbService.saveClysj(zjtSlxxbDTO.getYwslh(), zjtSlxxbDTO.getZpytbid(), zjtSlxxbDTO.getXm());
        }

        //保存电话号码
        hjxxDhhmxxbService.saveDhhm(zjtSlxxbDTO.getSqrgmsfhm(), zjtSlxxbDTO.getSqrxm(), zjtSlxxbDTO.getSqrlxdh(),
                CkBaseConstants.DHHM_SJLY_SFZYW, zjtSlxxbDTO.getRyid(), zjtSlxxbDTO.getRynbid(), zjtSlxxbDTO.getCzsj());

        // 更新申请表的受理状态
        zjtSqxxbService.syncZjtSqxxbFromZjtSlxxb(zjtSlxxbDTO);

        // 保存办证收费流水-收入
        zjtBzsflsbService.insert(zjtSlxxbDTO, ZjtBzsflsbType.SR);

        // 保存证件流水
        zjtSfzywczbService.insert(zjtSlxxbDTO.getYwslh(), zjtSlxxbDTO.getSlh(), zjtSlxxbDTO.getSlzt(), CkConstants.GNBH_ZJT_SL, null);

        //在线支付：费用支付流水
        ZjtFyzflsbDTO fyzflsbDTO = zjtFyzflsbService.addZjtSlxxbZjtFyzflsb(findById(zjtSlxxbDTO.getNbslid()));

        if (zjtSlxxbDTO.getSfje().compareTo(BigDecimal.ZERO) > 0 && fyzflsbDTO == null) {
            throw new ServiceException(CkBaseErrorCode.SLXX_CANNOT_COMMIT, "缴费信息异常，请联系管理人员。");
        }

        //回填政务网数据
        if (zjtSlxxbDTO.getHlwsqid() != null) {
            ckhlwGaythShManager.updateSqzt20(zjtSlxxbDTO.getHlwsqid(), zjtSlxxbDTO.getNbslid());
        }

        return zjtSlxxbDTO;
    }

    public ZjtSlxxbDTO findLssfzSlxxb(String gmsfhm, String czsj) {

        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.ge(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_PCSDSH)
                .lt(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YLZ)
                .eq(ZjtSlxxbDO::getLssfzslbz, Constants.NO)
                .eq(ZjtSlxxbDO::getGmsfhm, gmsfhm)
                .gt(ZjtSlxxbDO::getCzsj, czsj)
                .orderBy(ZjtSlxxbDO::getCzsj, false);

        List<ZjtSlxxbDO> list = list(queryWrapper);

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        ZjtSlxxbDTO slxxb = convertToDTO(list.getFirst());

        List<XtYhsjfwbDTO> sjfwList = xtYhsjfwbService.listByYhidAndXqlx(SecurityUtils.getUserId(),
                SysadminConstants.XTCSB_XQLX_XQN);
        if (CollectionUtils.isEmpty(sjfwList)) {
            return null;
        }
        boolean ifSn = false;
        boolean ifSw = false;
        for (XtYhsjfwbDTO poXtYhsjfwb : sjfwList) {
            String likeBm = getLikeBmByYhsjfw(poXtYhsjfwb.getSjfw());
            if (slxxb.getHjdsjgsdwdm().startsWith(likeBm)) {
                ifSn = true;
                break;
            }
            if (slxxb.getSldsjgsdwdm().startsWith(likeBm)) {
                ifSw = true;
            }
        }

        if (ifSn) {
            slxxb.setZzlx(ZjConstant.ZJ_ZZLX_PTZJ);
            return slxxb;
        }
        if (ifSw) {
            slxxb.setZzlx(ZjConstant.ZJ_ZZLX_SNYDZJ_NEW);
            return slxxb;
        }
        return null;
    }

    @Override
    public ZjtSlxxbDTO findNewestNormal(String gmsfhm) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YLZ)
                .eq(ZjtSlxxbDO::getGmsfhm, gmsfhm)
                .orderBy(ZjtSlxxbDO::getCzsj, false)
                .limit(1);
        return find(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean processZjslYsfcl(String nbslid) {

        ZjtSlxxbDTO zjtSlxxbDTO = findById(nbslid);

        //判断受理信息是否存在
        if (zjtSlxxbDTO == null) {
            throw new ServiceException(CkBaseErrorCode.SLXX_NOT_FOUND, "未找到受理信息[" + nbslid + "]");
        }

        //不满足状态条件，无法做初审功能
        if (!ZjConstant.ZJ_BLBZ_2ID_SJWZ.equals(zjtSlxxbDTO.getSlzt())) {
            throw new ServiceException(CkBaseErrorCode.SLXX_CANNOT_COMMIT, "受理状态[" + zjtSlxxbDTO.getSlzt() + "]不能做已收费操作");
        }

        //保存受理信息
        zjtSlxxbDTO.setSlzt(ZjConstant.ZJ_BLBZ_2ID_PCSDSH);
        update(zjtSlxxbDTO);
        //更新申请表
        zjtSqxxbService.syncZjtSqxxbFromZjtSlxxb(zjtSlxxbDTO);

        zjtSfzywczbService.insert(zjtSlxxbDTO.getYwslh(), zjtSlxxbDTO.getSlh(), zjtSlxxbDTO.getSlzt(),
                CkConstants.GNBH_ZJT_ZJYSF, "");

        return true;
    }

    @Override
    public void updateLssfzslbzById(String nbslid, String lssfzslbz) {
        ZjtSlxxbDO zjtSlxxb = new ZjtSlxxbDO();
        zjtSlxxb.setNbslid(nbslid);
        zjtSlxxb.setLssfzslbz(lssfzslbz);
        updateById(zjtSlxxb);
    }

    /**
     * 判断是否有正常办理中的证件
     *
     * @param gmsfhm
     * @return
     */
    @Override
    public long countByGmsfhmIsZcblz(String gmsfhm) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ZjtSlxxbDO::getGmsfhm, gmsfhm)
                .lt(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YLZ)
                .notIn(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_ZF, ZjConstant.ZJ_BLBZ_2ID_CSBTG,
                        ZjConstant.ZJ_BLBZ_2ID_SLYCH, ZjConstant.ZJ_BLBZ_2ID_CH, ZjConstant.ZJ_BLBZ_2ID_QXSHBTG,
                        ZjConstant.ZJ_BLBZ_2ID_DSSHBTG, ZjConstant.ZJ_BLBZ_2ID_STSHBTG, ZjConstant.ZJ_BLBZ_2ID_DBSZLBHG,
                        ZjConstant.ZJ_BLBZ_2ID_ZLBHG, ZjConstant.ZJ_BLBZ_2ID_KSSHTG, ZjConstant.ZJ_BLBZ_2ID_DSZJYSBTG,
                        ZjConstant.ZJ_BLBZ_2ID_QXZJYSBTG, ZjConstant.ZJ_BLBZ_2ID_PCSZJYSBTG);
        return count(queryWrapper);
    }

    @Override
    public int queryLtzfjsyl() {
        String deptCode = SecurityUtils.getDeptCode();
        XtDwxxbDTO pcsDwxxb = xtDwxxbService.findById(deptCode);

        //根据用户的单位信息查询分局配置的每日绿通证数量
        String fjjgdm = pcsDwxxb.getFjjgdm();
        if (fjjgdm == null) {
            throw new ServiceException(CkBaseErrorCode.QUERY_LTZSYL_ERROR, pcsDwxxb.getDm() + pcsDwxxb.getMc() + "的分局代码为空!");
        }
        XtDwxxbDTO fjxx = xtDwxxbService.findById(fjjgdm.substring(0, 9));

        if (fjxx == null) {
            throw new ServiceException(CkBaseErrorCode.QUERY_LTZSYL_ERROR, pcsDwxxb.getMc() + "的分局信息未查询到");
        }
        if (!StringUtils.isNumeric(fjxx.getKzzda())) {
            throw new ServiceException(CkBaseErrorCode.QUERY_LTZSYL_ERROR, fjxx.getMc() + "的绿通证最大限制量未配置");
        }

        //查询当日身份证受理量，按分局统计
        String ltzsqsj = ServerTimeUtils.getCurrentDate();

        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ZjtSlxxbDO::getSldfjsjgsdwdm, fjjgdm)
                .likeLeft(ZjtSlxxbDO::getLtzsqsj, ltzsqsj)
                .eq(ZjtSlxxbDO::getSfsqltz, Constants.YES)
                .in(ZjtSlxxbDO::getLtzshjg, ZjConstant.LTZ_SHJG_00_WSP, ZjConstant.LTZ_SHJG_11_FJSPTG,
                        ZjConstant.LTZ_SHJG_21_SJSPTG);

        long sfzsll = count(queryWrapper);

        //计算剩余量
        int maxLtzblsl = Integer.parseInt(fjxx.getKzzda());
        long syl = maxLtzblsl - sfzsll;
        if (syl < 0) {
            syl = 0;
        }
        return Math.toIntExact(syl);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveApplyLtz(ZjtSlxxbApplyLtzReq req) {
        ZjtSlxxbDTO zjtSlxxbDTO = findById(req.getNbslid());
        if (zjtSlxxbDTO == null) {
            throw new ServiceException(CkBaseErrorCode.SLXX_NOT_FOUND);
        }

        //判断分局绿通证每日办理量是否已达到最大限度
        int syl = queryLtzfjsyl();
        if (syl <= 0) {
            throw new ServiceException(CkBaseErrorCode.SAVE_LTZSQ_ERROR, "分局的绿通证每日申请量已满");
        }

        //业务绿通证受理标志为1并且绿通证审核结果为00，11,21时不允许再次申请绿通证
        if (Constants.YES.equals(zjtSlxxbDTO.getSfsqltz()) && zjtSlxxbDTO.getLtzshjg() != null
                && "[00][11][21]".contains(zjtSlxxbDTO.getLtzshjg())) {
            throw new ServiceException(CkBaseErrorCode.SAVE_LTZSQ_ERROR, "该笔业务已申请绿通证办理,无需再次申请!");
        }

        zjtSlxxbDTO.setSfsqltz(Constants.YES);
        zjtSlxxbDTO.setLtzshjg(ZjConstant.LTZ_SHJG_00_WSP);
        zjtSlxxbDTO.setLtzsqsy(req.getLtzsqsy());
        zjtSlxxbDTO.setLtzsqyy(req.getLtzsqyy());
        zjtSlxxbDTO.setLtzsqsj(ServerTimeUtils.getCurrentTime());
        this.update(zjtSlxxbDTO);

        // 保存证件流水
        zjtSfzywczbService.insert(zjtSlxxbDTO.getYwslh(), zjtSlxxbDTO.getSlh(), zjtSlxxbDTO.getSlzt(),
                CkConstants.GNBH_ZJT_LTZSQBC, "绿通证申请保存");

        // 保存申请材料
        List<RktYwslClysjDTO> list = RktYwslClysjConvert.INSTANCE.convertToDTO(req.getSqclList());
        list.forEach(rktYwslClysjDTO -> {
            rktYwslClysjDTO.setYwslh(zjtSlxxbDTO.getYwslh());
            rktYwslClysjDTO.setSjgsdwdm(zjtSlxxbDTO.getSldsjgsdwdm());
            rktYwslClysjDTO.setCllylx(CkBaseConstants.CLLYLX_SQCL);
            rktYwslClysjDTO.setLcywlx(CkBaseConstants.LCYWLX_LTZSQ);
            rktYwslClysjDTO.setCllxdm(CkBaseConstants.CLLXDM_LTZSQCL);
            rktYwslClysjDTO.setClmc(StringUtils.defaultIfEmpty(rktYwslClysjDTO.getClmc(), "绿通证申请材料"));
            rktYwslClysjDTO.setWjlx(StringUtils.defaultIfEmpty(rktYwslClysjDTO.getWjlx(),  MediaType.IMAGE_JPEG_VALUE));
        });

        rktYwslClysjFileManager.save(list);
    }


    @Override
    public long countByGmsfhmIsBlz(String gmsfhm) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ZjtSlxxbDO::getGmsfhm, gmsfhm)
                .lt(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YLZ)
                .ne(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_ZF);
        return count(queryWrapper);
    }

    private String getLikeBmByYhsjfw(String yhsjfw) {
        if ("|".equals(yhsjfw.substring(7, 8))) {
            if ("00".equals(yhsjfw.substring(4, 6))) {
                return yhsjfw.substring(0, 4);
            }
            return yhsjfw.substring(0, 6);
        }
        return yhsjfw.substring(7, 16);
    }


    /**
     * 受理信息提交时判断是否有办理中的证件
     *
     * @param gmsfhm
     * @return
     */
    private long countByGmsfhmIsblz(String gmsfhm) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ZjtSlxxbDO::getGmsfhm, gmsfhm)
                .lt(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YLZ)
                .ne(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_ZF);
        return count(queryWrapper);
    }

    /**
     * 首次身份证办理用的判断语句
     *
     * @param gmsfhm
     * @return
     */
    private long countGmsfhmSfyzj(String gmsfhm) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ZjtSlxxbDO::getGmsfhm, gmsfhm)
                .lt(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YCB)
                .ne(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_ZF);
        return count(queryWrapper);
    }

    private ZjtSqxxbDTO validateZjtSqxxb(String ywslh, String ryid) {
        ZjtSqxxbDTO zjtSqxxb = zjtSqxxbService.findById(ywslh);
        if (zjtSqxxb == null) {
            throw new ServiceException(500, "未找到ywslh=" + ywslh
                    + "的证件业务申请信息, 请确认ywslh是否正确.");
        }
        if (!StringUtils.equals(ryid, zjtSqxxb.getRyid())) {
            throw new ServiceException(500, "您提供的ryid=" + ryid
                    + "和ywslh=" + ywslh + "不是同一个人的受理信息.");
        }
        return zjtSqxxb;
    }

    /**
     * 身份证首次申领判断
     */
    private int validateSfzScslpd(String gmsfhm, String slyy) {
        int bz = 0;

        if (!slyy.startsWith("1")) {
            return bz;
        }

        //查询MYSQL的证件受理表是否存在正常状态的身份证
        long msSfz = countGmsfhmSfyzj(gmsfhm);
        if (msSfz > 0) {
            bz++;
            return bz;
        }

        //查询MYSQL的证件受理历史表是否存在正常状态的身份证
        long msSfzLs = zjtSlxxbLsService.countGmsfhmSfyzj(gmsfhm);
        if (msSfzLs > 0) {
            bz++;
            return bz;
        }

        //查询CKDSHZK的证件受理表是否存在正常状态的身份证
        long dshzkSfz = zjywSlxxbService.countGmsfhmSfyzj(gmsfhm);
        if (dshzkSfz > 0) {
            bz++;
            return bz;
        }

        return bz;
    }

    private String insertZjtRxxxb(ZjtSlxxbDTO entity) {
        String zplsid = entity.getZplsid();

        // 新增时必须有照片.
        if (StringUtils.isBlank(zplsid)) {
            throw new ServiceException(500, "公民身份证异地受理时：人像照片未采集.");
        }

        HjxxZplsbDTO zplsb = hjxxZplsbService.findById(zplsid);
        if (zplsb == null || zplsb.getZp() == null) {
            throw new ServiceException(CkBaseErrorCode.ZPLSB_NOT_FOUND,
                    "证件受理业务无法完成: 临时照片id“" + zplsid + "”无对应照片信息,请重新核查。");
        }

        // 只要前端有提供照片则不管新增还是更新都插入一条新的照片记录.
        ZjtRxxxbDTO zjtRxxxb = new ZjtRxxxbDTO();
        zjtRxxxb.setBase64zp(Base64.getEncoder().encodeToString(zplsb.getZp()));
        zjtRxxxb.setRyid(entity.getRyid());
        zjtRxxxb.setGmsfhm(entity.getGmsfhm());
        zjtRxxxb.setXm(entity.getXm());
        zjtRxxxb.setXplx(ZjConstant.ZJ_XPLX_EDZSDZP);
        zjtRxxxb = zjtRxxxbService.insert(zjtRxxxb);
        return zjtRxxxb.getZpid();
    }


    private void syncZjtSlxxbFromZjtSqxxb(ZjtSlxxbDTO zjtSlxxb, ZjtSqxxbDTO zjtSqxxb) {
        if (zjtSlxxb == null || zjtSqxxb == null) {
            return;
        }
        zjtSlxxb.setSqrgmsfhm(zjtSqxxb.getSqrgmsfhm());//从申请表拷贝申请人身份证
        zjtSlxxb.setSqrxm(zjtSqxxb.getSqrxm());//从申请表拷贝申请人姓名
    }


    // 新增指纹信息
    private String insertZjtZwcjb(ZjtSlxxbDTO entity) {
        String zwtxlsid = entity.getZwtxlsid();
        if (StringUtils.isBlank(zwtxlsid)) {
            throw new ServiceException(CkBaseErrorCode.ZWLSB_NOT_FOUND, "公民身份证异地受理时：指纹未采集.");
        }

        ZjtZwcjlsbDTO zjtZwcjlsb = zwcjlsbService.findById(zwtxlsid);
        if (zjtZwcjlsb == null) {
            throw new ServiceException(CkBaseErrorCode.ZWLSB_NOT_FOUND,
                    "证件受理业务无法完成: 临时指纹id“" + zwtxlsid + "”无对应指纹信息,请重新核查。");
        }

        ZjtZwcjbDTO zjtZwcjb = ZjtZwcjbConvert.INSTANCE.convertToDTO(zjtZwcjlsb);

        zjtZwcjb.setZwtxid(null);   // 将twtxid设置成null从而确保是新增照片操作.
        zjtZwcjb.setSlh(entity.getSlh());
        zjtZwcjb.setRyid(entity.getRyid());
        zjtZwcjb.setGmsfhm(entity.getGmsfhm());
        zjtZwcjb.setXm(entity.getXm());
        zjtZwcjb.setHjdsjgsdwdm(entity.getHjdsjgsdwdm());
        zjtZwcjb.setHjdsjgsdwmc(entity.getHjdsjgsdwmc());
        zjtZwcjb.setSldsjgsdwdm(entity.getSldsjgsdwdm());
        zjtZwcjb.setSldsjgsdwmc(entity.getSldsjgsdwmc());

        //指纹信息保存时，如果是政务网的受理信息，则不校验指纹采集器ID
        if (ZjConstant.ZJ_FWDX_51_ZWW.equals(entity.getFwdx())) {
            zjtZwcjb = zjtZwcjbService.insertWithNotValidateZwcjqid(zjtZwcjb);
        } else {
            zjtZwcjb = zjtZwcjbService.insert(zjtZwcjb);
        }

        entity.setZwyzw(zjtZwcjb.getZwyzw());
        entity.setZwyzcjg(zjtZwcjb.getZwyzcjg());
        entity.setZwezw(zjtZwcjb.getZwezw());
        entity.setZwezcjg(zjtZwcjb.getZwezcjg());
        entity.setZwcjjgdm(zjtZwcjb.getZwcjjgdm());
        entity.setSzyczkdm(zjtZwcjb.getSzyczkdm());
        return zjtZwcjb.getZwtxid();
    }

//    private void insertRktYwslClqds(ZjtSlxxbReq entity) {
//        List<RktYwslClqdReq> rktYwslClqdReqList = entity.getYwslClqdList();
//        if (CollectionUtils.isEmpty(rktYwslClqdReqList)) {
//            logger.debug("省内异地制证时没有申请材料, 不需要保存.");
//            return;
//        }
//
//        List<RktYwslClqd> rktYwslClqdList =
//                JSON.parseArray(JSON.toJSONString(rktYwslClqdReqList),RktYwslClqd.class);
//        for (RktYwslClqd clqd : rktYwslClqdList) {
//            clqd.setYwslh(entity.getYwslh());
//        }
//        List<RktYwslClqd> save = rktYwslClqdService.save(rktYwslClqdList);
//        entity.setYwslClqdList(JSON.parseArray(JSON.toJSONString(save),RktYwslClqdReq.class));
//    }


    private void initBeforeSave(ZjtSlxxbDTO entity) {
        entity.setSlzt(ZjConstant.ZJ_BLBZ_2ID_ZWYCJ);
        entity.setSfzwzj(Constants.YES); // 指纹证件
        entity.setTbbz(ZjConstant.ZZDD_TBBZ_WTB);  // 未同步, 表示还没有向制证中心打包.

        // rwid填写默认值表示未调度, 作用: 在制证调度时需要根据rwid过滤出未调度的数据,
        // 如果不填默认值则查询条件为rwid is null, 这样查询不走索引影响性能.
        entity.setRwid(ZjConstant.ZZDD_RWDD_WDD);

        CustomUserDetails user = SecurityUtils.getCurrentUser();
        entity.setCzyid(String.valueOf(user.getUserId()));
        entity.setCzyxm(user.getName());
        entity.setCzyip(user.getRemoteAddress());
        entity.setCzydwmc(user.getDeptName());
        entity.setCzsj(ServerTimeUtils.getCurrentTime());

        if (StringUtils.isBlank(entity.getLssfzslbz())) {
            entity.setLssfzslbz(Constants.NO);//是否办理临时身份证：办理临时身份证时将这个字段设置为1(YES)
        }
        if (StringUtils.isBlank(entity.getSlyckrxsfbd())) {
            entity.setSlyckrxsfbd(Constants.NO); // 受理与常口人像是否比对, 默认为否
        }
        if (StringUtils.isBlank(entity.getSlylszwsfbd())) {
            entity.setSlylszwsfbd(Constants.NO); // 受理与历史指纹是否比对, 默认为否
        }
        if (StringUtils.isBlank(entity.getLzszwsfhy())) {
            entity.setLzszwsfhy(Constants.NO); // 领证时指纹是否核验, 默认为否
        }
        if (StringUtils.isBlank(entity.getLzssfjhjz())) {
            entity.setLzssfjhjz(Constants.NO); // 领证时是否交回旧证, 默认为否
        }

        //取得修正后的联系电话
        entity.setCzylxdh(user.getMobile());

        //20211015 制证类型由中间件设置，户籍地与受理地一致则设置1 不一致则设置6
        entity.setZzlx(CkCommonUtils.getZzlx(entity.getHjdsjgsdwdm(), entity.getSldsjgsdwdm()));
        //如果前端没有传区域范围代码，则自行根据受理地数据归属单位代码和户籍地数据归属单位代码判断
        //20241022调整为后端生成
        if (StringUtils.isBlank(entity.getQyfwdm())) {
            entity.setQyfwdm(CkCommonUtils.getQyfwdm(entity.getSldsjgsdwdm(), entity.getHjdsjgsdwdm()));
        }
        //20241017 hubin 新增辖区类型
        entity.setXqlx(xtywqxManager.getXqlx(entity.getHjdsjgsdwdm(), SecurityUtils.getCurrentUser().getUserId()));
        // 20241022 hubin 如果受理方式新为空，则通过受理方式进行转换
        if (StringUtils.isBlank(entity.getSlfsx())) {
            entity.setSlfsx(CkCommonUtils.getSlfsToSlfsx(entity.getSlfs()));
        }
    }

    private List<String> getSlztList(ZjtSlxxbDTO dto) {
        List<String> slztList = new ArrayList<>();
        if (Boolean.TRUE.equals(dto.getSlztYsh())) {
            if (Boolean.TRUE.equals(dto.getSnzj()) && Boolean.FALSE.equals(dto.getKszj())) {
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_QXSHTG);
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_QXSHBTG);
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_YQF);
            } else if (Boolean.FALSE.equals(dto.getSnzj()) && Boolean.TRUE.equals(dto.getKszj())) {
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_KSSHTG);
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_KSSHBTG);
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_KSYQF);
            } else {
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_QXSHTG);
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_QXSHBTG);
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_YQF);
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_KSSHTG);
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_KSSHBTG);
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_KSYQF);
            }
        } else if (Boolean.FALSE.equals(dto.getSlztYsh())) {
            if (Boolean.TRUE.equals(dto.getSnzj()) && Boolean.FALSE.equals(dto.getKszj())) {
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_ZWYCJ);
            } else if (Boolean.FALSE.equals(dto.getSnzj()) && Boolean.TRUE.equals(dto.getKszj())) {
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_DKSSHQF);
            } else {
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_ZWYCJ);
                slztList.add(ZjConstant.ZJ_BLBZ_2ID_DKSSHQF);
            }
        }

        // 待签发的受理状态
        if (Boolean.TRUE.equals(dto.getSlztDqf())) {
            slztList.add(ZjConstant.ZJ_BLBZ_2ID_QXSHTG);
            slztList.add(ZjConstant.ZJ_BLBZ_2ID_KSSHTG);
        }

        // 受理信息初审查询状态
        if (CkBaseConstants.SLXXCS_CXZT_WSFHYSF.equals(dto.getCscxzt())) {
            slztList.add(ZjConstant.ZJ_BLBZ_2ID_SJWZ);
            slztList.add(ZjConstant.ZJ_BLBZ_2ID_PCSDSH);
        } else if (CkBaseConstants.SLXXCS_CXZT_WSF.equals(dto.getCscxzt())) {
            slztList.add(ZjConstant.ZJ_BLBZ_2ID_SJWZ);
        } else if (CkBaseConstants.SLXXCS_CXZT_YSF.equals(dto.getCscxzt())) {
            slztList.add(ZjConstant.ZJ_BLBZ_2ID_PCSDSH);
        } else if (CkBaseConstants.SLXXCS_CXZT_YSH.equals(dto.getCscxzt())) {
            slztList.add(ZjConstant.ZJ_BLBZ_2ID_ZWYCJ);
            slztList.add(ZjConstant.ZJ_BLBZ_2ID_CSBTG);
        }
        return slztList;
    }

}
