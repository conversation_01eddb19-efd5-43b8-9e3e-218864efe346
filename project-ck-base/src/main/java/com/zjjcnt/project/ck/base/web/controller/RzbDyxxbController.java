package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.RzbDyxxbConvert;
import com.zjjcnt.project.ck.base.dto.RzbDyxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.RzbDyxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.RzbDyxxbPageResp;
import com.zjjcnt.project.ck.base.service.RzbDyxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 打印信息表前端控制器
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@RequiredArgsConstructor
@Tag(name = "打印信息表")
@RestController
@RequestMapping("/rzbDyxxb")
public class RzbDyxxbController extends AbstractCrudController<RzbDyxxbDTO> {

    private final RzbDyxxbService rzbDyxxbService;

    @Override
    protected IBaseService<RzbDyxxbDTO> getService() {
        return rzbDyxxbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询打印信息表")
    public CommonResult<PageResult<RzbDyxxbPageResp>> page(RzbDyxxbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, RzbDyxxbConvert.INSTANCE::convertToDTO, RzbDyxxbConvert.INSTANCE::convertToPageResp);
    }

}

