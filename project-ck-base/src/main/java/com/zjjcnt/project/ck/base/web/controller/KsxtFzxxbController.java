package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.KsxtFzxxbConvert;
import com.zjjcnt.project.ck.base.dto.KsxtFzxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtFzxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtFzxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtFzxxbViewResp;
import com.zjjcnt.project.ck.base.service.KsxtFzxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 跨省协同辅助信息表前端控制器
*
* <AUTHOR>
* @date 2025-08-05 13:49:11
*/
@RequiredArgsConstructor
@Tag(name = "跨省协同辅助信息表")
@RestController
@RequestMapping("/ksxtFzxxb")
public class KsxtFzxxbController extends AbstractCrudController<KsxtFzxxbDTO> {

    private final KsxtFzxxbService ksxtFzxxbService;

    @Override
    protected IBaseService<KsxtFzxxbDTO> getService() {
        return ksxtFzxxbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询跨省协同辅助信息表")
    public CommonResult<PageResult<KsxtFzxxbPageResp>> page(KsxtFzxxbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, KsxtFzxxbConvert.INSTANCE::convertToDTO, KsxtFzxxbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看跨省协同辅助信息表详情")
    public CommonResult<KsxtFzxxbViewResp> view(String id) {
        return super.view(id, KsxtFzxxbConvert.INSTANCE::convertToViewResp);
    }

}
