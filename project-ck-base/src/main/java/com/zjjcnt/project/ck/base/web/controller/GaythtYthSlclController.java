package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.GaythtYthSlclConvert;
import com.zjjcnt.project.ck.base.convert.GaythtYthSlclsjConvert;
import com.zjjcnt.project.ck.base.dto.GaythtYthSlclDTO;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclCreateReq;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclPageReq;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclSaveYwslClysjReq;
import com.zjjcnt.project.ck.base.dto.req.GaythtYthSlclUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclItemResp;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclPageResp;
import com.zjjcnt.project.ck.base.dto.resp.GaythtYthSlclViewResp;
import com.zjjcnt.project.ck.base.service.GaythtYthSlclService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 前端控制器
*
* <AUTHOR>
* @date 2025-07-25 16:34:24
*/
@RequiredArgsConstructor
@Tag(name = "一体化受理材料")
@RestController
@RequestMapping("/gaythtYthSlcl")
public class GaythtYthSlclController extends AbstractCrudController<GaythtYthSlclDTO> {

    private final GaythtYthSlclService gaythtYthSlclService;

    @Override
    protected IBaseService<GaythtYthSlclDTO> getService() {
        return gaythtYthSlclService;
    }

    @GetMapping("page")
    @Operation(summary = "查询 F42104")
    public CommonResult<PageResult<GaythtYthSlclPageResp>> page(GaythtYthSlclPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, GaythtYthSlclConvert.INSTANCE::convertToDTO, GaythtYthSlclConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看详情")
    public CommonResult<GaythtYthSlclViewResp> view(String id) {
        return super.view(id, GaythtYthSlclConvert.INSTANCE::convertToViewResp);
    }

    @GetMapping("list")
    @Operation(summary = "列表")
    public CommonResult<List<GaythtYthSlclItemResp>> list(@RequestParam String projectId) {
        return CommonResult.success(GaythtYthSlclConvert.INSTANCE.convertToItemResp(gaythtYthSlclService.list(projectId)));
    }

    @PostMapping("create")
    @Operation(summary = "新增")
    public CommonResult<GaythtYthSlclCreateResp> create(@RequestBody GaythtYthSlclCreateReq req) {
        return super.create(req, GaythtYthSlclConvert.INSTANCE::convertToDTO, GaythtYthSlclConvert.INSTANCE::convertToCreateResp);
    }

    @PostMapping("saveYwslClysjAll")
    @Operation(summary = "保存业务受理材料")
    public CommonResult<Boolean> saveYwslClysjAll(@Validated @RequestBody GaythtYthSlclSaveYwslClysjReq req) {
        gaythtYthSlclService.saveYwslClysjAll(GaythtYthSlclsjConvert.INSTANCE.convertToDTO(req));
        return CommonResult.success(true);
    }

    @PostMapping("update")
    @Operation(summary = "编辑")
    public CommonResult<Boolean> update(@RequestBody GaythtYthSlclUpdateReq req) {
        return super.update(req, GaythtYthSlclConvert.INSTANCE::convertToDTO);
    }

    @DeleteMapping(value = "delete")
    @Operation(summary = "删除")
    public CommonResult<Boolean> delete(@RequestBody String[] id) {
        return super.delete(id);
    }

}
