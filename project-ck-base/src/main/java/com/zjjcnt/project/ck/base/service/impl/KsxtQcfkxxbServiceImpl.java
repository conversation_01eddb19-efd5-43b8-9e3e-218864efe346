package com.zjjcnt.project.ck.base.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.base.convert.KsxtQcfkxxbConvert;
import com.zjjcnt.project.ck.base.dto.KsxtQcfkxxbDTO;
import com.zjjcnt.project.ck.base.entity.KsxtQcfkxxbDO;
import com.zjjcnt.project.ck.base.mapper.KsxtQcfkxxbMapper;
import com.zjjcnt.project.ck.base.service.KsxtQcfkxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 跨省协同迁出反馈信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 */
@Service
public class KsxtQcfkxxbServiceImpl extends AbstractBaseServiceImpl<KsxtQcfkxxbMapper, KsxtQcfkxxbDO, KsxtQcfkxxbDTO> implements KsxtQcfkxxbService {

    KsxtQcfkxxbConvert convert = KsxtQcfkxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(KsxtQcfkxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(KsxtQcfkxxbDO::getKsxtid, dto.getKsxtid(), StringUtils.isNotEmpty(dto.getKsxtid()));
        query.eq(KsxtQcfkxxbDO::getZaglywxtbh, dto.getZaglywxtbh(), StringUtils.isNotEmpty(dto.getZaglywxtbh()));
        query.eq(KsxtQcfkxxbDO::getCzrgmsfhm, ColumnUtils.encryptColumn(dto.getCzrgmsfhm()), StringUtils.isNotEmpty(dto.getCzrgmsfhm()));
        query.eq(KsxtQcfkxxbDO::getCzrxm, ColumnUtils.encryptColumn(dto.getCzrxm()), StringUtils.isNotEmpty(dto.getCzrxm()));
        query.eq(KsxtQcfkxxbDO::getYzzssxqdm, dto.getYzzssxqdm(), StringUtils.isNotEmpty(dto.getYzzssxqdm()));
        query.eq(KsxtQcfkxxbDO::getYzzqhnxxdz, dto.getYzzqhnxxdz(), StringUtils.isNotEmpty(dto.getYzzqhnxxdz()));
        query.eq(KsxtQcfkxxbDO::getYzzcxfldm, dto.getYzzcxfldm(), StringUtils.isNotEmpty(dto.getYzzcxfldm()));
        query.eq(KsxtQcfkxxbDO::getQwdssxqdm, dto.getQwdssxqdm(), StringUtils.isNotEmpty(dto.getQwdssxqdm()));
        query.eq(KsxtQcfkxxbDO::getQwdqhnxxdz, dto.getQwdqhnxxdz(), StringUtils.isNotEmpty(dto.getQwdqhnxxdz()));
        query.eq(KsxtQcfkxxbDO::getQwdhkdjjggajgjgdm, dto.getQwdhkdjjggajgjgdm(), StringUtils.isNotEmpty(dto.getQwdhkdjjggajgjgdm()));
        query.eq(KsxtQcfkxxbDO::getQwdhkdjjggajgmc, dto.getQwdhkdjjggajgmc(), StringUtils.isNotEmpty(dto.getQwdhkdjjggajgmc()));
        query.eq(KsxtQcfkxxbDO::getQyzbh, dto.getQyzbh(), StringUtils.isNotEmpty(dto.getQyzbh()));
        query.eq(KsxtQcfkxxbDO::getQyzqfjggajgjgdm, dto.getQyzqfjggajgjgdm(), StringUtils.isNotEmpty(dto.getQyzqfjggajgjgdm()));
        query.eq(KsxtQcfkxxbDO::getQyzqfjggajgmc, dto.getQyzqfjggajgmc(), StringUtils.isNotEmpty(dto.getQyzqfjggajgmc()));
        query.ge(KsxtQcfkxxbDO::getQyzqfrq, dto.getQyzqfrqStart(), StringUtils.isNotEmpty(dto.getQyzqfrqStart()));
        query.le(KsxtQcfkxxbDO::getQyzqfrq, dto.getQyzqfrqEnd(), StringUtils.isNotEmpty(dto.getQyzqfrqEnd()));
        query.ge(KsxtQcfkxxbDO::getQyzyxqjzrq, dto.getQyzyxqjzrqStart(), StringUtils.isNotEmpty(dto.getQyzyxqjzrqStart()));
        query.le(KsxtQcfkxxbDO::getQyzyxqjzrq, dto.getQyzyxqjzrqEnd(), StringUtils.isNotEmpty(dto.getQyzyxqjzrqEnd()));
        query.eq(KsxtQcfkxxbDO::getDzqydzzzbz, dto.getDzqydzzzbz(), StringUtils.isNotEmpty(dto.getDzqydzzzbz()));
        query.eq(KsxtQcfkxxbDO::getDzqyzagldzzzbh, dto.getDzqyzagldzzzbh(), StringUtils.isNotEmpty(dto.getDzqyzagldzzzbh()));
        query.eq(KsxtQcfkxxbDO::getZqzbh, dto.getZqzbh(), StringUtils.isNotEmpty(dto.getZqzbh()));
        query.eq(KsxtQcfkxxbDO::getDzzqdzzzbz, dto.getDzzqdzzzbz(), StringUtils.isNotEmpty(dto.getDzzqdzzzbz()));
        query.eq(KsxtQcfkxxbDO::getDzzqzagldzzzbh, dto.getDzzqzagldzzzbh(), StringUtils.isNotEmpty(dto.getDzzqzagldzzzbh()));
        query.eq(KsxtQcfkxxbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(KsxtQcfkxxbDO::getYczrgxjtgxdm, dto.getYczrgxjtgxdm(), StringUtils.isNotEmpty(dto.getYczrgxjtgxdm()));
        query.eq(KsxtQcfkxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(KsxtQcfkxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(KsxtQcfkxxbDO::getCym, dto.getCym(), StringUtils.isNotEmpty(dto.getCym()));
        query.eq(KsxtQcfkxxbDO::getXbdm, dto.getXbdm(), StringUtils.isNotEmpty(dto.getXbdm()));
        query.eq(KsxtQcfkxxbDO::getMzdm, dto.getMzdm(), StringUtils.isNotEmpty(dto.getMzdm()));
        query.ge(KsxtQcfkxxbDO::getCsrq, dto.getCsrqStart(), StringUtils.isNotEmpty(dto.getCsrqStart()));
        query.le(KsxtQcfkxxbDO::getCsrq, dto.getCsrqEnd(), StringUtils.isNotEmpty(dto.getCsrqEnd()));
        query.eq(KsxtQcfkxxbDO::getCsdgjhdqdm, dto.getCsdgjhdqdm(), StringUtils.isNotEmpty(dto.getCsdgjhdqdm()));
        query.eq(KsxtQcfkxxbDO::getCsdssxqdm, dto.getCsdssxqdm(), StringUtils.isNotEmpty(dto.getCsdssxqdm()));
        query.eq(KsxtQcfkxxbDO::getCsdqhnxxdz, dto.getCsdqhnxxdz(), StringUtils.isNotEmpty(dto.getCsdqhnxxdz()));
        query.eq(KsxtQcfkxxbDO::getJggjhdqdm, dto.getJggjhdqdm(), StringUtils.isNotEmpty(dto.getJggjhdqdm()));
        query.eq(KsxtQcfkxxbDO::getJgssxqdm, dto.getJgssxqdm(), StringUtils.isNotEmpty(dto.getJgssxqdm()));
        query.eq(KsxtQcfkxxbDO::getJgqhnxxdz, dto.getJgqhnxxdz(), StringUtils.isNotEmpty(dto.getJgqhnxxdz()));
        query.eq(KsxtQcfkxxbDO::getXldm, dto.getXldm(), StringUtils.isNotEmpty(dto.getXldm()));
        query.eq(KsxtQcfkxxbDO::getHyzkdm, dto.getHyzkdm(), StringUtils.isNotEmpty(dto.getHyzkdm()));
        query.eq(KsxtQcfkxxbDO::getZy, dto.getZy(), StringUtils.isNotEmpty(dto.getZy()));
        query.eq(KsxtQcfkxxbDO::getQyldyydm, dto.getQyldyydm(), StringUtils.isNotEmpty(dto.getQyldyydm()));
        query.eq(KsxtQcfkxxbDO::getHjdgajgjgdm, dto.getHjdgajgjgdm(), StringUtils.isNotEmpty(dto.getHjdgajgjgdm()));
        query.eq(KsxtQcfkxxbDO::getHjdgajgmc, dto.getHjdgajgmc(), StringUtils.isNotEmpty(dto.getHjdgajgmc()));
        query.eq(KsxtQcfkxxbDO::getHjdlxdh, ColumnUtils.encryptColumn(dto.getHjdlxdh()), StringUtils.isNotEmpty(dto.getHjdlxdh()));
        query.eq(KsxtQcfkxxbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(KsxtQcfkxxbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(KsxtQcfkxxbDO::getBlrxm, ColumnUtils.encryptColumn(dto.getBlrxm()), StringUtils.isNotEmpty(dto.getBlrxm()));
        query.ge(KsxtQcfkxxbDO::getBlsj, dto.getBlsjStart(), StringUtils.isNotEmpty(dto.getBlsjStart()));
        query.le(KsxtQcfkxxbDO::getBlsj, dto.getBlsjEnd(), StringUtils.isNotEmpty(dto.getBlsjEnd()));
        query.eq(KsxtQcfkxxbDO::getQyfwdm, dto.getQyfwdm(), StringUtils.isNotEmpty(dto.getQyfwdm()));
        query.eq(KsxtQcfkxxbDO::getFsdwsjgsdwdm, dto.getFsdwsjgsdwdm(), StringUtils.isNotEmpty(dto.getFsdwsjgsdwdm()));
        query.eq(KsxtQcfkxxbDO::getFsdwsjgsdwmc, dto.getFsdwsjgsdwmc(), StringUtils.isNotEmpty(dto.getFsdwsjgsdwmc()));
        query.eq(KsxtQcfkxxbDO::getJsdwsjgsdwdm, dto.getJsdwsjgsdwdm(), StringUtils.isNotEmpty(dto.getJsdwsjgsdwdm()));
        query.eq(KsxtQcfkxxbDO::getJsdwsjgsdwmc, dto.getJsdwsjgsdwmc(), StringUtils.isNotEmpty(dto.getJsdwsjgsdwmc()));
        query.ge(KsxtQcfkxxbDO::getRksj, dto.getRksjStart(), StringUtils.isNotEmpty(dto.getRksjStart()));
        query.le(KsxtQcfkxxbDO::getRksj, dto.getRksjEnd(), StringUtils.isNotEmpty(dto.getRksjEnd()));
        return query;
    }

    @Override
    public KsxtQcfkxxbDTO convertToDTO(KsxtQcfkxxbDO ksxtQcfkxxbDO) {
        return convert.convert(ksxtQcfkxxbDO);
    }

    @Override
    public KsxtQcfkxxbDO convertToDO(KsxtQcfkxxbDTO ksxtQcfkxxbDTO) {
        return convert.convertToDO(ksxtQcfkxxbDTO);
    }
}
