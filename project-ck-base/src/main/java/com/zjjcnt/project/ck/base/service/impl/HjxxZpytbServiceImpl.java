package com.zjjcnt.project.ck.base.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.base.convert.HjxxZpytbConvert;
import com.zjjcnt.project.ck.base.dto.HjxxZpytbDTO;
import com.zjjcnt.project.ck.base.entity.HjxxZpytbDO;
import com.zjjcnt.project.ck.base.mapper.HjxxZpytbMapper;
import com.zjjcnt.project.ck.base.service.HjxxZpytbService;
import com.zjjcnt.project.ck.core.file.FileObjectService;
import com.zjjcnt.project.ck.core.file.domain.DefaultFileMeta;
import com.zjjcnt.project.ck.core.file.domain.DefaultFileObject;
import com.zjjcnt.project.ck.core.file.domain.FileMeta;
import com.zjjcnt.project.ck.core.file.domain.FileObject;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.dto.RktYwslClqdDTO;
import com.zjjcnt.project.ck.sysadmin.dto.RktYwslClysjDTO;
import com.zjjcnt.project.ck.sysadmin.service.RktYwslClqdService;
import com.zjjcnt.project.ck.sysadmin.service.RktYwslClysjService;
import jakarta.annotation.Resource;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Base64;
import java.util.Objects;

/**
 * 照片原图表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
@Service
public class HjxxZpytbServiceImpl extends AbstractBaseServiceImpl<HjxxZpytbMapper, HjxxZpytbDO, HjxxZpytbDTO> implements HjxxZpytbService {

    HjxxZpytbConvert convert = HjxxZpytbConvert.INSTANCE;

    @Resource(name = "zpytFileObjectService")
    private FileObjectService fileObjectService;

    @Resource
    private RktYwslClqdService rktYwslClqdService;

    @Resource
    private RktYwslClysjService rktYwslClysjService;

    @Override
    protected QueryWrapper genQueryWrapper(HjxxZpytbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(HjxxZpytbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.ge(HjxxZpytbDO::getBcsj, dto.getBcsjStart(), StringUtils.isNotEmpty(dto.getBcsjStart()));
        query.le(HjxxZpytbDO::getBcsj, dto.getBcsjEnd(), StringUtils.isNotEmpty(dto.getBcsjEnd()));
        query.eq(HjxxZpytbDO::getZpdx, dto.getZpdx(), Objects.nonNull(dto.getZpdx()));
        query.eq(HjxxZpytbDO::getZpwjlx, dto.getZpwjlx(), StringUtils.isNotEmpty(dto.getZpwjlx()));
        query.eq(HjxxZpytbDO::getZpsjdzlx, dto.getZpsjdzlx(), StringUtils.isNotEmpty(dto.getZpsjdzlx()));
        query.eq(HjxxZpytbDO::getZpsjdz, dto.getZpsjdz(), StringUtils.isNotEmpty(dto.getZpsjdz()));
        query.eq(HjxxZpytbDO::getZphash, dto.getZphash(), StringUtils.isNotEmpty(dto.getZphash()));
        query.eq(HjxxZpytbDO::getZpcjlx, dto.getZpcjlx(), StringUtils.isNotEmpty(dto.getZpcjlx()));
        query.eq(HjxxZpytbDO::getZpsbbsh, dto.getZpsbbsh(), StringUtils.isNotEmpty(dto.getZpsbbsh()));
        query.eq(HjxxZpytbDO::getZpsbppxh, dto.getZpsbppxh(), StringUtils.isNotEmpty(dto.getZpsbppxh()));
        query.eq(HjxxZpytbDO::getZpsbppxhdm, dto.getZpsbppxhdm(), StringUtils.isNotEmpty(dto.getZpsbppxhdm()));
        query.eq(HjxxZpytbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        return query;
    }

    @Override
    public HjxxZpytbDTO convertToDTO(HjxxZpytbDO hjxxZpytbDO) {
        return convert.convert(hjxxZpytbDO);
    }

    @Override
    public HjxxZpytbDO convertToDO(HjxxZpytbDTO hjxxZpytbDTO) {
        return convert.convertToDO(hjxxZpytbDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public HjxxZpytbDTO insert(HjxxZpytbDTO dto) {

        byte[] zpytData = dto.getZpytData();

        dto.setZpdx((long) zpytData.length);

        dto.setZphash(DigestUtils.md5Hex(dto.getZpytData()));

        dto.setBcsj(StringUtils.defaultIfEmpty(dto.getBcsj(), ServerTimeUtils.getCurrentTime()));
        dto.setZpwjlx(StringUtils.defaultIfBlank(dto.getZpwjlx(), MediaType.IMAGE_JPEG_VALUE));

        FileObject fo = new DefaultFileObject(zpytData, MediaType.IMAGE_JPEG_VALUE, dto.getGmsfhm());

        String wjdz = fileObjectService.save(fo, "zpyp");
        String zpsjdzlx = fileObjectService.getStorageType();

        dto.setZpsjdz(wjdz);
        dto.setZpsjdzlx(zpsjdzlx);

        return super.insert(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveClysj(String ywslh, Long zpytbid, String xm) {

        if (ywslh == null || zpytbid == null || ywslh.isEmpty()) {
            return;
        }

        //查询照片原图信息
        HjxxZpytbDTO zpytb = findById(zpytbid);
        if (zpytb == null) {
            return;
        }

        String nowTime = ServerTimeUtils.getCurrentTime();

        // 计算当前最大序号
        int maxXh = rktYwslClqdService.findMaxXh(ywslh);
        maxXh++;

        //保存材料清单信息
        RktYwslClqdDTO pocldq = new RktYwslClqdDTO();
        pocldq.setYwslh(ywslh);
        pocldq.setLcywlx("0102010100");
        pocldq.setXh(maxXh);
        pocldq.setCllxdm("9080");
        pocldq.setCllxmc("办理身份证的照片原图");
        pocldq.setYjfs(0);
        pocldq.setFyjfs(0);
        pocldq.setClsl(1);
        rktYwslClqdService.insert(pocldq);

        RktYwslClysjDTO poclysj = new RktYwslClysjDTO();
        poclysj.setSlclqdbh(pocldq.getSlclqdbh());
        poclysj.setYwslh(ywslh);
        poclysj.setLcywlx("0102010100");
        poclysj.setXh(rktYwslClysjService.calculateXh(pocldq.getSlclqdbh(), pocldq.getXh()));
        poclysj.setClmc(xm + "的身份证照片原图");
        poclysj.setCllxdm("9080");
        poclysj.setCllxmc("办理身份证的照片原图");
        poclysj.setWjdx(zpytb.getZpdx());
        poclysj.setWjlx(zpytb.getZpwjlx());
        poclysj.setWjsjdz(zpytb.getZpsjdz());
        poclysj.setHash(zpytb.getZphash());
        poclysj.setSjgsdwdm("33");
        poclysj.setFycs(1);
        poclysj.setCllylx("1");
        poclysj.setDycs(0);
        // 设置采集人信息

        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();

        poclysj.setClcjr(currentUser.getName());
        poclysj.setClcjrid(String.valueOf(currentUser.getUserId()));
        poclysj.setClcjrip(currentUser.getRemoteAddress());
        poclysj.setClcjsj(nowTime);
        poclysj.setClxgr(currentUser.getName());
        poclysj.setClxgrid(String.valueOf(currentUser.getUserId()));
        poclysj.setClxgrip(currentUser.getRemoteAddress());
        poclysj.setClxgsj(nowTime);
        poclysj.setYxbz(Constants.YES);
        rktYwslClysjService.insert(poclysj);
    }

    @Override
    public String getBase64Zp(String zpsjdz) {

        if (StringUtils.isEmpty(zpsjdz)) {
            return null;
        }
        FileMeta fileMeta = new DefaultFileMeta();
        fileMeta.setFileLocation(zpsjdz);

        byte[] data = fileObjectService.getData(fileMeta);
        if (ArrayUtils.isEmpty(data)) {
            return null;
        }
        return Base64.getEncoder().encodeToString(data);
    }
}
