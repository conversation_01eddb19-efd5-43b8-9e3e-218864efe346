package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.KsxtCssbxxbConvert;
import com.zjjcnt.project.ck.base.dto.KsxtCssbxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtCssbxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtCssbxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtCssbxxbViewResp;
import com.zjjcnt.project.ck.base.service.KsxtCssbxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 跨省协同出生申报信息表前端控制器
*
* <AUTHOR>
* @date 2025-08-05 13:59:15
*/
@RequiredArgsConstructor
@Tag(name = "跨省协同出生申报信息表")
@RestController
@RequestMapping("/ksxtCssbxxb")
public class KsxtCssbxxbController extends AbstractCrudController<KsxtCssbxxbDTO> {

    private final KsxtCssbxxbService ksxtCssbxxbService;

    @Override
    protected IBaseService<KsxtCssbxxbDTO> getService() {
        return ksxtCssbxxbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询跨省协同出生申报信息表")
    public CommonResult<PageResult<KsxtCssbxxbPageResp>> page(KsxtCssbxxbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, KsxtCssbxxbConvert.INSTANCE::convertToDTO, KsxtCssbxxbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看跨省协同出生申报信息表详情")
    public CommonResult<KsxtCssbxxbViewResp> view(String id) {
        return super.view(id, KsxtCssbxxbConvert.INSTANCE::convertToViewResp);
    }

}
