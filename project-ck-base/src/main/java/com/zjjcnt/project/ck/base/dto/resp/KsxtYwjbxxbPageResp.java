package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 跨省协同基本信息表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-05 13:49:11
 */
@Data
@Schema(description = "跨省协同基本信息表响应对象")
public class KsxtYwjbxxbPageResp {

    @Schema(description = "跨省业务ID")
    private String ksxtid;

    @Schema(description = "治安管理业务协同编号")
    private String zaglywxtbh;

    @Dict(value = CkDictTypeConstants.DM_ZAGLYWLBDM)
    @Schema(description = "治安管理业务类别代码")
    private String zaglywlbdm;

    @Schema(description = "治安管理政务服务事项编码")
    private String zaglzwfwsxbm;

    @Dict(value = CkDictTypeConstants.DM_GABKSYWLX)
    @Schema(description = "业务类型")
    private String ywlx;

    @Schema(description = "申请人公民身份号码")
    private String sqrgmsfhm;

    @Schema(description = "申请人姓名")
    private String sqrxm;

    @Schema(description = "申请人联系电话")
    private String sqrlxdh;

    @Schema(description = "户主公民身份号码")
    private String hzgmsfhm;

    @Schema(description = "户主姓名")
    private String hzxm;

    @Dict(CkDictTypeConstants.DM_QYFW)
    @Schema(description = "迁移范围代码")
    private String qyfwdm;

    @Schema(description = "户籍地数据归属单位代码")
    private String hjdsjgsdwdm;

    @Schema(description = "户籍地数据归属单位名称")
    private String hjdsjgsdwmc;

    @Schema(description = "户籍地联系电话")
    private String hjdlxdh;

    @Schema(description = "受理地数据归属单位代码")
    private String sldsjgsdwdm;

    @Schema(description = "受理地数据归属单位名称")
    private String sldsjgsdwmc;

    @Schema(description = "受理地联系电话")
    private String sldlxdh;

    @Schema(description = "受理人ID")
    private String slrid;

    @Schema(description = "受理人姓名")
    private String slrxm;

    @StringDateTimeFormat
    @Schema(description = "受理时间")
    private String slsj;

    @StringDateTimeFormat
    @Schema(description = "入库时间")
    private String rksj;

    @Dict(CkDictTypeConstants.DM_GABKSYWBLZT)
    @Schema(description = "办理状态")
    private String blzt;

    @Schema(description = "审批业务受理号")
    private String spywslh;

    @Dict(CkDictTypeConstants.YWBM)
    @Schema(description = "审批业务类型")
    private String spywlx;

    @StringDateTimeFormat
    @Schema(description = "业务办理时间")
    private String ywblsj;

    @Schema(description = "业务办理人姓名")
    private String ywblrxm;

    @Dict(CkDictTypeConstants.DM_KSTBSHJGDM)
    @Schema(description = "审核结果代码")
    private String shjg;

    @Schema(description = "审核结果简要情况")
    private String shms;

    @Schema(description = "审核人ID")
    private String shrid;

    @Schema(description = "审核人姓名")
    private String shrxm;

    @StringDateTimeFormat
    @Schema(description = "审核时间")
    private String shsj;

    @StringDateTimeFormat
    @Schema(description = "办结时间")
    private String bjsj;

    @Dict(CkDictTypeConstants.DM_SFBZ)
    @Schema(description = "是否需要快递标志")
    private String sfxykdbz;

    @Schema(description = "收件人_姓名")
    private String sjrxm;

    @Schema(description = "收件人_公民身份号码")
    private String sjrgmsfhm;

    @Schema(description = "收件人_联系电话")
    private String sjrlxdh;

    @Schema(description = "收件地址_省市县（区）")
    private String sjdzssxqdm;

    @Schema(description = "收件地址_区划内详细地址")
    private String sjdzqhnxxdz;
}
