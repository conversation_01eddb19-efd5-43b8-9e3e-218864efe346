package com.zjjcnt.project.ck.base.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.Data;

import com.zjjcnt.common.core.entity.IdEntity;
import com.zjjcnt.common.core.annotation.Crypto;

import com.zjjcnt.common.core.annotation.Dict;
import java.lang.Double;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import java.lang.String;
import java.lang.Integer;
import com.zjjcnt.common.core.annotation.DictSsxq;
import com.zjjcnt.common.core.annotation.StringDateFormat;

/**
 * 业务受理情况基本信息DO
 *
 * <AUTHOR>
 * @date 2025-07-22 13:26:28
 * @see com.zjjcnt.project.ck.base.dto.RktYwslJbLsDTO
 */
@Crypto
@Data
@Table("rkt_ywsl_jb_ls")
public class RktYwslJbLsDO implements IdEntity<String> {
    private static final long serialVersionUID = 721833717741926151L;

    /**
     * 
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String ywslh;

    /**
     * 流程定义ID，格式为 流程KEY：版本号：部署号
     */
    @Column(value = "lcdyid")
    private String lcdyid;

    /**
     * 
     */
    @Column(value = "lcmc")
    private String lcmc;

    /**
     * 
     */
    @Column(value = "spyj")
    private String spyj;

    /**
     * 
     */
    @Column(value = "slrs")
    private Integer slrs;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 金铖流程业务类型，扩展自D_ZAGLYWFL治安管理业务分类与代码
            0100	实有人口管理	
            0101	　户籍管理	
            0102	　居民身份证管理	
            
     */
    @Column(value = "lcywlx")
    private String lcywlx;

    /**
     * 
     */
    @Column(value = "lcslid")
    private String lcslid;

    /**
     * 关于**等几人落户的申请报告
     */
    @Column(value = "lcywbt")
    private String lcywbt;

    /**
     * 
     */
    @Column(value = "lcrwjd")
    private String lcrwjd;

    /**
     * 01数据采集中
            02民警审核中
            10派出所领导审批中
            20区县业务部门审批中
            30区县领导审批中
            40地市业务部门审批中
            50地市领导审批中
            60审批已通过
            70审批不通过
            80业务已办理
            91作废
            99已打印归档
            
     */
    @Column(value = "lczt")
    private String lczt;

    /**
     * 
     */
    @Column(value = "blzt")
    private String blzt;

    /**
     * 申请人_公民身份号码
     */
    @Crypto
    @Column(value = "sqrgmsfhm")
    private String sqrgmsfhm;

    /**
     * 申请人_姓名
     */
    @Crypto
    @Column(value = "sqrxm")
    private String sqrxm;

    /**
     * 
     */
    @Column(value = "sqrxb")
    private String sqrxb;

    /**
     * 
     */
    @Crypto
    @Column(value = "sqrlxdh")
    private String sqrlxdh;

    /**
     * 
     */
    @Column(value = "sqrybdrgx")
    private String sqrybdrgx;

    /**
     * 申请人住址省市县（区）
     */
    @Column(value = "sqrzzssxq")
    private String sqrzzssxq;

    /**
     * 申请人住址详址
     */
    @Column(value = "sqrzzxz")
    private String sqrzzxz;

    /**
     * 申请人户口登记机关
     */
    @Column(value = "sqrhkdjjg")
    private String sqrhkdjjg;

    /**
     * 收件人姓名
     */
    @Crypto
    @Column(value = "sjrxm")
    private String sjrxm;

    /**
     * 收件人联系电话
     */
    @Crypto
    @Column(value = "sjrlxdh")
    private String sjrlxdh;

    /**
     * 申请人通讯地址
     */
    @Column(value = "sqrtxdz")
    private String sqrtxdz;

    /**
     * 申请人证件名称
     */
    @Column(value = "sqrzjmc")
    private String sqrzjmc;

    /**
     * 申请人证件号码
     */
    @Column(value = "sqrzjhm")
    private String sqrzjhm;

    /**
     * 申请人工作单位
     */
    @Column(value = "sqrgzdw")
    private String sqrgzdw;

    /**
     * 户籍事项查询类别
     */
    @Column(value = "hjsxcxlb")
    private String hjsxcxlb;

    /**
     * 申请查询理由
     */
    @Column(value = "sqcxly")
    private String sqcxly;

    /**
     * 是否生成电子签章户口页
     */
    @Column(value = "sfscdzqzhky")
    private String sfscdzqzhky;

    /**
     * 是否打印户口簿首页
     */
    @Column(value = "sfdyhkbsy")
    private String sfdyhkbsy;

    /**
     * 是否按照集体户方式打印
     */
    @Column(value = "sfazjthfsdy")
    private String sfazjthfsdy;

    /**
     * 是否打印全户人员
     */
    @Column(value = "sfdyqhry")
    private String sfdyqhry;

    /**
     * 是否打印出生原因
     */
    @Column(value = "sfdycsyy")
    private String sfdycsyy;

    /**
     * 受理人联系电话
     */
    @Crypto
    @Column(value = "slrlxdh")
    private String slrlxdh;

    /**
     * 审批人联系电话
     */
    @Crypto
    @Column(value = "sprlxdh")
    private String sprlxdh;

    /**
     * 是否需要邮寄证件
     */
    @Column(value = "sfxyyjzj")
    private String sfxyyjzj;

    /**
     * 是否已经邮寄证件
     */
    @Column(value = "sfyjyjzj")
    private String sfyjyjzj;

    /**
     * 快递单号
     */
    @Column(value = "kddh")
    private String kddh;

    /**
     * 审批信息来源类别
     */
    @Column(value = "spxxlylb")
    private String spxxlylb;

    /**
     * 审批信息来源
     */
    @Column(value = "spxxly")
    private String spxxly;

    /**
     * 预申报信息编号
     */
    @Column(value = "ysbxxbh")
    private String ysbxxbh;

    /**
     * 原申报系统业务文件名称
     */
    @Column(value = "ysbxtywwjmc")
    private String ysbxtywwjmc;

    /**
     * 原申报系统业务文件类型
     */
    @Column(value = "ysbxtywwjlx")
    private String ysbxtywwjlx;

    /**
     * 
     */
    @Column(value = "sqrq")
    private String sqrq;

    /**
     * 
     */
    @Column(value = "spdwgajgjgdm")
    private String spdwgajgjgdm;

    /**
     * 
     */
    @Column(value = "spdwgajgmc")
    private String spdwgajgmc;

    /**
     * 
     */
    @Crypto
    @Column(value = "sprxm")
    private String sprxm;

    /**
     * 
     */
    @Column(value = "spsj")
    private String spsj;

    /**
     * 
     */
    @Column(value = "spjgdm")
    private String spjgdm;

    /**
     * 受理单位_公安机关机构代码
     */
    @Column(value = "sldwgajgjgdm")
    private String sldwgajgjgdm;

    /**
     * 受理单位_公安机关名称
     */
    @Column(value = "sldwgajgmc")
    private String sldwgajgmc;

    /**
     * 受理人姓名
     */
    @Crypto
    @Column(value = "slrxm")
    private String slrxm;

    /**
     * 受理时间
     */
    @Column(value = "slsj")
    private String slsj;

    /**
     * 数据归属单位代码
     */
    @Column(value = "sjgsdwdm")
    private String sjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "sjgsdwmc")
    private String sjgsdwmc;

    /**
     * 居（村）委会
     */
    @Column(value = "jcwh")
    private String jcwh;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 办理户籍业务id
     */
    @Column(value = "blhjywid")
    private String blhjywid;

    /**
     * 户籍业务办理时间
     */
    @Column(value = "hjywblsj")
    private String hjywblsj;

    /**
     * 受理人ID
     */
    @Column(value = "slrid")
    private String slrid;

    /**
     * 评价结果
     */
    @Column(value = "pjjg")
    private String pjjg;

    /**
     * 跑了几次评价
     */
    @Column(value = "pjpljc")
    private String pjpljc;

    /**
     * 评价时间
     */
    @Column(value = "pjsj")
    private String pjsj;

    /**
     * 是否需要上报0否1是
     */
    @Column(value = "sbsfxy")
    private String sbsfxy;

    /**
     * 上报状态10受理中30办结可以上报80上报完成
     */
    @Column(value = "sbzt")
    private String sbzt;

    /**
     * 上报业务类型代码
     */
    @Column(value = "sbywlx")
    private String sbywlx;

    /**
     * 上报业务名称
     */
    @Column(value = "sbywmc")
    private String sbywmc;

    /**
     * 上报申报时间
     */
    @Column(value = "sbsbsj")
    private String sbsbsj;

    /**
     * 上报受理时间
     */
    @Column(value = "sbslsj")
    private String sbslsj;

    /**
     * 上报审核时间
     */
    @Column(value = "sbshsj")
    private String sbshsj;

    /**
     * 上报办结时间
     */
    @Column(value = "sbbjsj")
    private String sbbjsj;

    /**
     * 上报审批开始时间
     */
    @Column(value = "sbspkssj")
    private String sbspkssj;

    /**
     * 上报审批结束时间
     */
    @Column(value = "sbspjssj")
    private String sbspjssj;

    /**
     * 上报审批人姓名
     */
    @Crypto
    @Column(value = "sbsprxm")
    private String sbsprxm;

    /**
     * 上报审批单位代码
     */
    @Column(value = "sbsprdwdm")
    private String sbsprdwdm;

    /**
     * 上报审批结果代码
     */
    @Column(value = "sbspjgdm")
    private String sbspjgdm;

    /**
     * 上报审批意见
     */
    @Column(value = "sbspyj")
    private String sbspyj;

    /**
     * 户籍地民警首次审核耗时(分)
     */
    @Column(value = "mjscshhs")
    private Integer mjscshhs;

    /**
     * 审批信息采集方式
     */
    @Column(value = "spxxcjfs")
    private String spxxcjfs;

    /**
     * 户主姓名
     */
    @Crypto
    @Column(value = "hzxm")
    private String hzxm;

    /**
     * 户主公民身份号码
     */
    @Crypto
    @Column(value = "hzgmsfhm")
    private String hzgmsfhm;

    /**
     * 档案备案标志
     */
    @Column(value = "dababz")
    private String dababz;

    /**
     * 档案备案时间
     */
    @Column(value = "dabasj")
    private String dabasj;

    /**
     * 入立户标志
     */
    @Column(value = "rlhbz")
    private String rlhbz;

    /**
     * 人社局审核结果
     */
    @Column(value = "rsjshjg")
    private String rsjshjg;

    /**
     * 人社局审核理由
     */
    @Column(value = "rsjshly")
    private String rsjshly;

    /**
     * 人社局审核时间
     */
    @Column(value = "rsjshsj")
    private String rsjshsj;

    /**
     * 人社局审核人
     */
    @Column(value = "rsjshr")
    private String rsjshr;

    /**
     * 高技能证书编号
     */
    @Column(value = "gjnzsbh")
    private String gjnzsbh;

    /**
     * 政务网大厅编号
     */
    @Column(value = "zwwdtbh")
    private String zwwdtbh;

    /**
     * 政务网大厅名称
     */
    @Column(value = "zwwdtmc")
    private String zwwdtmc;

    /**
     * 互联网申请类型
     */
    @Column(value = "hlwsqlx")
    private String hlwsqlx;

    /**
     * 政务大厅用户编号
     */
    @Column(value = "halluserid")
    private String halluserid;

    /**
     * 政务大厅用户名称
     */
    @Column(value = "hallusername")
    private String hallusername;

    /**
     * 互联网申报来源
     */
    @Column(value = "applyfrom")
    private String applyfrom;

    /**
     * 互联网业务办理类型
     */
    @Column(value = "bustype")
    private String bustype;

    /**
     * 是否提取互联网材料0否1是
     */
    @Column(value = "sftqhlwcl")
    private String sftqhlwcl;

    /**
     * 打印出件类型
     */
    @Column(value = "dycjlx")
    private String dycjlx;

    /**
     * 变更一件事上报标志
     */
    @Column(value = "bgyjssbbz")
    private String bgyjssbbz;

    /**
     * 变更一件事上报时间
     */
    @Column(value = "bgyjssbsj")
    private String bgyjssbsj;

    /**
     * 申请表是否标准签名
     */
    @Column(value = "sqbsfbzqm")
    private String sqbsfbzqm;

    /**
     * 常表是否标准签名
     */
    @Column(value = "cbsfbzqm")
    private String cbsfbzqm;

    /**
     * 是否跨省协同迁入
     */
    @Column(value = "sfksxtqr")
    private String sfksxtqr;

    /**
     * 其它申请人
     */
    @Column(value = "qtsqr")
    private String qtsqr;

    /**
     * 申请人人像比对时间
     */
    @Column(value = "sqrrxbdsj")
    private String sqrrxbdsj;

    /**
     * 申请人人像比对相似度
     */
    @Column(value = "sqrrxbdxsd")
    private Double sqrrxbdxsd;

    /**
     * 申请人人像比对结果
     */
    @Column(value = "sqrrxbdjg")
    private String sqrrxbdjg;

    /**
     * 申请人工作单位类别
     */
    @Column(value = "sqrgzdwlb")
    private String sqrgzdwlb;

    /**
     * 申请人工作单位统一社会信用代码
     */
    @Column(value = "sqrgzdwtyshxydm")
    private String sqrgzdwtyshxydm;

    /**
     * 其它申请人公民身份号码
     */
    @Crypto
    @Column(value = "qtsqrgmsfhm")
    private String qtsqrgmsfhm;

    /**
     * 其它申请人联系电话
     */
    @Crypto
    @Column(value = "qtsqrlxdh")
    private String qtsqrlxdh;


    @Override
    public String getId() {
        return this.ywslh;
    }

    @Override
    public void setId(String id) {
        this.ywslh = id;
    }
}
