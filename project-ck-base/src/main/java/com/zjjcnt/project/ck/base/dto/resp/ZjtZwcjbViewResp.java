package com.zjjcnt.project.ck.base.dto.resp;

import com.zjjcnt.common.core.annotation.Dict;
import com.zjjcnt.common.core.annotation.StringDateTimeFormat;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 指纹采集表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-06 10:45:36
 */
@Data
@Schema(description ="指纹采集表响应对象")
public class ZjtZwcjbViewResp {

    @Schema(description = "指纹图像ID")
    private String zwtxid;

    @Schema(description = "人员ID")
    private String ryid;

    @Schema(description = "公民身份号码")
    private String gmsfhm;

    @Schema(description = "姓名")
    private String xm;

    @Dict(CkDictTypeConstants.DM_ZWCJJGDM)
    @Schema(description = "指纹采集结果代码")
    private String zwcjjgdm;

    @Schema(description = "手指异常状况代码")
    private String szyczkdm;

    @Dict(CkDictTypeConstants.DM_ZWZCJGDM)
    @Schema(description = "指纹一注册结果")
    private String zwyzcjg;

    @Dict(CkDictTypeConstants.DM_ZWDM)
    @Schema(description = "指纹一指位")
    private String zwyzw;

    @Schema(description = "指纹一指纹图像文件编号")
    private String zwytxwjbh;

    @Schema(description = "指纹一图像质量值")
    private BigDecimal zwytxzlz;

    @Schema(description = "指纹一指纹特征数据")
    private byte[] zwyzwtzsj;

    @Dict(CkDictTypeConstants.DM_ZWZCJGDM)
    @Schema(description = "指纹二注册结果")
    private String zwezcjg;

    @Dict(CkDictTypeConstants.DM_ZWDM)
    @Schema(description = "指纹二指位")
    private String zwezw;

    @Schema(description = "指纹二指纹图像文件编号")
    private String zwetxwjbh;

    @Schema(description = "指纹二图像质量值")
    private BigDecimal zwetxzlz;

    @Schema(description = "指纹二指纹特征数据")
    private byte[] zwezwtzsj;

    @Schema(description = "指纹前端系统注册号")
    private String zwqdxtzch;

    @Schema(description = "指纹采集器id")
    private String zwcjqid;

    @Schema(description = "指纹算法版本号")
    private String zwsfbbh;

    @Schema(description = "指纹算法开发者代码")
    private String zwsfkfzdm;

    @Schema(description = "操作人ID")
    private String czrid;

    @StringDateTimeFormat
    @Schema(description = "保存时间")
    private String bcsj;

    @Schema(description = "操作员姓名")
    private String czyxm;

    @Schema(description = "操作员单位代码")
    private String czydwdm;

    @Schema(description = "操作员单位名称")
    private String czydwmc;

    @StringDateTimeFormat
    @Schema(description = "上报时间")
    private String sbsj;

    @Schema(description = "受理号")
    private String slh;

    @Schema(description = "数据归属单位代码")
    private String hjdsjgsdwdm;

    @Schema(description = "数据归属单位名称")
    private String hjdsjgsdwmc;

    @Schema(description = "数据归属单位代码")
    private String sldsjgsdwdm;

    @Schema(description = "数据归属单位名称")
    private String sldsjgsdwmc;
}
