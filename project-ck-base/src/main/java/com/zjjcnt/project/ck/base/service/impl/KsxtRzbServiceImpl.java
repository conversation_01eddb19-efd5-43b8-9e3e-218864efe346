package com.zjjcnt.project.ck.base.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.base.convert.KsxtRzbConvert;
import com.zjjcnt.project.ck.base.dto.KsxtRzbDTO;
import com.zjjcnt.project.ck.base.entity.KsxtRzbDO;
import com.zjjcnt.project.ck.base.mapper.KsxtRzbMapper;
import com.zjjcnt.project.ck.base.service.KsxtRzbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 跨省协同日志表ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-08-05 13:59:15
 */
@Service
public class KsxtRzbServiceImpl extends AbstractBaseServiceImpl<KsxtRzbMapper, KsxtRzbDO, KsxtRzbDTO> implements KsxtRzbService {

    KsxtRzbConvert convert = KsxtRzbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(KsxtRzbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(KsxtRzbDO::getKsxtid, dto.getKsxtid(), StringUtils.isNotEmpty(dto.getKsxtid()));
        query.eq(KsxtRzbDO::getZaglywxtbh, dto.getZaglywxtbh(), StringUtils.isNotEmpty(dto.getZaglywxtbh()));
        query.eq(KsxtRzbDO::getBlzt, dto.getBlzt(), StringUtils.isNotEmpty(dto.getBlzt()));
        query.ge(KsxtRzbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(KsxtRzbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(KsxtRzbDO::getCzyid, dto.getCzyid(), StringUtils.isNotEmpty(dto.getCzyid()));
        query.eq(KsxtRzbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(KsxtRzbDO::getCzip, dto.getCzip(), StringUtils.isNotEmpty(dto.getCzip()));
        query.eq(KsxtRzbDO::getCzydwdm, dto.getCzydwdm(), StringUtils.isNotEmpty(dto.getCzydwdm()));
        query.eq(KsxtRzbDO::getCzydwmc, dto.getCzydwmc(), StringUtils.isNotEmpty(dto.getCzydwmc()));
        query.eq(KsxtRzbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        return query;
    }

    @Override
    public KsxtRzbDTO convertToDTO(KsxtRzbDO ksxtRzbDO) {
        return convert.convert(ksxtRzbDO);
    }

    @Override
    public KsxtRzbDO convertToDO(KsxtRzbDTO ksxtRzbDTO) {
        return convert.convertToDO(ksxtRzbDTO);
    }
}
