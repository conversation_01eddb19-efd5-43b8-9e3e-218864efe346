package com.zjjcnt.project.ck.base.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.base.convert.ZjtBzsflsbConvert;
import com.zjjcnt.project.ck.base.dto.ZjtBzsflsbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtBzsflsbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtBzsflsbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtBzsflsbViewResp;
import com.zjjcnt.project.ck.base.service.ZjtBzsflsbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 办证收费流水表前端控制器
*
* <AUTHOR>
* @date 2025-08-06 14:38:16
*/
@RequiredArgsConstructor
@Tag(name = "办证收费流水表")
@RestController
@RequestMapping("/zjtBzsflsb")
public class ZjtBzsflsbController extends AbstractCrudController<ZjtBzsflsbDTO> {

    private final ZjtBzsflsbService zjtBzsflsbService;

    @Override
    protected IBaseService<ZjtBzsflsbDTO> getService() {
        return zjtBzsflsbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询办证收费流水表")
    public CommonResult<PageResult<ZjtBzsflsbPageResp>> page(ZjtBzsflsbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, ZjtBzsflsbConvert.INSTANCE::convertToDTO, ZjtBzsflsbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看办证收费流水表详情")
    public CommonResult<ZjtBzsflsbViewResp> view(String id) {
        return super.view(id, ZjtBzsflsbConvert.INSTANCE::convertToViewResp);
    }

}
